# Core/GraphBase.py

import uuid
from blinker import Signal
from collections import Counter
from PyFlow.Core.Utils import get_raw_node_instance
from PyFlow.Core.PackageManager import get_pin_default_value_by_type
from PyFlow.Core.Variable import Variable
from PyFlow.Core.Interfaces import ISerializable
import weakref
from PyFlow.Core.Common import arePinsConnected, connectPins
from PyFlow.Core.Enums import AccessLevel

class GraphBase(ISerializable):
    """Data structure representing a nodes graph
    """

    __slots__ = (
        'graphManager', '_isRoot', 'nameChanged', 'categoryChanged', '__name', '__category',
        '_parentGraph', 'childGraphs', '_nodes', '_vars', 'uid', '__weakref__'
    )

    def __init__(
        self, name, manager, parentGraph=None, category="", uid=None, *args, **kwargs):
        super(GraphBase, self).__init__(*args, **kwargs)
        self.graphManager = manager
        self._isRoot = False

        self.nameChanged = Signal(str)
        self.categoryChanged = Signal(str)

        self.__name = name
        self.__category = category

        self._parentGraph = None
        self.childGraphs = set()

        self.parentGraph = parentGraph

        self._nodes = {}
        self._vars = {}
        self.uid = uuid.uuid4() if uid is None else uid

        manager.add(self)

    def setIsRoot(self, bIsRoot):
        self._isRoot = bIsRoot

    def isRoot(self):
        return self._isRoot

    def getVars(self):
        return self._vars

    @property
    def parentGraph(self):
        return self._parentGraph

    @parentGraph.setter
    def parentGraph(self, newParentGraph):
        if self.isRoot():
            self._parentGraph = None
            return

        if newParentGraph is not None:
            if self._parentGraph is not None:
                if self in self._parentGraph.childGraphs:
                    self._parentGraph.childGraphs.remove(self)
            newParentGraph.childGraphs.add(self)
            self._parentGraph = newParentGraph

    def depth(self):
        result = 1
        parent = self._parentGraph
        while parent is not None:
            result += 1
            parent = parent.parentGraph
        return result

    def getVarList(self):
        result = list(self._vars.values())
        parent = self._parentGraph
        while parent is not None:
            result += list(parent._vars.values())
            parent = parent.parentGraph
        return result

    def serialize(self, *args, **kwargs):
        result = {
            "name": self.name,
            "category": self.category,
            "vars": [v.serialize() for v in self._vars.values()],
            "nodes": [n.serialize() for n in self._nodes.values()],
            "depth": self.depth(),
            "isRoot": self.isRoot(),
            "parentGraphName": str(self._parentGraph.name)
            if self._parentGraph is not None
            else str(None),
        }
        return result

    def populateFromJson(self, jsonData):
        self.clear()
        self.name = self.graphManager.getUniqGraphName(jsonData["name"])
        self.category = jsonData["category"]
        self.setIsRoot(jsonData["isRoot"])
        if self.isRoot():
            self.name = "root"
        for varJson in jsonData["vars"]:
            var = Variable.deserialize(self, varJson)
            self._vars[var.uid] = var
        for nodeJson in jsonData["nodes"]:
            nodeKwargs = {}
            if nodeJson["type"] in ("getVar", "setVar"):
                nodeKwargs["var"] = self._vars[uuid.UUID(nodeJson["varUid"])]
            nodeJson["owningGraphName"] = self.name
            node = get_raw_node_instance(
                nodeJson["type"],
                packageName=nodeJson["package"],
                libName=nodeJson["lib"],
                **nodeKwargs,
            )
            self.addNode(node, nodeJson)

        for nodeJson in jsonData["nodes"]:
            for nodeOutputJson in nodeJson["outputs"]:
                for linkData in nodeOutputJson["linkedTo"]:
                    try:
                        lhsNode = self._nodes[uuid.UUID(linkData["lhsNodeUid"])]
                    except Exception as e:
                        lhsNode = self.findNode(linkData["lhsNodeName"])

                    try:
                        lhsPin = lhsNode.orderedOutputs[linkData["outPinId"]]
                    except Exception as e:
                        continue

                    try:
                        rhsNode = self._nodes[uuid.UUID(linkData["rhsNodeUid"])]
                    except Exception as e:
                        rhsNode = self.findNode(linkData["rhsNodeName"])

                    try:
                        rhsPin = rhsNode.orderedInputs[linkData["inPinId"]]
                    except Exception as e:
                        continue

                    if not arePinsConnected(lhsPin, rhsPin):
                        connected = connectPins(lhsPin, rhsPin)
                        if not connected:
                            print("Failed to restore connection", lhsPin, rhsPin)
                            connectPins(lhsPin, rhsPin)

    def remove(self):
        for childGraph in set(self.childGraphs):
            childGraph.remove()
        self.graphManager.removeGraph(self)

    def clear(self):
        for childGraph in set(self.childGraphs):
            childGraph.clear()

        for node in list(self._nodes.values()):
            node.kill()
        self._nodes.clear()

        for var in list(self._vars.values()):
            self.killVariable(var)
        self._vars.clear()

    @property
    def name(self):
        return self.__name

    @name.setter
    def name(self, value):
        value = str(value)
        if self.__name != value:
            self.__name = value
            self.nameChanged.send(self.__name)

    @property
    def category(self):
        return self.__category

    @category.setter
    def category(self, value):
        self.__category = str(value)
        self.categoryChanged.send(self.__category)

    def Tick(self, deltaTime):
        for node in self._nodes.values():
            node.Tick(deltaTime)

    @property
    def pins(self):
        result = {}
        for n in self.getNodesList():
            for pin in tuple(n.inputs.values()) + tuple(n.outputs.values()):
                result[pin.uid] = pin
        return result

    def createVariable(
        self,
        dataType=str("AnyPin"),
        accessLevel=AccessLevel.public,
        uid=None,
        name=str("var"),
    ):
        name = self.graphManager.getUniqVariableName(name)
        var = Variable(
            self,
            get_pin_default_value_by_type(dataType),
            name,
            dataType,
            accessLevel=accessLevel,
            uid=uid,
        )
        self._vars[var.uid] = var
        return var

    def killVariable(self, var):
        assert isinstance(var, Variable)
        if var.uid in self._vars:
            popped = self._vars.pop(var.uid)
            popped.killed.send()

    def getNodes(self):
        return self._nodes

    def getNodesList(self, classNameFilters=None):
        if classNameFilters is None:
            classNameFilters = []
        if len(classNameFilters) > 0:
            return [
                n
                for n in self._nodes.values()
                if n.__class__.__name__ in classNameFilters
            ]
        else:
            return [n for n in self._nodes.values()]

    def findNode(self, name):
        for i in self._nodes.values():
            if i.name == name:
                return i
        return None

    def getNodesByClassName(self, className):
        nodes = []
        for i in self.getNodesList():
            if i.__class__.__name__ == className:
                nodes.append(i)
        return nodes

    def findPinByUid(self, uid):
        pin = None
        if uid in self.pins:
            pin = self.pins[uid]
        return pin

    def findPin(self, pinName):
        result = None
        for pin in self.pins.values():
            if pinName == pin.getFullName():
                result = pin
                break
        return result

    def getInputNode(self):
        node = get_raw_node_instance("graphInputs", "PyFlowBase")
        self.addNode(node)
        return node

    def getOutputNode(self):
        node = get_raw_node_instance("graphOutputs", "PyFlowBase")
        self.addNode(node)
        return node

    def addNode(self, node, jsonTemplate=None):
        from PyFlow.Core.PathsRegistry import PathsRegistry

        assert node is not None, "failed to add node, None is passed"
        if node.uid in self._nodes:
            return False

        if node.__class__.__name__ in ["getVar", "setVar"]:
            var = self.graphManager.findVariableByUid(node.variableUid())
            variableLocation = var.location()
            if len(variableLocation) > len(self.location()):
                return False
            if len(variableLocation) == len(self.location()):
                if Counter(variableLocation) != Counter(self.location()):
                    return False

        node.graph = weakref.ref(self)
        if jsonTemplate is not None:
            jsonTemplate["name"] = self.graphManager.getUniqNodeName(
                jsonTemplate["name"]
            )
        else:
            node.setName(self.graphManager.getUniqNodeName(node.name))

        self._nodes[node.uid] = node
        node.postCreate(jsonTemplate)
        PathsRegistry().rebuild()
        return True

    def location(self):
        result = [self.name]
        parent = self._parentGraph
        while parent is not None:
            result.insert(0, parent.name)
            parent = parent.parentGraph
        return result

    def count(self):
        return self._nodes.__len__()

    def plot(self):
        depth = self.depth()
        prefix = "".join(["-"] * depth) if depth > 1 else ""
        parentGraphString = (
            str(None) if self.parentGraph is None else self.parentGraph.name
        )
        print(prefix + "GRAPH:" + self.name + ", parent:{0}".format(parentGraphString))

        assert self not in self.childGraphs

        for child in self.childGraphs:
            child.plot()
