# Core/PinBase.py

import json
import uuid
from copy import copy
from blinker import Signal
from PyFlow.Core.EvaluationEngine import EvaluationEngine
from PyFlow.Core.Interfaces import IPin
import weakref

from PyFlow.Core.Common import clearSignal, disconnectPins, getConnectedPins, push
from PyFlow.Core.Enums import PinDirection, PinOptions, PinReconnectionPolicy, StructureType
from PyFlow.Core.Utils import DictElement, PFDict

class PinBase(IPin):
    """
    Base class for pins
    """

    __slots__ = (
        'serializationHook', 'onPinConnected', 'onPinDisconnected', 'nameChanged', 'killed',
        'onExecute', 'containerTypeChanged', 'dataBeenSet', 'dictChanged', 'markedAsDirty',
        'errorOccurred', 'errorCleared', '_lastError', 'owningNode', '_uid', '_data',
        '_defaultValue', 'reconnectionPolicy', 'dirty', 'affects', 'affected_by', 'name',
        '_group', 'direction', '_wrapper', '__wrapperJsonData', 'annotationDescriptionDict',
        '_inputWidgetVariant', 'constraint', 'structConstraint', '_flags', '_origFlags',
        '_structure', '_currStructure', '_isAny', '_isArray', '_isDict', '_alwaysList', '_alwaysDict',
        '_alwaysSingle', '_defaultSupportedDataTypes', '_supportedDataTypes', 'canChange',
        '_isDictElement', 'hidden', 'super', 'activeDataType', 'pinIndex', 'description',
        '_keyType', '__weakref__'
    )

    _packageName = ""

    def __init__(self, name, owningNode, direction):
        super(PinBase, self).__init__()
        self.serializationHook = Signal()
        self.onPinConnected = Signal(object)
        self.onPinDisconnected = Signal(object)
        self.nameChanged = Signal(str)
        self.killed = Signal()
        self.onExecute = Signal(object)
        self.containerTypeChanged = Signal()
        self.dataBeenSet = Signal(object)
        self.dictChanged = Signal(str)
        self.markedAsDirty = Signal()

        self.errorOccurred = Signal(object)
        self.errorCleared = Signal()
        self._lastError = None

        self.owningNode = weakref.ref(owningNode)

        self._uid = uuid.uuid4()
        self._data = None
        self._defaultValue = None
        self.reconnectionPolicy = PinReconnectionPolicy.DisconnectIfHasConnections
        self.dirty = True
        self.affects = set()
        self.affected_by = set()

        self.name = name
        self._group = ""
        self.direction = direction

        self._wrapper = None
        self.__wrapperJsonData = None
        self.annotationDescriptionDict = None
        self._inputWidgetVariant = "DefaultWidget"

        self.constraint = None
        self.structConstraint = None

        self._flags = PinOptions.Storable
        self._origFlags = self._flags
        self._structure = StructureType.Single
        self._currStructure = self._structure
        self._isAny = False
        self._isArray = False
        self._isDict = False
        self._alwaysList = False
        self._alwaysDict = False
        self._alwaysSingle = False
        self._defaultSupportedDataTypes = self._supportedDataTypes = self.supportedDataTypes()
        self.canChange = False
        self._isDictElement = False
        self.hidden = False

        self.super = self.__class__
        self.activeDataType = self.__class__.__name__
        self._keyType = None

        self.owningNode().pins.add(self)
        self.owningNode().pinsCreationOrder[self.uid] = self

        self.pinIndex = 0
        if direction == PinDirection.Input:
            self.pinIndex = len(self.owningNode().orderedInputs)
        if direction == PinDirection.Output:
            self.pinIndex = len(self.owningNode().orderedOutputs)

        self.description = "{} instance".format(self.dataType)

    @property
    def wrapperJsonData(self):
        try:
            dt = self.__wrapperJsonData.copy()
            return dt
        except Exception as e:
            return None

    def getInputWidgetVariant(self):
        return self._inputWidgetVariant

    def setInputWidgetVariant(self, value):
        self._inputWidgetVariant = value

    def path(self):
        owningNodePath = self.owningNode().path()
        return "{}.{}".format(owningNodePath, self.getName())

    @property
    def group(self):
        return self._group

    @group.setter
    def group(self, value):
        self._group = value

    def enableOptions(self, *options):
        for option in options:
            self._flags = self._flags | option
        self._origFlags = self._flags

    def disableOptions(self, *options):
        for option in options:
            self._flags = self._flags & ~option
        self._origFlags = self._flags

    def optionEnabled(self, option):
        return bool(self._flags & option)

    def isAny(self):
        return self._isAny

    @property
    def packageName(self):
        return self._packageName

    @property
    def linkedTo(self):
        result = list()
        if self.direction == PinDirection.Output:
            for i in getConnectedPins(self):
                connection = {"lhsNodeName": self.owningNode().getName(), "outPinId": self.pinIndex,
                              "rhsNodeName": i.owningNode().getName(), "inPinId": i.pinIndex,
                              "lhsNodeUid": str(self.owningNode().uid), "rhsNodeUid": str(i.owningNode().uid)}
                result.append(connection)

        if self.direction == PinDirection.Input:
            for i in getConnectedPins(self):
                connection = {"lhsNodeName": i.owningNode().getName(), "outPinId": i.pinIndex,
                              "rhsNodeName": self.owningNode().getName(), "inPinId": self.pinIndex,
                              "lhsNodeUid": str(i.owningNode().uid), "rhsNodeUid": str(self.owningNode().uid)}
                result.append(connection)
        return result

    def __repr__(self):
        return "[{0}:{1}:{2}:{3}]".format(self.dataType, self.getFullName(), self.dirty, self.currentData())

    def isExec(self):
        return False

    def initAsArray(self, bIsArray):
        self._alwaysList = bool(bIsArray)
        if bool(bIsArray):
            self._alwaysDict = False
        self.setAsArray(bool(bIsArray))

    def initAsDict(self, bIsDict):
        self._alwaysDict = bool(bIsDict)
        if bool(bIsDict):
            self._alwaysList = False
        self.setAsDict(bool(bIsDict))

    def setAsArray(self, bIsArray):
        bIsArray = bool(bIsArray)
        if self._isArray == bIsArray:
            return

        self._isArray = bIsArray
        if bIsArray:
            if self.isDict():
                self.setAsDict(False)
            self.enableOptions(PinOptions.SupportsOnlyArrays)
            self._currStructure = StructureType.Array
            self._isDict = False
        else:
            self._currStructure = self._structure
        self._data = self.defaultValue()
        self.containerTypeChanged.send()

    def setAsDict(self, bIsDict):
        bIsDict = bool(bIsDict)
        if self._isDict == bIsDict:
            return

        self._isDict = bIsDict
        if bIsDict:
            if self.isArray():
                self.setAsArray(False)
            self.enableOptions(PinOptions.SupportsOnlyArrays)
            self._currStructure = StructureType.Dict
            self._isArray = False
        else:
            self._currStructure = self._structure
            self._keyType = None
        self._data = self.defaultValue()
        self.containerTypeChanged.send()

    def isArray(self):
        return self._isArray

    def isDict(self):
        return self._isDict

    def setWrapper(self, wrapper):
        if self._wrapper is None:
            self._wrapper = weakref.ref(wrapper)

    def getWrapper(self):
        return self._wrapper

    def deserialize(self, jsonData):
        self.setName(jsonData["name"])
        self.uid = uuid.UUID(jsonData["uuid"])

        for opt in PinOptions:
            if opt.value in jsonData["options"]:
                self.enableOptions(opt)
            else:
                self.disableOptions(opt)

        self.changeStructure(jsonData["structure"])
        self._alwaysList = jsonData["alwaysList"]
        self._alwaysSingle = jsonData["alwaysSingle"]
        self._alwaysDict = jsonData["alwaysDict"]

        try:
            self.setData(json.loads(jsonData["value"], cls=self.jsonDecoderClass()) if jsonData["value"] else self.defaultValue())
        except Exception as e:
            self.setData(self.defaultValue())

        if "wrapper" in jsonData:
            self.__wrapperJsonData = jsonData["wrapper"]

    def serialize(self):
        storable = self.optionEnabled(PinOptions.Storable)

        serializedData = None
        if not self.dataType == "AnyPin":
            if storable:
                serializedData = json.dumps(self.currentData(),
                                            cls=self.jsonEncoderClass())

        data = {
            "name": self.name,
            "package": self.packageName,
            "fullName": self.getFullName(),
            "dataType": self.__class__.__name__,
            "direction": int(self.direction),
            "value": serializedData,
            "uuid": str(self.uid),
            "linkedTo": list(self.linkedTo),
            "pinIndex": self.pinIndex,
            "options": [i.value for i in PinOptions if self.optionEnabled(i)],
            "structure": int(self._currStructure),
            "alwaysList": self._alwaysList,
            "alwaysSingle": self._alwaysSingle,
            "alwaysDict": self._alwaysDict,
        }

        wrapperData = self.serializationHook.send(self)
        if wrapperData is not None:
            if len(wrapperData) > 0:
                data["wrapper"] = wrapperData[0][1]
        return data

    @property
    def uid(self):
        return self._uid

    @uid.setter
    def uid(self, value):
        if not value == self._uid:
            self._uid = value

    def setName(self, name, force=False):
        if not force:
            if not self.optionEnabled(PinOptions.RenamingEnabled):
                return False
        if name == self.name:
            return False
        self.name = self.owningNode().getUniqPinName(name)
        self.nameChanged.send(self.name)
        return True

    def getName(self):
        return self.name

    def getFullName(self):
        return self.owningNode().name + "_" + self.name

    def allowedDataTypes(self, checked=None, dataTypes=None, selfCheck=True, defaults=False):
        return list(self.supportedDataTypes())

    def checkFree(self, checked=None, selfCheck=True):
        return False

    def defaultValue(self):
        if self.isArray():
            return []
        elif self.isDict():
            return PFDict("StringPin", "AnyPin")
        else:
            return self._defaultValue

    def getData(self):
        return EvaluationEngine().getPinData(self)

    def clearError(self):
        if self._lastError is not None:
            self._lastError = None
            self.errorCleared.send()

    def setError(self, err):
        self._lastError = err
        self.errorOccurred.send(self._lastError)

    def validateArray(self, array, func):
        valid = True
        if isinstance(array, list):
            for i in array:
                self.validateArray(i, func)
        else:
            func(array)
        return valid

    def setData(self, data):
        if self.super is None:
            return
        try:
            self.setDirty()
            if isinstance(data, DictElement) and not self.optionEnabled(PinOptions.DictElementSupported):
                data = data[1]
            if not self.isArray() and not self.isDict():
                if isinstance(data, DictElement):
                    self._data = DictElement(data[0], self.super.processData(data[1]))
                else:
                    if isinstance(data, list):
                        self._data = data
                    else:
                        self._data = self.super.processData(data)
            elif self.isArray():
                if isinstance(data, list):
                    self._data = data
                else:
                    self._data = [self.super.processData(data)]
            elif self.isDict():
                if isinstance(data, PFDict):
                    self._data = PFDict(data.keyType, data.valueType)
                    for key, value in data.items():
                        self._data[key] = self.super.processData(value)
                elif isinstance(data, DictElement) and len(data) == 2:
                    self._data.clear()
                    self._data[data[0]] = self.super.processData(data[1])

            if self.direction == PinDirection.Output:
                for i in self.affects:
                    i.setData(self.currentData())

            elif self.direction == PinDirection.Input and self.owningNode().__class__.__name__ == "compound":
                for i in self.affects:
                    i.setData(self.currentData())

            if self.direction == PinDirection.Input or self.optionEnabled(PinOptions.AlwaysPushDirty):
                push(self)
            self.clearError()
            self.dataBeenSet.send(self)
        except Exception as exc:
            self.setError(exc)
            self.setDirty()
        if self._lastError is not None:
            self.owningNode().setError(self._lastError)
        wrapper = self.owningNode().getWrapper()
        if wrapper:
            wrapper.update()

    def call(self, *args, **kwargs):
        if self.owningNode().isValid():
            self.onExecute.send(*args, **kwargs)

    def disconnectAll(self):
        if self.direction == PinDirection.Input:
            for o in list(self.affected_by):
                disconnectPins(self, o)
            self.affected_by.clear()

        if self.direction == PinDirection.Output:
            for i in list(self.affects):
                disconnectPins(self, i)
            self.affects.clear()

    @property
    def dataType(self):
        return self.__class__.__name__

    @property
    def structureType(self):
        return self._structure

    @structureType.setter
    def structureType(self, structure):
        self._structure = structure
        self._currStructure = structure

    def kill(self, *args, **kwargs):
        self.disconnectAll()
        if self in self.owningNode().pins:
            self.owningNode().pins.remove(self)
        if self.uid in self.owningNode().pinsCreationOrder:
            self.owningNode().pinsCreationOrder.pop(self.uid)

        if self.optionEnabled(PinOptions.Dynamic):
            index = 1
            if self.direction == PinDirection.Input:
                for inputPin in self.owningNode().orderedInputs.values():
                    if inputPin == self:
                        continue
                    inputPin.pinIndex = index
                    index += 1
            index = 1
            if self.direction == PinDirection.Output:
                for outputPin in self.owningNode().orderedOutputs.values():
                    if outputPin == self:
                        continue
                    outputPin.pinIndex = index
                    index += 1
        self.killed.send()
        clearSignal(self.killed)

    def currentData(self):
        if self._data is None:
            return self._defaultValue
        return self._data

    def aboutToConnect(self, other):
        if other.structureType != self.structureType:
            if self.optionEnabled(PinOptions.ChangeTypeOnConnection) or self.structureType == StructureType.Multi:
                self.changeStructure(other._currStructure)
                self.onPinConnected.send(other)

    def getCurrentStructure(self):
        if self.structureType == StructureType.Multi:
            if self._alwaysSingle:
                return StructureType.Single
            elif self._alwaysList:
                return StructureType.Array
            elif self._alwaysDict:
                return StructureType.Dict
            else:
                return self.structureType
        else:
            return self.structureType

    def changeStructure(self, newStruct, init=False):
        free = self.canChangeStructure(newStruct, [], init=init)
        if free:
            self.updateConstrainedPins(set(), newStruct, init, connecting=True)

    def canChangeStructure(self, newStruct, checked=None, selfCheck=True, init=False):
        if checked is None:
            checked = []
        if not init and (self._alwaysList or self._alwaysSingle or self._alwaysDict):
            return False
        if self.structConstraint is None or self.structureType == StructureType.Multi:
            return True
        elif self.structureType != StructureType.Multi:
            return False
        else:
            con = []
            if selfCheck:
                free = not self.hasConnections()
                if not free:
                    for c in getConnectedPins(self):
                        if c not in checked:
                            con.append(c)
            else:
                checked.append(self)
            free = True
            if selfCheck:
                if any(
                    [
                        self._currStructure == StructureType.Single
                        and newStruct == StructureType.Array
                        and not self.optionEnabled(PinOptions.ArraySupported)
                        and self.hasConnections(),
                        self._currStructure == StructureType.Single
                        and newStruct == StructureType.Dict
                        and not self.optionEnabled(PinOptions.DictSupported)
                        and self.hasConnections(),
                        self._currStructure == StructureType.Array
                        and newStruct == StructureType.Single
                        and self.optionEnabled(PinOptions.SupportsOnlyArrays)
                        and self.hasConnections(),
                        self._currStructure == StructureType.Dict
                        and newStruct == StructureType.Single
                        and self.optionEnabled(PinOptions.SupportsOnlyArrays)
                        and self.hasConnections(),
                        self._currStructure == StructureType.Array
                        and newStruct == StructureType.Dict
                        and self.hasConnections(),
                        self._currStructure == StructureType.Dict
                        and newStruct == StructureType.Array
                        and self.hasConnections(),
                    ]
                ):
                    def testfree():
                        free = False
                        for pin in getConnectedPins(self):
                            if pin._structure == StructureType.Multi:
                                free = True
                            else:
                                free = False
                                break
                        return free
                    free = testfree()
            if free:
                for port in (self.owningNode().structConstraints[self.structConstraint] + con):
                    if port not in checked:
                        checked.append(port)
                        free = port.canChangeStructure(newStruct, checked, True, init=init)
                        if not free:
                            break
            return free

    def updateConstrainedPins(self, traversed, newStruct, init=False, connecting=False):
        if self.structConstraint is not None:
            nodePins = set(self.owningNode().structConstraints[self.structConstraint])
        else:
            nodePins = {self}
        for connectedPin in getConnectedPins(self):
            if connectedPin.structureType == StructureType.Multi:
                if connectedPin.canChangeStructure(self._currStructure, init=init):
                    nodePins.add(connectedPin)
        for neighbor in nodePins:
            if neighbor not in traversed and neighbor.structureType == StructureType.Multi:
                neighbor.setAsArray(newStruct == StructureType.Array)
                neighbor.setAsDict(newStruct == StructureType.Dict)
                if connecting:
                    if init:
                        neighbor._alwaysList = newStruct == StructureType.Array
                        neighbor._alwaysSingle = newStruct == StructureType.Single
                        neighbor._alwaysDict = newStruct == StructureType.Dict
                    neighbor._currStructure = newStruct
                    neighbor.disableOptions(PinOptions.ArraySupported)
                    neighbor.disableOptions(PinOptions.DictSupported)
                    if newStruct == StructureType.Array:
                        neighbor.enableOptions(PinOptions.ArraySupported)
                    elif newStruct == StructureType.Dict:
                        neighbor.enableOptions(PinOptions.DictSupported)
                    elif newStruct == StructureType.Multi:
                        neighbor.enableOptions(PinOptions.ArraySupported)
                        neighbor.enableOptions(PinOptions.DictSupported)
                    elif newStruct == StructureType.Single:
                        neighbor.disableOptions(PinOptions.SupportsOnlyArrays)
                else:
                    neighbor._currStructure = neighbor._structure
                    neighbor._data = neighbor.defaultValue()
                traversed.add(neighbor)
                try:
                    neighbor.setData(neighbor.getData())
                except:
                    neighbor.setData(neighbor.defaultValue())
                neighbor.updateConstrainedPins(traversed, newStruct, init, connecting=connecting)

    def pinConnected(self, other):
        if self.isDict():
            self.updateConnectedDicts([], self._data.keyType)

    def pinDisconnected(self, other):
        self.onPinDisconnected.send(other)

    def canChangeTypeOnConnection(self, checked=None, can=True, extraPins=None, selfCheck=True):
        if checked is None:
            checked = []
        if extraPins is None:
            extraPins = []
        if not self.optionEnabled(PinOptions.ChangeTypeOnConnection):
            return False
        con = []
        neis = []
        if selfCheck:
            if self.hasConnections():
                for c in getConnectedPins(self):
                    if c not in checked:
                        con.append(c)
        else:
            checked.append(self)
        if self.constraint:
            neis = self.owningNode().constraints[self.constraint]
        for port in neis + con + extraPins:
            if port not in checked and can:
                checked.append(port)
                can = port.canChangeTypeOnConnection(checked, can, selfCheck=True)
        return can

    def getDictElementNode(self, checked=None, node=None):
        if checked is None:
            checked = []
        if self.owningNode().__class__.__name__ == "makeDictElement":
            return self.owningNode()
        con = []
        neis = []
        if self.hasConnections() and self.direction == PinDirection.Input:
            for c in getConnectedPins(self):
                if c not in checked:
                    con.append(c)
        if self.constraint:
            neis = self.owningNode().constraints[self.constraint]
        for port in con + neis:
            if port not in checked and node is None:
                checked.append(port)
                node = port.getDictElementNode(checked, node)
        return node

    def getDictNode(self, checked=None, node=None):
        if checked is None:
            checked = []
        if self.owningNode().__class__.__name__ in ["makeDict", "makeAnyDict"]:
            return self.owningNode()
        con = []
        neis = []
        if self.hasConnections():
            for c in getConnectedPins(self):
                if c not in checked:
                    con.append(c)
        if self.constraint:
            neis = self.owningNode().constraints[self.constraint]
        for port in con + neis:
            if port not in checked and node is None:
                checked.append(port)
                node = port.getDictNode(checked, node)
        return node

    def supportDictElement(self, checked=None, can=True, selfCheck=True):
        if checked is None:
            checked = []
        if not self.optionEnabled(PinOptions.DictElementSupported):
            return False
        con = []
        neis = []
        if selfCheck:
            if self.hasConnections() and self.direction == PinDirection.Input:
                for c in getConnectedPins(self):
                    if c not in checked:
                        con.append(c)
        else:
            checked.append(self)
        if self.constraint and self.owningNode().__class__.__name__ != "makeDictElement":
            neis = self.owningNode().constraints[self.constraint]
        for port in neis + con:
            if port not in checked and can:
                checked.append(port)
                can = port.supportDictElement(checked, can, selfCheck=True)
        return can

    def supportOnlyDictElement(self, checked=None, can=False, selfCheck=True):
        if checked is None:
            checked = []
        if self.isDict():
            return True
        con = []
        neis = []
        if selfCheck:
            if self.hasConnections() and self.direction == PinDirection.Output:
                for c in getConnectedPins(self):
                    if c not in checked:
                        con.append(c)
        else:
            checked.append(self)
        if self.constraint and self.owningNode().__class__.__name__ != "makeDictElement":
            neis = self.owningNode().constraints[self.constraint]
        for port in neis + con:
            if port not in checked and not can:
                checked.append(port)
                can = port.supportOnlyDictElement(checked, can, selfCheck=True)
        return can

    def updateConnectedDicts(self, checked=None, keyType=None):
        if checked is None:
            checked = []
        if not self.isDict():
            return
        con = []
        neis = []
        if self.hasConnections():
            for c in getConnectedPins(self):
                if c not in checked:
                    con.append(c)
        if self.constraint:
            neis = self.owningNode().constraints[self.constraint]
        for port in con + neis:
            if port not in checked and port.isDict():
                checked.append(port)
                port._keyType = keyType
                if port._data.keyType != keyType:
                    port._data = PFDict(keyType, port.dataType)
                port.dictChanged.send(keyType)
                if port.getWrapper():
                    port.getWrapper()().update()
                port.updateConnectedDicts(checked, keyType)

    def setClean(self):
        self.dirty = False

    def setDirty(self):
        if self.isExec():
            return
        self.dirty = True
        for i in self.affects:
            i.dirty = True
        self.markedAsDirty.send()

    def hasConnections(self):
        numConnections = 0
        if self.direction == PinDirection.Input:
            numConnections += len(self.affected_by)
        elif self.direction == PinDirection.Output:
            numConnections += len(self.affects)
        return numConnections > 0

    def setDefaultValue(self, val):
        self._defaultValue = copy(val)

    def updateConstraint(self, constraint):
        self.constraint = constraint
        if constraint in self.owningNode().constraints:
            self.owningNode().constraints[constraint].append(self)
        else:
            self.owningNode().constraints[constraint] = [self]

    def updateStructConstraint(self, constraint):
        self.structConstraint = constraint
        if constraint in self.owningNode().structConstraints:
            self.owningNode().structConstraints[constraint].append(self)
        else:
            self.owningNode().structConstraints[constraint] = [self]

    @staticmethod
    def IsValuePin():
        return True

    @staticmethod
    def pinDataTypeHint():
        raise NotImplementedError("pinDataTypeHint method of PinBase is not implemented")

    @staticmethod
    def supportedDataTypes():
        return ()

    @staticmethod
    def jsonEncoderClass():
        return json.JSONEncoder

    @staticmethod
    def jsonDecoderClass():
        return json.JSONDecoder
