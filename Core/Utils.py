# Core/Utils.py

import os
import orjson  # Ensure orjson is installed
from PyFlow.Core.Enums import StructureType
from PyFlow.Core.PackageManager import get_package_checked, get_package_path, PINCLASS_CACHE
from PyFlow.Core.NodeBase import NodeBase  # Assuming NodeBase is correctly defined
from PyFlow.Core.Common import clamp, extractDigitsFromEndOfString, findGoodId, removeDigitsFromEndOfString  # Import only necessary functions

def get_raw_node_instance(nodeClassName, packageName=None, libName=None, **kwargs):
    """
    Retrieves an instance of a raw node based on its class name, package, and library.

    Args:
        nodeClassName (str): The name of the node class to instantiate.
        packageName (str, optional): The name of the package where the node resides.
        libName (str, optional): The name of the library within the package.
        **kwargs: Additional keyword arguments for node instantiation.

    Returns:
        NodeBase or CompoundNode: An instance of the requested node.
    """
    package = get_package_checked(packageName)

    # Try to find function first
    if libName is not None:
        for key, lib in package.GetFunctionLibraries().items():
            foos = lib.getFunctions()
            if libName == key and nodeClassName in foos:
                return NodeBase.initializeFromFunction(foos[nodeClassName])

    # Try to find node class
    nodes = package.GetNodeClasses()
    if nodeClassName in nodes:
        return nodes[nodeClassName](nodeClassName, **kwargs)

    # Try to find exported py nodes
    packagePath = get_package_path(packageName)
    pyNodesPath = os.path.join(packagePath, "PyNodes")
    if os.path.exists(pyNodesPath):
        for path, dirs, files in os.walk(pyNodesPath):
            for pyNodeFileName in files:
                pyNodeName, _ = os.path.splitext(pyNodeFileName)
                if nodeClassName == pyNodeName:
                    pythonNode = get_raw_node_instance("pythonNode", "PyFlowBase")
                    pyNodeFullPath = os.path.join(path, pyNodeFileName)
                    with open(pyNodeFullPath, "rb") as f:  # Open in binary mode for orjson
                        pythonNode._nodeData = orjson.loads(f.read())
                    return pythonNode

    # Try to find exported compound nodes
    compoundNodesPath = os.path.join(packagePath, "Compounds")
    if os.path.exists(compoundNodesPath):
        for path, dirs, files in os.walk(compoundNodesPath):
            for compoundNodeFileName in files:
                compoundNodeName, _ = os.path.splitext(compoundNodeFileName)
                compoundNodeFullPath = os.path.join(path, compoundNodeFileName)
                with open(compoundNodeFullPath, "rb") as f:  # Open in binary mode for orjson
                    compoundData = orjson.loads(f.read())
                    if compoundData["name"] == nodeClassName:
                        compoundNode = get_raw_node_instance("compound", "PyFlowBase")
                        compoundNodeFullPath = os.path.join(path, compoundNodeFileName)
                        with open(compoundNodeFullPath, "rb") as f:
                            jsonString = f.read()
                            compoundNode._rawGraphJson = orjson.loads(jsonString)
                        return compoundNode

    return None  # Return None if no matching node is found


def find_pin_class_by_type(dataType):
    """
    Retrieves the pin class corresponding to the given data type from the cache.

    Args:
        dataType (str): The data type to find the pin class for.

    Returns:
        PinClass or None: The corresponding pin class if found; otherwise, None.
    """
    return PINCLASS_CACHE.get(dataType, None)


def get_pin_default_value_by_type(dataType):
    """
    Retrieves the default value for a given pin data type.

    Args:
        dataType (str): The data type of the pin.

    Returns:
        Any: The default value for the pin's data type.
    """
    pin = find_pin_class_by_type(dataType)
    if pin:
        return pin.pinDataTypeHint()[1]
    return None

class DictElement(tuple):
    """PyFlow dict element class

    This subclass of Python's `tuple` is to represent dict elements to construct typed dicts.
    """

    def __new__(cls, a=None, b=None):
        if a is None and b is None:
            new = ()
        elif b is None:
            if isinstance(a, tuple) and len(a) <= 2:
                new = a
            else:
                raise Exception("Invalid Input")
        else:
            new = (a, b)
        return super(DictElement, cls).__new__(cls, new)


class PFDict(dict):
    """This subclass of Python's `dict` implements a key-typed dictionary.

    Only defined data types can be used as keys, and only hashable ones as determined by `isinstance(dataType, collections.abc.Hashable)`.

    To make a class Hashable, some methods should be implemented:

    Example:
    ::
        class C:
            def __init__(self, x):
                self.x = x
            def __repr__(self):
                return "C({})".format(self.x)
            def __hash__(self):
                return hash(self.x)
            def __eq__(self, other):
                return (self.__class__ == other.__class__ and self.x == other.x)
    """

    def __init__(self, keyType, valueType="AnyPin", inp=None):
        """
        :param keyType: Key dataType
        :param valueType: Value dataType, defaults to "AnyPin"
        :param inp: Construct from another dict, defaults to {}
        :type inp: dict, optional
        """
        if inp is None:
            inp = {}
        super(PFDict, self).__init__(inp)
        self.keyType = keyType
        self.valueType = valueType

    def __setitem__(self, key, item):
        """Reimplements Python Dict __setitem__ to only allow Typed Keys.

        Will throw an Exception if non-Valid KeyType.

        :param key: Key to set
        :param item: Value to set
        """
        expected_class = self.getClassFromType(self.keyType)
        if isinstance(key, expected_class):
            super(PFDict, self).__setitem__(key, item)
        else:
            raise TypeError(
                f"Valid key should be of type {expected_class.__name__}."
            )

    @staticmethod
    def getClassFromType(pinType):
        """
        Gets the internal data structure for a defined pin type.

        :param pinType: pinType Name
        :type pinType: str
        :returns: Class corresponding to the pinType
        :rtype: type or None
        """
        pin = find_pin_class_by_type(pinType)
        if pin:
            return pin.internalDataStructure()
        return None


class SingletonDecorator:
    """Decorator to make a class a singleton, ensuring only one instance exists."""
    allInstances = []

    @staticmethod
    def destroyAll():
        for instance in SingletonDecorator.allInstances:
            instance.destroy()

    def __init__(self, cls):
        self.cls = cls
        self.instance = None
        SingletonDecorator.allInstances.append(self)

    def destroy(self):
        if hasattr(self.instance, 'destroy'):
            self.instance.destroy()
        del self.instance
        self.instance = None

    def __call__(self, *args, **kwargs):
        if self.instance is None:
            self.instance = self.cls(*args, **kwargs)
        if hasattr(self.instance, 'instanceCount'):
            self.instance.instanceCount += 1
        return self.instance

