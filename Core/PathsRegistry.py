# Core/PathsRegistry.py

from PyFlow.Core.GraphManager import GraphManagerSingleton
from PyFlow.Core.Utils import SingletonDecorator

@SingletonDecorator
class PathsRegistry(object):
    """Holds paths to nodes and pins. Can rebuild paths and return entities by paths."""
    __slots__ = ('_data',)

    def __init__(self):
        self._data = {}

    def rebuild(self):
        man = GraphManagerSingleton().get()
        allNodes = man.getAllNodes()
        self._data.clear()
        for node in allNodes:
            self._data[node.path()] = node
            for pin in node.pins:
                self._data[pin.path()] = pin

    def getAllPaths(self):
        return list(self._data)

    def contains(self, path):
        return path in self._data

    def getEntity(self, path):
        if self.contains(path):
            return self._data[path]
        return None
