# Core/EvaluationEngine.py

from PyFlow.Core.Interfaces import IEvaluationEngine
from PyFlow.Core.Enums import StructureType
from PyFlow.Core.Utils import SingletonDecorator

from collections import deque
from concurrent.futures import ThreadPoolExecutor


class DefaultEvaluationEngine_Impl(IEvaluationEngine):
    """Default evaluation engine implementation."""

    # Using __slots__ to reduce overhead
    __slots__ = ()

    def __init__(self):
        super(DefaultEvaluationEngine_Impl, self).__init__()

    @staticmethod
    def getPinData(pin):
        # Parallel execution optimization
        if not pin.hasConnections():
            return pin.currentData()

        bOwningNodeCallable = pin.owningNode().bCallable

        if not bOwningNodeCallable:
            return pin.currentData()

        order = DefaultEvaluationEngine_Impl.getEvaluationOrderIterative(pin.owningNode())

        # Execute nodes in parallel where possible
        with ThreadPoolExecutor() as executor:
            list(executor.map(lambda node: node.processNode(), order))

        if not bOwningNodeCallable:
            pin.owningNode().processNode()
        return pin.currentData()

    @staticmethod
    def TEST_getPinData(pin):
        if not pin.hasConnections():
            return pin.currentData()

        bOwningNodeCallable = pin.owningNode().bCallable

        if not pin.dirty:
            return pin.currentData()

        order = DefaultEvaluationEngine_Impl.getEvaluationOrderIterative(pin.owningNode())
        [node.processNode() for node in order]

        return pin.currentData()

    @staticmethod
    def getEvaluationOrderIterative(node, forward=False):
        visited = set()
        # Using deque for stack operations
        stack = deque([node])
        order = []
        while stack:
            current_node = stack.pop()

            if current_node not in visited:
                order.insert(0, current_node)
                visited.add(current_node)
            if not forward:
                lhsNodes = DefaultEvaluationEngine_Impl.getNextLayerNodes(current_node)
            else:
                lhsNodes = DefaultEvaluationEngine_Impl.getForwardNextLayerNodes(current_node)
            for n in lhsNodes:
                if n not in visited:
                    stack.append(n)
        if order:
            order.pop()
        return order

    @staticmethod
    def getEvaluationOrder(node):
        visited = set()
        order = []

        def dfsWalk(n):
            visited.add(n)
            nextNodes = DefaultEvaluationEngine_Impl.getNextLayerNodes(n)
            for lhsNode in nextNodes:
                if lhsNode not in visited:
                    dfsWalk(lhsNode)
            order.append(n)

        dfsWalk(node)
        if order:
            order.pop()
        return order

    @staticmethod
    def getNextLayerNodes(node):
        nodes = set()
        nodeInputs = node.inputs

        if nodeInputs:
            for inputPin in nodeInputs.values():
                if inputPin.affected_by:
                    # Check if it is a compound node and dive in
                    affectedByPins = set()
                    for pin in inputPin.affected_by:
                        if pin.owningNode().isCompoundNode:
                            innerPin = pin.owningNode().outputsMap[pin]
                            affectedByPins.add(innerPin)
                        else:
                            affectedByPins.add(pin)

                    for outPin in affectedByPins:
                        outPinNode = outPin.owningNode()
                        if not outPinNode.bCallable:
                            nodes.add(outPinNode)
        elif node.__class__.__name__ == "graphInputs":
            # graph inputs node
            for subgraphInputPin in node.outputs.values():
                for outPin in subgraphInputPin.affected_by:
                    owningNode = outPin.owningNode()
                    nodes.add(owningNode)
        return nodes

    @staticmethod
    def getForwardNextLayerNodes(node):
        nodes = set()
        nodeOutputs = node.outputs

        if nodeOutputs:
            for outputPin in nodeOutputs.values():
                if outputPin.affects:
                    # Check if it is a compound node and dive in
                    affectedByPins = set()
                    for pin in outputPin.affects:
                        if pin.owningNode().isCompoundNode:
                            innerPin = pin.owningNode().inputsMap[pin]
                            affectedByPins.add(innerPin)
                        else:
                            affectedByPins.add(pin)

                    for inPin in affectedByPins:
                        inPinNode = inPin.owningNode()
                        if not inPinNode.bCallable:
                            nodes.add(inPinNode)
        elif node.__class__.__name__ == "graphOutputs":
            # graph outputs node
            for subgraphOutputPin in node.inputs.values():
                for outPin in subgraphOutputPin.affects:
                    owningNode = outPin.owningNode()
                    nodes.add(owningNode)
        return nodes


@SingletonDecorator
class EvaluationEngine(object):
    __slots__ = ('_impl',)

    def __init__(self):
        self._impl = DefaultEvaluationEngine_Impl()

    def getPinData(self, pin):
        return self._impl.getPinData(pin)
