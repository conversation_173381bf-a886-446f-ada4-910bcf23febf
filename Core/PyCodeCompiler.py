# Core/PyCodeCompiler.py

from PyFlow.Core.Interfaces import ICodeCompiler

class Py3FunctionCompiler(ICodeCompiler):
    """Compiles string to python function
    """
    __slots__ = ('_fooName', '_code_cache')

    def __init__(self, fooName=None, *args, **kwargs):
        super(Py3FunctionCompiler, self).__init__(*args, **kwargs)
        assert isinstance(fooName, str)
        self._fooName = fooName
        self._code_cache = {}  # Code caching dictionary

    def compile(self, code):
        # Check cache first
        if code in self._code_cache:
            return self._code_cache[code]

        foo = "def {}(self):".format(self._fooName)
        lines = [i for i in code.split("\n") if len(i) > 0]
        for line in lines:
            foo += "\n\t{}".format(line)
        if len(lines) == 0:
            foo += "\n\tpass"
        codeObject = compile(foo, "PyFlowCodeCompiler", "exec")
        mem = {}
        exec(codeObject, mem)
        compiled_function = mem[self._fooName]
        self._code_cache[code] = compiled_function
        return compiled_function


class Py3CodeCompiler(ICodeCompiler):
    """Generic python code compiler"""

    __slots__ = ()

    def __init__(self):
        super(Py3CodeCompiler, self).__init__()

    def compile(self, code, moduleName="PyFlowCodeCompiler", scope=None):
        if scope is None:
            scope = {}
        codeObject = compile(code, moduleName, "exec")
        exec(codeObject, scope)
        return scope
