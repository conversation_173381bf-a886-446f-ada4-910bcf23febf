# Core/Common.py

"""
    This is a common definitions file. This file is imported in almost all others files of the program
"""

import re
import math
import time
import struct
import weakref
import sys

from PyFlow.Core.Enums import (
    StructureType,
    PinOptions,
    PinReconnectionPolicy,
    PinDirection,
)
from PyFlow.Core.version import Version
from PyFlow.Core.PackageManager import get_packages

maxint = 2 ** (struct.Struct("i").size * 8 - 1) - 1

FLOAT_RANGE_MIN = 0.1 + (-maxint - 1.0)
FLOAT_RANGE_MAX = maxint + 0.1
INT_RANGE_MIN = -maxint + 0
INT_RANGE_MAX = maxint + 0
DEFAULT_IN_EXEC_NAME = "inExec"
DEFAULT_OUT_EXEC_NAME = "outExec"
DEFAULT_WIDGET_VARIANT = "DefaultWidget"
REF = "Reference"

GlobalVariables = {}


def connectPinsByIndexes(lhsNode=None, lhsOutPinIndex=0, rhsNode=None, rhsInPinIndex=0):
    """Connects pins regardless name.

    This function uses pin locations on node. Top most pin have position index 1, pin below - 2 etc.

    :param lhsNode: Left hand side node
    :type lhsNode: :class:`~PyFlow.Core.NodeBase.NodeBase`
    :param lhsOutPinIndex: Out pin position on left hand side node
    :type lhsOutPinIndex: int
    :param rhsNode: Right hand side node
    :type rhsNode: :class:`~PyFlow.Core.NodeBase.NodeBase`
    :param rhsInPinIndex: Out pin position on right hand side node
    :type rhsInPinIndex: int
    """
    if lhsNode is None:
        return False

    if rhsNode is None:
        return False

    if lhsOutPinIndex not in lhsNode.orderedOutputs:
        return False

    if rhsInPinIndex not in rhsNode.orderedInputs:
        return False

    lhsPin = lhsNode.orderedOutputs[lhsOutPinIndex]
    rhsPin = rhsNode.orderedInputs[rhsInPinIndex]

    return connectPins(lhsPin, rhsPin)

def traverseConstrainedPins(startFrom, callback):
    """Iterate over constrained and connected pins

    Iterates over all constrained chained pins of type :class:`Any <PyFlow.Packages.PyFlowBase.Pins.AnyPin.AnyPin>` and passes pin into callback function. Callback will be executed once for every pin

    :param startFrom: First pin to start Iteration
    :type startFrom: :class:`~PyFlow.Core.PinBase.PinBase`
    :param callback: Functor to execute in each iterated pin.
    :type callback: callback(:class:`~PyFlow.Core.PinBase.PinBase`)
    """
    if not startFrom.isAny():

        return
    traversed = set()

    def worker(pin):
        traversed.add(pin)
        callback(pin)

        if pin.constraint is None:
            nodePins = set()
        else:
            nodePins = set(pin.owningNode().constraints[pin.constraint])

        for connectedPin in getConnectedPins(pin):
            if connectedPin.isAny():
                nodePins.add(connectedPin)
        for neighbor in nodePins:
            if neighbor not in traversed:
                worker(neighbor)

    worker(startFrom)


def fetchPackageNames(graphJson):
    """Parses serialized graph and returns all package names it uses

    :param graphJson: Serialized graph
    :type graphJson: dict
    :rtype: list[str]
    """
    packages = set()

    def worker(graphData):
        for node in graphData["nodes"]:
            packages.add(node["package"])

            for inpJson in node["inputs"]:
                packages.add(inpJson["package"])

            for outJson in node["inputs"]:
                packages.add(outJson["package"])

            if "graphData" in node:
                worker(node["graphData"])

    worker(graphJson)
    return list(packages)


def validateGraphDataPackages(graphData, missedPackages=None):
    """Checks if packages used in serialized data are accessible

    Missed packages will be added to the output set.

    :param graphData: Serialized graph
    :type graphData: dict
    :param missedPackages: Package names that are missing
    :type missedPackages: set, optional
    :rtype: bool
    """
    if missedPackages is None:
        missedPackages = set()
    existingPackages = get_packages().keys()
    graphPackages = fetchPackageNames(graphData)
    for pkg in graphPackages:
        if pkg not in existingPackages:
            missedPackages.add(pkg)
    return len(missedPackages) == 0


def lerp(start, end, alpha):
    """Performs a linear interpolation

    >>> lerp(0, 10, 0.5)
    5.0

    :param start: Start value
    :param end: End value
    :param alpha: Interpolation factor (0.0 to 1.0)
    :returns: The result of the linear interpolation
    """
    return start + alpha * (end - start)


def GetRangePct(MinValue, MaxValue, Value):
    """Calculates the percentage along a line from **MinValue** to **MaxValue** that **Value** is.

    :param MinValue: Minimum Value
    :param MaxValue: Maximum Value
    :param Value: Input value
    :returns: The percentage (from 0.0 to 1.0) between the two values where input value is
    """
    return (Value - MinValue) / (MaxValue - MinValue)


def mapRangeClamped(Value, InRangeA, InRangeB, OutRangeA, OutRangeB):
    """Returns **Value** mapped from one range into another where the Value is clamped to the Input Range.

    (e.g., 0.5 normalized from the range 0->1 to 0->50 would result in 25)

    :param Value: The value to map
    :param InRangeA: Input range start
    :param InRangeB: Input range end
    :param OutRangeA: Output range start
    :param OutRangeB: Output range end
    :returns: Mapped value
    """
    ClampedPct = clamp(GetRangePct(InRangeA, InRangeB, Value), 0.0, 1.0)
    return lerp(OutRangeA, OutRangeB, ClampedPct)


def mapRangeUnclamped(Value, InRangeA, InRangeB, OutRangeA, OutRangeB):
    """Returns **Value** mapped from one range into another without clamping to the Input Range.

    (e.g., 0.5 normalized from the range 0->1 to 0->50 would result in 25)

    :param Value: The value to map
    :param InRangeA: Input range start
    :param InRangeB: Input range end
    :param OutRangeA: Output range start
    :param OutRangeB: Output range end
    :returns: Mapped value
    """
    return lerp(OutRangeA, OutRangeB, GetRangePct(InRangeA, InRangeB, Value))


def sign(x):
    """Returns the sign of x. -1 if x is negative, 1 if positive, and 0 if 0.

    >>> sign(-5)
    -1
    >>> sign(3)
    1
    >>> sign(0)
    0
    """
    return (1 if x > 0 else -1) if x != 0 else 0


def currentProcessorTime():
    """Returns the current processor time."""
    return time.process_time()


def clamp(n, vmin, vmax):
    """Clamps the value **n** to be within the range **vmin** to **vmax**.

    :param n: Input value
    :param vmin: Minimum value
    :param vmax: Maximum value
    :returns: Clamped value
    """
    return max(min(n, vmax), vmin)


def roundup(x, to):
    """Rounds up **x** to the nearest multiple of **to**.

    >>> roundup(7, 8)
    8
    >>> roundup(8, 8)
    8
    >>> roundup(9, 8)
    16

    :param x: Value to round
    :param to: Value to round to
    :returns: Rounded value
    :rtype: int
    """
    return int(math.ceil(x / to)) * to


_currentVersion = Version(sys.version_info.major, sys.version_info.minor, 0)
python32 = Version(3, 2, 0)

if _currentVersion <= python32:
    def clearList(lst):
        """Clears a Python list (compatible with Python <=3.2).

        :param lst: List to clear
        :type lst: list
        :returns: Cleared list
        :rtype: list
        """
        del lst[:]
else:
    def clearList(lst):
        """Clears a Python list.

        :param lst: List to clear
        :type lst: list
        :returns: Cleared list
        :rtype: list
        """
        lst.clear()


def findGoodId(ids):
    """
    Finds the smallest unique integer starting from 1 that is not in **ids**.

    :param ids: A collection of occupied ids
    :type ids: list | set | tuple
    :returns: Unique Id
    :rtype: int
    """
    if not ids:
        return 1

    sorted_ids = sorted(set(ids))
    lastID = min(sorted_ids)

    if lastID > 1:
        return 1

    for ID in sorted_ids:
        diff = ID - lastID
        if diff > 1:
            return lastID + 1
        lastID = ID
    else:
        return lastID + 1


def wrapStringToFunctionDef(functionName, scriptString, kwargs=None):
    """Generates a function string which can be compiled and executed.

    Example:
    ::
        wrapStringToFunctionDef('test', 'print(a)', {'a': 5})

    Will produce the following function:
    ::
        def test(a=5):
            print(a)

    :param functionName: Name of the function to create
    :param scriptString: Function body as a string
    :param kwargs: Dictionary of default arguments
    :returns: Function definition as a string
    """
    kwargsString = ""
    if kwargs:
        for argname, argValue in kwargs.items():
            if isinstance(argValue, str):
                argValue = f"'{argValue}'"
            kwargsString += f"{argname}={argValue}, "
        kwargsString = kwargsString.rstrip(", ")

    result = f"def {functionName}({kwargsString}):\n"

    for scriptLine in scriptString.split("\n"):
        result += f"\t{scriptLine}\n"
    return result


def cycleCheck(src, dst):
    """Checks for cyclic connections between nodes.

    :param src: Source pin
    :type src: PinBase
    :param dst: Destination pin
    :type dst: PinBase
    :returns: True if a cycle is detected, False otherwise
    :rtype: bool
    """
    if src.direction == PinDirection.Input:
        src, dst = dst, src
    start = src
    if src in dst.affects:
        return True
    for i in dst.affects:
        if cycleCheck(start, i):
            return True
    return False


def extractDigitsFromEndOfString(string):
    """Get digits at end of a string

    Example:

    >>> nums = extractDigitsFromEndOfString("h3ello154")
    >>> print(nums, type(nums))
    >>> 154 <class 'int'>

    :param string: Input numbered string
    :type string: str
    :returns: Numbers in the final of the string
    :rtype: int
    """
    result = re.search("(\d+)$", string)
    if result is not None:
        return int(result.group(0))


def removeDigitsFromEndOfString(string):
    """Delete the numbers at the end of a string

    Similar to :func:`~PyFlow.Core.Common.extractDigitsFromEndOfString`, but removes digits in the end.

    :param string: Input string
    :type string: string
    :returns: Modified string
    :rtype: string
    """
    return re.sub(r"\d+$", "", string)


def disconnectPins(src, dst):
    """Disconnects two pins

    :param src: left hand side pin
    :type src: :py:class:`~PyFlow.Core.PinBase.PinBase`
    :param dst: right hand side pin
    :type dst: :py:class:`~PyFlow.Core.PinBase.PinBase`
    :returns: True if disconnection success
    :rtype: bool
    """
    if arePinsConnected(src, dst):
        if src.direction == PinDirection.Input:
            src, dst = dst, src
        src.affects.remove(dst)
        dst.affected_by.remove(src)
        src.pinDisconnected(dst)
        dst.pinDisconnected(src)
        push(dst)
        if src.isExec() and dst.isExec():
            src.onExecute.disconnect(dst.call)
        return True
    return False

def push(start_from):
    """Marks dirty all ports from start to the right

    this part of graph will be recomputed every tick

    :param start_from: pin from which recursion begins
    :type start_from: :py:class:`~PyFlow.Core.PinBase.PinBase`
    """
    #print("push", start_from.name, start_from.owningNode().name)
    if not len(start_from.affects) == 0:
        start_from.setDirty()
        for i in start_from.affects:
            i.setDirty()
            push(i)

def clearSignal(signal):
    """Disconnects all receivers

    :param signal: emitter
    :type signal: :class:`~blinker.base.Signal`
    """
    for receiver in list(signal.receivers.values()):
        if isinstance(receiver, weakref.ref):
            signal.disconnect(receiver())
        else:
            signal.disconnect(receiver)

def arePinsConnected(src, dst):
    """Checks if two pins are connected.

    .. note:: Pins can be passed in any order. If **src** pin is an input pin, they will be swapped.

    :param src: First pin
    :type src: PinBase
    :param dst: Second pin
    :type dst: PinBase
    :returns: True if pins are connected, False otherwise
    :rtype: bool
    """
    if src.direction == dst.direction:
        return False
    if src.owningNode() == dst.owningNode():
        return False
    if src.direction == PinDirection.Input:
        src, dst = dst, src
    return dst in src.affects and src in dst.affected_by


def getConnectedPins(pin):
    """Finds all pins connected to the given pin.

    :param pin: Pin to search connected pins
    :type pin: PinBase
    :returns: Set of connected pins
    :rtype: set(PinBase)
    """
    result = set()
    if pin.direction == PinDirection.Input:
        for lhsPin in pin.affected_by:
            result.add(lhsPin)
    if pin.direction == PinDirection.Output:
        for rhsPin in pin.affects:
            result.add(rhsPin)
    return result


def pinAffects(lhs, rhs):
    """Establishes dependencies between pins.

    .. warning:: Used internally; users will rarely need this.

    :param lhs: First pin to connect
    :type lhs: PinBase
    :param rhs: Second pin to connect
    :type rhs: PinBase
    """
    assert lhs is not rhs, "A pin cannot affect itself."
    lhs.affects.add(rhs)
    rhs.affected_by.add(lhs)


def canConnectPins(src, dst):
    """Checks if a connection between two pins is possible.

    **Very important fundamental function.**

    :param src: Source pin to connect
    :type src: PinBase
    :param dst: Destination pin to connect
    :type dst: PinBase
    :returns: True if connection can be made, False otherwise
    :rtype: bool
    """
    if src is None or dst is None:
        return False

    if src.direction == dst.direction:
        return False

    if arePinsConnected(src, dst):
        return False

    if src.direction == PinDirection.Input:
        src, dst = dst, src

    if cycleCheck(src, dst):
        return False

    if src.isExec() and dst.isExec():
        return True

    if not src.isArray() and dst.isArray():
        if dst.optionEnabled(PinOptions.SupportsOnlyArrays):
            if not src.canChangeStructure(dst._currStructure, []):
                return False

        if not dst.canChangeStructure(src._currStructure, [], selfCheck=False):
            if not src.canChangeStructure(dst._currStructure, [], selfCheck=False):
                return False

    if not src.isDict() and dst.isDict():
        if dst.optionEnabled(PinOptions.SupportsOnlyArrays):
            if not (
                src.canChangeStructure(dst._currStructure, [])
                or dst.canChangeStructure(src._currStructure, [], selfCheck=False)
            ):
                return False
        elif (
            not src.supportDictElement([], src.optionEnabled(PinOptions.DictElementSupported))
            and dst.optionEnabled(PinOptions.SupportsOnlyArrays)
            and not dst.canChangeStructure(src._currStructure, [], selfCheck=False)
        ):
            return False
        else:
            DictElementNode = src.getDictElementNode([])
            dictNode = dst.getDictNode([])
            nodeFree = False
            if dictNode:
                nodeFree = dictNode.KeyType.checkFree([])
            if DictElementNode:
                if not DictElementNode.key.checkFree([]) and not nodeFree:
                    if dst._data.keyType != DictElementNode.key.dataType:
                        return False

    if src.isArray() and not dst.isArray():
        srcCanChangeStruct = src.canChangeStructure(dst._currStructure, [])
        dstCanChangeStruct = dst.canChangeStructure(src._currStructure, [], selfCheck=False)
        if not dst.optionEnabled(PinOptions.ArraySupported) and not (
            srcCanChangeStruct or dstCanChangeStruct
        ):
            return False

    if src.isDict() and not dst.isDict():
        srcCanChangeStruct = src.canChangeStructure(dst._currStructure, [])
        dstCanChangeStruct = dst.canChangeStructure(src._currStructure, [], selfCheck=False)
        if not dst.optionEnabled(PinOptions.DictSupported) and not (
            srcCanChangeStruct or dstCanChangeStruct
        ):
            return False

    if dst.hasConnections():
        if (
            not dst.optionEnabled(PinOptions.AllowMultipleConnections)
            and dst.reconnectionPolicy == PinReconnectionPolicy.ForbidConnection
        ):
            return False

    if src.hasConnections():
        if (
            not src.optionEnabled(PinOptions.AllowMultipleConnections)
            and src.reconnectionPolicy == PinReconnectionPolicy.ForbidConnection
        ):
            return False

    if src.owningNode().graph() is None or dst.owningNode().graph() is None:
        return False

    if src.owningNode().graph() is not dst.owningNode().graph():
        return False

    if src.isAny() and dst.isExec():
        if src.dataType not in dst.supportedDataTypes():
            return False

    if src.isExec() and not dst.isExec():
        return False

    if not src.isExec() and dst.isExec():
        return False

    if src.IsValuePin() and dst.IsValuePin():
        if (
            src.dataType in dst.allowedDataTypes([], dst._supportedDataTypes)
            or dst.dataType in src.allowedDataTypes([], src._supportedDataTypes)
        ):
            a = src.dataType == "AnyPin" and not src.canChangeTypeOnConnection(
                [], src.optionEnabled(PinOptions.ChangeTypeOnConnection), []
            )
            b = dst.canChangeTypeOnConnection(
                [], dst.optionEnabled(PinOptions.ChangeTypeOnConnection), []
            ) and not dst.optionEnabled(PinOptions.AllowAny)
            c = (
                not dst.canChangeTypeOnConnection(
                    [], dst.optionEnabled(PinOptions.ChangeTypeOnConnection), []
                )
                and not dst.optionEnabled(PinOptions.AllowAny)
            )
            if all([a, b or c]):
                return False
            if (
                not src.isDict()
                and dst.supportOnlyDictElement([], dst.isDict())
                and not (
                    dst.checkFree([], selfCheck=False)
                    and dst.canChangeStructure(src._currStructure, [], selfCheck=False)
                )
            ):
                if not src.supportDictElement(
                    [], src.optionEnabled(PinOptions.DictElementSupported)
                ) and dst.supportOnlyDictElement([], dst.isDict()):
                    return False
            return True
        else:
            if src.dataType not in dst.supportedDataTypes():
                return False

            if all(
                [
                    src.dataType
                    in list(
                        dst.allowedDataTypes(
                            [],
                            dst._defaultSupportedDataTypes,
                            selfCheck=dst.optionEnabled(PinOptions.AllowMultipleConnections),
                            defaults=True,
                        )
                    )
                    + ["AnyPin"],
                    dst.checkFree(
                        [],
                        selfCheck=dst.optionEnabled(PinOptions.AllowMultipleConnections),
                    ),
                ]
            ):
                return True
            if all(
                [
                    dst.dataType
                    in list(
                        src.allowedDataTypes(
                            [], src._defaultSupportedDataTypes, defaults=True
                        )
                    )
                    + ["AnyPin"],
                    src.checkFree([]),
                ]
            ):
                return True
        return False

    if src.owningNode == dst.owningNode:
        return False

    return True



def connectPins(src, dst):
    """**Connects two pins**

    These are the rules how pins connect:

    * Input value pins can have one output connection if :py:class:`PyFlow.Core.Common.PinOptions.AllowMultipleConnections` flag is disabled
    * Output value pins can have any number of connections
    * Input execs can have any number of connections
    * Output execs can have only one connection

    :param src: left hand side pin
    :type src: :py:class:`PyFlow.Core.PinBase.PinBase`
    :param dst: right hand side pin
    :type dst: :py:class:`PyFlow.Core.PinBase.PinBase`
    :returns: True if connected Successfully
    :rtype: bool
    """
    if src.direction == PinDirection.Input:
        src, dst = dst, src

    if not canConnectPins(src, dst):
        return False

    # input value pins can have one output connection if `AllowMultipleConnections` flag is disabled
    # output value pins can have any number of connections
    if src.IsValuePin() and dst.IsValuePin():
        if dst.hasConnections():
            if not dst.optionEnabled(PinOptions.AllowMultipleConnections):
                dst.disconnectAll()

    # input execs can have any number of connections
    # output execs can have only one connection
    if src.isExec() and dst.isExec():
        if src.hasConnections():
            if not src.optionEnabled(PinOptions.AllowMultipleConnections):
                src.disconnectAll()

    if src.isExec() and dst.isExec():
        src.onExecute.connect(dst.call)

    dst.aboutToConnect(src)
    src.aboutToConnect(dst)

    pinAffects(src, dst)
    src.setDirty()

    dst.setData(src.currentData())

    dst.pinConnected(src)
    src.pinConnected(dst)
    #push(dst)
    return True


def getUniqNameFromList(existingNames, name):
    """Create unique name

    Iterates over **existingNames** and extracts the end digits to find a new unique id

    :param existingNames: List or set of strings where to search for existing indexes
    :type existingNames: list[str]|set[str]
    :param name: Name to obtain a unique version from
    :type name: str
    :returns: New name non overlapping with any in existingNames
    :rtype: str
    """
    if name not in existingNames:
        return name
    ids = set()
    for existingName in existingNames:
        digits = extractDigitsFromEndOfString(existingName)
        if digits is not None:
            ids.add(digits)
    idx = findGoodId(ids)
    nameNoDigits = removeDigitsFromEndOfString(name)
    return nameNoDigits + str(idx)


def findStructFromValue(value):
    """Finds :class:`~PyFlow.Core.Common.StructureType` from value

    :param value: input value to find structure.
    :returns: Structure Type for input value
    :rtype: :class:`~PyFlow.Core.Common.StructureType`
    """

    if isinstance(value, list):
        return StructureType.Array
    if isinstance(value, dict):
        return StructureType.Dict
    return StructureType.Single

