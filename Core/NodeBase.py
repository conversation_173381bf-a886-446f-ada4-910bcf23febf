# Core/NodeBase.py

from blinker import Signal
import uuid
from collections import Ordered<PERSON>ict
from copy import copy
from inspect import getfullargspec
from types import MethodType
import traceback
from PyFlow.Core.PackageManager import get_pin_default_value_by_type, create_raw_pin
from PyFlow.Core.Enums import NodeMeta, NodeTypes, PinDirection, PinOptions, PinSelectionGroup, PinSpecifiers, StructureType
from PyFlow.Core.Interfaces import INode
from datetime import datetime
from PyFlow.Core.Common import DEFAULT_IN_EXEC_NAME, DEFAULT_OUT_EXEC_NAME, pinAffects, getUniqNameFromList

class NodePinsSuggestionsHelper(object):
    """Describes node's pins types and structs for inputs and outputs separately. Used by nodebox."""
    __slots__ = ('template', 'inputTypes', 'outputTypes', 'inputStructs', 'outputStructs', '__weakref__')

    def __init__(self):
        super(NodePinsSuggestionsHelper, self).__init__()
        self.template = {
            "types": {"inputs": [], "outputs": []},
            "structs": {"inputs": [], "outputs": []},
        }
        self.inputTypes = set()
        self.outputTypes = set()
        self.inputStructs = set()
        self.outputStructs = set()

    def addInputDataType(self, dataType):
        self.inputTypes.add(dataType)

    def addOutputDataType(self, dataType):
        self.outputTypes.add(dataType)

    def addInputStruct(self, struct):
        self.inputStructs.add(struct)

    def addOutputStruct(self, struct):
        self.outputStructs.add(struct)


class NodeBase(INode):
    _packageName = ""

    __slots__ = (
        'bCacheEnabled', 'cacheMaxSize', 'cache', 'killed', 'tick', 'setDirty', 'computing', 'computed',
        'errorOccurred', 'errorCleared', 'dirty', '_uid', 'graph', 'name', 'pinsCreationOrder',
        '_pins', 'x', 'y', 'bCallable', '_wrapper', '_constraints', '_structConstraints', 'lib',
        'isCompoundNode', '_lastError', '__wrapperJsonData', '_nodeMetaData', 'headerColor',
        '_deprecated', '_deprecationMessage', '_experimental', '_computingTime'
    )

    def __init__(self, name, uid=None):
        super(NodeBase, self).__init__()
        self.bCacheEnabled = True
        self.cacheMaxSize = 1000
        self.cache = {}

        self.killed = Signal()
        self.tick = Signal(float)
        self.setDirty = Signal()
        self.computing = Signal()
        self.computed = Signal()
        self.errorOccurred = Signal(object)
        self.errorCleared = Signal()

        self.dirty = True
        self._uid = uuid.uuid4() if uid is None else uid
        self.graph = None
        self.name = name
        self.pinsCreationOrder = OrderedDict()
        self._pins = set()
        self.x = 0.0
        self.y = 0.0
        self.bCallable = False
        self._wrapper = None
        self._constraints = {}
        self._structConstraints = {}
        self.lib = None
        self.isCompoundNode = False
        self._lastError = None
        self.__wrapperJsonData = None
        self._nodeMetaData = None
        self.headerColor = None
        self._deprecated = False
        self._deprecationMessage = "This node is deprecated"
        self._experimental = False
        self._computingTime = None

    def setDeprecated(self, message):
        self._deprecated = True
        self._deprecationMessage = "This node will be removed in later releases! {}".format(
            message
        )

    def isDeprecated(self):
        return self._deprecated

    def isExperimental(self):
        return self._experimental

    def setExperimental(self):
        self._experimental = True

    def deprecationMessage(self):
        return self._deprecationMessage

    def getMetaData(self):
        return self._nodeMetaData

    @property
    def wrapperJsonData(self):
        try:
            dt = self.__wrapperJsonData.copy()
            self.__wrapperJsonData.clear()
            self.__wrapperJsonData = None
            return dt
        except Exception as e:
            return None

    def isValid(self):
        return self._lastError is None

    def getLastErrorMessage(self):
        return self._lastError

    def clearError(self):
        self._lastError = None
        self.errorCleared.send()

    def setError(self, err):
        self._lastError = str(err)
        self.errorOccurred.send(self._lastError)

    def checkForErrors(self):
        failedPins = {}
        for pin in self._pins:
            if pin._lastError is not None:
                failedPins[pin.name] = pin._lastError
        if len(failedPins):
            self._lastError = "Error on Pins:%s" % str(failedPins)
        else:
            self.clearError()
        wrapper = self.getWrapper()
        if wrapper:
            wrapper.update()

    @property
    def packageName(self):
        return self._packageName

    @property
    def constraints(self):
        return self._constraints

    @property
    def structConstraints(self):
        return self._structConstraints

    def getOrderedPins(self):
        return self.pinsCreationOrder.values()

    def getter(self, pinName):
        pin = self.getPinByName(pinName)
        if not pin:
            raise Exception()
        else:
            return pin

    def __getitem__(self, pinName):
        try:
            return self.getter(pinName)
        except Exception as x:
            if "<str>" in str(x):
                try:
                    return self.getter(str(pinName))
                except:
                    raise Exception("Could not find pin with name:{0}".format(pinName))
            else:
                raise Exception(
                    "Could not find signature for __getitem__:{0}".format(type(pinName))
                )

    @property
    def pins(self):
        return self._pins

    @property
    def inputs(self):
        result = OrderedDict()
        for pin in self.pins:
            if pin.direction == PinDirection.Input:
                result[pin.uid] = pin
        return result

    @property
    def orderedInputs(self):
        result = {}
        sortedInputs = sorted(self.inputs.values(), key=lambda x: x.pinIndex)
        for inp in sortedInputs:
            result[inp.pinIndex] = inp
        return result

    @property
    def namePinInputsMap(self):
        result = OrderedDict()
        for pin in self.pins:
            if pin.direction == PinDirection.Input:
                result[pin.name] = pin
        return result

    @property
    def outputs(self):
        result = OrderedDict()
        for pin in self.pins:
            if pin.direction == PinDirection.Output:
                result[pin.uid] = pin
        return result

    @property
    def orderedOutputs(self):
        result = {}
        sortedOutputs = sorted(self.outputs.values(), key=lambda x: x.pinIndex)
        for out in sortedOutputs:
            result[out.pinIndex] = out
        return result

    @property
    def namePinOutputsMap(self):
        result = OrderedDict()
        for pin in self.pins:
            if pin.direction == PinDirection.Output:
                result[pin.name] = pin
        return result

    def setWrapper(self, wrapper):
        if self._wrapper is None:
            self._wrapper = wrapper

    def getWrapper(self):
        return self._wrapper

    def location(self):
        return self.graph().location()

    def path(self):
        location = "/".join(self.location())
        return "{}/{}".format(location, self.getName())

    @property
    def uid(self):
        return self._uid

    @uid.setter
    def uid(self, value):
        if self.graph is not None:
            self.graph().getNodes()[value] = self.graph().getNodes().pop(self._uid)
        self._uid = value

    @staticmethod
    def jsonTemplate():
        template = {
            "package": None,
            "lib": None,
            "type": None,
            "owningGraphName": None,
            "name": None,
            "uuid": None,
            "inputs": [],
            "outputs": [],
            "meta": {"var": {}},
            "wrapper": {},
        }
        return template

    def serialize(self):
        template = NodeBase.jsonTemplate()

        uidString = str(self.uid)
        nodeName = self.name

        template["package"] = self.packageName
        template["lib"] = self.lib
        template["type"] = self.__class__.__name__
        template["name"] = nodeName
        template["owningGraphName"] = self.graph().name
        template["uuid"] = uidString
        template["inputs"] = [i.serialize() for i in self.inputs.values()]
        template["outputs"] = [o.serialize() for o in self.outputs.values()]
        template["meta"]["label"] = self.name
        template["x"] = self.x
        template["y"] = self.y

        wrapper = self.getWrapper()
        if wrapper:
            template["wrapper"] = wrapper.serializationHook()
        return template

    def isUnderActiveGraph(self):
        return self.graph() == self.graph().graphManager.activeGraph()

    def kill(self, *args, **kwargs):
        from PyFlow.Core.PathsRegistry import PathsRegistry

        if self.uid not in self.graph().getNodes():
            return

        self.killed.send()

        for pin in self.inputs.values():
            pin.kill()
        for pin in self.outputs.values():
            pin.kill()
        self.graph().getNodes().pop(self.uid)

        PathsRegistry().rebuild()

    def Tick(self, delta):
        self.tick.send(delta)

    @staticmethod
    def category():
        return "Default"

    @staticmethod
    def keywords():
        return []

    @staticmethod
    def pinTypeHints():
        return NodePinsSuggestionsHelper()

    @staticmethod
    def description():
        return "Default node description"

    def getName(self):
        return self.name

    def setName(self, name):
        self.name = str(name)

    def isDirty(self):
        inpDirty = any([pin.dirty for pin in self.inputs.values() if pin.IsValuePin()])
        outDirty = any([pin.dirty for pin in self.outputs.values() if pin.IsValuePin()])
        return inpDirty or outDirty

    def afterCompute(self):
        for pin in self.inputs.values():
            pin.setClean()
        for pin in self.outputs.values():
            pin.setClean()

    def processNode(self, *args, **kwargs):
        start = datetime.now()
        self.computing.send()
        if self.bCacheEnabled:
            if self.isDirty():
                try:
                    self.compute()
                    self.clearError()
                    self.checkForErrors()
                    self.afterCompute()
                except Exception as e:
                    self.setError(traceback.format_exc())
        else:
            try:
                self.compute()
                self.clearError()
                self.checkForErrors()
            except Exception as e:
                self.setError(traceback.format_exc())
        delta = datetime.now() - start
        self._computingTime = delta
        self.computed.send()

    def compute(self, *args, **kwargs):
        pass

    def isCallable(self):
        for p in list(self.inputs.values()) + list(self.outputs.values()):
            if p.isExec():
                return True
        return False

    def setPosition(self, x, y):
        self.x = x
        self.y = y

    def autoAffectPins(self):
        for i in self.inputs.values():
            for o in self.outputs.values():
                if i is not o:
                    if not i.IsValuePin() and o.IsValuePin():
                        continue
                    if i.IsValuePin() and not o.IsValuePin():
                        continue
                    pinAffects(i, o)

    def createInputPin(
        self,
        pinName,
        dataType,
        defaultValue=None,
        callback=None,
        structure=StructureType.Single,
        constraint=None,
        structConstraint=None,
        supportedPinDataTypes=None,
        group="",
    ):
        pinName = self.getUniqPinName(pinName)
        p = create_raw_pin(pinName, self, dataType, PinDirection.Input)
        p.structureType = structure
        p.group = group

        if structure == StructureType.Array:
            p.initAsArray(True)
        elif structure == StructureType.Dict:
            p.initAsDict(True)
        elif structure == StructureType.Multi:
            p.enableOptions(PinOptions.ArraySupported)

        if callback:
            p.onExecute.connect(callback, weak=False)

        if defaultValue is not None or dataType == "AnyPin":
            p.setDefaultValue(defaultValue)
            p.setData(defaultValue)
            if dataType == "AnyPin":
                p.setTypeFromData(defaultValue)
        else:
            p.setDefaultValue(get_pin_default_value_by_type(dataType))

        if dataType == "AnyPin" and supportedPinDataTypes:
            def supportedDataTypes():
                return supportedPinDataTypes
            p._supportedDataTypes = p._defaultSupportedDataTypes = tuple(
                supportedPinDataTypes
            )
            p.supportedDataTypes = supportedDataTypes
        if constraint is not None:
            p.updateConstraint(constraint)
        if structConstraint is not None:
            p.updateStructConstraint(structConstraint)
        p.dataBeenSet.connect(self.setDirty.send)
        p.markedAsDirty.connect(self.setDirty.send)
        return p

    def createOutputPin(
        self,
        pinName,
        dataType,
        defaultValue=None,
        structure=StructureType.Single,
        constraint=None,
        structConstraint=None,
        supportedPinDataTypes=None,
        group="",
    ):
        pinName = self.getUniqPinName(pinName)
        p = create_raw_pin(pinName, self, dataType, PinDirection.Output)
        p.structureType = structure
        p.group = group

        if structure == StructureType.Array:
            p.initAsArray(True)
        elif structure == StructureType.Dict:
            p.initAsDict(True)
        elif structure == StructureType.Multi:
            p.enableOptions(PinOptions.ArraySupported)

        if defaultValue is not None or dataType == "AnyPin":
            p.setDefaultValue(defaultValue)
            p.setData(defaultValue)
            if dataType == "AnyPin":
                p.setTypeFromData(defaultValue)
        else:
            p.setDefaultValue(get_pin_default_value_by_type(dataType))

        if dataType == "AnyPin" and supportedPinDataTypes:
            def supportedDataTypes():
                return supportedPinDataTypes
            p.supportedDataTypes = supportedDataTypes
        if constraint is not None:
            p.updateConstraint(constraint)
        if structConstraint is not None:
            p.updateStructConstraint(structConstraint)
        return p

    def setData(self, pinName, data, pinSelectionGroup=PinSelectionGroup.BothSides):
        p = self.getPinSG(str(pinName), pinSelectionGroup)
        assert p is not None, "Failed to find pin by name: {}".format(pinName)
        p.setData(data)

    def getData(self, pinName, pinSelectionGroup=PinSelectionGroup.BothSides):
        p = self.getPinSG(str(pinName), pinSelectionGroup)
        assert p is not None, "Failed to find pin by name: {}".format(pinName)
        return p.getData()

    def getUniqPinName(self, name):
        pinNames = [
            i.name
            for i in list(list(self.inputs.values()))
            + list(list(self.outputs.values()))
        ]
        return getUniqNameFromList(pinNames, name)

    def __repr__(self):
        graphName = self.graph().name if self.graph is not None else str(None)
        return "<class[{0}]; name[{1}]; graph[{2}]>".format(
            self.__class__.__name__, self.getName(), graphName
        )

    def call(self, name, *args, **kwargs):
        namePinOutputsMap = self.namePinOutputsMap
        namePinInputsMap = self.namePinInputsMap
        if name in namePinOutputsMap:
            p = namePinOutputsMap[name]
            if p.isExec():
                p.call(*args, **kwargs)
        if name in namePinInputsMap:
            p = namePinInputsMap[name]
            if p.isExec():
                p.call(*args, **kwargs)

    def getPinSG(self, name, pinsSelectionGroup=PinSelectionGroup.BothSides):
        inputs = self.inputs
        outputs = self.outputs
        if pinsSelectionGroup == PinSelectionGroup.BothSides:
            for p in list(inputs.values()) + list(outputs.values()):
                if p.name == name:
                    return p
        elif pinsSelectionGroup == PinSelectionGroup.Inputs:
            for p in list(inputs.values()):
                if p.name == name:
                    return p
        else:
            for p in list(outputs.values()):
                if p.name == name:
                    return p

    def getPinByName(self, name):
        inputs = self.inputs
        outputs = self.outputs
        for p in list(inputs.values()) + list(outputs.values()):
            if p.name == name:
                return p

    def postCreate(self, jsonTemplate=None):
        if jsonTemplate is not None:
            self.uid = uuid.UUID(jsonTemplate["uuid"])
            self.setName(jsonTemplate["name"])
            self.x = jsonTemplate["x"]
            self.y = jsonTemplate["y"]

            sortedInputs = sorted(
                jsonTemplate["inputs"], key=lambda pinDict: pinDict["pinIndex"]
            )
            for inpJson in sortedInputs:
                dynamicEnabled = PinOptions.Dynamic.value in inpJson["options"]
                if dynamicEnabled or inpJson["name"] not in self.namePinInputsMap:
                    continue
                pin = self.getPinSG(str(inpJson["name"]), PinSelectionGroup.Inputs)
                pin.deserialize(inpJson)

            sortedOutputs = sorted(
                jsonTemplate["outputs"], key=lambda pinDict: pinDict["pinIndex"]
            )
            for outJson in sortedOutputs:
                dynamicEnabled = PinOptions.Dynamic.value in outJson["options"]
                if dynamicEnabled or outJson["name"] not in self.namePinOutputsMap:
                    continue
                pin = self.getPinSG(str(outJson["name"]), PinSelectionGroup.Outputs)
                pin.deserialize(outJson)

            if "wrapper" in jsonTemplate:
                self.__wrapperJsonData = jsonTemplate["wrapper"]

        if self.isCallable():
            self.bCallable = True

        self.autoAffectPins()
        self.checkForErrors()

    @staticmethod
    def initializeFromFunction(foo):
        meta = foo.__annotations__["meta"]
        returnType = returnDefaultValue = None
        returnPinOptionsToEnable = None
        returnPinOptionsToDisable = None
        returnWidgetVariant = "DefaultWidget"
        retAnyOpts = None
        retConstraint = None
        retStructConstraint = None
        returnAnnotationDict = None

        if foo.__annotations__["return"] is not None:
            returnType = foo.__annotations__["return"][0]
            returnDefaultValue = foo.__annotations__["return"][1]
            if len(foo.__annotations__["return"]) == 3:
                returnAnnotationDict = foo.__annotations__["return"][2]

                if PinSpecifiers.SUPPORTED_DATA_TYPES in returnAnnotationDict:
                    retAnyOpts = returnAnnotationDict[PinSpecifiers.SUPPORTED_DATA_TYPES]
                if PinSpecifiers.CONSTRAINT in returnAnnotationDict:
                    retConstraint = returnAnnotationDict[PinSpecifiers.CONSTRAINT]
                if PinSpecifiers.STRUCT_CONSTRAINT in returnAnnotationDict:
                    retStructConstraint = returnAnnotationDict[PinSpecifiers.STRUCT_CONSTRAINT]
                if PinSpecifiers.ENABLED_OPTIONS in returnAnnotationDict:
                    returnPinOptionsToEnable = returnAnnotationDict[PinSpecifiers.ENABLED_OPTIONS]
                if PinSpecifiers.DISABLED_OPTIONS in returnAnnotationDict:
                    returnPinOptionsToDisable = returnAnnotationDict[PinSpecifiers.DISABLED_OPTIONS]
                if PinSpecifiers.INPUT_WIDGET_VARIANT in returnAnnotationDict:
                    returnWidgetVariant = returnAnnotationDict[PinSpecifiers.INPUT_WIDGET_VARIANT]

        nodeType = foo.__annotations__["nodeType"]
        _packageName = foo.__annotations__["packageName"]
        libName = foo.__annotations__["lib"]
        fooArgNames = getfullargspec(foo).args

        @staticmethod
        def description():
            return foo.__doc__

        @staticmethod
        def category():
            return meta[NodeMeta.CATEGORY]

        @staticmethod
        def keywords():
            return meta[NodeMeta.KEYWORDS]

        def constructor(self, name, **kwargs):
            NodeBase.__init__(self, name, **kwargs)

        nodeClass = type(
            foo.__name__,
            (NodeBase,),
            {
                "__init__": constructor,
                "category": category,
                "keywords": keywords,
                "description": description,
            },
        )

        nodeClass._packageName = _packageName

        raw_inst = nodeClass(foo.__name__)
        raw_inst.lib = libName

        refs = []
        outExec = None

        def compute(self, *args, **kwargs):
            kwds = {}
            for i in list(self.inputs.values()):
                if not i.isExec():
                    kwds[i.name] = i.getData()
            for ref in refs:
                if not ref.isExec():
                    kwds[ref.name] = ref.setData
            foo.owningNode = self
            result = foo(**kwds)
            if returnType is not None:
                self.setData(str("out"), result)
            if nodeType == NodeTypes.Callable:
                outExec.call(*args, **kwargs)

        raw_inst.compute = MethodType(compute, raw_inst)

        raw_inst._nodeMetaData = meta
        if "CacheEnabled" in meta:
            raw_inst.bCacheEnabled = meta["CacheEnabled"]

        if nodeType == NodeTypes.Callable:
            raw_inst.createInputPin(
                DEFAULT_IN_EXEC_NAME, "ExecPin", None, raw_inst.compute
            )
            outExec = raw_inst.createOutputPin(DEFAULT_OUT_EXEC_NAME, "ExecPin")
            raw_inst.bCallable = True
            raw_inst.bCacheEnabled = False

        if returnType is not None:
            p = raw_inst.createOutputPin(
                "out",
                returnType,
                returnDefaultValue,
                supportedPinDataTypes=retAnyOpts,
                constraint=retConstraint,
                structConstraint=retStructConstraint,
            )
            p.setData(returnDefaultValue)
            p.setDefaultValue(returnDefaultValue)
            p.initAsArray(isinstance(returnDefaultValue, list))
            p.initAsDict(isinstance(returnDefaultValue, dict))
            p.setInputWidgetVariant(returnWidgetVariant)
            p.annotationDescriptionDict = (
                copy(returnAnnotationDict) if returnAnnotationDict is not None else None
            )
            if (
                p.annotationDescriptionDict is not None
                and "Description" in p.annotationDescriptionDict
            ):
                p.description = p.annotationDescriptionDict["Description"]
            if returnPinOptionsToEnable is not None:
                p.enableOptions(returnPinOptionsToEnable)
            if returnPinOptionsToDisable is not None:
                p.disableOptions(returnPinOptionsToDisable)
            if not p.isArray() and p.optionEnabled(PinOptions.ArraySupported):
                p.structureType = StructureType.Multi
            elif p.isArray():
                p.structureType = StructureType.Array

        for index in range(len(fooArgNames)):
            argName = fooArgNames[index]
            pinDescriptionTuple = foo.__annotations__[argName]
            anyOpts = None
            constraint = None
            structConstraint = None
            pinOptionsToEnable = None
            pinOptionsToDisable = None
            inputWidgetVariant = "DefaultWidget"
            if str("Reference") == pinDescriptionTuple[0]:
                pinDataType = pinDescriptionTuple[1][0]
                pinDefaultValue = pinDescriptionTuple[1][1]
                pinDict = None
                if len(pinDescriptionTuple[1]) == 3:
                    pinDict = pinDescriptionTuple[1][2]

                if pinDict is not None:
                    if PinSpecifiers.SUPPORTED_DATA_TYPES in pinDict:
                        anyOpts = pinDict[PinSpecifiers.SUPPORTED_DATA_TYPES]
                    if PinSpecifiers.CONSTRAINT in pinDict:
                        constraint = pinDict[PinSpecifiers.CONSTRAINT]
                    if PinSpecifiers.STRUCT_CONSTRAINT in pinDict:
                        structConstraint = pinDict[PinSpecifiers.STRUCT_CONSTRAINT]
                    if PinSpecifiers.ENABLED_OPTIONS in pinDict:
                        pinOptionsToEnable = pinDict[PinSpecifiers.ENABLED_OPTIONS]
                    if PinSpecifiers.DISABLED_OPTIONS in pinDict:
                        pinOptionsToDisable = pinDict[PinSpecifiers.DISABLED_OPTIONS]
                    if PinSpecifiers.INPUT_WIDGET_VARIANT in pinDict:
                        inputWidgetVariant = pinDict[PinSpecifiers.INPUT_WIDGET_VARIANT]

                outRef = raw_inst.createOutputPin(
                    argName,
                    pinDataType,
                    supportedPinDataTypes=anyOpts,
                    constraint=constraint,
                    structConstraint=structConstraint,
                )
                outRef.annotationDescriptionDict = (
                    copy(pinDict) if pinDict is not None else None
                )
                if (
                    outRef.annotationDescriptionDict is not None
                    and "Description" in outRef.annotationDescriptionDict
                ):
                    outRef.description = outRef.annotationDescriptionDict["Description"]
                outRef.initAsArray(isinstance(pinDefaultValue, list))
                outRef.initAsDict(isinstance(pinDefaultValue, dict))
                outRef.setDefaultValue(pinDefaultValue)
                outRef.setData(pinDefaultValue)
                outRef.setInputWidgetVariant(inputWidgetVariant)
                if pinOptionsToEnable is not None:
                    outRef.enableOptions(pinOptionsToEnable)
                if pinOptionsToDisable is not None:
                    outRef.disableOptions(pinOptionsToDisable)
                if not outRef.isArray() and outRef.optionEnabled(
                    PinOptions.ArraySupported
                ):
                    outRef.structureType = StructureType.Multi
                elif outRef.isArray():
                    outRef.structureType = StructureType.Array
                refs.append(outRef)
            else:
                pinDataType = pinDescriptionTuple[0]
                pinDefaultValue = pinDescriptionTuple[1]
                pinDict = None
                if len(pinDescriptionTuple) == 3:
                    pinDict = pinDescriptionTuple[2]

                if pinDict is not None:
                    if PinSpecifiers.SUPPORTED_DATA_TYPES in pinDict:
                        anyOpts = pinDict[PinSpecifiers.SUPPORTED_DATA_TYPES]
                    if PinSpecifiers.CONSTRAINT in pinDict:
                        constraint = pinDict[PinSpecifiers.CONSTRAINT]
                    if PinSpecifiers.STRUCT_CONSTRAINT in pinDict:
                        structConstraint = pinDict[PinSpecifiers.STRUCT_CONSTRAINT]
                    if PinSpecifiers.ENABLED_OPTIONS in pinDict:
                        pinOptionsToEnable = pinDict[PinSpecifiers.ENABLED_OPTIONS]
                    if PinSpecifiers.DISABLED_OPTIONS in pinDict:
                        pinOptionsToDisable = pinDict[PinSpecifiers.DISABLED_OPTIONS]
                    if PinSpecifiers.INPUT_WIDGET_VARIANT in pinDict:
                        inputWidgetVariant = pinDict[PinSpecifiers.INPUT_WIDGET_VARIANT]

                inp = raw_inst.createInputPin(
                    argName,
                    pinDataType,
                    supportedPinDataTypes=anyOpts,
                    constraint=constraint,
                    structConstraint=structConstraint,
                )
                inp.annotationDescriptionDict = (
                    copy(pinDict) if pinDict is not None else None
                )
                if (
                    inp.annotationDescriptionDict is not None
                    and "Description" in inp.annotationDescriptionDict
                ):
                    inp.description = inp.annotationDescriptionDict["Description"]
                inp.initAsArray(isinstance(pinDefaultValue, list))
                inp.initAsDict(isinstance(pinDefaultValue, dict))
                inp.setData(pinDefaultValue)
                inp.setDefaultValue(pinDefaultValue)
                inp.setInputWidgetVariant(inputWidgetVariant)
                if pinOptionsToEnable is not None:
                    inp.enableOptions(pinOptionsToEnable)
                if pinOptionsToDisable is not None:
                    inp.disableOptions(pinOptionsToDisable)
                if not inp.isArray() and inp.optionEnabled(PinOptions.ArraySupported):
                    inp.structureType = StructureType.Multi
                elif inp.isArray():
                    inp.structureType = StructureType.Array
        raw_inst.autoAffectPins()
        return raw_inst
