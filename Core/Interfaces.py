# Core/Interfaces.py

from PyFlow.Core.Common import StructureType

class ISerializable(object):
    """
    Interface for serialization and deserialization
    """

    __slots__ = ()

    def __init__(self):
        super(ISerializable, self).__init__()

    def serialize(self, *args, **Kwargs):
        raise NotImplementedError(
            "serialize method of ISerializable is not implemented"
        )

    def deserialize(self, jsonData):
        raise NotImplementedError(
            "deserialize method of ISerializable is not implemented"
        )


class IItemBase(ISerializable):
    """Base class for pins and nodes
    """

    __slots__ = ()

    def __init__(self):
        super(IItemBase, self).__init__()

    def getWrapper(self):
        return None

    def setWrapper(self, wrapper):
        pass

    @property
    def uid(self):
        raise NotImplementedError("uid property of IItemBase should be implemented")

    @uid.setter
    def uid(self, value):
        raise NotImplementedError("uid setter of IItemBase should be implemented")

    @uid.deleter
    def uid(self):
        raise NotImplementedError("uid property of IItemBase can not be deleted")

    def getName(self):
        raise NotImplementedError("getName method of IItemBase is not implemented")

    def setName(self, name):
        raise NotImplementedError("setName method of IItemBase is not implemented")

    def kill(self):
        raise NotImplementedError("kill method of IItemBase is not implemented")

    def path(self):
        raise NotImplementedError("path method of IItemBase is not implemented")


class IPin(IItemBase):
    """Pin interface
    """

    __slots__ = ()

    def __init__(self):
        super(IPin, self).__init__()

    @staticmethod
    def IsValuePin():
        raise NotImplementedError("IsValuePin method of IPin is not implemented")

    @staticmethod
    def color():
        return (255, 0, 0, 255)

    def isExec(self):
        raise NotImplementedError("isExec method of IPin is not implemented")

    def isArray(self):
        raise NotImplementedError("isArray method of IPin is not implemented")

    def isAny(self):
        raise NotImplementedError("isAny method of IPin is not implemented")

    @staticmethod
    def internalDataStructure():
        raise NotImplementedError(
            "internalDataStructure method of IPin is not implemented"
        )

    @staticmethod
    def processData(data):
        raise NotImplementedError("processData method of IPin is not implemented")

    @staticmethod
    def supportedDataTypes():
        raise NotImplementedError(
            "supportedDataTypes method of IPin is not implemented"
        )

    def defaultValue(self):
        raise NotImplementedError("defaultValue method of IPin is not implemented")

    def getData(self):
        raise NotImplementedError("getData method of IPin is not implemented")

    def setData(self, value):
        raise NotImplementedError("setData method of IPin is not implemented")

    def call(self, *args, **kwargs):
        pass

    @property
    def dataType(self):
        raise NotImplementedError("dataType getter method of IPin is not implemented")

    @dataType.setter
    def dataType(self, value):
        raise NotImplementedError("dataType setter method of IPin is not implemented")

    @staticmethod
    def jsonEncoderClass():
        raise NotImplementedError("jsonEncoderClass method of IPin is not implemented")

    @staticmethod
    def jsonDecoderClass():
        raise NotImplementedError("jsonEncoderClass method of IPin is not implemented")

    def setAsArray(self, bIsArray):
        raise NotImplementedError("setAsArray method of IPin is not implemented")


class INode(IItemBase):
    __slots__ = ()

    def __init__(self):
        super(INode, self).__init__()

    def compute(self, *args, **kwargs):
        raise NotImplementedError("compute method of INode is not implemented")

    def isCallable(self):
        raise NotImplementedError("isCallable method of INode is not implemented")

    def call(self, outPinName, *args, **kwargs):
        raise NotImplementedError("call method of INode is not implemented")

    def createInputPin(
        self,
        pinName,
        dataType,
        defaultValue=None,
        callback=None,
        structure=StructureType.Single,
        constraint=None,
        structConstraint=None,
        supportedPinDataTypes=None,
        group="",
    ):
        raise NotImplementedError("createInputPin method of INode is not implemented")

    def createOutputPin(
        self,
        pinName,
        dataType,
        defaultValue=None,
        structure=StructureType.Single,
        constraint=None,
        structConstraint=None,
        supportedPinDataTypes=None,
        group="",
    ):
        raise NotImplementedError("createOutputPin method of INode is not implemented")

    def getUniqPinName(self, name):
        raise NotImplementedError("getUniqPinName method of INode is not implemented")

    def postCreate(self, jsonTemplate=None):
        raise NotImplementedError("postCreate method of INode is not implemented")

    def setData(self, pinName, data, pinSelectionGroup):
        raise NotImplementedError("setData method of INode is not implemented")

    def getData(self, pinName, pinSelectionGroup):
        raise NotImplementedError("getData method of INode is not implemented")


class ICodeCompiler(object):
    __slots__ = ()

    def __init__(self, *args, **kwargs):
        super(ICodeCompiler, self).__init__(*args, **kwargs)

    def compile(self, code):
        raise NotImplementedError("compile method of ICodeCompiler is not implemented")


class IEvaluationEngine(object):
    """docstring for IEvaluationEngine."""
    __slots__ = ()

    def __init__(self):
        super(IEvaluationEngine, self).__init__()

    @staticmethod
    def getPinData(pin):
        raise NotImplementedError(
            "getPinData method of IEvaluationEngine is not implemented"
        )
