# Core/PackageManager.py

import os
import pkgutil
import importlib
from copy import copy
import collections.abc

__PACKAGES = {}
__PACKAGE_PATHS = {}
__HASHABLE_TYPES = []

# Caching for pin classes
PINCLASS_CACHE = {}

# Cache for discovered package directories to avoid redundant scans
DISCOVERED_PACKAGE_DIRS = set()

def get_packages():
    return __PACKAGES


def get_package_path(packageName):
    return __PACKAGE_PATHS.get(packageName, None)


def get_package_checked(package_name):
    assert package_name in __PACKAGES, f"Package '{package_name}' not found."
    return __PACKAGES[package_name]


def get_all_pin_classes():
    result = []
    for package in list(__PACKAGES.values()):
        result += list(package.GetPinClasses().values())
    return result


def find_pin_class_by_type(dataType):
    return PINCLASS_CACHE.get(dataType, None)


def get_pin_default_value_by_type(dataType):
    pin = find_pin_class_by_type(dataType)
    if pin:
        return pin.pinDataTypeHint()[1]
    return None


def get_hashable_data_types():
    global __HASHABLE_TYPES
    if not __HASHABLE_TYPES:
        for pin in get_all_pin_classes():
            t = pin.internalDataStructure()
            if t is not type(None) and t is not None:
                if isinstance(pin.pinDataTypeHint()[1], collections.abc.Hashable):
                    __HASHABLE_TYPES.append(pin.__name__)
    return copy(__HASHABLE_TYPES)


def get_pin_from_data(data):
    for pin in [pin for pin in get_all_pin_classes() if pin.IsValuePin()]:
        pType = pin.internalDataStructure()
        if data == pType:
            return pin


def create_raw_pin(name, owningNode, dataType, direction, **kwds):
    pinClass = find_pin_class_by_type(dataType)
    if pinClass is None:
        return None
    inst = pinClass(name, owningNode, direction, **kwds)
    return inst


def discover_packages(packagePaths):
    """
    Discover available packages in the given packagePaths.
    This function populates the __PACKAGES and __PACKAGE_PATHS dictionaries.
    """
    global DISCOVERED_PACKAGE_DIRS
    for packagePath in packagePaths:
        if packagePath in DISCOVERED_PACKAGE_DIRS:
            continue  # Skip already discovered paths
        DISCOVERED_PACKAGE_DIRS.add(packagePath)
        for importer, modname, ispkg in pkgutil.iter_modules([packagePath]):
            try:
                if ispkg:
                    spec = importer.find_spec(modname)
                    if spec is not None:
                        mod = importlib.util.module_from_spec(spec)
                        spec.loader.exec_module(mod)
                        package = getattr(mod, modname)()
                        __PACKAGES[modname] = package
                        __PACKAGE_PATHS[modname] = os.path.normpath(mod.__path__[0])
            except Exception as e:
                # Log the error and continue
                import traceback
                traceback_str = ''.join(traceback.format_exception(None, e, e.__traceback__))
                print(f"Error loading package '{modname}':\n{traceback_str}")
                continue


def initialize_packages(software=""):
    """
    Initialize the discovered packages by registering their components.
    """
    # Build PINCLASS_CACHE once after all packages are loaded
    for package_name, package in get_packages().items():
        pins = package.GetPinClasses()
        for dataType, pinClass in pins.items():
            PINCLASS_CACHE[dataType] = pinClass

    registeredInternalPinDataTypes = set()

    for name, package in __PACKAGES.items():
        packageName = package.__class__.__name__
        for node in package.GetNodeClasses().values():
            node._packageName = packageName

        for pin in package.GetPinClasses().values():
            pin._packageName = packageName
            if pin.IsValuePin():
                internalType = pin.internalDataStructure()
                if internalType in registeredInternalPinDataTypes:
                    raise Exception(
                        f"Pin with {internalType} internal data type already been registered."
                    )
                registeredInternalPinDataTypes.add(internalType)

        uiPinsFactory = package.UIPinsFactory()
        if uiPinsFactory is not None:
            from PyFlow.UI.Canvas.UIPinBase import REGISTER_UI_PIN_FACTORY
            REGISTER_UI_PIN_FACTORY(packageName, uiPinsFactory)

        uiPinInputWidgetsFactory = package.PinsInputWidgetFactory()
        if uiPinInputWidgetsFactory is not None:
            from PyFlow.UI.Widgets.InputWidgets import REGISTER_UI_INPUT_WIDGET_PIN_FACTORY
            REGISTER_UI_INPUT_WIDGET_PIN_FACTORY(packageName, uiPinInputWidgetsFactory)

        uiNodesFactory = package.UINodesFactory()
        if uiNodesFactory is not None:
            from PyFlow.UI.Canvas.UINodeBase import REGISTER_UI_NODE_FACTORY
            REGISTER_UI_NODE_FACTORY(packageName, uiNodesFactory)

        for toolClass in package.GetToolClasses().values():
            supportedSoftwares = toolClass.supportedSoftwares()
            if "any" not in supportedSoftwares:
                if software not in supportedSoftwares:
                    continue
            from PyFlow.UI.Tool import REGISTER_TOOL
            REGISTER_TOOL(packageName, toolClass)
    get_hashable_data_types()
