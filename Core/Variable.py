# Core/Variable.py

from blinker import Signal
import json
import uuid
import weakref  # Added for weak references
import msgpack  # Added for binary serialization
from PyFlow.Core.PackageManager import get_pin_default_value_by_type, find_pin_class_by_type
from PyFlow.Core.Interfaces import IItemBase

from PyFlow.Core.Enums import AccessLevel, StructureType
from PyFlow.Core.Utils import PFDict

class Variable(IItemBase):
    """Variable representation
    """

    # Using __slots__ to reduce overhead
    __slots__ = (
        'nameChanged',
        'valueChanged',
        'dataTypeChanged',
        'structureChanged',
        'accessLevelChanged',
        'killed',
        '_name',
        '_value',
        '_dataType',
        '_structure',
        '_accessLevel',
        '_packageName',
        '_uid',
        '_uiWrapper',
        '_graph',
        'packageNameChanged'
    )

    def __init__(
        self,
        graph,
        value,
        name,
        dataType,
        accessLevel=AccessLevel.public,
        structure=StructureType.Single,
        uid=None,
    ):
        super(Variable, self).__init__()
        self.nameChanged = Signal(str)
        self.valueChanged = Signal(str)
        self.dataTypeChanged = Signal(str)
        self.structureChanged = Signal(str)
        self.accessLevelChanged = Signal(str)
        self.killed = Signal()
        self.packageNameChanged = Signal(str)  # Defined signal for packageNameChanged

        # Store graph as weak reference to avoid memory leaks
        self._graph = weakref.ref(graph)

        self._name = name
        self._value = value
        self._dataType = dataType
        self._structure = structure
        self._accessLevel = accessLevel
        self._packageName = None
        self._uid = uuid.uuid4() if uid is None else uid
        assert isinstance(self._uid, uuid.UUID)
        self.updatePackageName()
        self._uiWrapper = None

    def getWrapper(self):
        if self._uiWrapper is not None:
            return self._uiWrapper()
        return None

    def setWrapper(self, wrapper):
        if self._uiWrapper is None:
            self._uiWrapper = weakref.ref(wrapper)

    @property
    def graph(self):
        return self._graph()

    def location(self):
        return self.graph.location()

    def findRefs(self):
        return self.graph.graphManager.findVariableRefs(self)

    def updatePackageName(self):
        self._packageName = find_pin_class_by_type(self._dataType)._packageName

    @property
    def packageName(self):
        return self._packageName

    @packageName.setter
    def packageName(self, value):
        assert isinstance(value, str)
        self._packageName = value
        self.packageNameChanged.send(value)

    @property
    def accessLevel(self):
        return self._accessLevel

    @accessLevel.setter
    def accessLevel(self, value):
        assert isinstance(value, AccessLevel)
        self._accessLevel = value
        self.accessLevelChanged.send(value)

    @property
    def name(self):
        return self._name

    @name.setter
    def name(self, value):
        assert isinstance(value, str)
        self._name = value
        self.nameChanged.send(value)

    @property
    def value(self):
        return self._value

    @value.setter
    def value(self, value):
        if not self.dataType == "AnyPin":
            supportedDataTypes = find_pin_class_by_type(self.dataType).supportedDataTypes()
            if self.dataType not in supportedDataTypes:
                return

        try:
            if self._value != value or type(self._value) != type(value):
                self._value = value
                self.valueChanged.send(value)
        except:
            self._value = value
            self.valueChanged.send(value)

    @property
    def dataType(self):
        return self._dataType

    @dataType.setter
    def dataType(self, value):
        assert isinstance(value, str)
        if value != self._dataType:
            self._dataType = value
            self.updatePackageName()
            self.value = get_pin_default_value_by_type(value)
            self.dataTypeChanged.send(value)

    @property
    def structure(self):
        return self._structure

    @structure.setter
    def structure(self, value):
        assert isinstance(value, StructureType)
        if value != self._structure:
            self._structure = value
            if self._structure == StructureType.Array:
                self.value = list()
            if self._structure == StructureType.Dict:
                self.value = PFDict("IntPin", "BoolPin")
            self.structureChanged.send(self._structure)

    @property
    def uid(self):
        return self._uid

    @uid.setter
    def uid(self, value):
        assert isinstance(value, uuid.UUID)
        self.graph.getVars()[value] = self.graph.getVars().pop(self._uid)
        self._uid = value

    def serialize(self):
        # Using msgpack for binary serialization
        data = {
            "name": self.name,
            "value": self.value,
            "dataType": self.dataType,
            "structure": self.structure.name,
            "accessLevel": self.accessLevel.name,
            "package": self._packageName,
            "uuid": str(self.uid)
        }
        if self.structure == StructureType.Dict:
            data["dictKeyType"] = self.value.keyType
            data["dictValueType"] = self.value.valueType

        return msgpack.packb(data, use_bin_type=True)

    @staticmethod
    def deserialize(graph, packed_data, *args, **kwargs):
        data = msgpack.unpackb(packed_data, raw=False)
        name = data["name"]

        if data["structure"] == StructureType.Dict.name:
            keyDataType = data["dictKeyType"]
            valueDataType = data["dictValueType"]
            value = PFDict(keyDataType, valueDataType)
            dataType = valueDataType
        else:
            dataType = data["dataType"]
            if dataType != "AnyPin":
                pinClass = find_pin_class_by_type(dataType)
                # Since we used msgpack, value is already primitive or easily decodable
                value = data["value"]
            else:
                value = get_pin_default_value_by_type("AnyPin")

        accessLevel = AccessLevel[data["accessLevel"]]
        structure = StructureType[data["structure"]]
        uid = uuid.UUID(data["uuid"])
        return Variable(graph, value, name, dataType, accessLevel, structure, uid)

    @staticmethod
    def jsonTemplate():
        template = {
            "name": None,
            "value": None,
            "dataType": None,
            "accessLevel": None,
            "package": None,
            "uuid": None,
        }
        return template
