# Core/__init__.py

import os
from dotenv import load_dotenv
from PyFlow.Core.PackageManager import discover_packages, initialize_packages

# 1) load .env into os.environ
load_dotenv()

def INITIALIZE(additionalPackageLocations=None, software=""):
    """
    Initializes PyFlow by discovering and initializing packages.
    Package paths are pulled from the PYFLOW_PACKAGE_PATHS env var
    (split on os.pathsep), plus any additionalPackageLocations passed in.
    """
    # 2) read your env var (or fall back to the default path)
    env_paths = os.getenv(
        "PYFLOW_PACKAGE_PATHS",
        "/usr/local/lib/python3.10/dist-packages/PyFlow/Packages"
    )
    # 3) split into a list, normalize them
    package_dirs = [
        os.path.normpath(p) for p in env_paths.split(os.pathsep) if p
    ]
    # 4) append any extras passed in (e.g. from ConfigManager)
    if additionalPackageLocations:
        package_dirs.extend(os.path.normpath(p) for p in additionalPackageLocations)
    # 5) discover + init
    discover_packages(package_dirs)
    initialize_packages(software)
