# Core/Enums.py

from enum import IntEnum, Flag, auto


class PinSpecifiers:
    """Pin specifiers constants

    :var SUPPORTED_DATA_TYPES: To specify supported data types list
    :var CONSTRAINT: To specify type constraint key
    :var STRUCT_CONSTRAINT: To specify struct constraint key
    :var ENABLED_OPTIONS: To enable options
    :var DISABLED_OPTIONS: To disable options
    :var INPUT_WIDGET_VARIANT: To specify widget variant string
    :var DESCRIPTION: To specify description for pin, which will be used as tooltip
    :var VALUE_LIST: Specific for string pin. If specified, combo box will be created
    :var VALUE_RANGE: Specific for ints and floats. If specified, slider will be created instead of value box
    :var DRAGGER_STEPS: To specify custom value dragger steps
    """

    SUPPORTED_DATA_TYPES = "supportedDataTypes"
    CONSTRAINT = "constraint"
    STRUCT_CONSTRAINT = "structConstraint"
    ENABLED_OPTIONS = "enabledOptions"
    DISABLED_OPTIONS = "disabledOptions"
    INPUT_WIDGET_VARIANT = "inputWidgetVariant"
    DESCRIPTION = "Description"
    VALUE_LIST = "ValueList"
    VALUE_RANGE = "ValueRange"
    DRAGGER_STEPS = "DraggerSteps"

class NodeMeta:
    """Node meta constants

    :var CATEGORY: To specify category for node. Will be considered by node box
    :var KEYWORDS: To specify list of additional keywords, used in node box search field
    :var CACHE_ENABLED: To specify if node is cached or not
    """
    CATEGORY = "Category"
    KEYWORDS = "Keywords"
    CACHE_ENABLED = "CacheEnabled"

class PinReconnectionPolicy(IntEnum):
    """How to behave if pin has connections and another connection is about to be performed."""
    DisconnectIfHasConnections = 0  #: Current connection will be broken
    ForbidConnection = 1  #: New connection will be cancelled


class PinOptions(Flag):
    """Used to determine how Pin behaves.

    Apply flags on pin instances.

    .. seealso:: :meth:`~PyFlow.Core.PinBase.PinBase.enableOptions` :meth:`~PyFlow.Core.PinBase.PinBase.disableOptions`
    """
    ArraySupported = auto()  #: Pin can hold array data structure
    DictSupported = auto()  #: Pin can hold dict data structure
    SupportsOnlyArrays = auto()  #: Pin will only support other pins with array data structure
    AllowMultipleConnections = auto()  #: Allows more than one input connection
    ChangeTypeOnConnection = auto()  #: Allows changing data type on new connection
    RenamingEnabled = auto()  #: Determines if pin can be renamed
    Dynamic = auto()  #: Specifies if pin was created dynamically (during runtime)
    AlwaysPushDirty = auto()  #: Pin will always be seen as dirty (computation needed)
    Storable = auto()  #: Determines if pin data can be stored when serialized
    AllowAny = auto()  #: Allows a pin to be AnyPin (non-typed without error)
    DictElementSupported = auto()  #: Dict pins will only allow other dicts until this flag is enabled


class StructureType(IntEnum):
    """Used to determine structure type for values."""
    Single = 0  #: Single data structure
    Array = 1  #: Python list structure, represented as arrays -> typed and lists -> non-typed
    Dict = 2  #: PFDict structure, a typed Python dict
    Multi = 3  #: Can become any of the previous types on connection/user action


class PinDirection(IntEnum):
    """Determines whether it is an input pin or output pin."""
    Input = 0  #: Left side pins
    Output = 1  #: Right side pins


class NodeTypes(IntEnum):
    """Determines whether it is a callable node or pure."""
    Callable = 0  #: Callable node with exec pins
    Pure = 1  #: Normal nodes


class Direction(IntEnum):
    """Direction identifiers."""
    Left = 0  #: Left
    Right = 1  #: Right
    Up = 2  #: Up
    Down = 3  #: Down


class PinSelectionGroup(IntEnum):
    """Used in NodeBase.getPinSG for optimization purposes."""
    Inputs = 0  #: Input pins
    Outputs = 1  #: Output pins
    BothSides = 2  #: Both sides pins


class AccessLevel(IntEnum):
    """Can be used for code generation."""
    public = 0  #: public
    private = 1  #: private
    protected = 2  #: protected
