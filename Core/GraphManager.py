# Core/GraphManager.py

from blinker import Signal
from PyFlow.Core.GraphBase import GraphBase
from PyFlow.Core.Utils import SingletonDecorator
from PyFlow.Core.Common import getUniqNameFromList
from PyFlow.Core.version import currentVersion,Version
import threading

ROOT_GRAPH_NAME = "root"

class GraphManager(object):
    """Data structure that holds graph tree
    """
    __slots__ = (
        'terminationRequested', 'graphChanged', '_graphs', '_activeGraph', '_lock'
    )

    def __init__(self):
        super(GraphManager, self).__init__()
        self.terminationRequested = False
        self.graphChanged = Signal(object)
        self._graphs = {}
        self._lock = threading.Lock()  # Initialize _lock first

        # Now create the active graph, which will use the lock
        self._activeGraph = GraphBase(ROOT_GRAPH_NAME, self)
        self._activeGraph.setIsRoot(True)

    def findRootGraph(self):
        roots = []
        for graph in self.getAllGraphs():
            if graph.isRoot():
                roots.append(graph)
        assert len(roots) == 1, "Fatal! Multiple roots!"
        return roots[0]

    def selectRootGraph(self):
        self.selectGraph(self.findRootGraph())

    def serialize(self):
        rootGraph = self.findRootGraph()
        saved = rootGraph.serialize()
        saved["fileVersion"] = str(currentVersion())
        saved["activeGraph"] = self.activeGraph().name
        return saved

    def removeGraphByName(self, name):
        graph = self.findGraph(name)
        if graph is not None:
            graph.clear()
            self._graphs.pop(graph.uid)
            if graph.parentGraph is not None:
                if graph in graph.parentGraph.childGraphs:
                    graph.parentGraph.childGraphs.remove(graph)
            del graph

    def removeGraph(self, graph):
        if graph.uid in self._graphs:
            graph.clear()
            self._graphs.pop(graph.uid)
            if graph.parentGraph is not None:
                if graph in graph.parentGraph.childGraphs:
                    graph.parentGraph.childGraphs.remove(graph)
            del graph

    def deserialize(self, data):
        if "fileVersion" in data:
            fileVersion = Version.fromString(data["fileVersion"])
        else:
            pass
        self.clear(keepRoot=False)
        self._activeGraph = GraphBase(str("root"), self)
        self._activeGraph.populateFromJson(data)
        self._activeGraph.setIsRoot(True)
        self.selectGraph(self._activeGraph)

    def clear(self, keepRoot=True, *args, **kwargs):
        self.selectGraphByName(ROOT_GRAPH_NAME)
        self.removeGraphByName(ROOT_GRAPH_NAME)
        self._graphs.clear()
        self._graphs = {}
        del self._activeGraph
        self._activeGraph = None
        if keepRoot:
            self._activeGraph = GraphBase(ROOT_GRAPH_NAME, self)
            self.selectGraph(self._activeGraph)
            self._activeGraph.setIsRoot(True)

    def Tick(self, deltaTime):
        for graph in self._graphs.values():
            graph.Tick(deltaTime)

    def findVariableRefs(self, variable):
        result = []
        for node in self.getAllNodes(classNameFilters=["getVar", "setVar"]):
            if node.variableUid() == variable.uid:
                result.append(node)
        return result

    def findGraph(self, name):
        graphs = self.getGraphsDict()
        if name in graphs:
            return graphs[name]
        return None

    def findPinByName(self, pinFullName):
        result = None
        for graph in self.getAllGraphs():
            result = graph.findPin(pinFullName)
            if result is not None:
                break
        return result

    def findNode(self, name):
        result = None
        for graph in self.getAllGraphs():
            result = graph.findNode(name)
            if result is not None:
                break
        return result

    def findVariableByUid(self, uuid):
        result = None
        for graph in self._graphs.values():
            if uuid in graph.getVars():
                result = graph.getVars()[uuid]
                break
        return result

    def findVariableByName(self, name):
        for graph in self._graphs.values():
            for var in graph.getVars().values():
                if var.name == name:
                    return var
        return None

    def location(self):
        return self.activeGraph().location()

    def getGraphsDict(self):
        # Using generator expression and dict comprehension
        return {graph.name: graph for graph in self.getAllGraphs()}

    def add(self, graph):
        with self._lock:
            graph.name = self.getUniqGraphName(graph.name)
            self._graphs[graph.uid] = graph

    def activeGraph(self):
        return self._activeGraph

    def selectGraphByName(self, name):
        graphs = self.getGraphsDict()
        if name in graphs:
            if name != self.activeGraph().name:
                newGraph = graphs[name]
                self._activeGraph = newGraph
                self.graphChanged.send(self.activeGraph())

    def selectGraph(self, graph):
        for newGraph in self.getAllGraphs():
            if newGraph.name == graph.name:
                if newGraph.name != self.activeGraph().name:
                    self._activeGraph = newGraph
                    self.graphChanged.send(self.activeGraph())
                    break

    def getAllGraphs(self):
        # Return generator instead of list
        return (g for g in self._graphs.values())

    def getAllNodes(self, classNameFilters=None):
        if classNameFilters is None:
            classNameFilters = []
        if len(classNameFilters) == 0:
            return [node for graph in self.getAllGraphs() for node in graph.getNodes().values()]
        else:
            return [
                node
                for graph in self.getAllGraphs()
                for node in graph.getNodes().values()
                if node.__class__.__name__ in classNameFilters
            ]

    def getAllVariables(self):
        result = []
        for graph in self.getAllGraphs():
            result.extend(list(graph.getVars().values()))
        return result

    @staticmethod
    def getUniqGraphPinName(graph, name):
        existingNames = []
        for node in graph.getNodesList(
            classNameFilters=["graphInputs", "graphOutputs"]
        ):
            existingNames.extend([pin.name for pin in node.pins])
        return getUniqNameFromList(existingNames, name)

    def getAllNames(self):
        existingNames = [g.name for g in self.getAllGraphs()]
        existingNames.extend([n.name for n in self.getAllNodes()])
        existingNames.extend([var.name for var in self.getAllVariables()])
        for node in self.getAllNodes():
            existingNames.extend([pin.name for pin in node.pins])
        return existingNames

    def getUniqName(self, name):
        existingNames = self.getAllNames()
        return getUniqNameFromList(existingNames, name)

    def getUniqGraphName(self, name):
        existingNames = [g.name for g in self.getAllGraphs()]
        return getUniqNameFromList(existingNames, name)

    def getUniqNodeName(self, name):
        existingNames = [n.name for n in self.getAllNodes()]
        return getUniqNameFromList(existingNames, name)

    def getUniqVariableName(self, name):
        existingNames = [var.name for var in self.getAllVariables()]
        return getUniqNameFromList(existingNames, name)

    def plot(self):
        root = self.findRootGraph()
        print(
            "Active graph: {0}".format(str(self.activeGraph().name)),
            "All graphs:",
            [g.name for g in self._graphs.values()],
        )
        root.plot()


@SingletonDecorator
class GraphManagerSingleton(object):
    __slots__ = ('man',)
    def __init__(self):
        self.man = GraphManager()

    def get(self):
        return self.man
