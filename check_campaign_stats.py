#!/usr/bin/env python3
import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CommandNetSolutions.settings')
django.setup()

# Import models
from campaigns.models import Campaign

def analyze_campaign_types():
    """Analyze campaign types based on actual targets"""
    # Get all campaigns
    campaigns = Campaign.objects.all()
    print(f"Total campaigns: {campaigns.count()}")

    # Initialize counters
    location_only = 0
    username_only = 0
    mixed = 0
    no_targets = 0

    # Count campaigns by their actual targets
    print("\nCampaign Types:")
    for campaign in campaigns:
        has_location_targets = campaign.location_targets.exists()
        has_username_targets = campaign.username_targets.exists()
        
        print(f"ID: {campaign.id}")
        print(f"Name: {campaign.name}")
        print(f"Target Type: {campaign.target_type}")
        print(f"Has Location Targets: {has_location_targets}")
        print(f"Has Username Targets: {has_username_targets}")
        print("-" * 50)
        
        # Count by actual targets
        if has_location_targets and has_username_targets:
            mixed += 1
        elif has_location_targets:
            location_only += 1
        elif has_username_targets:
            username_only += 1
        else:
            no_targets += 1

    # Calculate percentages
    total_with_targets = location_only + username_only + mixed
    print(f"\nSummary:")
    print(f"Location Only: {location_only} campaigns")
    print(f"Username Only: {username_only} campaigns")
    print(f"Mixed: {mixed} campaigns")
    print(f"No Targets: {no_targets} campaigns")
    print(f"Total with targets: {total_with_targets} campaigns")
    print(f"Total campaigns: {campaigns.count()}")

    if total_with_targets > 0:
        location_percentage = round((location_only / total_with_targets) * 100)
        username_percentage = round((username_only / total_with_targets) * 100)
        mixed_percentage = round((mixed / total_with_targets) * 100)
        
        print(f"\nPercentages:")
        print(f"Location Only: {location_percentage}%")
        print(f"Username Only: {username_percentage}%")
        print(f"Mixed: {mixed_percentage}%")
        print(f"Total: {location_percentage + username_percentage + mixed_percentage}%")
    
    return {
        'location_only': location_only,
        'username_only': username_only,
        'mixed': mixed,
        'no_targets': no_targets,
        'total_with_targets': total_with_targets,
        'total_campaigns': campaigns.count()
    }

def fix_campaign_types():
    """Fix campaign types based on actual targets"""
    # Get all campaigns
    campaigns = Campaign.objects.all()
    print(f"Fixing campaign types for {campaigns.count()} campaigns...")

    # Track changes
    changes = 0

    # Update campaign types based on actual targets
    for campaign in campaigns:
        has_location_targets = campaign.location_targets.exists()
        has_username_targets = campaign.username_targets.exists()
        
        # Determine correct target type
        correct_type = None
        if has_location_targets and has_username_targets:
            correct_type = 'mixed'
        elif has_location_targets:
            correct_type = 'location'
        elif has_username_targets:
            correct_type = 'username'
        
        # Update if needed and campaign has targets
        if correct_type and campaign.target_type != correct_type:
            print(f"Updating campaign '{campaign.name}' from '{campaign.target_type}' to '{correct_type}'")
            campaign.target_type = correct_type
            campaign.save(update_fields=['target_type'])
            changes += 1
    
    print(f"Updated {changes} campaigns")
    return changes

if __name__ == "__main__":
    print("Analyzing current campaign types...")
    before = analyze_campaign_types()
    
    print("\n" + "="*50)
    print("Fixing campaign types...")
    changes = fix_campaign_types()
    
    if changes > 0:
        print("\n" + "="*50)
        print("After fixes:")
        after = analyze_campaign_types()
    else:
        print("No changes needed.")
