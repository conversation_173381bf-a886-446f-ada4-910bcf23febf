# Data Flow Architecture Analysis: Instagram Data Collection System

## 1. Airflow DAGs

### Inputs
- **From Django Backend**:
  - Campaign ID (UUID)
  - Target type ('location', 'username', or 'mixed')
  - Audience type ('profile', 'followers', 'following', or 'both')
  - Username targets (list of usernames)
  - Location targets (list of location objects with location_id, city, country)
  - Configuration parameters (max_accounts, min_followers, max_followers)
  - Trigger events (via AirflowService.trigger_dag method)

- **From System Configuration**:
  - DAG scheduling parameters
  - Retry policies
  - Timeout configurations
  - Environment variables

### Orchestration & Processing Responsibilities
- **Workflow Validation**:
  - Validates campaign configuration parameters
  - Ensures required fields are present
  - Verifies target data integrity

- **Task Sequencing**:
  - Coordinates execution order of data collection tasks
  - Manages dependencies between tasks
  - Handles conditional execution paths based on campaign type

- **Resource Management**:
  - Prevents concurrent execution of conflicting workflows
  - Implements random delays to prevent API rate limiting
  - Monitors system resource utilization

### Outputs
- **To PyFlow**:
  - PyFlow workflow file path (e.g., FEED.pygraph or MANUAL_DATA_MINING.pygraph)
  - Command-line parameters (campaign_id, usernames, location_ids, batch_size)
  - Execution environment configuration

- **To Django**:
  - Campaign status updates ('pending', 'running', 'completed', 'failed')
  - Execution metadata (airflow_run_id, start/end timestamps)
  - Error messages and execution logs

- **To Redis**:
  - Workflow registration in the resource management queue
  - Execution status updates

### Monitoring & Error Handling
- **Progress Tracking**:
  - Monitors task execution status
  - Records task duration and performance metrics
  - Updates campaign progress in database

- **Error Management**:
  - Implements retry logic for failed tasks
  - Captures and logs error details
  - Updates campaign status on failure
  - Provides error context for debugging

- **Notification System**:
  - Logs critical events and errors
  - Can be configured to send alerts on failure

## 2. PyFlow Workflows

### Inputs
- **From Airflow DAGs**:
  - Campaign ID (for database association)
  - Username targets (direct parameter or CSV file path)
  - Location targets (location IDs)
  - Batch size (number of accounts to process)
  - Audience type configuration

- **From File System**:
  - CSV files with username lists (in ENGINE/INSTA/MEMORY/)
  - Workflow configuration files (.pygraph files)
  - Cached data and temporary files

- **From Instagram API**:
  - Account profile data
  - Follower/following lists
  - Post metadata
  - Location data

### Data Collection & Processing
- **Authentication & Session Management**:
  - Handles Instagram API authentication
  - Manages session cookies and tokens
  - Implements request throttling and rate limiting

- **Data Extraction**:
  - Scrapes profile information (bio, follower counts, etc.)
  - Collects follower/following lists based on audience type
  - Extracts engagement metrics and account statistics
  - Processes location-based data

- **Data Transformation**:
  - Normalizes and cleans collected data
  - Structures data according to defined schema
  - Enriches data with derived metrics
  - Formats data for storage

### Outputs
- **To Redis Queues**:
  - Discovered usernames (to 'discovered_usernames' queue)
  - Processed account data (to 'analyzed_accounts' queue)
  - Progress updates (to 'workflow_progress:<campaign_id>' hash)
  - Completion signals (to 'workflow_status' queue)

- **To Database (via Django models)**:
  - Account profile data
  - Relationship data (followers/following)
  - Engagement metrics
  - Raw JSON data for further processing

- **To File System**:
  - Log files with execution details
  - Cached data for subsequent runs
  - Debug information for troubleshooting

### Progress & Status Reporting
- **Via LogProgressNode**:
  - Reports current item being processed
  - Updates total items processed count
  - Calculates and reports percentage completion
  - Provides status messages for UI display

- **Via QueueMessageNode**:
  - Sends structured progress data to Redis
  - Updates workflow status (running, completed, failed)
  - Reports error conditions and exceptions
  - Signals workflow completion

## 3. Redis Queue System

### Middleware Function
- **Inter-Component Communication**:
  - Decouples producers from consumers
  - Enables asynchronous processing
  - Provides buffering during load spikes
  - Facilitates horizontal scaling

- **State Management**:
  - Maintains workflow execution state
  - Tracks resource allocation
  - Stores temporary processing results
  - Manages distributed locks

### Implemented Queues
- **discovered_usernames**:
  - Type: List
  - Content: Usernames discovered during data collection
  - Producers: PyFlow workflows
  - Consumers: Django background tasks, Airflow DAGs

- **analyzed_accounts**:
  - Type: List
  - Content: JSON-formatted account data with analysis results
  - Producers: PyFlow analysis workflows
  - Consumers: Django models, Airflow tagging DAGs

- **workflow_progress:<campaign_id>**:
  - Type: Hash
  - Content: Progress metrics (total_items, processed_items, percentage, status_message)
  - Producers: PyFlow LogProgressNode
  - Consumers: Django progress tracking service

- **resource_manager**:
  - Type: Sorted Set
  - Content: Registered workflows with priorities
  - Producers: Airflow DAGs, Django resource service
  - Consumers: Resource manager service

### Publishing & Consumption Patterns
- **Publishing Mechanisms**:
  - PyFlow uses QueueMessageNode to publish data
  - Airflow DAGs use Python Redis client
  - Django services use Django-Redis integration

- **Consumption Patterns**:
  - Polling (periodic checks for new data)
  - Batch processing (retrieving multiple items at once)
  - Pub/Sub for real-time notifications

### Role in Progress Tracking
- **Real-time Updates**:
  - Stores current progress percentage
  - Maintains status messages
  - Records timestamps for performance analysis

- **System State**:
  - Tracks active workflows
  - Manages workflow priorities
  - Handles workflow dependencies
  - Facilitates graceful shutdown and recovery

## 4. Django Backend & Frontend

### Backend Process Initiation & Monitoring
- **Campaign Management**:
  - Creates campaign records with configuration
  - Adds targets (usernames, locations)
  - Triggers Airflow DAGs via AirflowService
  - Updates campaign status based on DAG execution

- **Progress Monitoring**:
  - Polls Redis for workflow progress updates
  - Updates CampaignResult model with statistics
  - Tracks workflow execution status
  - Records performance metrics

- **Result Processing**:
  - Processes data from Redis queues
  - Populates database models with collected data
  - Performs tag analysis on collected accounts
  - Generates whitelist based on analysis results

### Database Models
- **Campaign Models**:
  - Campaign (core configuration)
  - UsernameTarget (username-based targets)
  - LocationTarget (location-based targets)
  - CampaignResult (aggregated statistics)

- **Account Data Models**:
  - TagAnalysisResult (stores account data and analysis)
  - AccountWhitelist (whitelisted accounts)
  - CampaignTag (tag assignments)

- **Workflow Models**:
  - WorkflowExecution (execution metadata)
  - WorkflowProgressUpdate (progress tracking)
  - WorkflowError (error logging)

### Frontend Visualization
- **Dashboard**:
  - Campaign status overview
  - Progress indicators and charts
  - Campaign type distribution
  - Recent campaign activity

- **Campaign Detail Views**:
  - Real-time progress updates
  - Target information display
  - Result statistics visualization
  - Action buttons (pause, resume, stop)

- **Account Data Display**:
  - Account list with filtering
  - Detailed account information
  - Tag visualization
  - Whitelist management

### User Interactions & Triggers
- **Campaign Creation**:
  - Form submission creates Campaign record
  - Target addition populates target models
  - Launch action triggers Airflow DAG

- **Campaign Management**:
  - Pause/Resume/Stop actions update campaign status
  - Analysis button triggers tagging workflow
  - Export action generates data exports

- **Account Management**:
  - Manual tagging updates TagAnalysisResult
  - Whitelist toggling updates is_whitelisted flag
  - Filtering actions update UI display

## Integration Analysis

### Responsibility Boundaries
- **Django Backend**:
  - User interface and interaction
  - Data persistence and retrieval
  - Campaign configuration management
  - Result visualization

- **Airflow DAGs**:
  - Workflow orchestration
  - Task sequencing and dependency management
  - Error handling and retries
  - Resource allocation

- **PyFlow Workflows**:
  - Instagram API interaction
  - Data collection and extraction
  - Initial data processing
  - Progress reporting

- **Redis Queue System**:
  - Inter-component communication
  - Temporary data storage
  - Progress tracking
  - System state management

### Communication Interfaces & Data Formats
- **Django → Airflow**:
  - REST API calls to Airflow API
  - JSON-formatted configuration data
  - Campaign ID as correlation key

- **Airflow → PyFlow**:
  - Command-line execution
  - Parameter passing
  - File system for configuration

- **PyFlow → Redis**:
  - QueueMessageNode for data publishing
  - JSON-formatted account data
  - Structured progress updates

- **Redis → Django**:
  - Django-Redis client for data retrieval
  - Polling for progress updates
  - Batch processing for account data

### End-to-End Data Flow
1. **User Request**:
   - User creates campaign with username targets in Django UI
   - User clicks "Launch Campaign" button

2. **Campaign Initialization**:
   - Django creates Campaign and UsernameTarget records
   - Django triggers Airflow DAG with campaign configuration

3. **Workflow Orchestration**:
   - Airflow validates configuration
   - Airflow determines appropriate PyFlow workflow
   - Airflow executes PyFlow with parameters

4. **Data Collection**:
   - PyFlow reads username targets from parameters or CSV
   - PyFlow collects data from Instagram API
   - PyFlow processes and structures the data
   - PyFlow reports progress to Redis via LogProgressNode

5. **Data Storage**:
   - PyFlow sends account data to Redis via QueueMessageNode
   - Django processes data from Redis queues
   - Django populates database models with account data

6. **Result Processing**:
   - Django performs tag analysis on collected accounts
   - Django updates CampaignResult with statistics
   - Django generates whitelist based on tag matches

7. **User Visualization**:
   - Django UI displays real-time progress updates
   - Django UI shows collected account data
   - Django UI visualizes analysis results and statistics

### Failure Handling & Data Consistency
- **Component Failure Recovery**:
   - Airflow implements task retries for transient failures
   - PyFlow captures and logs exceptions
   - Redis persists queues to disk for recovery
   - Django transactions ensure database consistency

- **Data Validation**:
   - Airflow validates configuration before execution
   - PyFlow validates collected data against schema
   - Django models enforce data integrity constraints
   - Redis ensures atomic operations for queue management

- **Error Propagation**:
   - PyFlow reports errors to Airflow via exit codes
   - Airflow updates campaign status on failure
   - Django displays error messages in UI
   - Logs capture detailed error context

- **Consistency Mechanisms**:
   - Database transactions for atomic updates
   - Redis atomic operations for queue management
   - Idempotent processing for retry safety
   - Correlation IDs (campaign_id) for end-to-end tracking
