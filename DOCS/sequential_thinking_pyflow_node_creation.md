# Sequential Thinking Example: Creating a Custom PyFlow Node

This document demonstrates how to use the Sequential Thinking MCP Server to design and implement a custom PyFlow node.

## Problem Statement

We need to create a custom PyFlow node that will analyze Instagram posts and determine if they meet certain criteria for engagement. The node should take a post as input, analyze various metrics (likes, comments, etc.), and output a boolean indicating if the post meets our engagement criteria.

## Sequential Thinking Process

### Initial Planning Phase

**Thought 1:**
First, I need to understand the structure of a PyFlow node. Looking at the codebase, I can see that nodes inherit from `NodeBase` in `Core/NodeBase.py`. Each node has input and output pins that can be created using `createInputPin` and `createOutputPin` methods. I'll need to define these pins based on the data I expect to receive and produce.

**Thought 2:**
For my Instagram post analyzer node, I'll need the following input pins:
- A post object (containing all post data)
- Minimum likes threshold (integer)
- Minimum comments threshold (integer)
- Consider hashtags (boolean)
- Minimum hashtag count (integer)

And the following output pins:
- Meets criteria (boolean)
- Engagement score (float)
- Exec output for when criteria are met
- Exec output for when criteria are not met

**Thought 3:**
I need to implement the `compute` method which will be called when the node is executed. This method will analyze the post data against the thresholds and set the output pins accordingly. I should also consider error handling for cases where the post data is invalid or missing required fields.

**Thought 4:**
I should also consider the UI representation of this node. In PyFlow, the node appearance can be customized by setting properties like `headerColor`. I'll want to make this node visually distinct so it's easy to identify in a workflow.

### Implementation Phase

**Thought 5:**
Now I'll start implementing the node class. I'll create a new file in the appropriate package directory, likely `Packages/Automator/Nodes/instagram_post_analyzer.py`. Here's the basic structure:

```python
from PyFlow.Core.NodeBase import NodeBase
from PyFlow.Core.Common import *
from PyFlow.Core.Enums import *

class InstagramPostAnalyzer(NodeBase):
    def __init__(self, name):
        super(InstagramPostAnalyzer, self).__init__(name)
        
        # Create input pins
        self.inExec = self.createInputPin(DEFAULT_IN_EXEC_NAME, 'ExecPin', None, self.compute)
        self.post = self.createInputPin('post', 'AnyPin', structure=StructureType.Dict)
        self.minLikes = self.createInputPin('minLikes', 'IntPin', 100)
        self.minComments = self.createInputPin('minComments', 'IntPin', 10)
        self.considerHashtags = self.createInputPin('considerHashtags', 'BoolPin', False)
        self.minHashtags = self.createInputPin('minHashtags', 'IntPin', 5)
        
        # Create output pins
        self.outExecTrue = self.createOutputPin('meetsConditions', 'ExecPin')
        self.outExecFalse = self.createOutputPin('doesNotMeetConditions', 'ExecPin')
        self.meetsCriteria = self.createOutputPin('meetsCriteria', 'BoolPin')
        self.engagementScore = self.createOutputPin('engagementScore', 'FloatPin')
        
        # Set node appearance
        self.headerColor = (0.2, 0.6, 0.8, 1.0)
```

**Thought 6:**
Now I need to implement the `compute` method that will analyze the post data:

```python
def compute(self, *args, **kwargs):
    # Get input values
    post_data = self.post.getData()
    min_likes = self.minLikes.getData()
    min_comments = self.minComments.getData()
    consider_hashtags = self.considerHashtags.getData()
    min_hashtags = self.minHashtags.getData()
    
    # Default values in case of missing data
    likes = 0
    comments = 0
    hashtags = []
    
    # Extract data from post
    if isinstance(post_data, dict):
        likes = post_data.get('likes', 0)
        comments = post_data.get('comments', 0)
        hashtags = post_data.get('hashtags', [])
    
    # Calculate if criteria are met
    likes_met = likes >= min_likes
    comments_met = comments >= min_comments
    hashtags_met = True
    if consider_hashtags:
        hashtags_met = len(hashtags) >= min_hashtags
    
    meets_criteria = likes_met and comments_met and hashtags_met
    
    # Calculate engagement score (simple formula)
    engagement_score = (likes + (comments * 2)) / 100.0
    
    # Set output values
    self.meetsCriteria.setData(meets_criteria)
    self.engagementScore.setData(engagement_score)
    
    # Call the appropriate exec output
    if meets_criteria:
        self.outExecTrue.call()
    else:
        self.outExecFalse.call()
```

### Testing and Refinement

**Thought 7:**
I should test this node with sample data to ensure it works correctly. I'll need to create a test workflow that provides sample post data and verifies the output. I should test various scenarios:
- Post that meets all criteria
- Post that fails the likes threshold
- Post that fails the comments threshold
- Post with hashtags when considering hashtags
- Invalid post data

**Thought 8:**
I realize I should add error handling to the `compute` method to handle cases where the post data is not a dictionary or is missing expected fields:

```python
def compute(self, *args, **kwargs):
    # Get input values
    post_data = self.post.getData()
    
    # Check if post_data is valid
    if not isinstance(post_data, dict):
        print(f"Error: Post data must be a dictionary, got {type(post_data)}")
        self.meetsCriteria.setData(False)
        self.engagementScore.setData(0.0)
        self.outExecFalse.call()
        return
    
    # Rest of the compute method...
```

**Thought 9:**
I should also consider adding documentation to the node to help users understand its purpose and how to use it:

```python
@staticmethod
def description():
    return "Analyzes Instagram posts to determine if they meet engagement criteria"

@staticmethod
def category():
    return "Instagram"

@staticmethod
def keywords():
    return ["instagram", "post", "analysis", "engagement", "social media"]
```

### Integration

**Thought 10:**
Finally, I need to register this node in the package's `__init__.py` file so it's available in the PyFlow editor:

```python
# In Packages/Automator/__init__.py
from Packages.Automator.Nodes.instagram_post_analyzer import InstagramPostAnalyzer

_NODES = {
    # ... existing nodes ...
    InstagramPostAnalyzer.__name__: InstagramPostAnalyzer,
}
```

## Complete Implementation

Combining all the thoughts above, here's the complete implementation of the Instagram Post Analyzer node:

```python
from PyFlow.Core.NodeBase import NodeBase
from PyFlow.Core.Common import *
from PyFlow.Core.Enums import *

class InstagramPostAnalyzer(NodeBase):
    def __init__(self, name):
        super(InstagramPostAnalyzer, self).__init__(name)
        
        # Create input pins
        self.inExec = self.createInputPin(DEFAULT_IN_EXEC_NAME, 'ExecPin', None, self.compute)
        self.post = self.createInputPin('post', 'AnyPin', structure=StructureType.Dict)
        self.minLikes = self.createInputPin('minLikes', 'IntPin', 100)
        self.minComments = self.createInputPin('minComments', 'IntPin', 10)
        self.considerHashtags = self.createInputPin('considerHashtags', 'BoolPin', False)
        self.minHashtags = self.createInputPin('minHashtags', 'IntPin', 5)
        
        # Create output pins
        self.outExecTrue = self.createOutputPin('meetsConditions', 'ExecPin')
        self.outExecFalse = self.createOutputPin('doesNotMeetConditions', 'ExecPin')
        self.meetsCriteria = self.createOutputPin('meetsCriteria', 'BoolPin')
        self.engagementScore = self.createOutputPin('engagementScore', 'FloatPin')
        
        # Set node appearance
        self.headerColor = (0.2, 0.6, 0.8, 1.0)
    
    @staticmethod
    def description():
        return "Analyzes Instagram posts to determine if they meet engagement criteria"

    @staticmethod
    def category():
        return "Instagram"

    @staticmethod
    def keywords():
        return ["instagram", "post", "analysis", "engagement", "social media"]
    
    def compute(self, *args, **kwargs):
        # Get input values
        post_data = self.post.getData()
        min_likes = self.minLikes.getData()
        min_comments = self.minComments.getData()
        consider_hashtags = self.considerHashtags.getData()
        min_hashtags = self.minHashtags.getData()
        
        # Check if post_data is valid
        if not isinstance(post_data, dict):
            print(f"Error: Post data must be a dictionary, got {type(post_data)}")
            self.meetsCriteria.setData(False)
            self.engagementScore.setData(0.0)
            self.outExecFalse.call()
            return
        
        # Extract data from post
        likes = post_data.get('likes', 0)
        comments = post_data.get('comments', 0)
        hashtags = post_data.get('hashtags', [])
        
        # Calculate if criteria are met
        likes_met = likes >= min_likes
        comments_met = comments >= min_comments
        hashtags_met = True
        if consider_hashtags:
            hashtags_met = len(hashtags) >= min_hashtags
        
        meets_criteria = likes_met and comments_met and hashtags_met
        
        # Calculate engagement score (simple formula)
        engagement_score = (likes + (comments * 2)) / 100.0
        
        # Set output values
        self.meetsCriteria.setData(meets_criteria)
        self.engagementScore.setData(engagement_score)
        
        # Call the appropriate exec output
        if meets_criteria:
            self.outExecTrue.call()
        else:
            self.outExecFalse.call()
```

This example demonstrates how the Sequential Thinking MCP Server can help break down the process of creating a custom PyFlow node into manageable steps, allowing for a systematic approach to development.
