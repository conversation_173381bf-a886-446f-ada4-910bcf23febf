import logging
import json
import redis
from PyFlow.Core.NodeBase import NodeBase

class QueueMessageNode(NodeBase):
    """Simple node that queues discovered usernames for the DAG to process"""
    
    def __init__(self, name):
        super(QueueMessageNode, self).__init__(name)
        
        # Input pins - just the essential data
        self.createInputPin('username', 'StringPin')  # The discovered username
        self.createInputPin('queue_name', 'StringPin', defaultValue='discovered_usernames')
        
        # Execution pins
        self.execution = self.createInputPin('Execute', 'ExecPin')
        self.completed = self.createOutputPin('Completed', 'ExecPin')
        
        # Connect execution pin
        self.execution.onExecute.connect(self.compute)
    
    @staticmethod
    def category():
        return 'Messaging'
    
    @staticmethod
    def keywords():
        return ['queue', 'username', 'discovery']
    
    @staticmethod
    def description():
        return "Queues a discovered username for processing by the DAG"
    
    def compute(self, *args, **kwargs):
        username = self.getPinByName('username').getData()
        queue_name = self.getPinByName('queue_name').getData()
        
        try:
            # Connect to Redis (using a fixed connection for simplicity)
            r = redis.Redis(host='localhost', port=6379, db=0)
            
            # Just queue the username - DAG will handle the rest
            r.lpush(queue_name, username)
            
            # Log for debugging
            logging.info(f"Queued username: {username}")
            
            self.completed.call()
        except Exception as e:
            logging.error(f"Error queuing username: {str(e)}")
            self.completed.call()  # Still continue the workflow