"""
ResourceManagerNode for integrating PyFlow workflows with the resource management service.

This node provides functionality for:
1. Registering API usage with the resource manager
2. Checking rate limits before making API calls
3. Handling resource constraints and pausing/resuming workflows
"""
import logging
import json
import redis
import time
import requests
from PyFlow.Core.NodeBase import NodeBase

class ResourceManagerNode(NodeBase):
    """Node for integrating with the resource management service"""
    
    def __init__(self, name):
        super(ResourceManagerNode, self).__init__(name)
        
        # Input pins
        self.createInputPin('workflow_id', 'StringPin')  # Workflow execution ID
        self.createInputPin('api_type', 'StringPin')     # Type of API call (follow, like, comment, dm)
        self.createInputPin('count', 'IntPin', defaultValue=1)  # Number of API calls
        self.createInputPin('wait_if_limited', 'BoolPin', defaultValue=True)  # Whether to wait if rate limited
        self.createInputPin('max_wait_time', 'IntPin', defaultValue=300)  # Maximum wait time in seconds
        
        # Output pins
        self.createOutputPin('can_proceed', 'BoolPin')   # Whether the workflow can proceed
        self.createOutputPin('wait_time', 'IntPin')      # Suggested wait time if rate limited
        
        # Execution pins
        self.execution = self.createInputPin('Execute', 'ExecPin')
        self.success = self.createOutputPin('Success', 'ExecPin')
        self.failure = self.createOutputPin('Failure', 'ExecPin')
        
        # Connect execution pin
        self.execution.onExecute.connect(self.compute)
        
        # Redis client for communication
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
    
    @staticmethod
    def category():
        return 'Resource Management'
    
    @staticmethod
    def keywords():
        return ['resource', 'rate limit', 'api']
    
    @staticmethod
    def description():
        return "Integrates with the resource management service to track API usage and check rate limits"
    
    def log_pin_values(self, phase):
        logging.debug(f"# Node {self.name} - {phase} phase")
        logging.debug("~~Input:")
        logging.debug(f"~~~~workflow_id: {self.getPinByName('workflow_id').getData()}")
        logging.debug(f"~~~~api_type: {self.getPinByName('api_type').getData()}")
        logging.debug(f"~~~~count: {self.getPinByName('count').getData()}")
        logging.debug(f"~~~~wait_if_limited: {self.getPinByName('wait_if_limited').getData()}")
        logging.debug(f"~~~~max_wait_time: {self.getPinByName('max_wait_time').getData()}")
        logging.debug("~~Output:")
        logging.debug(f"~~~~can_proceed: {self.getPinByName('can_proceed').getData()}")
        logging.debug(f"~~~~wait_time: {self.getPinByName('wait_time').getData()}")
    
    def compute(self, *args, **kwargs):
        logging.debug(f"==> Node {self.name} - Compute start")
        self.log_pin_values("Start")
        
        # Get input values
        workflow_id = self.getPinByName('workflow_id').getData()
        api_type = self.getPinByName('api_type').getData()
        count = self.getPinByName('count').getData()
        wait_if_limited = self.getPinByName('wait_if_limited').getData()
        max_wait_time = self.getPinByName('max_wait_time').getData()
        
        # Check if we can proceed
        can_proceed, wait_time = self._check_rate_limit(workflow_id, api_type, count)
        
        # Set output pins
        self.getPinByName('can_proceed').setData(can_proceed)
        self.getPinByName('wait_time').setData(wait_time)
        
        if can_proceed:
            # Register API usage
            self._register_api_usage(workflow_id, api_type, count)
            self.success.call()
        elif wait_if_limited and wait_time > 0 and wait_time <= max_wait_time:
            # Wait and retry
            logging.info(f"Rate limited for {api_type}. Waiting {wait_time} seconds before retrying.")
            time.sleep(wait_time)
            
            # Check again after waiting
            can_proceed, _ = self._check_rate_limit(workflow_id, api_type, count)
            if can_proceed:
                # Register API usage
                self._register_api_usage(workflow_id, api_type, count)
                self.success.call()
            else:
                logging.warning(f"Still rate limited for {api_type} after waiting {wait_time} seconds.")
                self.failure.call()
        else:
            # Cannot proceed
            logging.warning(f"Rate limited for {api_type}. Cannot proceed.")
            self.failure.call()
        
        self.log_pin_values("End")
        logging.debug(f"<== Node {self.name} - Compute end\n")
    
    def _check_rate_limit(self, workflow_id, api_type, count):
        """
        Check if the API call would exceed rate limits.
        
        Args:
            workflow_id (str): Workflow execution ID
            api_type (str): Type of API call
            count (int): Number of API calls
            
        Returns:
            tuple: (can_proceed, wait_time)
        """
        try:
            # Call resource manager service API
            response = requests.get(
                'http://localhost:8000/api/resource-manager/check-rate-limit/',
                params={
                    'workflow_id': workflow_id,
                    'api_type': api_type,
                    'count': count
                },
                timeout=5
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get('can_proceed', False), data.get('wait_time', 0)
            else:
                # Fallback to Redis check
                return self._check_rate_limit_redis(api_type, count)
        except Exception as e:
            logging.error(f"Error checking rate limit: {str(e)}")
            # Fallback to Redis check
            return self._check_rate_limit_redis(api_type, count)
    
    def _check_rate_limit_redis(self, api_type, count):
        """Fallback method to check rate limits using Redis directly"""
        try:
            # Get current usage
            key = f"api_rate:{api_type}"
            current = self.redis_client.get(key)
            current = int(current) if current else 0
            
            # Get rate limit settings
            rate_limits = {
                'default': {'limit': 200, 'window': 3600},
                'follow': {'limit': 60, 'window': 3600},
                'like': {'limit': 120, 'window': 3600},
                'comment': {'limit': 20, 'window': 3600},
                'dm': {'limit': 20, 'window': 3600}
            }
            
            rate_limit = rate_limits.get(api_type, rate_limits['default'])
            limit = rate_limit['limit']
            window = rate_limit['window']
            
            # Check if limit would be exceeded
            if current + count > limit:
                # Calculate wait time
                ttl = self.redis_client.ttl(key)
                if ttl > 0:
                    return False, ttl
                else:
                    return False, window
            else:
                return True, 0
        except Exception as e:
            logging.error(f"Error in Redis rate limit check: {str(e)}")
            # Be conservative on error
            return False, 3600
    
    def _register_api_usage(self, workflow_id, api_type, count):
        """Register API usage with the resource manager"""
        try:
            # Call resource manager service API
            response = requests.post(
                'http://localhost:8000/api/resource-manager/track-api-usage/',
                json={
                    'workflow_id': workflow_id,
                    'api_type': api_type,
                    'count': count
                },
                timeout=5
            )
            
            if response.status_code != 200:
                # Fallback to Redis update
                self._register_api_usage_redis(api_type, count)
        except Exception as e:
            logging.error(f"Error registering API usage: {str(e)}")
            # Fallback to Redis update
            self._register_api_usage_redis(api_type, count)
    
    def _register_api_usage_redis(self, api_type, count):
        """Fallback method to register API usage using Redis directly"""
        try:
            # Update Redis rate limit counter
            key = f"api_rate:{api_type}"
            
            # Get rate limit settings
            rate_limits = {
                'default': {'limit': 200, 'window': 3600},
                'follow': {'limit': 60, 'window': 3600},
                'like': {'limit': 120, 'window': 3600},
                'comment': {'limit': 20, 'window': 3600},
                'dm': {'limit': 20, 'window': 3600}
            }
            
            rate_limit = rate_limits.get(api_type, rate_limits['default'])
            window = rate_limit['window']
            
            # Increment counter
            current = self.redis_client.incr(key, count)
            
            # Set expiry if new key
            if current == count:
                self.redis_client.expire(key, window)
        except Exception as e:
            logging.error(f"Error in Redis API usage update: {str(e)}")
