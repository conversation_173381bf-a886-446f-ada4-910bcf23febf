import logging
from PyFlow.Core.NodeBase import NodeBase

class LogProgressNode(NodeBase):
    """Simple node that logs progress for the DAG to parse"""
    
    def __init__(self, name):
        super(LogProgressNode, self).__init__(name)
        
        # Input pins - just current progress
        self.createInputPin('current', 'IntPin')  # Current item number
        self.createInputPin('total', 'IntPin')    # Total items
        
        # Execution pins
        self.execution = self.createInputPin('Execute', 'ExecPin')
        self.completed = self.createOutputPin('Completed', 'ExecPin')
        
        # Connect execution pin
        self.execution.onExecute.connect(self.compute)
    
    @staticmethod
    def category():
        return 'Messaging'
    
    @staticmethod
    def keywords():
        return ['progress', 'log']
    
    @staticmethod
    def description():
        return "Logs progress for the DAG to parse"
    
    def compute(self, *args, **kwargs):
        current = self.getPinByName('current').getData()
        total = self.getPinByName('total').getData()
        
        # Simple progress format that's easy to parse
        logging.info(f"PROGRESS:{current}/{total}")
        
        self.completed.call()