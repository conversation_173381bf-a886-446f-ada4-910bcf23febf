#!/usr/bin/env python3
"""
Quick test script for the comprehensive whitelist testing system.

This script provides an easy way to test the whitelist functionality
without needing to set up Airflow or complex configurations.

Usage:
    python test_whitelist_system.py
    python test_whitelist_system.py --campaign-name "Tech Campaign"
    python test_whitelist_system.py --num-accounts 200
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# Add Django directory to path
django_dir = Path(__file__).parent
if str(django_dir) not in sys.path:
    sys.path.append(str(django_dir))

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

def setup_django():
    """Setup Django environment"""
    try:
        import django
        django.setup()
        return True
    except Exception as e:
        print(f"Error setting up Django: {e}")
        return False

def get_available_campaigns():
    """Get list of available campaigns"""
    from campaigns.models import Campaign
    campaigns = Campaign.objects.all()
    return [(str(c.id), c.name, c.status) for c in campaigns]

def run_whitelist_simulation(campaign_id, num_accounts=100, clear_existing=True):
    """Run the whitelist simulation for a campaign"""
    print(f"🧪 Running Whitelist Testing Simulation")
    print(f"=" * 50)

    # Build command
    cmd = [
        'python', 'manage.py', 'simulate_whitelist_testing',
        campaign_id,
        '--num-accounts', str(num_accounts)
    ]

    if clear_existing:
        cmd.append('--clear-existing')

    print(f"Command: {' '.join(cmd)}")
    print()

    # Run the simulation
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, cwd=django_dir)

        if result.returncode == 0:
            print("✅ Simulation completed successfully!")
            print()
            print("📊 Output:")
            print(result.stdout)

            # Get campaign details
            from campaigns.models import Campaign
            campaign = Campaign.objects.get(id=campaign_id)

            print(f"🎯 Next Steps:")
            print(f"   1. View the Whitelist Analytics Dashboard:")
            print(f"      http://127.0.0.1:8001/campaigns/{campaign_id}/simulation/")
            print(f"   2. Check the campaign detail page:")
            print(f"      http://127.0.0.1:8001/campaigns/{campaign_id}/")
            print(f"   3. Review the generated data in the admin interface")

            return True
        else:
            print("❌ Simulation failed!")
            print()
            print("Error output:")
            print(result.stderr)
            print()
            print("Standard output:")
            print(result.stdout)
            return False

    except Exception as e:
        print(f"❌ Error running simulation: {str(e)}")
        return False

def main():
    # Setup Django
    if not setup_django():
        print("❌ Failed to setup Django environment")
        return

    parser = argparse.ArgumentParser(
        description="Test the comprehensive whitelist system",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s                                    # Interactive mode
  %(prog)s --campaign-id 7a946308-771a-4ccb-b359-8b08ce144943
  %(prog)s --campaign-name "Fitness Campaign"
  %(prog)s --num-accounts 200 --no-clear
        """
    )

    parser.add_argument(
        '--campaign-id',
        type=str,
        help='Campaign ID to test (UUID format)'
    )

    parser.add_argument(
        '--campaign-name',
        type=str,
        help='Campaign name to search for'
    )

    parser.add_argument(
        '--num-accounts',
        type=int,
        default=100,
        help='Number of accounts to simulate (default: 100)'
    )

    parser.add_argument(
        '--no-clear',
        action='store_true',
        help='Do not clear existing data'
    )

    parser.add_argument(
        '--list-campaigns',
        action='store_true',
        help='List available campaigns and exit'
    )

    args = parser.parse_args()

    # Get available campaigns
    campaigns = get_available_campaigns()

    if args.list_campaigns:
        print("📋 Available Campaigns:")
        print("=" * 50)
        for campaign_id, name, status in campaigns:
            print(f"ID: {campaign_id}")
            print(f"Name: {name}")
            print(f"Status: {status}")
            print("-" * 30)
        return

    if not campaigns:
        print("❌ No campaigns found. Please create a campaign first.")
        return

    # Determine which campaign to use
    campaign_id = None
    campaign_name = None

    if args.campaign_id:
        campaign_id = args.campaign_id
        # Verify campaign exists
        try:
            from campaigns.models import Campaign
            campaign = Campaign.objects.get(id=campaign_id)
            campaign_name = campaign.name
        except Campaign.DoesNotExist:
            print(f"❌ Campaign with ID {campaign_id} does not exist")
            return
    elif args.campaign_name:
        # Search by name
        matching_campaigns = [c for c in campaigns if args.campaign_name.lower() in c[1].lower()]
        if not matching_campaigns:
            print(f"❌ No campaigns found matching '{args.campaign_name}'")
            return
        elif len(matching_campaigns) > 1:
            print(f"❌ Multiple campaigns found matching '{args.campaign_name}':")
            for cid, name, status in matching_campaigns:
                print(f"   - {name} (ID: {cid})")
            return
        else:
            campaign_id, campaign_name, _ = matching_campaigns[0]
    else:
        # Interactive mode
        print("📋 Available Campaigns:")
        print("=" * 30)
        for i, (cid, name, status) in enumerate(campaigns, 1):
            print(f"{i}. {name} ({status})")

        print()
        try:
            choice = input("Select a campaign (number): ").strip()
            choice_idx = int(choice) - 1
            if 0 <= choice_idx < len(campaigns):
                campaign_id, campaign_name, _ = campaigns[choice_idx]
            else:
                print("❌ Invalid selection")
                return
        except (ValueError, KeyboardInterrupt):
            print("\n❌ Invalid selection or cancelled")
            return

    print(f"🎯 Selected Campaign: {campaign_name}")
    print(f"📊 Campaign ID: {campaign_id}")
    print(f"🔢 Number of accounts: {args.num_accounts}")
    print(f"🗑️ Clear existing data: {not args.no_clear}")
    print()

    # Confirm before proceeding
    try:
        confirm = input("Proceed with simulation? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ Simulation cancelled")
            return
    except KeyboardInterrupt:
        print("\n❌ Simulation cancelled")
        return

    # Run the simulation
    success = run_whitelist_simulation(
        campaign_id=campaign_id,
        num_accounts=args.num_accounts,
        clear_existing=not args.no_clear
    )

    if success:
        print()
        print("🎉 Whitelist testing system is working correctly!")
        print("   You can now test tag filtering, whitelist generation, and analytics.")
    else:
        print()
        print("💡 Troubleshooting Tips:")
        print("   1. Make sure Django server is running")
        print("   2. Check database connections")
        print("   3. Verify campaign has valid configuration")
        print("   4. Check Django logs for detailed errors")

if __name__ == "__main__":
    main()
