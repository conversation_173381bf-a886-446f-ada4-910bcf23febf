/* Button Fixes CSS */

/* Fix for button groups */
.btn-group {
  display: inline-flex;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  overflow: hidden;
}

.btn-group .btn {
  border-radius: 0;
  margin: 0;
  box-shadow: none;
}

.btn-group .btn:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.btn-group .btn:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

/* Fix for detail page button group */
.detail-action-buttons.btn-group {
  display: flex !important;
  flex-wrap: nowrap !important;
  box-shadow: none !important;
  gap: 5px !important;
  background: transparent !important;
  border: none !important;
}

.detail-action-buttons.btn-group .btn {
  border-radius: 6px !important;
  margin: 0 2px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Fix for small buttons */
.btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.875rem;
}

/* Fix for button colors */
.btn-primary {
  background-color: #3498db;
  border-color: #3498db;
  color: white;
  width: auto;
  min-width: auto;
  display: inline-block;
}

.btn-primary:hover {
  background-color: #2980b9;
  border-color: #2980b9;
  color: white;
}

/* Fix for search buttons */
.btn-search {
  width: auto !important;
  min-width: 80px !important;
  max-width: 100px !important;
}

/* Fix for location search button */
#location-search-btn {
  width: auto !important;
  min-width: 80px !important;
  max-width: 100px !important;
}

/* Fix for add buttons */
.btn-add {
  width: auto !important;
  min-width: 80px !important;
  max-width: 100px !important;
}

/* Fix for action buttons in tables */
.action-buttons .btn,
.btn-group .btn {
  width: auto !important;
  min-width: 40px !important;
  padding: 0.4rem 0.8rem !important;
}

.btn-secondary {
  background-color: #95a5a6;
  border-color: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background-color: #7f8c8d;
  border-color: #7f8c8d;
  color: white;
}

.btn-success {
  background-color: #2ecc71;
  border-color: #2ecc71;
  color: white;
}

.btn-success:hover {
  background-color: #27ae60;
  border-color: #27ae60;
  color: white;
}

.btn-danger {
  background-color: #e74c3c;
  border-color: #e74c3c;
  color: white;
}

.btn-danger:hover {
  background-color: #c0392b;
  border-color: #c0392b;
  color: white;
}

/* Fix for button icons */
.btn i {
  margin-right: 5px;
}

.btn-sm i {
  margin-right: 0;
}

/* Fix for button transitions */
.btn {
  transition: all 0.2s ease;
}

.btn:hover {
  transform: translateY(-1px);
}

/* Fix for button outlines */
.btn:focus, .btn:active {
  outline: none;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Campaign page specific button fixes */
.campaign-page .btn-primary {
  min-width: auto !important;
  width: auto !important;
}

/* Search button fixes */
.campaign-page .search-box .btn,
.campaign-page [type="search"] + .btn {
  width: auto !important;
  min-width: 100px !important;
  max-width: 120px !important;
}

/* Add button fixes */
.campaign-page .btn-add,
.campaign-page .btn-primary[href*="create"],
.campaign-page .btn-primary:has(i.fa-plus-circle) {
  width: auto !important;
  min-width: 80px !important;
  max-width: 100px !important;
}

/* Table action button fixes */
.campaign-page .table .btn,
.campaign-page .btn-group .btn {
  width: 40px !important;
  min-width: 40px !important;
  padding: 0.4rem !important;
}

/* Fix for username add button */
#add-username-btn {
  width: auto !important;
  min-width: auto !important;
  max-width: 80px !important;
  padding: 0.4rem 0.8rem !important;
}

/* Fix for detail page action buttons */
.detail-action-buttons {
  display: flex !important;
  flex-wrap: nowrap !important;
  box-shadow: none !important;
}

.detail-action-buttons .btn {
  width: auto !important;
  min-width: auto !important;
  max-width: none !important;
  padding: 0.5rem 1rem !important;
  margin: 0 !important;
  border-radius: 0.25rem !important;
}

/* Fix for edit buttons */
.edit-button,
.btn-secondary[href*="update"],
a[href*="update"].btn-secondary,
.btn-secondary[href*="edit"],
a[href*="edit"].btn-secondary {
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
}

/* Fix for back buttons */
.back-button,
.btn-outline-primary[href*="list"],
a[href*="list"].btn-outline-primary {
  width: 140px !important;
  min-width: 140px !important;
  max-width: 140px !important;
}

/* Fix for launch buttons */
.launch-campaign-btn,
.btn-success[href*="launch"],
a[href*="launch"].btn-success {
  width: 180px !important;
  min-width: 180px !important;
  max-width: 180px !important;
}
