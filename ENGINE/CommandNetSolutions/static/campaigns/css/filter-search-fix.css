/* Filter and Search Box Fixes */

/* Fix for filter dropdown being cut off */
.dropdown-menu {
  max-height: 400px !important;
  overflow-y: auto !important;
  z-index: 1030 !important;
  width: auto !important;
  min-width: 200px !important;
}

/* Ensure dropdown menu is visible */
.dropdown-menu.show {
  display: block !important;
}

/* Fix for search box clear button */
.search-box {
  position: relative !important;
}

.search-box input {
  padding-right: 45px !important;
}

.clear-search {
  position: absolute !important;
  right: 15px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 10 !important;
  width: 24px !important;
  height: 24px !important;
  padding: 0 !important;
  margin-right: 5px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background: transparent !important;
  border: none !important;
  color: #6c757d !important;
  cursor: pointer !important;
  transition: color 0.2s ease !important;
  line-height: 1 !important;
}

.clear-search:hover {
  color: #495057 !important;
}

.clear-search i {
  font-size: 12px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure filter dropdown has proper width */
#filterDropdown {
  min-width: 120px !important;
}

.dropdown-menu-end {
  right: 0 !important;
  left: auto !important;
}
