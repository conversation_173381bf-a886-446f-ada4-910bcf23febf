/* 
 * Resource Manager Dashboard Styles
 * Bootstrap 5.2.3 compatible
 */

/* General Layout */
.dashboard-container {
    padding: 1.5rem;
}

.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-title {
    font-size: 1.75rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.dashboard-subtitle {
    color: #6c757d;
    font-size: 1rem;
}

/* Card Styling */
.resource-card {
    border-radius: 0.75rem;
    box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
    overflow: hidden;
    border: none;
    height: 100%;
}

.resource-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.resource-card .card-header {
    background-color: #4e73df;
    color: white;
    font-weight: 600;
    padding: 1rem 1.25rem;
    border-bottom: none;
    display: flex;
    align-items: center;
}

.resource-card .card-header i {
    margin-right: 0.75rem;
    font-size: 1.1rem;
}

.resource-card .card-header h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.resource-card .card-body {
    padding: 1.5rem;
}

/* Resource Values */
.resource-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #4e73df;
}

.resource-value {
    font-size: 2.25rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: #2e59d9;
}

.resource-subtitle {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.available {
    background-color: #1cc88a;
}

.status-indicator.busy {
    background-color: #f6c23e;
}

.status-indicator.error {
    background-color: #e74a3b;
}

/* Progress Bars */
.progress {
    height: 0.75rem;
    margin-top: 0.75rem;
    margin-bottom: 0.5rem;
    border-radius: 0.375rem;
    background-color: #eaecf4;
}

.progress-bar {
    border-radius: 0.375rem;
}

/* Workflow Cards */
.workflow-card {
    border-left: 5px solid #4e73df;
    margin-bottom: 1rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    padding: 1rem;
    background-color: #fff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.workflow-card:hover {
    transform: translateX(3px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
}

.workflow-card.paused {
    border-left-color: #f6c23e;
}

.workflow-card.high-priority {
    border-left-color: #e74a3b;
}

/* API Usage Table */
.api-usage-table {
    width: 100%;
    margin-bottom: 0;
}

.api-usage-table th, .api-usage-table td {
    text-align: center;
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
}

.api-usage-table th {
    background-color: #f8f9fc;
    color: #4e73df;
    font-weight: 600;
    border-top: none;
}

.api-usage-table tbody tr:hover {
    background-color: #f8f9fc;
}

/* Buttons */
.refresh-btn {
    margin-bottom: 0;
    border-radius: 50px;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
}

.btn-icon {
    margin-right: 0.5rem;
}

/* Last Updated Text */
#last-updated {
    font-style: italic;
    color: #858796;
    margin-bottom: 1.25rem;
    font-size: 0.9rem;
}

/* Charts */
.chart-container {
    height: 300px;
    margin-bottom: 1.875rem;
}

/* Connection Error Alert */
.connection-error-alert {
    background-color: #fff3cd;
    color: #856404;
    border-color: #ffeeba;
    padding: 0.75rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.connection-error-alert i {
    font-size: 1.5rem;
    margin-right: 0.75rem;
    color: #f6c23e;
}

/* Modal Styling */
.modal-header.bg-primary {
    background-color: #4e73df !important;
}

.modal-title {
    font-weight: 600;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .resource-card {
        margin-bottom: 1rem;
    }

    .resource-value {
        font-size: 1.75rem;
    }
    
    .dashboard-title {
        font-size: 1.5rem;
    }
}
