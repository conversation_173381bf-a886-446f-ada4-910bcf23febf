/* Campaign Header Buttons Fix */

/* Fix for the Add Favorite button */
.campaign-detail-buttons-fix .detail-action-buttons .btn-warning,
.campaign-detail-buttons-fix .detail-action-buttons .btn-outline-warning {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100px ;
  min-width: 50px !important;
  max-width: 160px !important;
  height: 38px !important;
  line-height: 24px !important;
  padding: 6px 12px !important;
  margin: 0 !important;
  text-align: center !important;
  vertical-align: middle !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  overflow: visible !important;
  white-space: nowrap !important;
  text-overflow: clip !important;
  background-color: #f39c12 !important;
  border-color: #f39c12 !important;
  color: white !important;
}

/* Fix for the favorite button text */
.campaign-detail-buttons-fix .detail-action-buttons .btn-warning span,
.campaign-detail-buttons-fix .detail-action-buttons .btn-outline-warning span {
  display: inline-block !important;
  overflow: visible !important;
  white-space: nowrap !important;
  text-overflow: clip !important;
  font-size: 14px !important;
}

.campaign-detail-buttons-fix .detail-action-buttons .btn-outline-warning {
  background-color: transparent !important;
  color: #f39c12 !important;
}

.campaign-detail-buttons-fix .detail-action-buttons .btn-outline-warning:hover {
  background-color: #f39c12 !important;
  color: white !important;
}

/* Fix for the Export button dropdown */
.campaign-detail-buttons-fix .detail-action-buttons .btn-info {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 120px !important;
  min-width: 120px !important;
  max-width: 120px !important;
  height: 38px !important;
  line-height: 24px !important;
  padding: 6px 12px !important;
  margin: 0 !important;
  text-align: center !important;
  vertical-align: middle !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  overflow: visible !important;
  white-space: nowrap !important;
  text-overflow: clip !important;
  background-color: #3498db !important;
  border-color: #3498db !important;
  color: white !important;
  font-size: 14px !important;
}

/* Fix for the dropdown menu */
.campaign-detail-buttons-fix .detail-action-buttons .dropdown-menu {
  min-width: 160px !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  padding: 8px 0 !important;
}

.campaign-detail-buttons-fix .detail-action-buttons .dropdown-item {
  padding: 8px 16px !important;
  font-size: 14px !important;
}

.campaign-detail-buttons-fix .detail-action-buttons .dropdown-item:hover {
  background-color: #f8f9fa !important;
}

/* Fix for button spacing */
.campaign-detail-buttons-fix .detail-action-buttons {
  gap: 8px !important;
}

/* Additional fix for favorite button */
.campaign-detail-buttons-fix .detail-action-buttons .favorite-button {
  width: auto !important;
  min-width: 50px !important;
  max-width: 120px !important;
  overflow: visible !important;
  text-overflow: clip !important;
  white-space: nowrap !important;
  /* padding: 6px 12px !important; */
  font-size: 14px !important;
}

/* Fix for button hover effects */
.campaign-detail-buttons-fix .detail-action-buttons .btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Fix for button active effects */
.campaign-detail-buttons-fix .detail-action-buttons .btn:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}
