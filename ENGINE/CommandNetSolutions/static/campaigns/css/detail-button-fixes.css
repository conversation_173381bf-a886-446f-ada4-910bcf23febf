/* Campaign Detail Page Button Fixes */

/* Reset all button styles in the detail page header */
.campaign-detail-buttons-fix {
  display: flex !important;
  justify-content: flex-end !important;
  width: auto !important;
  min-width: 350px !important;
  height: 40px !important;
  position: relative !important;
  z-index: 100 !important;
  margin-left: auto !important;
}

/* Target the specific buttons in the campaign detail page */
.campaign-detail-buttons-fix .detail-action-buttons {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  gap: 5px !important;
  box-shadow: none !important;
  background: transparent !important;
  border: none !important;
  width: auto !important;
  overflow: visible !important;
  justify-content: flex-end !important;
  align-items: center !important;
}

/* Override any conflicting styles */
.campaign-detail-buttons-fix .btn-group {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: nowrap !important;
  gap: 5px !important;
  box-shadow: none !important;
  background: transparent !important;
  border: none !important;
  width: auto !important;
  overflow: visible !important;
  justify-content: flex-end !important;
  align-items: center !important;
}

/* Fix for the Edit button */
.campaign-detail-buttons-fix .detail-action-buttons .edit-button {
  display: inline-block !important;
  width: 100px !important;
  min-width: 100px !important;
  max-width: 100px !important;
  height: 38px !important;
  line-height: 24px !important;
  padding: 6px 12px !important;
  margin: 0 !important;
  text-align: center !important;
  vertical-align: middle !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
}

/* Fix for the Back to List button */
.campaign-detail-buttons-fix .detail-action-buttons .back-button {
  display: inline-block !important;
  width: 140px !important;
  min-width: 140px !important;
  max-width: 140px !important;
  height: 38px !important;
  line-height: 24px !important;
  padding: 6px 12px !important;
  margin: 0 !important;
  text-align: center !important;
  vertical-align: middle !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
}

/* Fix for the Launch Campaign button */
.campaign-detail-buttons-fix .detail-action-buttons .launch-campaign-btn {
  display: inline-block !important;
  width: 180px !important;
  min-width: 180px !important;
  max-width: 180px !important;
  height: 38px !important;
  line-height: 24px !important;
  padding: 6px 12px !important;
  margin: 0 !important;
  text-align: center !important;
  vertical-align: middle !important;
  border-radius: 6px !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
}

/* Fix for button icons */
.campaign-detail-buttons-fix .detail-action-buttons .btn i {
  margin-right: 5px !important;
  display: inline-block !important;
  vertical-align: middle !important;
}

/* Fix for button text */
.campaign-detail-buttons-fix .detail-action-buttons .btn span {
  display: inline-block !important;
  vertical-align: middle !important;
}
