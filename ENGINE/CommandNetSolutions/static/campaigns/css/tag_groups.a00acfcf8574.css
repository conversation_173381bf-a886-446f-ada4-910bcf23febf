/* Tag Group Styles */

.tag-badge {
    display: inline-block;
    padding: 0.5rem 0.75rem;
    margin: 0.25rem;
    border-radius: 30px;
    font-size: 0.85rem;
    font-weight: 500;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
    transition: all 0.2s ease;
}

.tag-badge:hover {
    background-color: #e9ecef;
    cursor: pointer;
}

.tag-badge i {
    margin-right: 0.5rem;
}

.tag-badge.tag-primary {
    background-color: #e6f3ff;
    border-color: #b8daff;
    color: #0d6efd;
}

.tag-badge.tag-success {
    background-color: #e6fff2;
    border-color: #b8ffda;
    color: #198754;
}

.tag-badge.tag-warning {
    background-color: #fff9e6;
    border-color: #ffeeba;
    color: #ffc107;
}

.tag-badge.tag-danger {
    background-color: #ffe6e6;
    border-color: #ffb8b8;
    color: #dc3545;
}

.tag-badge.tag-info {
    background-color: #e6f9ff;
    border-color: #b8f0ff;
    color: #0dcaf0;
}

.tag-group-card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
    margin-bottom: 1.5rem;
}

.tag-group-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.tag-group-card .card-header {
    border-radius: 0.5rem 0.5rem 0 0;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    padding: 1rem 1.25rem;
}

.tag-group-card .card-body {
    padding: 1.25rem;
}

.tag-group-card .card-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    border-radius: 0 0 0.5rem 0.5rem;
    padding: 0.75rem 1.25rem;
}

.tag-group-card .tag-count {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    background-color: #f8f9fa;
    color: #6c757d;
}

.tag-group-card .global-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    background-color: #198754;
    color: #fff;
}

.tag-group-actions {
    display: flex;
    gap: 0.5rem;
}

.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 1rem;
    text-align: center;
}

.empty-state-icon {
    font-size: 3rem;
    color: #6c757d;
    margin-bottom: 1rem;
}

.empty-state h4 {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.empty-state p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.search-box {
    position: relative;
    margin-bottom: 1rem;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

.search-box input {
    padding-left: 2.5rem;
    border-radius: 2rem;
}

.action-buttons .btn {
    margin-right: 0.25rem;
}

.action-buttons .btn:last-child {
    margin-right: 0;
}
