/* Campaigns App Custom Styles */

:root {
  --primary-color: #3498db;
  --primary-dark: #2980b9;
  --secondary-color: #2ecc71;
  --secondary-dark: #27ae60;
  --warning-color: #f39c12;
  --danger-color: #e74c3c;
  --light-color: #ecf0f1;
  --dark-color: #34495e;
  --gray-color: #95a5a6;
  --white-color: #ffffff;
  --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  --border-radius: 8px;
}

/* General Styles */
body {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

.campaigns-container {
  padding: 20px 0;
  background-color: #f8f9fa;
  min-height: calc(100vh - 56px);
  overflow-x: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.page-title {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 10px;
}

.page-subtitle {
  font-size: 1.1rem;
  color: var(--gray-color);
  margin-bottom: 30px;
}

/* Card Styles */
.card {
  background-color: var(--white-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin-bottom: 25px;
  border: none;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.card .card-header {
  background-color: var(--white-color);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  padding: 18px 25px;
  font-weight: 600;
}

.card .card-body {
  padding: 25px;
}

.card .card-footer {
  background-color: var(--white-color);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  padding: 18px 25px;
}

/* For backward compatibility */
.campaign-card {
  background-color: var(--white-color);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  margin-bottom: 25px;
  border: none;
  overflow: hidden;
}

.campaign-card .card-body {
  padding: 25px;
}

/* Stats Cards */
.stats-card {
  text-align: center;
  padding: 30px 20px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  background-color: var(--white-color);
  transition: transform 0.3s ease;
  margin-bottom: 25px;
}

.stats-card:hover {
  transform: translateY(-5px);
}

.stats-card .stats-icon {
  font-size: 2.5rem;
  margin-bottom: 15px;
  color: var(--primary-color);
}

.stats-card .stats-title {
  font-size: 1rem;
  color: var(--gray-color);
  margin-bottom: 10px;
}

.stats-card .stats-value {
  font-size: 2.2rem;
  font-weight: 700;
  color: var(--dark-color);
}

.stats-card.primary {
  border-top: 4px solid var(--primary-color);
}

.stats-card.secondary {
  border-top: 4px solid var(--secondary-color);
}

.stats-card.warning {
  border-top: 4px solid var(--warning-color);
}

.stats-card.danger {
  border-top: 4px solid var(--danger-color);
}

/* Status Badges */
.campaign-status {
  display: inline-block;
  padding: 5px 10px;
  border-radius: 50px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-draft {
  background-color: #e0e0e0;
  color: #555;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-running {
  background-color: #d1ecf1;
  color: #0c5460;
}

.status-completed {
  background-color: #d4edda;
  color: #155724;
}

.status-failed {
  background-color: #f8d7da;
  color: #721c24;
}

/* Buttons */
.btn {
  border-radius: 6px;
  font-weight: 500;
  padding: 10px 20px;
  transition: all 0.2s ease;
  font-size: 15px;
}

.btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.875rem;
  border-radius: 4px;
}

.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  box-shadow: 0 2px 4px rgba(52, 152, 219, 0.2);
  width: auto;
  min-width: auto;
}

.btn-primary:hover {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  color: white;
  box-shadow: 0 4px 6px rgba(52, 152, 219, 0.3);
  transform: translateY(-1px);
}

.btn-success {
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);
  color: white;
  box-shadow: 0 2px 4px rgba(46, 204, 113, 0.2);
}

.btn-success:hover {
  background-color: var(--secondary-dark);
  border-color: var(--secondary-dark);
  color: white;
  box-shadow: 0 4px 6px rgba(46, 204, 113, 0.3);
  transform: translateY(-1px);
}

.btn-secondary {
  background-color: var(--gray-color);
  border-color: var(--gray-color);
  color: white;
  box-shadow: 0 2px 4px rgba(149, 165, 166, 0.2);
}

.btn-secondary:hover {
  background-color: #7f8c8d;
  border-color: #7f8c8d;
  color: white;
  box-shadow: 0 4px 6px rgba(149, 165, 166, 0.3);
  transform: translateY(-1px);
}

.btn-danger {
  background-color: var(--danger-color);
  border-color: var(--danger-color);
  color: white;
  box-shadow: 0 2px 4px rgba(231, 76, 60, 0.2);
}

.btn-danger:hover {
  background-color: #c0392b;
  border-color: #c0392b;
  color: white;
  box-shadow: 0 4px 6px rgba(231, 76, 60, 0.3);
  transform: translateY(-1px);
}

.btn-outline-primary {
  color: var(--primary-color);
  border-color: var(--primary-color);
  background-color: transparent;
}

.btn-outline-primary:hover {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
  transform: translateY(-1px);
}

.btn-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-icon i {
  margin-right: 5px;
}

/* Button Groups */
.btn-group {
  display: inline-flex;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  overflow: hidden;
}

.btn-group .btn {
  border-radius: 0;
  margin: 0;
  box-shadow: none;
}

.btn-group .btn:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.btn-group .btn:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.btn-group .btn-sm {
  padding: 0.4rem 0.8rem;
  font-size: 0.875rem;
}

/* Tables */
.table-campaigns {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.table-campaigns thead th {
  background-color: #f8f9fa;
  color: var(--dark-color);
  font-weight: 600;
  padding: 12px 15px;
  border-bottom: 2px solid #dee2e6;
}

.table-campaigns tbody tr {
  transition: background-color 0.2s ease;
}

.table-campaigns tbody tr:hover {
  background-color: rgba(52, 152, 219, 0.05);
}

.table-campaigns tbody td {
  padding: 12px 15px;
  vertical-align: middle;
  border-bottom: 1px solid #dee2e6;
}

/* Target Lists */
.target-list {
  max-height: 300px;
  overflow-y: auto;
  border-radius: var(--border-radius);
  border: 1px solid #dee2e6;
}

.location-item, .username-item {
  padding: 15px 20px;
  border-bottom: 1px solid #dee2e6;
  transition: background-color 0.2s ease;
}

.location-item:last-child, .username-item:last-child {
  border-bottom: none;
}

.location-item:hover, .username-item:hover {
  background-color: rgba(52, 152, 219, 0.05);
}

/* Forms */
.form-control, .form-select {
  border-radius: var(--border-radius);
  padding: 10px 15px;
  border: 1px solid #dee2e6;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 8px;
}

.input-group-text {
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.input-group .form-control {
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* Campaign Detail Sections */
.detail-section {
  margin-bottom: 30px;
  padding: 5px 0;
}

.detail-section-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--dark-color);
  margin-bottom: 20px;
  padding: 0 5px 12px 5px;
  border-bottom: 1px solid #dee2e6;
}

.detail-item {
  margin-bottom: 20px;
  padding: 0 5px;
}

.detail-label {
  font-weight: 600;
  color: var(--gray-color);
  margin-bottom: 8px;
}

.detail-value {
  color: var(--dark-color);
  padding: 3px 0;
}

/* Progress Bars */
.progress {
  height: 12px;
  border-radius: 50px;
  margin-bottom: 20px;
  background-color: #e9ecef;
}

.progress-bar {
  border-radius: 50px;
  background-color: var(--primary-color);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .campaigns-container {
    padding: 15px;
  }

  .page-title {
    font-size: 1.8rem;
  }

  .stats-card {
    margin-bottom: 20px;
  }
}

/* Animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease forwards;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 25px;
  background-color: #f8f9fa;
  border-radius: var(--border-radius);
  margin: 15px 0;
}

.empty-state-icon {
  font-size: 4rem;
  color: var(--gray-color);
  margin-bottom: 20px;
  background-color: rgba(149, 165, 166, 0.1);
  width: 100px;
  height: 100px;
  line-height: 100px;
  border-radius: 50%;
  display: inline-block;
}

.empty-state-text {
  font-size: 1.2rem;
  color: var(--gray-color);
  margin-bottom: 20px;
  font-weight: 500;
}

/* Search Box */
.search-box {
  position: relative;
  margin-bottom: 0;
  width: 250px;
}

.search-box input {
  padding-left: 40px;
  border-radius: 50px;
  height: 38px;
}

.search-box i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-color);
  z-index: 5;
}

/* Pagination */
.pagination {
  justify-content: center;
  margin-top: 30px;
}

.pagination .page-item .page-link {
  color: var(--primary-color);
  border-color: #dee2e6;
  margin: 0 3px;
  border-radius: 5px;
}

.pagination .page-item.active .page-link {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.pagination .page-item .page-link:hover {
  background-color: #e9ecef;
}

/* Chart Container */
canvas {
  max-width: 100%;
}

/* Tooltips */
.tooltip-inner {
  background-color: var(--dark-color);
  border-radius: var(--border-radius);
  padding: 8px 12px;
}

.bs-tooltip-auto[x-placement^=top] .arrow::before,
.bs-tooltip-top .arrow::before {
  border-top-color: var(--dark-color);
}

/* Loading Spinner */
.spinner-border {
  color: var(--primary-color);
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

/* Alerts */
.alert {
  border-radius: var(--border-radius);
  border: none;
  box-shadow: var(--shadow);
  padding: 15px 20px;
  margin-bottom: 20px;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
}

.alert-warning {
  background-color: #fff3cd;
  color: #856404;
}

.alert-info {
  background-color: #d1ecf1;
  color: #0c5460;
}
