/*
 * Resource Manager Dashboard Styles
 * Bootstrap 5.2.3 compatible
 */

/* General Layout */
.dashboard-container {
    padding: 1.5rem;
}

.dashboard-header {
    margin-bottom: 2rem;
}

.dashboard-title {
    font-size: 32px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.dashboard-subtitle {
    color: #7f8c8d;
    font-size: 16px;
}

/* Card Styling */
.resource-card {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
    margin-bottom: 30px;
    transition: all 0.3s ease;
    overflow: hidden;
    border: none;
    height: 100%;
}

.resource-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.resource-card .card-header {
    background-color: #3498db;
    color: white;
    font-weight: 600;
    padding: 18px 20px;
    border-bottom: none;
    display: flex;
    align-items: center;
}

.resource-card .card-header i {
    margin-right: 12px;
    font-size: 1.2rem;
}

.resource-card .card-header h5 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: white;
}

.resource-card .card-body {
    padding: 25px;
}

/* Resource Values */
.resource-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #3498db;
}

.resource-value {
    font-size: 36px;
    font-weight: 700;
    margin-bottom: 10px;
    color: #2c3e50;
}

.resource-subtitle {
    font-size: 14px;
    color: #7f8c8d;
    margin-bottom: 15px;
}

/* Status Indicators */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.available {
    background-color: #2ecc71;
}

.status-indicator.busy {
    background-color: #f39c12;
}

.status-indicator.error {
    background-color: #e74c3c;
}

/* Progress Bars */
.progress {
    height: 12px;
    margin-top: 12px;
    margin-bottom: 8px;
    border-radius: 6px;
    background-color: #f5f5f5;
    overflow: hidden;
}

.progress-bar {
    border-radius: 6px;
    transition: width 0.3s ease;
}

/* Workflow Cards */
.workflow-card {
    border-left: 5px solid #3498db;
    margin-bottom: 15px;
    border-radius: 8px;
    transition: all 0.2s ease;
    padding: 15px;
    background-color: #fff;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
}

.workflow-card:hover {
    transform: translateX(3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.12);
}

.workflow-card.paused {
    border-left-color: #f39c12;
}

.workflow-card.high-priority {
    border-left-color: #e74c3c;
}

/* API Usage Table */
.api-usage-table {
    width: 100%;
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

.api-usage-table th, .api-usage-table td {
    text-align: center;
    padding: 12px 8px;
    vertical-align: middle;
}

.api-usage-table th {
    background-color: #f8f9fa;
    color: #3498db;
    font-weight: 600;
    border-top: none;
    border-bottom: 2px solid #eee;
}

.api-usage-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Buttons */
.refresh-btn {
    margin-bottom: 0;
    border-radius: 50px;
    padding: 8px 20px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.refresh-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.btn-icon {
    margin-right: 8px;
}

/* Last Updated Text */
#last-updated {
    font-style: italic;
    color: #7f8c8d;
    margin-bottom: 20px;
    font-size: 14px;
}

/* Charts */
.chart-container {
    height: 300px;
    margin-bottom: 30px;
}

/* Connection Error Alert */
.connection-error-alert {
    background-color: #fff3cd;
    color: #856404;
    border-color: #ffeeba;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.connection-error-alert i {
    font-size: 24px;
    margin-right: 15px;
    color: #f39c12;
}

/* Modal Styling */
.modal-header.bg-primary {
    background-color: #3498db !important;
}

.modal-title {
    font-weight: 600;
    color: white;
}

.modal-content {
    border-radius: 10px;
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modal-body {
    padding: 25px;
}

.modal-footer {
    padding: 15px 25px;
    border-top: 1px solid #eee;
}

/* List Group Styling */
.list-group-item {
    padding: 15px;
    border-left: 4px solid transparent;
    transition: all 0.2s ease;
    margin-bottom: 8px;
    border-radius: 8px !important;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(3px);
}

.list-group-item.border-danger {
    border-left-color: #e74c3c;
}

/* Action Buttons */
.btn-action {
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.btn-action:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .resource-card {
        margin-bottom: 15px;
    }

    .resource-value {
        font-size: 28px;
    }

    .dashboard-title {
        font-size: 24px;
    }

    .card-body {
        padding: 15px;
    }

    .btn-lg {
        padding: 8px 16px;
        font-size: 16px;
    }
}
