/* Export Dropdown Fix CSS */

/* Fix for export dropdown in action buttons */
.export-dropdown {
  position: relative !important;
  display: inline-block !important;
}

.export-dropdown .dropdown-toggle::after {
  display: none !important;
}

.export-dropdown .dropdown-menu {
  position: absolute !important;
  top: 100% !important;
  right: 0 !important;
  left: auto !important;
  z-index: 1021 !important;
  min-width: 160px !important;
  padding: 8px 0 !important;
  margin: 5px 0 0 !important;
  font-size: 14px !important;
  text-align: left !important;
  list-style: none !important;
  background-color: #fff !important;
  background-clip: padding-box !important;
  border: 1px solid rgba(0, 0, 0, 0.15) !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
}

.export-dropdown .dropdown-item {
  display: block !important;
  width: 100% !important;
  padding: 8px 16px !important;
  clear: both !important;
  font-weight: 400 !important;
  color: #212529 !important;
  text-align: inherit !important;
  white-space: nowrap !important;
  background-color: transparent !important;
  border: 0 !important;
  text-decoration: none !important;
}

.export-dropdown .dropdown-item:hover,
.export-dropdown .dropdown-item:focus {
  color: #16181b !important;
  text-decoration: none !important;
  background-color: #f8f9fa !important;
}
