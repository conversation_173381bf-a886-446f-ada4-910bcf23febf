/* Action Buttons Fix CSS */

/* Fix for action buttons in tables */
.action-buttons {
  display: inline-flex !important;
  flex-wrap: nowrap !important;
  gap: 3px !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.action-buttons .btn {
  border-radius: 4px !important;
  width: 32px !important;
  height: 32px !important;
  padding: 6px !important;
  margin: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.action-buttons .btn i {
  margin: 0 !important;
  font-size: 14px !important;
}

/* Fix for dropdown button */
.action-buttons .dropdown-toggle::after {
  display: none !important;
}

/* Fix for dropdown container */
.action-buttons .dropdown,
.action-buttons .btn-group,
.action-buttons .export-dropdown {
  display: inline-block !important;
  position: relative !important;
}

.action-buttons .dropdown button,
.action-buttons .btn-group button,
.action-buttons .export-dropdown button {
  border-radius: 4px !important;
  width: 32px !important;
  height: 32px !important;
  padding: 6px !important;
  margin: 0 !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

.action-buttons .dropdown-menu,
.action-buttons .btn-group .dropdown-menu,
.action-buttons .export-dropdown .dropdown-menu {
  min-width: 160px !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1) !important;
  border: 1px solid rgba(0, 0, 0, 0.1) !important;
  padding: 8px 0 !important;
  margin-top: 5px !important;
  z-index: 1021 !important;
  position: absolute !important;
  left: auto !important;
  right: 0 !important;
}

.action-buttons .dropdown-item,
.action-buttons .btn-group .dropdown-item,
.action-buttons .export-dropdown .dropdown-item {
  padding: 8px 16px !important;
  font-size: 14px !important;
  display: block !important;
  width: 100% !important;
  clear: both !important;
  text-align: left !important;
  white-space: nowrap !important;
  color: #212529 !important;
  text-decoration: none !important;
}

.action-buttons .dropdown-item:hover,
.action-buttons .btn-group .dropdown-item:hover,
.action-buttons .export-dropdown .dropdown-item:hover {
  background-color: #f8f9fa !important;
  color: #16181b !important;
}

/* Fix for button hover effects */
.action-buttons .btn:hover {
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
}

/* Fix for button active effects */
.action-buttons .btn:active {
  transform: translateY(0) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
}

/* Fix for button colors */
.action-buttons .btn-primary {
  background-color: #3498db !important;
  border-color: #3498db !important;
}

.action-buttons .btn-secondary {
  background-color: #95a5a6 !important;
  border-color: #95a5a6 !important;
}

.action-buttons .btn-success {
  background-color: #2ecc71 !important;
  border-color: #2ecc71 !important;
}

.action-buttons .btn-info {
  background-color: #17a2b8 !important;
  border-color: #17a2b8 !important;
}

.action-buttons .btn-warning {
  background-color: #f39c12 !important;
  border-color: #f39c12 !important;
}

.action-buttons .btn-danger {
  background-color: #e74c3c !important;
  border-color: #e74c3c !important;
}

.action-buttons .btn-outline-warning {
  color: #f39c12 !important;
  border-color: #f39c12 !important;
  background-color: transparent !important;
}

.action-buttons .btn-outline-warning:hover {
  color: #fff !important;
  background-color: #f39c12 !important;
}
