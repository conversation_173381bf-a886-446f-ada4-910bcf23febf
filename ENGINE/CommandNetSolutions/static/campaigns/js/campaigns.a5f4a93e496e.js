// Campaigns App JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.campaign-card, .stats-card');
    cards.forEach((card, index) => {
        card.classList.add('fade-in');
        card.style.animationDelay = `${index * 0.1}s`;
    });

    // Handle target type change in campaign form
    const targetTypeSelect = document.getElementById('id_target_type');
    const audienceTypeSelect = document.getElementById('id_audience_type');
    
    if (targetTypeSelect && audienceTypeSelect) {
        targetTypeSelect.addEventListener('change', function() {
            const targetType = this.value;
            
            // If target type is username, restrict audience type to profile only
            if (targetType === 'username') {
                // Save the current selection
                const currentAudienceType = audienceTypeSelect.value;
                
                // Clear options
                audienceTypeSelect.innerHTML = '';
                
                // Add only the profile option
                const option = document.createElement('option');
                option.value = 'profile';
                option.textContent = 'Profile Only';
                audienceTypeSelect.appendChild(option);
                
                // Set the value to profile
                audienceTypeSelect.value = 'profile';
                audienceTypeSelect.disabled = true;
            } else {
                // Re-enable and repopulate the audience type select
                audienceTypeSelect.disabled = false;
                audienceTypeSelect.innerHTML = '';
                
                const options = [
                    { value: 'profile', text: 'Profile Only' },
                    { value: 'followers', text: 'Followers Only' },
                    { value: 'following', text: 'Following Only' },
                    { value: 'both', text: 'Followers and Following' }
                ];
                
                options.forEach(opt => {
                    const option = document.createElement('option');
                    option.value = opt.value;
                    option.textContent = opt.text;
                    audienceTypeSelect.appendChild(option);
                });
                
                // Set the value to the default or current value
                audienceTypeSelect.value = 'profile';
            }
        });
    }

    // Search functionality for location targets
    const searchInput = document.getElementById('search-location');
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const locationItems = document.querySelectorAll('.location-item');
            
            locationItems.forEach(item => {
                const locationText = item.textContent.toLowerCase();
                if (locationText.includes(searchTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }

    // Confirmation for campaign deletion
    const deleteButtons = document.querySelectorAll('.delete-campaign-btn');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to delete this campaign? This action cannot be undone.')) {
                e.preventDefault();
            }
        });
    });

    // Confirmation for campaign launch
    const launchButtons = document.querySelectorAll('.launch-campaign-btn');
    launchButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirm('Are you sure you want to launch this campaign? This will trigger the Airflow DAG.')) {
                e.preventDefault();
            } else {
                // Show loading overlay
                const loadingOverlay = document.createElement('div');
                loadingOverlay.className = 'loading-overlay';
                loadingOverlay.innerHTML = `
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                `;
                document.body.appendChild(loadingOverlay);
                
                // Remove overlay after 3 seconds (simulating loading)
                setTimeout(() => {
                    document.body.removeChild(loadingOverlay);
                }, 3000);
            }
        });
    });

    // Progress bars animation
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const width = bar.getAttribute('aria-valuenow');
        bar.style.width = '0%';
        setTimeout(() => {
            bar.style.width = width + '%';
            bar.style.transition = 'width 1s ease-in-out';
        }, 500);
    });
});
