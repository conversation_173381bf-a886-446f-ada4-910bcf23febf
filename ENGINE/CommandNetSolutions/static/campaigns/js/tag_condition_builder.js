/**
 * Tag Condition Builder
 * Handles the creation and management of multiple conditions for tag creation
 */

// Field definitions by category
const fieldDefinitions = {
    text: [
        { id: 'bio', name: '<PERSON><PERSON>', icon: 'fa-align-left', iconColor: 'text-primary' },
        { id: 'username', name: 'Userna<PERSON>', icon: 'fa-at', iconColor: 'text-info' },
        { id: 'full_name', name: 'Full Name', icon: 'fa-user', iconColor: 'text-success' },
        { id: 'account_type', name: 'Account Type', icon: 'fa-id-badge', iconColor: 'text-warning' }
    ],
    numeric: [
        { id: 'followers', name: 'Followers', icon: 'fa-users', iconColor: 'text-primary' },
        { id: 'following', name: 'Following', icon: 'fa-user-friends', iconColor: 'text-success' },
        { id: 'number_of_posts', name: 'Number of Posts', icon: 'fa-images', iconColor: 'text-warning' }
    ],
    lists: [
        { id: 'interests', name: 'Interests', icon: 'fa-heart', iconColor: 'text-danger' },
        { id: 'locations', name: 'Locations', icon: 'fa-map-marker-alt', iconColor: 'text-primary' },
        { id: 'links', name: 'Links', icon: 'fa-link', iconColor: 'text-info' },
        { id: 'phone_number', name: 'Phone Number', icon: 'fa-phone', iconColor: 'text-danger' }
    ],
    boolean: [
        { id: 'is_verified', name: 'Verified Status', icon: 'fa-check-circle', iconColor: 'text-primary' },
        { id: 'avoid', name: 'Avoid Flag', icon: 'fa-ban', iconColor: 'text-danger' }
    ]
};

// Operator definitions by field type
const operatorDefinitions = {
    string: [
        { id: 'icontains', name: 'Contains (Case Insensitive)', icon: 'fa-search', iconColor: 'text-primary' },
        { id: 'contains', name: 'Contains (Case Sensitive)', icon: 'fa-search', iconColor: 'text-primary' },
        { id: 'iexact', name: 'Equals (Case Insensitive)', icon: 'fa-equals', iconColor: 'text-success' },
        { id: 'exact', name: 'Equals (Case Sensitive)', icon: 'fa-equals', iconColor: 'text-success' },
        { id: 'istartswith', name: 'Starts With (Case Insensitive)', icon: 'fa-step-forward', iconColor: 'text-info' },
        { id: 'startswith', name: 'Starts With (Case Sensitive)', icon: 'fa-step-forward', iconColor: 'text-info' },
        { id: 'iendswith', name: 'Ends With (Case Insensitive)', icon: 'fa-step-backward', iconColor: 'text-warning' },
        { id: 'endswith', name: 'Ends With (Case Sensitive)', icon: 'fa-step-backward', iconColor: 'text-warning' },
        { id: 'not_icontains', name: 'Does Not Contain (Case Insensitive)', icon: 'fa-ban', iconColor: 'text-danger' },
        { id: 'not_contains', name: 'Does Not Contain (Case Sensitive)', icon: 'fa-ban', iconColor: 'text-danger' },
        { id: 'iregex', name: 'Regular Expression (Case Insensitive)', icon: 'fa-code', iconColor: 'text-secondary' },
        { id: 'regex', name: 'Regular Expression (Case Sensitive)', icon: 'fa-code', iconColor: 'text-secondary' }
    ],
    number: [
        { id: 'eq', name: 'Equal To', icon: 'fa-equals', iconColor: 'text-primary' },
        { id: 'gt', name: 'Greater Than', icon: 'fa-greater-than', iconColor: 'text-success' },
        { id: 'gte', name: 'Greater Than or Equal', icon: 'fa-greater-than-equal', iconColor: 'text-info' },
        { id: 'lt', name: 'Less Than', icon: 'fa-less-than', iconColor: 'text-warning' },
        { id: 'lte', name: 'Less Than or Equal', icon: 'fa-less-than-equal', iconColor: 'text-danger' }
    ],
    array: [
        { id: 'contains', name: 'Contains', icon: 'fa-search', iconColor: 'text-primary' },
        { id: 'not_contains', name: 'Does Not Contain', icon: 'fa-ban', iconColor: 'text-danger' },
        { id: 'length_gt', name: 'Length Greater Than', icon: 'fa-ruler', iconColor: 'text-success' },
        { id: 'length_lt', name: 'Length Less Than', icon: 'fa-ruler-combined', iconColor: 'text-warning' }
    ],
    boolean: [
        { id: 'is_true', name: 'Is True', icon: 'fa-check', iconColor: 'text-success' },
        { id: 'is_false', name: 'Is False', icon: 'fa-times', iconColor: 'text-danger' }
    ]
};

// Field type mapping
const fieldTypeMapping = {
    bio: 'string',
    username: 'string',
    full_name: 'string',
    account_type: 'string',
    phone_number: 'array',  // Changed from 'string' to 'array' as phone numbers are stored as a list
    followers: 'number',
    following: 'number',
    number_of_posts: 'number',
    interests: 'array',
    locations: 'array',
    links: 'array',
    is_verified: 'boolean',
    avoid: 'boolean'
};

// Condition counter for unique IDs
let conditionCounter = 0;
// Array to store all conditions
let conditions = [];

/**
 * Initialize the condition builder
 */
function initConditionBuilder() {
    // Add event listener for the "Add Condition" button
    document.getElementById('add-condition-btn').addEventListener('click', addCondition);

    // Add event listener for condition logic radio buttons
    document.querySelectorAll('input[name="condition-logic"]').forEach(radio => {
        radio.addEventListener('change', updateConditionLogicReview);
    });

    // Add event listener for the next button to validate conditions
    const nextBtn = document.getElementById('conditions-next-btn');
    if (nextBtn) {
        nextBtn.addEventListener('click', function(e) {
            if (!validateConditions()) {
                e.preventDefault();
                e.stopPropagation();
                alert('Please complete all fields for your conditions before proceeding.');
            } else {
                // Update the conditions JSON hidden input
                const conditionsJson = document.getElementById('conditions-json');
                if (conditionsJson) {
                    conditionsJson.value = JSON.stringify(conditions);
                }
                // Update the review section
                updateReview();
            }
        });
    }

    // Add event listener for form submission
    const tagForm = document.getElementById('tag-form');
    if (tagForm) {
        tagForm.addEventListener('submit', function(e) {
            console.log('Tag condition builder form submit handler');

            // Always update the conditions JSON hidden input
            const conditionsJson = document.getElementById('conditions-json');
            if (conditionsJson) {
                console.log('Conditions before JSON stringify:', conditions);
                conditionsJson.value = JSON.stringify(conditions);
                console.log('Updated conditions_json value:', conditionsJson.value);
            }

            if (!validateConditions()) {
                e.preventDefault();
                alert('Please complete all fields for your conditions before submitting.');
                return false;
            } else {
                // Allow the form to submit
                return true;
            }
        });
    }

    // Make sure the "No Conditions" message is visible if it exists
    const noConditionsMsg = document.getElementById('no-conditions-message');
    if (noConditionsMsg) {
        noConditionsMsg.style.display = 'block';
    }

    // Start with empty conditions by default
    // The user will need to click "Add Condition" to add their first condition
    // Clear any existing conditions in the UI
    const activeConditionsContainer = document.getElementById('active-conditions');
    if (activeConditionsContainer) {
        activeConditionsContainer.innerHTML = '';
    }

    // Reset conditions array
    conditions = [];

    // Check if we're editing an existing tag with conditions
    const conditionsJsonElement = document.getElementById('conditions-json');
    if (conditionsJsonElement) {
        const existingConditionsJson = conditionsJsonElement.value;
        console.log('Existing conditions JSON:', existingConditionsJson);

        if (existingConditionsJson && existingConditionsJson !== '[]') {
            try {
                // Parse existing conditions
                const existingConditions = JSON.parse(existingConditionsJson);
                console.log('Parsed existing conditions:', existingConditions);

                if (existingConditions && Array.isArray(existingConditions) && existingConditions.length > 0) {
                    // Clear existing conditions array
                    conditions = [];

                    // Clear any existing conditions in the UI
                    const activeConditionsContainer = document.getElementById('active-conditions');
                    if (activeConditionsContainer) {
                        activeConditionsContainer.innerHTML = '';
                    }

                    // Load existing conditions
                    existingConditions.forEach(condition => {
                        if (condition) {
                            addConditionFromData(condition);
                        }
                    });

                    // Hide the no conditions message
                    const noConditionsMsg = document.getElementById('no-conditions-message');
                    if (noConditionsMsg) {
                        noConditionsMsg.style.display = 'none';
                    }

                    console.log('Loaded conditions:', conditions);
                }
            } catch (e) {
                console.error('Error parsing existing conditions:', e);
            }
        }
    }
}

/**
 * Add a new condition to the form
 */
function addCondition() {
    // Hide the no conditions message if it exists
    const noConditionsMsg = document.getElementById('no-conditions-message');
    if (noConditionsMsg) {
        noConditionsMsg.style.display = 'none';
    }

    // Clone the condition template
    const template = document.querySelector('.condition-template .condition').cloneNode(true);

    // Set unique IDs for the radio buttons
    const conditionId = conditionCounter++;
    template.querySelectorAll('.field-category-radio').forEach(radio => {
        const oldId = radio.id;
        const newId = oldId.replace('-0', `-${conditionId}`);
        radio.id = newId;
        radio.name = `field-category-${conditionId}`;

        // Update the corresponding label
        const label = template.querySelector(`label[for="${oldId}"]`);
        if (label) {
            label.setAttribute('for', newId);
        }
    });

    // Add event listeners
    template.querySelector('.remove-condition').addEventListener('click', function() {
        removeCondition(this);
    });

    template.querySelectorAll('.field-category-radio').forEach(radio => {
        radio.addEventListener('change', function() {
            updateFieldOptions(this);
        });
    });

    template.querySelector('.field-select').addEventListener('change', function() {
        updateOperatorOptions(this);
    });

    // Add the condition to the active conditions container
    document.getElementById('active-conditions').appendChild(template);

    // Initialize field options for the default category (text)
    const checkedRadio = template.querySelector('.field-category-radio:checked');
    updateFieldOptions(checkedRadio);

    // Select the first field option by default
    const fieldSelect = template.querySelector('.field-select');
    if (fieldSelect.options.length > 1) {
        fieldSelect.selectedIndex = 1; // Select the first field (index 0 is the placeholder)

        // Initialize operator options based on the selected field
        updateOperatorOptions(fieldSelect);

        // Select the first operator option by default
        const operatorSelect = template.querySelector('.operator-select');
        if (operatorSelect.options.length > 1) {
            operatorSelect.selectedIndex = 1; // Select the first operator (index 0 is the placeholder)

            // Initialize value input based on the selected operator
            updateConditionValue(operatorSelect);
        }
    }

    // Add to conditions array with default values
    const defaultField = fieldSelect.options.length > 1 ? fieldSelect.options[1].value : '';
    const defaultFieldType = defaultField ? fieldTypeMapping[defaultField] : '';
    const operatorSelect = template.querySelector('.operator-select');
    const defaultOperator = operatorSelect.options.length > 1 ? operatorSelect.options[1].value : '';

    conditions.push({
        id: conditionId,
        field_category: checkedRadio.value,
        field: defaultField,
        field_type: defaultFieldType,
        operator: defaultOperator,
        value: '',
        required: true
    });
}

/**
 * Remove a condition from the form
 */
function removeCondition(button) {
    const condition = button.closest('.condition');
    const conditionId = parseInt(condition.querySelector('.field-category-radio').name.split('-')[2]);

    // Remove from conditions array
    conditions = conditions.filter(c => c.id !== conditionId);

    // Remove from DOM
    condition.remove();

    // Show the no conditions message if there are no conditions
    const activeConditions = document.querySelectorAll('#active-conditions .condition');
    const noConditionsMsg = document.getElementById('no-conditions-message');
    if (activeConditions.length === 0 && noConditionsMsg) {
        noConditionsMsg.style.display = 'block';
    }
}

/**
 * Update field options based on the selected category
 */
function updateFieldOptions(categoryRadio) {
    // Check if categoryRadio is null or undefined
    if (!categoryRadio) {
        console.warn('categoryRadio is null or undefined in updateFieldOptions');
        return;
    }

    const condition = categoryRadio.closest('.condition');
    if (!condition) {
        console.warn('Could not find condition element in updateFieldOptions');
        return;
    }

    const fieldSelect = condition.querySelector('.field-select');
    if (!fieldSelect) {
        console.warn('Could not find field-select element in updateFieldOptions');
        return;
    }

    const category = categoryRadio.value;
    const conditionId = parseInt(categoryRadio.name.split('-')[2]);

    // Update the condition in the array
    const conditionIndex = conditions.findIndex(c => c.id === conditionId);
    if (conditionIndex !== -1) {
        conditions[conditionIndex].field_category = category;
        conditions[conditionIndex].field = '';
        conditions[conditionIndex].field_type = '';
        conditions[conditionIndex].operator = '';
    }

    // Clear existing options
    fieldSelect.innerHTML = '<option value="">Select a field</option>';

    // Add new options based on the category
    fieldDefinitions[category].forEach(field => {
        const option = document.createElement('option');
        option.value = field.id;
        option.textContent = field.name;
        option.dataset.fieldType = fieldTypeMapping[field.id];
        fieldSelect.appendChild(option);
    });

    // Clear operator select
    const operatorSelect = condition.querySelector('.operator-select');
    if (operatorSelect) {
        operatorSelect.innerHTML = '<option value="">Select an operator</option>';
    }

    // Clear value input
    const valueInput = condition.querySelector('.value-input');
    if (valueInput) {
        valueInput.value = '';
    }
}

/**
 * Update operator options based on the selected field
 */
function updateOperatorOptions(fieldSelect) {
    try {
        const condition = fieldSelect.closest('.condition');
        if (!condition) {
            console.warn('Could not find condition element in updateOperatorOptions');
            return;
        }

        const operatorSelect = condition.querySelector('.operator-select');
        if (!operatorSelect) {
            console.warn('Could not find operator-select element in updateOperatorOptions');
            return;
        }

        const selectedField = fieldSelect.value;
        console.log(`Updating operator options for field: ${selectedField}`);

        // Get field type from fieldTypeMapping, default to 'string' if not found
        let fieldType = fieldTypeMapping[selectedField];
        if (!fieldType) {
            console.warn(`Field type not found for field "${selectedField}", defaulting to "string"`);
            fieldType = 'string';
        }
        console.log(`Field type: ${fieldType}`);

        // Get condition ID
        const fieldCategoryRadio = condition.querySelector('.field-category-radio');
        if (!fieldCategoryRadio) {
            console.warn('Could not find field-category-radio element in updateOperatorOptions');
            return;
        }
        const conditionId = parseInt(fieldCategoryRadio.name.split('-')[2]);

        // Update the condition in the array
        const conditionIndex = conditions.findIndex(c => c.id === conditionId);
        if (conditionIndex !== -1) {
            conditions[conditionIndex].field = selectedField;
            conditions[conditionIndex].field_type = fieldType;
            conditions[conditionIndex].operator = '';
        }

        // Clear existing options
        operatorSelect.innerHTML = '<option value="">Select an operator</option>';

        // Check if operatorDefinitions has the field type
        if (!operatorDefinitions[fieldType]) {
            console.warn(`Operator definitions not found for field type "${fieldType}", defaulting to "string"`);
            fieldType = 'string';

            // Update the condition in the array with the corrected field type
            if (conditionIndex !== -1) {
                conditions[conditionIndex].field_type = fieldType;
            }
        }

        // Add new options based on the field type
        operatorDefinitions[fieldType].forEach(operator => {
            const option = document.createElement('option');
            option.value = operator.id;
            option.textContent = operator.name;
            operatorSelect.appendChild(option);
        });

        // Remove existing event listener to prevent duplicates
        const newOperatorSelect = operatorSelect.cloneNode(true);
        operatorSelect.parentNode.replaceChild(newOperatorSelect, operatorSelect);

        // Add change event listener to operator select
        newOperatorSelect.addEventListener('change', function() {
            updateConditionValue(this);
        });
    } catch (error) {
        console.error('Error in updateOperatorOptions:', error);
        // Try to recover by setting default options
        try {
            const condition = fieldSelect.closest('.condition');
            const operatorSelect = condition.querySelector('.operator-select');

            // Clear existing options
            operatorSelect.innerHTML = '<option value="">Select an operator</option>';

            // Add default string operators
            operatorDefinitions['string'].forEach(operator => {
                const option = document.createElement('option');
                option.value = operator.id;
                option.textContent = operator.name;
                operatorSelect.appendChild(option);
            });

            // Remove existing event listener to prevent duplicates
            const newOperatorSelect = operatorSelect.cloneNode(true);
            operatorSelect.parentNode.replaceChild(newOperatorSelect, operatorSelect);

            // Add change event listener to operator select
            newOperatorSelect.addEventListener('change', function() {
                updateConditionValue(this);
            });
        } catch (recoveryError) {
            console.error('Failed to recover from error in updateOperatorOptions:', recoveryError);
        }
    }
}

/**
 * Update condition value when operator changes
 */
function updateConditionValue(operatorSelect) {
    const condition = operatorSelect.closest('.condition');
    const selectedOperator = operatorSelect.value;
    const conditionId = parseInt(condition.querySelector('.field-category-radio').name.split('-')[2]);

    // Update the condition in the array
    const conditionIndex = conditions.findIndex(c => c.id === conditionId);
    if (conditionIndex !== -1) {
        conditions[conditionIndex].operator = selectedOperator;
    }

    // Update value input help text based on operator
    const valueHelp = condition.querySelector('.value-help');
    const valueInput = condition.querySelector('.value-input');

    if (selectedOperator === 'is_true' || selectedOperator === 'is_false') {
        valueInput.value = selectedOperator === 'is_true' ? 'true' : 'false';
        if (conditionIndex !== -1) {
            conditions[conditionIndex].value = valueInput.value;
        }
    } else {
        valueHelp.textContent = 'Enter the value to match against the selected field.';
    }

    // Update the pattern field (hidden)
    if (conditionIndex !== -1 && selectedOperator) {
        document.getElementById('id_pattern').value = conditions[conditionIndex].value || '';
    }

    // Remove existing event listeners to prevent duplicates
    const newValueInput = valueInput.cloneNode(true);
    valueInput.parentNode.replaceChild(newValueInput, valueInput);

    // Add event listener to value input
    newValueInput.addEventListener('input', function() {
        updateConditionValueInArray(this);
    });

    // Remove existing event listeners from required checkbox
    const requiredCheck = condition.querySelector('.required-check');
    const newRequiredCheck = requiredCheck.cloneNode(true);
    requiredCheck.parentNode.replaceChild(newRequiredCheck, requiredCheck);

    // Add event listener to required checkbox
    newRequiredCheck.addEventListener('change', function() {
        updateConditionRequiredInArray(this);
    });
}

/**
 * Update condition value in the conditions array
 */
function updateConditionValueInArray(valueInput) {
    const condition = valueInput.closest('.condition');
    const conditionId = parseInt(condition.querySelector('.field-category-radio').name.split('-')[2]);

    // Update the condition in the array
    const conditionIndex = conditions.findIndex(c => c.id === conditionId);
    if (conditionIndex !== -1) {
        conditions[conditionIndex].value = valueInput.value;

        // Update the conditions JSON hidden input
        document.getElementById('conditions-json').value = JSON.stringify(conditions);

        // Update the pattern field (hidden) for backward compatibility
        document.getElementById('id_pattern').value = valueInput.value || '';
    }
}

/**
 * Update condition required flag in the conditions array
 */
function updateConditionRequiredInArray(requiredCheck) {
    const condition = requiredCheck.closest('.condition');
    const conditionId = parseInt(condition.querySelector('.field-category-radio').name.split('-')[2]);

    // Update the condition in the array
    const conditionIndex = conditions.findIndex(c => c.id === conditionId);
    if (conditionIndex !== -1) {
        conditions[conditionIndex].required = requiredCheck.checked;

        // Update the conditions JSON hidden input
        document.getElementById('conditions-json').value = JSON.stringify(conditions);
    }
}

/**
 * Update the condition logic in the review section
 */
function updateConditionLogicReview() {
    // Check if the element exists first (it's been removed from the UI)
    const reviewLogic = document.getElementById('review-condition-logic');
    if (!reviewLogic) {
        // Element doesn't exist, so just return without doing anything
        return;
    }

    // Get the selected logic type (with fallback to 'all' if not found)
    const logicRadio = document.querySelector('input[name="condition-logic"]:checked');
    const logicType = logicRadio ? logicRadio.value : 'all';

    // Update the text content based on the logic type
    if (logicType === 'all') {
        reviewLogic.textContent = 'Match ALL conditions (AND)';
    } else {
        reviewLogic.textContent = 'Match ANY condition (OR)';
    }
}

/**
 * Add a condition from existing data (for editing existing tags)
 */
function addConditionFromData(conditionData) {
    console.log('Adding condition from data:', conditionData);

    // Validate the condition data first
    const validatedCondition = validateConditionData(conditionData);
    console.log('Validated condition data:', validatedCondition);

    // Hide the no conditions message if it exists
    const noConditionsMsg = document.getElementById('no-conditions-message');
    if (noConditionsMsg) {
        noConditionsMsg.style.display = 'none';
    }

    // Clone the condition template
    const template = document.querySelector('.condition-template .condition').cloneNode(true);
    if (!template) {
        console.error('Could not find condition template');
        return;
    }

    // Set unique IDs for the radio buttons
    const conditionId = validatedCondition.id || conditionCounter++;
    template.querySelectorAll('.field-category-radio').forEach(radio => {
        const oldId = radio.id;
        const newId = oldId.replace('-0', `-${conditionId}`);
        radio.id = newId;
        radio.name = `field-category-${conditionId}`;

        // Update the corresponding label
        const label = template.querySelector(`label[for="${oldId}"]`);
        if (label) {
            label.setAttribute('for', newId);
        }

        // Check the radio button if it matches the field category
        if (radio.value === validatedCondition.field_category) {
            radio.checked = true;
        }
    });

    // Add event listeners
    const removeButton = template.querySelector('.remove-condition');
    if (removeButton) {
        removeButton.addEventListener('click', function() {
            removeCondition(this);
        });
    }

    // Add the condition to the active conditions container
    document.getElementById('active-conditions').appendChild(template);

    // Add to conditions array
    const conditionObj = {
        id: conditionId,
        field_category: validatedCondition.field_category,
        field: validatedCondition.field,
        field_type: validatedCondition.field_type || fieldTypeMapping[validatedCondition.field],
        operator: validatedCondition.operator,
        value: validatedCondition.value || '',
        required: validatedCondition.required !== false
    };

    conditions.push(conditionObj);
    console.log(`Added condition to array:`, conditionObj);

    // Set up the field options based on the field category
    const checkedRadio = template.querySelector('.field-category-radio:checked');
    if (checkedRadio) {
        // Set up field options
        const fieldSelect = template.querySelector('.field-select');
        if (fieldSelect) {
            // Clear existing options
            fieldSelect.innerHTML = '<option value="">Select a field</option>';

            // Add options for the selected category
            const category = checkedRadio.value;
            fieldDefinitions[category].forEach(field => {
                const option = document.createElement('option');
                option.value = field.id;
                option.textContent = field.name;
                option.dataset.fieldType = fieldTypeMapping[field.id];
                fieldSelect.appendChild(option);
            });

            // Select the field that matches our condition
            for (let i = 0; i < fieldSelect.options.length; i++) {
                if (fieldSelect.options[i].value === validatedCondition.field) {
                    fieldSelect.selectedIndex = i;
                    break;
                }
            }

            // Add event listener
            fieldSelect.addEventListener('change', function() {
                updateOperatorOptions(this);
            });

            // Set up operator options based on the field
            const operatorSelect = template.querySelector('.operator-select');
            if (operatorSelect) {
                // Clear existing options
                operatorSelect.innerHTML = '<option value="">Select an operator</option>';

                // Add options for the selected field type
                const fieldType = validatedCondition.field_type;
                if (operatorDefinitions[fieldType]) {
                    operatorDefinitions[fieldType].forEach(operator => {
                        const option = document.createElement('option');
                        option.value = operator.id;
                        option.textContent = operator.name;
                        operatorSelect.appendChild(option);
                    });

                    // Select the operator that matches our condition
                    for (let i = 0; i < operatorSelect.options.length; i++) {
                        if (operatorSelect.options[i].value === validatedCondition.operator) {
                            operatorSelect.selectedIndex = i;
                            break;
                        }
                    }

                    // Add event listener
                    operatorSelect.addEventListener('change', function() {
                        updateConditionValue(this);
                    });
                }
            }

            // Set the value input
            const valueInput = template.querySelector('.value-input');
            if (valueInput) {
                valueInput.value = validatedCondition.value || '';

                // Add event listener
                valueInput.addEventListener('input', function() {
                    updateConditionValueInArray(this);
                });
            }

            // Set the required checkbox
            const requiredCheck = template.querySelector('.required-check');
            if (requiredCheck) {
                requiredCheck.checked = validatedCondition.required !== false;

                // Add event listener
                requiredCheck.addEventListener('change', function() {
                    updateConditionRequiredInArray(this);
                });
            }
        }
    }

    // Update the conditions JSON hidden input
    const conditionsJson = document.getElementById('conditions-json');
    if (conditionsJson) {
        conditionsJson.value = JSON.stringify(conditions);
        console.log('Updated conditions_json after adding condition:', conditionsJson.value);
    }
}

/**
 * Validate that conditions are valid if they exist
 */
function validateConditions() {
    // Allow empty conditions - the user can create a tag without conditions
    if (conditions.length === 0) {
        return true;
    }

    // Check if all conditions are valid
    let valid = true;
    let invalidConditions = [];

    conditions.forEach((condition, index) => {
        if (!condition.field || !condition.operator || condition.value === '') {
            valid = false;
            invalidConditions.push(index + 1);
        }
    });

    // If there are invalid conditions, show a more detailed error message
    if (!valid) {
        const conditionNumbers = invalidConditions.join(', ');
        alert(`Please complete all fields for condition(s) ${conditionNumbers} before proceeding.`);
    }

    return valid;
}

/**
 * Load existing conditions into the condition builder
 * @param {Array|String} existingConditions - Array of condition objects or JSON string
 */
function loadExistingConditions(existingConditions) {
    console.log('Loading existing conditions:', existingConditions);

    // Reset conditions counter
    conditionCounter = 0;

    // Parse conditions if they're a string
    let conditionsArray = [];

    try {
        if (typeof existingConditions === 'string') {
            // Try to parse as JSON
            conditionsArray = JSON.parse(existingConditions);
            console.log('Parsed conditions from JSON string:', conditionsArray);
        } else if (Array.isArray(existingConditions)) {
            // Already an array
            conditionsArray = existingConditions;
        } else if (existingConditions && typeof existingConditions === 'object') {
            // Single object, wrap in array
            conditionsArray = [existingConditions];
        } else {
            console.warn('Invalid existingConditions format:', existingConditions);
            return;
        }
    } catch (e) {
        console.error('Error parsing existingConditions:', e);
        return;
    }

    // Check if we have any conditions to load
    if (!conditionsArray || conditionsArray.length === 0) {
        console.log('No existing conditions to load');
        return;
    }

    // Hide the no conditions message
    const noConditionsMsg = document.getElementById('no-conditions-message');
    if (noConditionsMsg) {
        noConditionsMsg.style.display = 'none';
    }

    // Clear existing conditions
    conditions = [];
    const activeConditionsContainer = document.getElementById('active-conditions');
    if (activeConditionsContainer) {
        activeConditionsContainer.innerHTML = '';
    }

    // Process each condition
    conditionsArray.forEach((condition, index) => {
        if (!condition) {
            console.warn(`Condition at index ${index} is null or undefined, skipping`);
            return;
        }

        console.log(`Processing condition ${index}:`, condition);

        // Ensure the condition has an ID
        if (condition.id === undefined) {
            condition.id = index;
        }

        // Make sure conditionCounter is higher than any existing ID
        if (typeof condition.id === 'number' && condition.id >= conditionCounter) {
            conditionCounter = condition.id + 1;
        }

        try {
            // Add the condition
            addConditionFromData(condition);
        } catch (error) {
            console.error(`Error adding condition ${index}:`, error);
        }
    });

    // Update the conditions JSON hidden input
    const conditionsJson = document.getElementById('conditions-json');
    if (conditionsJson) {
        conditionsJson.value = JSON.stringify(conditions);
        console.log('Updated conditions_json with existing conditions:', conditionsJson.value);
    }

    // Update the review section
    if (typeof updateReview === 'function') {
        updateReview();
    }
}

/**
 * Validate and fix condition data
 * @param {Object} condition - The condition data to validate
 * @returns {Object} - The validated condition data
 */
function validateConditionData(condition) {
    // Create a copy of the condition to avoid modifying the original
    const validatedCondition = { ...condition };

    // Validate field_category
    if (!validatedCondition.field_category || !fieldDefinitions[validatedCondition.field_category]) {
        console.warn(`Invalid field_category "${validatedCondition.field_category}", defaulting to "text"`);
        validatedCondition.field_category = 'text';
    }

    // Validate field
    if (!validatedCondition.field) {
        console.warn(`Missing field, defaulting to "bio"`);
        validatedCondition.field = 'bio';
    } else {
        // Check if the field exists in the selected category
        const fieldExists = fieldDefinitions[validatedCondition.field_category].some(f => f.id === validatedCondition.field);
        if (!fieldExists) {
            console.warn(`Field "${validatedCondition.field}" not found in category "${validatedCondition.field_category}"`);

            // Try to find the field in any category
            let foundInCategory = null;
            for (const category in fieldDefinitions) {
                if (fieldDefinitions[category].some(f => f.id === validatedCondition.field)) {
                    foundInCategory = category;
                    break;
                }
            }

            if (foundInCategory) {
                console.log(`Found field "${validatedCondition.field}" in category "${foundInCategory}", updating field_category`);
                validatedCondition.field_category = foundInCategory;
            } else {
                // If field not found in any category, default to "bio"
                console.warn(`Field "${validatedCondition.field}" not found in any category, defaulting to "bio"`);
                validatedCondition.field = 'bio';
                validatedCondition.field_category = 'text';
            }
        }
    }

    // Validate field_type
    if (!validatedCondition.field_type) {
        console.warn(`Missing field_type for field "${validatedCondition.field}"`);
        // Try to get field_type from fieldTypeMapping
        if (fieldTypeMapping[validatedCondition.field]) {
            validatedCondition.field_type = fieldTypeMapping[validatedCondition.field];
            console.log(`Set field_type to "${validatedCondition.field_type}" based on fieldTypeMapping`);
        } else {
            // Default to string if field_type not found
            console.warn(`Field type not found for field "${validatedCondition.field}", defaulting to "string"`);
            validatedCondition.field_type = 'string';
        }
    } else if (!operatorDefinitions[validatedCondition.field_type]) {
        console.warn(`Invalid field_type "${validatedCondition.field_type}"`);
        // Try to get field_type from fieldTypeMapping
        if (fieldTypeMapping[validatedCondition.field]) {
            validatedCondition.field_type = fieldTypeMapping[validatedCondition.field];
            console.log(`Set field_type to "${validatedCondition.field_type}" based on fieldTypeMapping`);
        } else {
            // Default to string if field_type not found
            console.warn(`Field type not found for field "${validatedCondition.field}", defaulting to "string"`);
            validatedCondition.field_type = 'string';
        }
    }

    // Validate operator
    if (!validatedCondition.operator) {
        console.warn(`Missing operator, defaulting to first operator for field type "${validatedCondition.field_type}"`);
        if (operatorDefinitions[validatedCondition.field_type] && operatorDefinitions[validatedCondition.field_type].length > 0) {
            validatedCondition.operator = operatorDefinitions[validatedCondition.field_type][0].id;
        } else {
            // Default to "icontains" if no operators found for field type
            console.warn(`No operators found for field type "${validatedCondition.field_type}", defaulting to "icontains"`);
            validatedCondition.operator = 'icontains';
        }
    } else {
        // Check if the operator exists for the field type
        const operatorExists = operatorDefinitions[validatedCondition.field_type] &&
                              operatorDefinitions[validatedCondition.field_type].some(o => o.id === validatedCondition.operator);
        if (!operatorExists) {
            console.warn(`Operator "${validatedCondition.operator}" not found for field type "${validatedCondition.field_type}"`);
            // Default to first operator for field type
            if (operatorDefinitions[validatedCondition.field_type] && operatorDefinitions[validatedCondition.field_type].length > 0) {
                validatedCondition.operator = operatorDefinitions[validatedCondition.field_type][0].id;
            } else {
                // Default to "icontains" if no operators found for field type
                console.warn(`No operators found for field type "${validatedCondition.field_type}", defaulting to "icontains"`);
                validatedCondition.operator = 'icontains';
            }
        }
    }

    // Validate value
    if (validatedCondition.value === undefined || validatedCondition.value === null) {
        console.warn(`Missing value, defaulting to empty string`);
        validatedCondition.value = '';
    }

    // Validate required
    if (validatedCondition.required === undefined || validatedCondition.required === null) {
        console.warn(`Missing required flag, defaulting to true`);
        validatedCondition.required = true;
    }

    return validatedCondition;
}

/**
 * Update the UI for a specific condition
 * @param {number} conditionId - The ID of the condition to update
 */
function updateConditionUI(conditionId) {
    try {
        // Find the condition in the array
        const conditionIndex = conditions.findIndex(c => c.id === conditionId);
        if (conditionIndex === -1) {
            console.warn(`Condition with ID ${conditionId} not found`);
            return;
        }

        const condition = conditions[conditionIndex];
        console.log(`Updating UI for condition ${conditionId}:`, condition);

        // Find the condition element in the DOM
        const conditionElements = document.querySelectorAll('#active-conditions .condition');
        let conditionElement = null;

        for (let i = 0; i < conditionElements.length; i++) {
            const element = conditionElements[i];
            const elementId = parseInt(element.querySelector('.field-category-radio').name.split('-')[2]);
            if (elementId === conditionId) {
                conditionElement = element;
                break;
            }
        }

        if (!conditionElement) {
            console.warn(`Condition element with ID ${conditionId} not found in the DOM`);
            return;
        }

        // Make sure field_category is valid
        if (!condition.field_category || !fieldDefinitions[condition.field_category]) {
            // Default to 'text' if field_category is invalid
            console.warn(`Invalid field_category "${condition.field_category}" for condition ${conditionId}, defaulting to "text"`);
            condition.field_category = 'text';
            conditions[conditionIndex].field_category = 'text';
        }

        // Make sure field is valid for the category
        if (condition.field) {
            const fieldExists = fieldDefinitions[condition.field_category].some(f => f.id === condition.field);
            if (!fieldExists) {
                console.warn(`Field "${condition.field}" not found in category "${condition.field_category}" for condition ${conditionId}`);
                // Try to find the field in any category
                let foundInCategory = null;
                for (const category in fieldDefinitions) {
                    if (fieldDefinitions[category].some(f => f.id === condition.field)) {
                        foundInCategory = category;
                        break;
                    }
                }

                if (foundInCategory) {
                    console.log(`Found field "${condition.field}" in category "${foundInCategory}", updating field_category`);
                    condition.field_category = foundInCategory;
                    conditions[conditionIndex].field_category = foundInCategory;
                }
            }
        }

        // Make sure field_type is valid
        if (condition.field && !condition.field_type) {
            console.warn(`Missing field_type for field "${condition.field}" in condition ${conditionId}`);
            // Try to get field_type from fieldTypeMapping
            if (fieldTypeMapping[condition.field]) {
                condition.field_type = fieldTypeMapping[condition.field];
                conditions[conditionIndex].field_type = fieldTypeMapping[condition.field];
                console.log(`Set field_type to "${condition.field_type}" based on fieldTypeMapping`);
            }
        }

        // Update field category radio buttons
        const fieldCategoryRadios = conditionElement.querySelectorAll('.field-category-radio');
        fieldCategoryRadios.forEach(radio => {
            if (radio.value === condition.field_category) {
                radio.checked = true;
            }

            // Remove existing event listeners to prevent duplicates
            const newRadio = radio.cloneNode(true);
            radio.parentNode.replaceChild(newRadio, radio);

            // Add event listener for field category radio
            newRadio.addEventListener('change', function() {
                updateFieldOptions(this);
            });
        });

        // Update field options
        const checkedRadio = conditionElement.querySelector('.field-category-radio:checked');
        if (checkedRadio) {
            // We need to manually update the field options without clearing the condition data
            const fieldSelect = conditionElement.querySelector('.field-select');
            if (fieldSelect) {
                const category = checkedRadio.value;

                // Clear existing options
                fieldSelect.innerHTML = '<option value="">Select a field</option>';

                // Add new options based on the category
                fieldDefinitions[category].forEach(field => {
                    const option = document.createElement('option');
                    option.value = field.id;
                    option.textContent = field.name;
                    option.dataset.fieldType = fieldTypeMapping[field.id];
                    fieldSelect.appendChild(option);
                });

                // Now select the field that matches our condition
                let fieldFound = false;
                for (let i = 0; i < fieldSelect.options.length; i++) {
                    if (fieldSelect.options[i].value === condition.field) {
                        fieldSelect.selectedIndex = i;
                        fieldFound = true;
                        break;
                    }
                }

                // If field not found in options, select the first non-empty option
                if (!fieldFound && fieldSelect.options.length > 1) {
                    console.warn(`Field "${condition.field}" not found in options for condition ${conditionId}, selecting first option`);
                    fieldSelect.selectedIndex = 1;
                    // Update the condition with the selected field
                    const selectedField = fieldSelect.options[1].value;
                    condition.field = selectedField;
                    conditions[conditionIndex].field = selectedField;

                    // Get field type from fieldTypeMapping, default to 'string' if not found
                    let fieldType = fieldTypeMapping[selectedField];
                    if (!fieldType) {
                        console.warn(`Field type not found for field "${selectedField}", defaulting to "string"`);
                        fieldType = 'string';
                    }

                    condition.field_type = fieldType;
                    conditions[conditionIndex].field_type = fieldType;
                    console.log(`Updated condition ${conditionId} field to "${selectedField}" and field_type to "${fieldType}"`);
                }

                // Remove existing event listeners to prevent duplicates
                const newFieldSelect = fieldSelect.cloneNode(true);
                fieldSelect.parentNode.replaceChild(newFieldSelect, fieldSelect);

                // Add event listener for field select
                newFieldSelect.addEventListener('change', function() {
                    updateOperatorOptions(this);
                });
            }
        } else {
            console.warn(`No checked radio button found for condition ${conditionId}`);
            // Force check the first radio button
            if (fieldCategoryRadios.length > 0) {
                fieldCategoryRadios[0].checked = true;
                updateFieldOptions(fieldCategoryRadios[0]);
            }
        }

        // Update operator options
        const fieldSelect = conditionElement.querySelector('.field-select');
        if (fieldSelect) {
            // Make sure field_type is set before calling updateOperatorOptions
            if (!condition.field_type) {
                console.warn(`Field type not set for condition ${conditionId}, defaulting to "string"`);
                condition.field_type = 'string';
                conditions[conditionIndex].field_type = 'string';
            }

            // We need to manually update the operator options without clearing the condition data
            const operatorSelect = conditionElement.querySelector('.operator-select');
            if (operatorSelect) {
                // Clear existing options
                operatorSelect.innerHTML = '<option value="">Select an operator</option>';

                // Check if operatorDefinitions has the field type
                let fieldType = condition.field_type;
                if (!operatorDefinitions[fieldType]) {
                    console.warn(`Operator definitions not found for field type "${fieldType}", defaulting to "string"`);
                    fieldType = 'string';
                    condition.field_type = 'string';
                    conditions[conditionIndex].field_type = 'string';
                }

                // Add new options based on the field type
                operatorDefinitions[fieldType].forEach(operator => {
                    const option = document.createElement('option');
                    option.value = operator.id;
                    option.textContent = operator.name;
                    operatorSelect.appendChild(option);
                });

                // Now select the operator that matches our condition
                let operatorFound = false;
                for (let i = 0; i < operatorSelect.options.length; i++) {
                    if (operatorSelect.options[i].value === condition.operator) {
                        operatorSelect.selectedIndex = i;
                        operatorFound = true;
                        break;
                    }
                }

                // If operator not found in options, select the first non-empty option
                if (!operatorFound && operatorSelect.options.length > 1) {
                    console.warn(`Operator "${condition.operator}" not found in options for condition ${conditionId}, selecting first option`);
                    operatorSelect.selectedIndex = 1;
                    // Update the condition with the selected operator
                    condition.operator = operatorSelect.options[1].value;
                    conditions[conditionIndex].operator = operatorSelect.options[1].value;
                }

                // Remove existing event listeners to prevent duplicates
                const newOperatorSelect = operatorSelect.cloneNode(true);
                operatorSelect.parentNode.replaceChild(newOperatorSelect, operatorSelect);

                // Add change event listener to operator select
                newOperatorSelect.addEventListener('change', function() {
                    updateConditionValue(this);
                });
            }
        }

        // Set value input
        const valueInput = conditionElement.querySelector('.value-input');
        if (valueInput) {
            valueInput.value = condition.value || '';

            // Remove existing event listeners to prevent duplicates
            const newValueInput = valueInput.cloneNode(true);
            valueInput.parentNode.replaceChild(newValueInput, valueInput);

            // Add event listener for value input
            newValueInput.addEventListener('input', function() {
                updateConditionValueInArray(this);
            });
        } else {
            console.warn(`Value input not found for condition ${conditionId}`);
        }

        // Set required checkbox
        const requiredCheck = conditionElement.querySelector('.required-check');
        if (requiredCheck) {
            requiredCheck.checked = condition.required !== false;

            // Remove existing event listeners to prevent duplicates
            const newRequiredCheck = requiredCheck.cloneNode(true);
            requiredCheck.parentNode.replaceChild(newRequiredCheck, requiredCheck);

            // Add event listener for required checkbox
            newRequiredCheck.addEventListener('change', function() {
                updateConditionRequiredInArray(this);
            });
        } else {
            console.warn(`Required checkbox not found for condition ${conditionId}`);
        }

        // Update the conditions JSON hidden input
        document.getElementById('conditions-json').value = JSON.stringify(conditions);
    } catch (error) {
        console.error(`Error updating condition UI for condition ${conditionId}:`, error);
    }
}

/**
 * Update the review section with the current conditions
 */
function updateReview() {
    // Update condition logic (if element exists)
    updateConditionLogicReview();

    // Update conditions review
    const reviewConditions = document.getElementById('review-conditions');
    if (!reviewConditions) return;

    reviewConditions.innerHTML = '';

    if (conditions.length === 0) {
        reviewConditions.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>
                No conditions added. This tag will not have any specific matching criteria.
            </div>
        `;

        // Update tag description preview for no conditions
        const tagDescriptionPreview = document.getElementById('tag-description-preview');
        if (tagDescriptionPreview) {
            tagDescriptionPreview.innerHTML = `
                <i class="fas fa-info-circle me-1"></i> This tag has no conditions and will not automatically match any accounts.
            `;
        }

        return;
    }

    conditions.forEach((condition, index) => {
        // Find field and operator details
        const fieldCategory = condition.field_category;
        const fieldDef = fieldDefinitions[fieldCategory].find(f => f.id === condition.field);
        const operatorDef = operatorDefinitions[condition.field_type]?.find(o => o.id === condition.operator);

        if (!fieldDef || !operatorDef) return;

        const conditionCard = document.createElement('div');
        conditionCard.className = 'card mb-3';
        conditionCard.innerHTML = `
            <div class="card-header bg-light">
                <h6 class="mb-0">Condition ${index + 1} ${condition.required ? '(Required)' : '(Optional)'}</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <strong>Field:</strong>
                    </div>
                    <div class="col-md-8">
                        <i class="fas ${fieldDef.icon} ${fieldDef.iconColor} me-1"></i> ${fieldDef.name}
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-4">
                        <strong>Operator:</strong>
                    </div>
                    <div class="col-md-8">
                        <i class="fas ${operatorDef.icon} ${operatorDef.iconColor} me-1"></i> ${operatorDef.name}
                    </div>
                </div>
                <div class="row mt-2">
                    <div class="col-md-4">
                        <strong>Value:</strong>
                    </div>
                    <div class="col-md-8">
                        ${condition.value}
                    </div>
                </div>
            </div>
        `;

        reviewConditions.appendChild(conditionCard);
    });

    // Update tag description preview
    const tagDescriptionPreview = document.getElementById('tag-description-preview');
    if (!tagDescriptionPreview) return;

    // Get the selected logic type (with fallback to 'all' if not found)
    const logicRadio = document.querySelector('input[name="condition-logic"]:checked');
    const logicType = logicRadio ? logicRadio.value : 'all';

    if (conditions.length === 1) {
        const condition = conditions[0];
        // Safely get field and operator definitions
        const fieldCategory = condition.field_category || 'text';
        const fieldDef = fieldDefinitions[fieldCategory]?.find(f => f.id === condition.field);
        const fieldType = condition.field_type || 'string';
        const operatorDef = operatorDefinitions[fieldType]?.find(o => o.id === condition.operator);

        if (fieldDef && operatorDef) {
            tagDescriptionPreview.innerHTML = `
                <i class="fas fa-info-circle me-1"></i> This tag will match accounts with "${condition.value}"
                ${operatorDef.name.toLowerCase()} in their ${fieldDef.name.toLowerCase()}.
            `;
        } else {
            // Fallback if definitions aren't found
            tagDescriptionPreview.innerHTML = `
                <i class="fas fa-info-circle me-1"></i> This tag will match accounts based on the condition you've defined.
            `;
        }
    } else {
        tagDescriptionPreview.innerHTML = `
            <i class="fas fa-info-circle me-1"></i> This tag will match accounts that meet
            ${logicType === 'all' ? 'all' : 'any'} of the ${conditions.length} conditions you've defined.
        `;
    }
}

/**
 * Test if a condition matches the test input
 */
function testCondition(condition, testInput) {
    const field = condition.field;
    const operator = condition.operator;
    const value = condition.value;

    // For demo purposes, we'll just test against the input text
    // In a real implementation, this would check against the appropriate field

    switch (operator) {
        // Case-insensitive string operators
        case 'icontains':
            return testInput.toLowerCase().includes(value.toLowerCase());
        case 'iexact':
            return testInput.toLowerCase() === value.toLowerCase();
        case 'istartswith':
            return testInput.toLowerCase().startsWith(value.toLowerCase());
        case 'iendswith':
            return testInput.toLowerCase().endsWith(value.toLowerCase());
        case 'not_icontains':
            return !testInput.toLowerCase().includes(value.toLowerCase());
        case 'iregex':
            try {
                const regex = new RegExp(value, 'i');
                return regex.test(testInput);
            } catch (e) {
                return false;
            }

        // Case-sensitive string operators
        case 'contains':
            return testInput.includes(value);
        case 'exact':
            return testInput === value;
        case 'startswith':
            return testInput.startsWith(value);
        case 'endswith':
            return testInput.endsWith(value);
        case 'not_contains':
            return !testInput.includes(value);
        case 'regex':
            try {
                const regex = new RegExp(value);
                return regex.test(testInput);
            } catch (e) {
                return false;
            }

        // Numeric operators
        case 'eq':
            return parseFloat(testInput) === parseFloat(value);
        case 'gt':
            return parseFloat(testInput) > parseFloat(value);
        case 'gte':
            return parseFloat(testInput) >= parseFloat(value);
        case 'lt':
            return parseFloat(testInput) < parseFloat(value);
        case 'lte':
            return parseFloat(testInput) <= parseFloat(value);

        // Array operators
        case 'length_gt':
            return testInput.length > parseFloat(value);
        case 'length_lt':
            return testInput.length < parseFloat(value);

        // Boolean operators
        case 'is_true':
            return testInput.toLowerCase() === 'true';
        case 'is_false':
            return testInput.toLowerCase() === 'false';
        default:
            return false;
    }
}

/**
 * Test all conditions against the test input
 */
function testAllConditions() {
    console.log("Testing conditions...");

    // Get the test input, results container, and message element
    const testInput = document.getElementById('test-input').value.trim();
    const testResults = document.getElementById('test-results');
    const testResultMessage = document.getElementById('test-result-message');

    // Make sure we have the required elements
    if (!testResults || !testResultMessage) {
        console.error("Test results elements not found");
        alert("Error: Test results elements not found. Please refresh the page and try again.");
        return;
    }

    // Check if test input is provided
    if (!testInput) {
        testResultMessage.textContent = 'Please enter some test input.';
        testResults.className = 'mt-3 alert alert-warning';
        testResults.style.display = 'block';
        return;
    }

    // Check if we have conditions to test
    if (!conditions || conditions.length === 0) {
        testResultMessage.textContent = 'Please add at least one condition to test.';
        testResults.className = 'mt-3 alert alert-warning';
        testResults.style.display = 'block';
        return;
    }

    console.log(`Testing ${conditions.length} conditions against input: "${testInput}"`);

    // Test each condition
    const matchingConditions = [];
    const nonMatchingConditions = [];

    conditions.forEach(condition => {
        try {
            if (testCondition(condition, testInput)) {
                matchingConditions.push(condition);
                console.log(`Condition matched: ${condition.field} ${condition.operator} "${condition.value}"`);
            } else {
                nonMatchingConditions.push(condition);
                console.log(`Condition not matched: ${condition.field} ${condition.operator} "${condition.value}"`);
            }
        } catch (error) {
            console.error(`Error testing condition:`, condition, error);
            nonMatchingConditions.push(condition);
        }
    });

    // Determine overall match based on condition logic
    // Default to 'all' if the radio button doesn't exist or isn't checked
    const logicRadio = document.querySelector('input[name="condition-logic"]:checked');
    const logicType = logicRadio ? logicRadio.value : 'all';
    const isMatch = logicType === 'all'
        ? matchingConditions.length === conditions.length
        : matchingConditions.length > 0;

    // Update the test results
    if (isMatch) {
        testResultMessage.innerHTML = `<strong>Match!</strong> The input matches ${matchingConditions.length} of ${conditions.length} conditions.`;
        testResults.className = 'mt-3 alert alert-success';
    } else {
        testResultMessage.innerHTML = `<strong>No match.</strong> The input matches ${matchingConditions.length} of ${conditions.length} conditions.`;
        testResults.className = 'mt-3 alert alert-danger';
    }

    // Add details about which conditions matched
    let details = '<ul class="mt-2 mb-0">';
    conditions.forEach((condition, index) => {
        try {
            // Get field and operator definitions safely
            const fieldCategory = condition.field_category || 'text';
            const fieldDef = fieldDefinitions[fieldCategory]?.find(f => f.id === condition.field) ||
                             { name: condition.field || 'Unknown field', icon: 'fa-question', iconColor: 'text-secondary' };

            const fieldType = condition.field_type || 'string';
            const operatorDef = operatorDefinitions[fieldType]?.find(o => o.id === condition.operator) ||
                               { name: condition.operator || 'Unknown operator', icon: 'fa-question', iconColor: 'text-secondary' };

            const isMatched = matchingConditions.includes(condition);
            details += `<li class="${isMatched ? 'text-success' : 'text-danger'}">
                <i class="fas ${isMatched ? 'fa-check' : 'fa-times'} me-1"></i>
                Condition ${index + 1}: ${fieldDef.name} ${operatorDef.name.toLowerCase()} "${condition.value || ''}"
            </li>`;
        } catch (error) {
            console.error(`Error generating condition details:`, condition, error);
            details += `<li class="text-warning">
                <i class="fas fa-exclamation-triangle me-1"></i>
                Condition ${index + 1}: Error displaying details
            </li>`;
        }
    });
    details += '</ul>';

    testResultMessage.innerHTML += details;
    testResults.style.display = 'block';

    console.log("Test results displayed");
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    try {
        console.log('DOM loaded, initializing tag condition builder');

        // Initialize the condition builder
        if (typeof initConditionBuilder === 'function') {
            initConditionBuilder();
        } else {
            console.error('initConditionBuilder function not found');
        }

        // Check for existing conditions in the global variable
        if (typeof window.existingConditions !== 'undefined' && window.existingConditions) {
            console.log('Found window.existingConditions:', window.existingConditions);

            // Load existing conditions
            if (typeof loadExistingConditions === 'function') {
                loadExistingConditions(window.existingConditions);
            } else {
                console.error('loadExistingConditions function not found');
            }
        } else {
            // Make sure the conditions JSON is properly initialized
            const conditionsJson = document.getElementById('conditions-json');
            if (conditionsJson && conditionsJson.value && conditionsJson.value !== '[]') {
                console.log('Found conditions in conditions-json input:', conditionsJson.value);

                try {
                    const parsedConditions = JSON.parse(conditionsJson.value);
                    if (Array.isArray(parsedConditions) && parsedConditions.length > 0) {
                        console.log('Parsed conditions from JSON:', parsedConditions);

                        // Load existing conditions
                        if (typeof loadExistingConditions === 'function') {
                            loadExistingConditions(parsedConditions);
                        } else {
                            console.error('loadExistingConditions function not found');
                        }
                    }
                } catch (e) {
                    console.error('Error parsing conditions JSON:', e);
                }
            } else {
                console.log('No existing conditions found in conditions-json input');
            }
        }

        // Add event listener for the test pattern button
        const testPatternBtn = document.getElementById('test-pattern-btn');
        if (testPatternBtn) {
            testPatternBtn.addEventListener('click', function() {
                if (typeof testAllConditions === 'function') {
                    testAllConditions();
                } else {
                    console.error('testAllConditions function not found');
                }
            });
        }
    } catch (error) {
        // Log any errors that occur during initialization
        console.error('Error initializing tag condition builder:', error);
    }
});
