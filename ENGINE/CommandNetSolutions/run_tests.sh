#!/bin/bash

# Script to run tests for the Campaign Analysis System
# Usage: ./run_tests.sh [unit|integration|all]

# Activate virtual environment
source venv/bin/activate

# Set default test type
TEST_TYPE=${1:-all}

# Set coverage options
COVERAGE_OPTIONS="--source=campaigns"

# Run tests based on type
case $TEST_TYPE in
    unit)
        echo "Running unit tests..."
        python -m coverage run $COVERAGE_OPTIONS manage.py test campaigns.tests.test_pyflow_service campaigns.tests.test_notification_service campaigns.tests.test_external_api_service
        ;;
    integration)
        echo "Running integration tests..."
        python -m coverage run $COVERAGE_OPTIONS manage.py test campaigns.tests.test_integration
        ;;
    all)
        echo "Running all tests..."
        python -m coverage run $COVERAGE_OPTIONS manage.py test campaigns.tests
        ;;
    *)
        echo "Invalid test type: $TEST_TYPE"
        echo "Usage: ./run_tests.sh [unit|integration|all]"
        exit 1
        ;;
esac

# Generate coverage report
python -m coverage report
python -m coverage html

echo "Coverage report generated in htmlcov/index.html"
