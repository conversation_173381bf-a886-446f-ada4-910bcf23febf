{% load static %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

<div class="widget-container">
    <div class="widget-header">
        <h3>Recent Campaigns</h3>
        <a href="/campaigns/" class="view-all-link">View All</a>
    </div>
    <div class="widget-content">
        {% if recent_campaigns %}
        <div class="table-responsive">
            <table class="table">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for campaign in recent_campaigns %}
                    <tr>
                        <td>
                            <div class="campaign-name">{{ campaign.name }}</div>
                        </td>
                        <td>
                            <span class="badge">
                                {{ campaign.get_target_type_display }}
                            </span>
                        </td>
                        <td>
                            <span class="status-badge status-{{ campaign.status }}">
                                {{ campaign.get_status_display }}
                            </span>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="/campaigns/{{ campaign.id }}/" class="btn btn-sm btn-primary action-btn" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="/campaigns/{{ campaign.id }}/update/" class="btn btn-sm btn-secondary action-btn" title="Edit Campaign">
                                    <i class="fas fa-edit"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="empty-state">
            <p>No campaigns created yet.</p>
            <a href="/campaigns/create/" class="btn btn-primary">Create Campaign</a>
        </div>
        {% endif %}
    </div>
</div>

<style>
    .widget-container {
        background-color: #fff;
        border-radius: 6px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
    }

    .widget-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
    }

    .widget-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
    }

    .view-all-link {
        font-size: 14px;
        color: #3498db;
        text-decoration: none;
    }

    .widget-content {
        padding: 15px 20px;
    }

    .table {
        width: 100%;
        border-collapse: collapse;
    }

    .table th {
        text-align: left;
        padding: 10px;
        border-bottom: 1px solid #eee;
        font-weight: 600;
        font-size: 14px;
    }

    .table td {
        padding: 10px;
        border-bottom: 1px solid #eee;
        font-size: 14px;
    }

    .campaign-name {
        font-weight: 500;
    }

    .badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        background-color: #f8f9fa;
        color: #333;
        font-size: 12px;
    }

    .status-badge {
        display: inline-block;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-draft {
        background-color: #e0e0e0;
        color: #555;
    }

    .status-pending {
        background-color: #fff3cd;
        color: #856404;
    }

    .status-running {
        background-color: #d1ecf1;
        color: #0c5460;
    }

    .status-completed {
        background-color: #d4edda;
        color: #155724;
    }

    .action-buttons {
        display: flex;
        gap: 5px;
    }

    .action-btn {
        width: 40px !important;
        min-width: 40px !important;
        padding: 5px !important;
    }

    .btn {
        display: inline-block;
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 13px;
        cursor: pointer;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.2s ease;
        border: none;
    }

    .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .btn-primary {
        background-color: #3498db;
        color: white;
    }

    .btn-primary:hover {
        background-color: #2980b9;
        color: white;
    }

    .btn-secondary {
        background-color: #95a5a6;
        color: white;
    }

    .btn-secondary:hover {
        background-color: #7f8c8d;
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 20px;
    }

    .empty-state p {
        margin-bottom: 15px;
        color: #7f8c8d;
    }
</style>
