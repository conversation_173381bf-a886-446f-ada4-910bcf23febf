# Generated by Django 4.2.16 on 2025-04-27 14:43

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('instagram', '0002_remove_scoringprofilerule_multiplier_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='accounts',
            name='campaign_id',
            field=models.CharField(blank=True, max_length=36, null=True, help_text="ID of the campaign that collected this account"),
        ),
        migrations.AddField(
            model_name='accounts',
            name='collection_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
    ]
