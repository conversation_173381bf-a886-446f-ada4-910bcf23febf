#!/usr/bin/env python
"""
Test script to verify campaign creation and deletion fixes.
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CommandNetSolutions.settings.dev')
django.setup()

from campaigns.models import Campaign
from django.contrib.auth.models import User


def test_campaign_creation():
    """Test campaign creation with creator field."""
    print("🧪 Testing Campaign Creation...")
    
    # Get a user to be the creator
    user = User.objects.first()
    if not user:
        print("❌ No users found in the system")
        return False
        
    print(f"   Using user: {user.username} (ID: {user.id})")
    
    try:
        campaign = Campaign.objects.create(
            name='Test Campaign Creation Fix',
            description='Testing if the creator field issue is fixed',
            creator=user,
            status='draft',
            target_type='location',
            audience_type='profile'
        )
        print(f"✅ Campaign created successfully!")
        print(f"   ID: {campaign.id}")
        print(f"   Name: {campaign.name}")
        print(f"   Creator: {campaign.creator}")
        print(f"   Status: {campaign.status}")
        return campaign
        
    except Exception as e:
        print(f"❌ Campaign creation failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_campaign_creation_without_creator():
    """Test campaign creation without creator (should work with nullable field)."""
    print("\n🧪 Testing Campaign Creation Without Creator...")
    
    try:
        campaign = Campaign.objects.create(
            name='Test Campaign No Creator',
            description='Testing campaign creation without creator',
            creator=None,
            status='draft',
            target_type='username',
            audience_type='profile'
        )
        print(f"✅ Campaign created successfully without creator!")
        print(f"   ID: {campaign.id}")
        print(f"   Name: {campaign.name}")
        print(f"   Creator: {campaign.creator}")
        return campaign
        
    except Exception as e:
        print(f"❌ Campaign creation without creator failed: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_campaign_deletion(campaign):
    """Test campaign deletion."""
    if not campaign:
        print("\n⏭️  Skipping deletion test (no campaign to delete)")
        return False
        
    print(f"\n🧪 Testing Campaign Deletion...")
    print(f"   Deleting campaign: {campaign.id} - {campaign.name}")
    
    try:
        campaign_id = campaign.id
        campaign_name = campaign.name
        campaign.delete()
        print(f"✅ Campaign deleted successfully: {campaign_id} - {campaign_name}")
        return True
        
    except Exception as e:
        print(f"❌ Campaign deletion failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all tests."""
    print("🚀 Starting Campaign Database Fixes Test\n")
    
    # Test 1: Campaign creation with creator
    campaign1 = test_campaign_creation()
    
    # Test 2: Campaign creation without creator
    campaign2 = test_campaign_creation_without_creator()
    
    # Test 3: Campaign deletion
    success1 = test_campaign_deletion(campaign1)
    success2 = test_campaign_deletion(campaign2)
    
    # Summary
    print("\n📊 Test Summary:")
    print(f"   Campaign creation with creator: {'✅ PASS' if campaign1 else '❌ FAIL'}")
    print(f"   Campaign creation without creator: {'✅ PASS' if campaign2 else '❌ FAIL'}")
    print(f"   Campaign deletion (with creator): {'✅ PASS' if success1 else '❌ FAIL'}")
    print(f"   Campaign deletion (without creator): {'✅ PASS' if success2 else '❌ FAIL'}")
    
    all_passed = all([campaign1, campaign2, success1, success2])
    print(f"\n🎯 Overall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    return all_passed


if __name__ == '__main__':
    main()
