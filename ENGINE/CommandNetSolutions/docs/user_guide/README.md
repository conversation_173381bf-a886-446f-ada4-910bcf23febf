# Campaign Analysis System User Guide

This guide provides detailed instructions for using the Campaign Analysis System, including creating campaigns, analyzing accounts, and interpreting results.

## Table of Contents

1. [Getting Started](#getting-started)
2. [Creating Campaigns](#creating-campaigns)
3. [Managing Targets](#managing-targets)
4. [Launching Campaigns](#launching-campaigns)
5. [Analyzing Accounts](#analyzing-accounts)
6. [Working with Tags](#working-with-tags)
7. [Understanding Results](#understanding-results)
8. [ROI Tracking](#roi-tracking)
9. [Notifications](#notifications)
10. [External API Integration](#external-api-integration)
11. [Troubleshooting](#troubleshooting)

## Getting Started

### System Requirements

- Modern web browser (Chrome, Firefox, Safari, Edge)
- Internet connection

### Accessing the System

1. Open your web browser
2. Navigate to the Campaign Analysis System URL
3. Log in with your username and password

### Dashboard Overview

The dashboard provides an overview of your campaigns, including:

- Active campaigns
- Recent results
- Performance metrics
- Notifications

## Creating Campaigns

### Creating a New Campaign

1. Click the "New Campaign" button on the dashboard
2. Enter a name and description for your campaign
3. Select the target type:
   - **Location Based**: Target accounts based on Instagram locations
   - **Username Based**: Target specific usernames or their followers/following
4. Select the audience type:
   - **Profile Only**: Target only the profile
   - **Followers**: Target followers of the profile
   - **Following**: Target accounts the profile is following
   - **Both**: Target both followers and following
5. Click "Create Campaign"

### Campaign Settings

After creating a campaign, you can configure additional settings:

1. Navigate to the campaign details page
2. Click the "Settings" tab
3. Configure the following settings:
   - **Analysis Settings**: Configure how accounts are analyzed
   - **ROI Settings**: Set up cost tracking and ROI calculation
   - **Notification Settings**: Configure notifications for campaign events

## Managing Targets

### Adding Location Targets

1. Navigate to the campaign details page
2. Click the "Targets" tab
3. Click "Add Location Target"
4. Search for a location by country and city
5. Select the location from the results
6. Click "Add Target"

### Adding Username Targets

1. Navigate to the campaign details page
2. Click the "Targets" tab
3. Click "Add Username Target"
4. Enter the username
5. Select the audience type:
   - **Profile Only**: Target only the profile
   - **Followers**: Target followers of the profile
   - **Following**: Target accounts the profile is following
   - **Both**: Target both followers and following
6. Click "Add Target"

### Managing Existing Targets

1. Navigate to the campaign details page
2. Click the "Targets" tab
3. View the list of targets
4. To remove a target, click the "Remove" button next to it

## Launching Campaigns

### Preparing for Launch

Before launching a campaign, ensure:

1. You have added at least one target
2. You have configured analysis settings
3. You have sufficient resources for the campaign

### Launching a Campaign

1. Navigate to the campaign details page
2. Click the "Launch" button
3. Select the launch method:
   - **Airflow**: Use Airflow for workflow management (default)
   - **PyFlow**: Use PyFlow for workflow management
4. Click "Launch Campaign"

### Monitoring Campaign Progress

1. Navigate to the campaign details page
2. View the "Status" section for current status
3. Check the "Workflows" tab for detailed workflow status
4. Monitor notifications for campaign status updates

## Analyzing Accounts

### Configuring Analysis Settings

1. Navigate to the campaign details page
2. Click the "Analysis Settings" tab
3. Configure the following settings:
   - **Auto Analyze**: Enable/disable automatic analysis after collection
   - **Analysis Frequency**: Set how often analysis should run
   - **Analysis Mode**: Choose between basic, advanced, or comprehensive analysis
   - **Follower Range**: Set minimum and maximum follower counts
   - **Tags**: Select tags to use for analysis
   - **Dynamic Tagging**: Enable/disable dynamic tag generation
   - **ML Analysis**: Enable/disable machine learning analysis
   - **NLP Analysis**: Enable/disable natural language processing

### Running Analysis

1. Navigate to the campaign details page
2. Click the "Analyze" button
3. Select the analysis method:
   - **Internal**: Use internal analysis engine
   - **PyFlow**: Use PyFlow for analysis
4. Configure analysis options (if needed)
5. Click "Start Analysis"

### Monitoring Analysis Progress

1. Navigate to the campaign details page
2. Check the "Analysis" tab for progress
3. Monitor notifications for analysis completion

## Working with Tags

### Creating Tags

1. Navigate to the "Tags" section
2. Click "New Tag"
3. Enter the following information:
   - **Name**: Tag name
   - **Description**: Tag description
   - **Category**: Select a category for the tag
   - **Tag Type**: Choose between keyword, regex, sentiment, category, ML, or NLP
   - **Pattern**: Enter the pattern to match
   - **Field**: Select the account field to analyze
   - **Is Global**: Enable/disable global availability
4. Click "Create Tag"

### Using the Tag Builder

1. Navigate to the "Tag Builder" section
2. Enter a sample text to test against
3. Select the field type (bio, interests, etc.)
4. Create a new tag or select an existing one
5. Test the tag against the sample text
6. Adjust the tag pattern based on results
7. Save the tag when satisfied

### Managing Tag Categories

1. Navigate to the "Tag Categories" section
2. View the category tree
3. Create new categories or edit existing ones
4. Organize tags into appropriate categories

## Understanding Results

### Viewing Campaign Results

1. Navigate to the campaign details page
2. Click the "Results" tab
3. View the following information:
   - **Accounts Found**: Total number of accounts found
   - **Accounts Processed**: Number of accounts processed
   - **Accounts Pending**: Number of accounts waiting to be processed
   - **Accounts Tagged**: Number of accounts that received at least one tag
   - **Accounts Whitelisted**: Number of accounts added to whitelist
   - **Average Confidence Score**: Average confidence score across all tag matches
   - **Analysis Duration**: Total time spent on analysis
   - **Engagement Rate**: Average engagement rate of processed accounts
   - **Quality Score**: Overall quality score of the results

### Understanding Tag Analysis

1. Navigate to the campaign details page
2. Click the "Tag Analysis" tab
3. View tag match statistics:
   - **Tag Name**: Name of the tag
   - **Matches**: Number of accounts that matched the tag
   - **Match Rate**: Percentage of processed accounts that matched the tag
   - **Average Confidence**: Average confidence score for the tag
   - **Whitelist Rate**: Percentage of matched accounts that were whitelisted

### Exporting Results

1. Navigate to the campaign details page
2. Click the "Export" button
3. Select the export format (CSV, JSON, Excel)
4. Choose what data to include in the export
5. Click "Export Data"

## ROI Tracking

### Configuring ROI Settings

1. Navigate to the campaign details page
2. Click the "ROI Settings" tab
3. Configure the following settings:
   - **Cost Type**: Choose between fixed cost, hourly rate, per account, or per whitelist entry
   - **Cost Value**: Set the cost value based on the selected cost type
   - **Estimated Value**: Set the estimated value per whitelisted account

### Viewing ROI Metrics

1. Navigate to the campaign details page
2. Click the "ROI" tab
3. View the following metrics:
   - **Total Cost**: Total campaign cost
   - **Estimated Value**: Estimated campaign value
   - **Actual Value**: Actual campaign value (if available)
   - **ROI Percentage**: Return on investment percentage
   - **Conversion Rate**: Percentage of processed accounts that were whitelisted
   - **Cost Per Account**: Average cost per processed account
   - **Cost Per Whitelist**: Average cost per whitelisted account

### Comparing Campaign ROI

1. Navigate to the "ROI Comparison" section
2. Select campaigns to compare
3. View side-by-side comparison of ROI metrics
4. Analyze trends and patterns

## Notifications

### Viewing Notifications

1. Click the notification icon in the top navigation bar
2. View the list of notifications
3. Click on a notification to view details or navigate to the related item

### Managing Notification Preferences

1. Navigate to your profile settings
2. Click the "Notifications" tab
3. Configure the following preferences:
   - **In-App Notifications**: Enable/disable in-app notifications
   - **Email Notifications**: Enable/disable email notifications
   - **Campaign Status**: Receive notifications for campaign status changes
   - **Analysis Completion**: Receive notifications when analysis is complete
   - **System Announcements**: Receive system-wide announcements

### Marking Notifications as Read

1. Click the notification icon in the top navigation bar
2. To mark a single notification as read, click the "Mark as Read" button next to it
3. To mark all notifications as read, click the "Mark All as Read" button

## External API Integration

### Configuring External APIs

1. Navigate to the "External APIs" section (admin only)
2. Click "New API"
3. Enter the following information:
   - **Name**: API name
   - **Base URL**: API base URL
   - **Auth Type**: Authentication type (none, API key, bearer, basic, OAuth2, HMAC)
   - **Authentication Details**: Enter authentication details based on the selected auth type
   - **Description**: API description
4. Click "Save API"

### Using External APIs

1. Navigate to the "External APIs" section
2. Select an API from the list
3. Click "Call API"
4. Enter the following information:
   - **Endpoint**: API endpoint
   - **Method**: HTTP method (GET, POST, PUT, DELETE)
   - **Parameters**: Query parameters (for GET requests)
   - **Data**: Request body (for POST/PUT requests)
   - **Headers**: Additional headers
   - **Cache Key**: Optional cache key for GET requests
   - **Cache TTL**: Cache time-to-live in seconds
5. Click "Send Request"
6. View the response

## Troubleshooting

### Common Issues

#### Campaign Won't Launch

- Ensure you have added at least one target
- Check that you have sufficient resources
- Verify that the campaign status is "draft"

#### Analysis Not Starting

- Ensure the campaign has collected accounts
- Check that you have configured analysis settings
- Verify that the analysis service is running

#### Missing Results

- Ensure the analysis has completed
- Check that you have configured appropriate tags
- Verify that the accounts match your analysis criteria

### Getting Help

If you encounter issues not covered in this guide:

1. Check the system status page for any known issues
2. Contact support through the "Help" section
3. Provide detailed information about the issue, including:
   - Steps to reproduce
   - Error messages
   - Campaign ID (if applicable)
   - Screenshots (if available)
