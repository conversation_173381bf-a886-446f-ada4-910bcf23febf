# Campaign Analysis System Documentation

Welcome to the Campaign Analysis System documentation. This documentation provides comprehensive information about the system, including its architecture, API, and usage guides.

## Table of Contents

1. [User Guide](#user-guide)
2. [API Documentation](#api-documentation)
3. [Architecture Documentation](#architecture-documentation)
4. [Development Guide](#development-guide)

## User Guide

The [User Guide](user_guide/README.md) provides detailed instructions for using the Campaign Analysis System, including:

- Creating and managing campaigns
- Working with targets
- Analyzing accounts
- Understanding results
- Tracking ROI
- Managing notifications

This guide is intended for end users of the system.

## API Documentation

The [API Documentation](api/README.md) provides detailed information about the Campaign Analysis System API, including:

- Authentication
- Endpoints
- Request/response formats
- Error handling

This documentation is intended for developers integrating with the Campaign Analysis System API.

## Architecture Documentation

The [Architecture Documentation](architecture/README.md) provides an overview of the Campaign Analysis System architecture, including:

- System components
- Data flow
- Design decisions
- Technology stack

This documentation is intended for developers and architects working on the system.

## Development Guide

### Setting Up Development Environment

1. Clone the repository
2. Install dependencies:
   ```
   pip install -r requirements.txt
   ```
3. Set up the database:
   ```
   python manage.py migrate
   ```
4. Create a superuser:
   ```
   python manage.py createsuperuser
   ```
5. Run the development server:
   ```
   python manage.py runserver
   ```

### Running Tests

Use the provided test script to run tests:

```
./run_tests.sh
```

Options:
- `./run_tests.sh unit`: Run unit tests only
- `./run_tests.sh integration`: Run integration tests only
- `./run_tests.sh all`: Run all tests (default)

### Code Structure

The codebase is organized as follows:

- `campaigns/`: Main application package
  - `api/`: API views and serializers
  - `management/`: Management commands
  - `migrations/`: Database migrations
  - `models.py`: Database models
  - `services/`: Business logic services
  - `tests/`: Test cases
  - `urls.py`: URL routing
  - `views.py`: View functions

### Contributing

1. Create a feature branch from `develop`
2. Make your changes
3. Write tests for your changes
4. Run the tests to ensure they pass
5. Submit a pull request

### Coding Standards

- Follow PEP 8 for Python code
- Write docstrings for all functions, classes, and modules
- Include type hints where appropriate
- Write unit tests for all new functionality
