# Campaign Analysis System API Documentation

This document provides detailed information about the Campaign Analysis System API endpoints, request/response formats, and authentication requirements.

## Table of Contents

1. [Authentication](#authentication)
2. [Campaigns](#campaigns)
3. [Tags](#tags)
4. [Tag Categories](#tag-categories)
5. [External APIs](#external-apis)
6. [Notifications](#notifications)

## Authentication

The Campaign Analysis System API uses token-based authentication. To authenticate, include the token in the Authorization header of your requests.

```
Authorization: Token <your_token>
```

### Obtaining a Token

To obtain a token, send a POST request to the `/api/auth/token/` endpoint with your username and password.

**Request:**
```json
{
  "username": "your_username",
  "password": "your_password"
}
```

**Response:**
```json
{
  "token": "your_auth_token"
}
```

## Campaigns

### List Campaigns

**Endpoint:** `GET /api/campaigns/`

**Description:** Get a list of all campaigns.

**Query Parameters:**
- `status` - Filter by campaign status (e.g., draft, running, completed)
- `target_type` - Filter by target type (e.g., location, username)
- `ordering` - Order results by field (e.g., created_at, -created_at for descending)

**Response:**
```json
{
  "count": 10,
  "next": "http://example.com/api/campaigns/?page=2",
  "previous": null,
  "results": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Example Campaign",
      "description": "Example campaign description",
      "status": "running",
      "target_type": "location",
      "audience_type": "followers",
      "created_at": "2023-01-01T12:00:00Z",
      "updated_at": "2023-01-02T12:00:00Z"
    },
    ...
  ]
}
```

### Create Campaign

**Endpoint:** `POST /api/campaigns/`

**Description:** Create a new campaign.

**Request:**
```json
{
  "name": "New Campaign",
  "description": "New campaign description",
  "target_type": "location",
  "audience_type": "followers"
}
```

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "New Campaign",
  "description": "New campaign description",
  "status": "draft",
  "target_type": "location",
  "audience_type": "followers",
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-01T12:00:00Z"
}
```

### Get Campaign

**Endpoint:** `GET /api/campaigns/{id}/`

**Description:** Get details of a specific campaign.

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "Example Campaign",
  "description": "Example campaign description",
  "status": "running",
  "target_type": "location",
  "audience_type": "followers",
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-02T12:00:00Z",
  "location_targets": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "country": "United States",
      "city": "New York",
      "location_id": "123456789"
    }
  ],
  "username_targets": []
}
```

### Update Campaign

**Endpoint:** `PUT /api/campaigns/{id}/`

**Description:** Update a campaign.

**Request:**
```json
{
  "name": "Updated Campaign",
  "description": "Updated campaign description"
}
```

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "Updated Campaign",
  "description": "Updated campaign description",
  "status": "running",
  "target_type": "location",
  "audience_type": "followers",
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-03T12:00:00Z"
}
```

### Delete Campaign

**Endpoint:** `DELETE /api/campaigns/{id}/`

**Description:** Delete a campaign.

**Response:** `204 No Content`

### Launch Campaign

**Endpoint:** `POST /api/campaigns/{id}/launch/`

**Description:** Launch a campaign.

**Request:**
```json
{
  "launch_method": "pyflow"
}
```

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "Example Campaign",
  "status": "running",
  "airflow_run_id": "/path/to/workflow.pygraph"
}
```

### Analyze Campaign

**Endpoint:** `POST /api/campaigns/{id}/analyze/`

**Description:** Analyze campaign accounts.

**Request:**
```json
{
  "analysis_method": "pyflow",
  "options": {
    "batch_size": 100
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Analysis workflow started",
  "workflow_path": "/path/to/analysis_workflow.pygraph"
}
```

### Get Campaign Workflows

**Endpoint:** `GET /api/campaigns/{id}/workflows/`

**Description:** Get PyFlow workflows for a campaign.

**Response:**
```json
[
  {
    "name": "campaign_550e8400_collection_12345678",
    "path": "/path/to/workflow.pygraph",
    "status": "completed",
    "created_at": "2023-01-01T12:00:00Z"
  },
  {
    "name": "campaign_550e8400_analysis_87654321",
    "path": "/path/to/analysis_workflow.pygraph",
    "status": "running",
    "created_at": "2023-01-02T12:00:00Z"
  }
]
```

### Get Workflow Status

**Endpoint:** `GET /api/campaigns/{id}/workflow_status/?workflow_name={workflow_name}`

**Description:** Get status of a PyFlow workflow.

**Response:**
```json
{
  "success": true,
  "status": "running",
  "workflow_path": "/path/to/workflow.pygraph"
}
```

## Tags

### List Tags

**Endpoint:** `GET /api/tags/`

**Description:** Get a list of all tags.

**Query Parameters:**
- `category` - Filter by category ID
- `tag_type` - Filter by tag type (e.g., keyword, regex, ml)
- `is_global` - Filter by global status (true/false)

**Response:**
```json
{
  "count": 10,
  "next": "http://example.com/api/tags/?page=2",
  "previous": null,
  "results": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Example Tag",
      "description": "Example tag description",
      "category": "550e8400-e29b-41d4-a716-************",
      "tag_type": "keyword",
      "pattern": "example",
      "field": "bio",
      "is_global": true,
      "confidence_level": "medium",
      "weight": 1.0,
      "created_at": "2023-01-01T12:00:00Z",
      "updated_at": "2023-01-01T12:00:00Z"
    },
    ...
  ]
}
```

### Create Tag

**Endpoint:** `POST /api/tags/`

**Description:** Create a new tag.

**Request:**
```json
{
  "name": "New Tag",
  "description": "New tag description",
  "category": "550e8400-e29b-41d4-a716-************",
  "tag_type": "keyword",
  "pattern": "example",
  "field": "bio",
  "is_global": true,
  "confidence_level": "medium",
  "weight": 1.0
}
```

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "name": "New Tag",
  "description": "New tag description",
  "category": "550e8400-e29b-41d4-a716-************",
  "tag_type": "keyword",
  "pattern": "example",
  "field": "bio",
  "is_global": true,
  "confidence_level": "medium",
  "weight": 1.0,
  "created_at": "2023-01-01T12:00:00Z",
  "updated_at": "2023-01-01T12:00:00Z"
}
```

### Test Tag

**Endpoint:** `POST /api/tags/{id}/test/`

**Description:** Test a tag on a text sample or account.

**Request:**
```json
{
  "text": "This is a sample text to test the tag",
  "field": "bio"
}
```

**Response:**
```json
{
  "matched": true,
  "confidence_score": 0.85,
  "match_details": {
    "matches": ["example"],
    "positions": [10]
  }
}
```

## Tag Categories

### List Tag Categories

**Endpoint:** `GET /api/tag-categories/`

**Description:** Get a list of all tag categories.

**Response:**
```json
{
  "count": 5,
  "next": null,
  "previous": null,
  "results": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Demographics",
      "description": "Demographic tags",
      "parent": null,
      "color": "#6c757d",
      "icon": "users",
      "priority": 1,
      "created_at": "2023-01-01T12:00:00Z",
      "updated_at": "2023-01-01T12:00:00Z"
    },
    ...
  ]
}
```

### Get Tag Category Tree

**Endpoint:** `GET /api/tag-categories/tree/`

**Description:** Get tag categories in a tree structure.

**Response:**
```json
{
  "categories": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Demographics",
      "description": "Demographic tags",
      "color": "#6c757d",
      "icon": "users",
      "priority": 1,
      "children": [
        {
          "id": "550e8400-e29b-41d4-a716-446655440002",
          "name": "Age",
          "description": "Age-related tags",
          "color": "#6c757d",
          "icon": "calendar",
          "priority": 1,
          "children": []
        }
      ]
    },
    ...
  ]
}
```

## External APIs

### List External APIs

**Endpoint:** `GET /api/external-apis/`

**Description:** List all configured external APIs.

**Response:**
```json
[
  {
    "name": "example_api",
    "base_url": "https://api.example.com",
    "auth_type": "api_key",
    "description": "Example API",
    "updated_at": "2023-01-01T12:00:00Z"
  },
  ...
]
```

### Get External API Configuration

**Endpoint:** `GET /api/external-apis/{name}/`

**Description:** Get configuration for a specific external API.

**Response:**
```json
{
  "base_url": "https://api.example.com",
  "auth_type": "api_key",
  "api_key_name": "X-API-Key",
  "api_key": "********",
  "api_key_location": "header",
  "description": "Example API",
  "updated_at": "2023-01-01T12:00:00Z"
}
```

### Configure External API

**Endpoint:** `POST /api/external-apis/`

**Description:** Configure a new external API.

**Request:**
```json
{
  "name": "new_api",
  "config": {
    "base_url": "https://api.new-example.com",
    "auth_type": "bearer",
    "token": "your_token",
    "description": "New API"
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "API new_api configured successfully"
}
```

### Call External API

**Endpoint:** `POST /api/external-apis/{name}/call/`

**Description:** Call an external API.

**Request:**
```json
{
  "endpoint": "users",
  "method": "GET",
  "params": {
    "page": 1,
    "limit": 10
  },
  "cache_key": "users_page_1",
  "cache_ttl": 300
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "users": [
      {
        "id": 1,
        "name": "John Doe",
        "email": "<EMAIL>"
      },
      ...
    ]
  },
  "status_code": 200
}
```

## Notifications

### List Notifications

**Endpoint:** `GET /api/notifications/`

**Description:** List notifications for the current user.

**Response:**
```json
{
  "notifications": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "title": "Campaign Running",
      "message": "Your campaign 'Example Campaign' is now running.",
      "notification_type": "info",
      "link": "/campaigns/550e8400-e29b-41d4-a716-************/",
      "data": {
        "campaign_id": "550e8400-e29b-41d4-a716-************",
        "status": "running"
      },
      "is_read": false,
      "created_at": "2023-01-01T12:00:00Z",
      "read_at": null
    },
    ...
  ],
  "unread_count": 5,
  "total_count": 10
}
```

### Get Notification

**Endpoint:** `GET /api/notifications/{id}/`

**Description:** Get a specific notification.

**Response:**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "title": "Campaign Running",
  "message": "Your campaign 'Example Campaign' is now running.",
  "notification_type": "info",
  "link": "/campaigns/550e8400-e29b-41d4-a716-************/",
  "data": {
    "campaign_id": "550e8400-e29b-41d4-a716-************",
    "status": "running"
  },
  "is_read": false,
  "created_at": "2023-01-01T12:00:00Z",
  "read_at": null
}
```

### Mark Notification as Read

**Endpoint:** `POST /api/notifications/{id}/mark_as_read/`

**Description:** Mark a notification as read.

**Response:**
```json
{
  "success": true,
  "id": "550e8400-e29b-41d4-a716-************",
  "is_read": true,
  "read_at": "2023-01-02T12:00:00Z"
}
```

### Mark All Notifications as Read

**Endpoint:** `POST /api/notifications/mark_all_as_read/`

**Description:** Mark all notifications as read.

**Response:**
```json
{
  "success": true,
  "count": 5
}
```

### Get Unread Notification Count

**Endpoint:** `GET /api/notifications/unread_count/`

**Description:** Get the number of unread notifications.

**Response:**
```json
{
  "unread_count": 5
}
```

### Send Notification

**Endpoint:** `POST /api/notifications/send/`

**Description:** Send a notification to users (admin only).

**Request:**
```json
{
  "user_ids": ["550e8400-e29b-41d4-a716-************"],
  "title": "Custom Notification",
  "message": "This is a custom notification.",
  "notification_type": "info",
  "link": "/custom/link/",
  "data": {
    "custom_key": "custom_value"
  }
}
```

**Response:**
```json
{
  "success": true,
  "notifications": ["550e8400-e29b-41d4-a716-************"],
  "count": 1
}
```
