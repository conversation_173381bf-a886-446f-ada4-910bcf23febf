#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create realistic campaign data for testing the refined campaign detail view.
"""
import os
import sys
import django
from datetime import datetime, timedelta
from django.utils import timezone
import random

# Setup Django environment
sys.path.append('/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/CommandNetSolutions')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CommandNetSolutions.settings')
django.setup()

from django.contrib.auth.models import User
from campaigns.models import Campaign, LocationTarget, UsernameTarget, CampaignResult
from instagram.models import Accounts, WhiteListEntry

def create_realistic_campaign():
    """Create a campaign with realistic data relationships."""
    
    # Get or create test user
    user, created = User.objects.get_or_create(
        username="test_user",
        defaults={
            "email": "<EMAIL>",
            "first_name": "Test",
            "last_name": "User"
        }
    )
    
    # Create campaign with realistic status
    campaign = Campaign.objects.create(
        name="Fitness Influencer Campaign",
        description="Campaign targeting fitness enthusiasts and health-conscious individuals in major US cities",
        status="running",
        target_type="mixed",
        audience_type="both",
        creator=user
    )
    
    print(f"Created campaign: {campaign.name} (ID: {campaign.id})")
    
    # Add location targets
    locations = [
        {"location_id": "*********", "city": "New York", "country": "United States"},
        {"location_id": "*********", "city": "Los Angeles", "country": "United States"},
        {"location_id": "213385407", "city": "Chicago", "country": "United States"},
        {"location_id": "213385409", "city": "Miami", "country": "United States"},
    ]
    
    for loc in locations:
        LocationTarget.objects.create(
            campaign=campaign,
            **loc
        )
    
    # Add username targets
    usernames = [
        {"username": "fitness_guru_official", "audience_type": "followers"},
        {"username": "healthy_lifestyle_coach", "audience_type": "both"},
        {"username": "gym_motivation_daily", "audience_type": "followers"},
    ]
    
    for user_data in usernames:
        UsernameTarget.objects.create(
            campaign=campaign,
            **user_data
        )
    
    # Create realistic account data (50 collected, 45 analyzed, 12 whitelisted)
    base_time = timezone.now() - timedelta(days=7)
    
    # Sample usernames with realistic fitness/health theme
    sample_usernames = [
        "fitlife_sarah", "gym_beast_mike", "yoga_zen_maria", "crossfit_champion",
        "healthy_eats_jen", "marathon_runner_tom", "pilates_queen_anna", "bodybuilder_max",
        "nutrition_expert_lisa", "cardio_king_james", "strength_coach_alex", "wellness_guru_kim",
        "fitness_model_emma", "personal_trainer_joe", "health_blogger_sam", "workout_warrior_chris",
        "diet_coach_rachel", "gym_owner_david", "fitness_influencer_kate", "health_advocate_mark",
        "yoga_instructor_lily", "crossfit_athlete_ryan", "nutrition_coach_sophie", "fitness_enthusiast_ben",
        "healthy_living_coach", "workout_motivation", "fitness_journey_blog", "health_tips_daily",
        "gym_life_official", "fitness_transformation", "healthy_recipes_chef", "workout_videos_pro",
        "fitness_gear_review", "health_science_facts", "gym_equipment_guide", "fitness_challenges",
        "healthy_lifestyle_tips", "workout_routines_pro", "fitness_nutrition_guide", "health_wellness_coach",
        "gym_workout_plans", "fitness_success_stories", "healthy_meal_prep", "workout_form_tips",
        "fitness_motivation_quotes", "health_research_updates", "gym_safety_tips", "fitness_community_hub",
        "healthy_snack_ideas", "workout_recovery_tips"
    ]
    
    accounts_created = []
    
    # Create 50 collected accounts
    for i in range(50):
        username = sample_usernames[i]
        collection_time = base_time + timedelta(hours=i*2, minutes=random.randint(0, 59))
        
        account = Accounts.objects.create(
            username=username,
            full_name=f"{username.replace('_', ' ').title()}",
            bio=f"Fitness enthusiast | Health coach | Inspiring others to live their best life 💪 #{random.choice(['fitness', 'health', 'wellness', 'gym'])}",
            followers=random.randint(1000, 50000),
            following=random.randint(200, 2000),
            number_of_posts=random.randint(50, 1000),
            account_type=random.choice(['personal', 'business']),
            is_verified=random.choice([True, False]) if random.random() > 0.8 else False,
            collection_date=collection_time,
            campaign_id=str(campaign.id),
            interests=['fitness', 'health', 'wellness', 'nutrition'],
            locations=['gym', 'fitness_center', 'health_club']
        )
        accounts_created.append(account)
    
    print(f"Created {len(accounts_created)} accounts")
    
    # Create campaign result with realistic numbers
    campaign_result = CampaignResult.objects.create(
        campaign=campaign,
        total_accounts_found=50,
        total_accounts_processed=45,  # 45 analyzed out of 50 collected
        last_processed_at=timezone.now() - timedelta(hours=2)
    )
    
    # Create whitelist entries for 12 accounts (realistic conversion rate)
    whitelisted_accounts = random.sample(accounts_created[:45], 12)  # Only from analyzed accounts
    
    for account in whitelisted_accounts:
        WhiteListEntry.objects.create(
            account=account,
            tags=['fitness_enthusiast', 'high_engagement'],
            dm=True,
            discover=True,
            comment=True,
            post_like=True,
            favorite=False,
            follow=True,
            is_auto=True
        )
    
    print(f"Created {len(whitelisted_accounts)} whitelist entries")
    print(f"Campaign statistics:")
    print(f"  - Collected: 50 accounts")
    print(f"  - Analyzed: 45 accounts")
    print(f"  - Whitelisted: 12 accounts")
    print(f"  - Conversion rate: {(12/45)*100:.1f}%")
    
    return campaign

if __name__ == "__main__":
    campaign = create_realistic_campaign()
    print(f"\nRealistic campaign created successfully!")
    print(f"Campaign ID: {campaign.id}")
    print(f"View at: /campaigns/{campaign.id}/")
