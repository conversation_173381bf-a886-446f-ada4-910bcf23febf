# CommandNetSolutions

This project is a Django-based application for managing Instagram campaigns and automation.

## Project Structure

- `campaigns/`: Main app for campaign management
  - `models/`: Contains all database models
    - `main.py`: Core models for campaigns, tags, and related entities
    - `workflow.py`: Models related to workflow management
  - `views/`: Contains all view functions and classes
    - `__init__.py`: Main views for the app
    - `tag_views.py`: Views for tag management
  - `static/`: Static files (CSS, JS, images)
    - `campaigns/js/tag_condition_builder.js`: JavaScript for building tag conditions
  - `templates/`: HTML templates
    - `campaigns/dynamic_tag_form.html`: Form for creating and editing dynamic tags
  - `forms.py`: Form classes for the app
  - `urls.py`: URL routing for the app

## Code Quality Tools

This project uses the following code quality tools:

- **Ruff**: A fast Python linter and formatter
  - Configuration: `pyproject.toml`
  - Run: `ruff check .` or `ruff format .`

- **Pylint**: A comprehensive Python linter with Django support
  - Configuration: `.pylintrc` and `pyproject.toml`
  - Run: `pylint --rcfile=.pylintrc --load-plugins=pylint_django --django-settings-module=CommandNetSolutions.settings ENGINE/CommandNetSolutions/`

- **SQLFluff**: A SQL linter and formatter
  - Configuration: `.sqlfluff` and `pyproject.toml`
  - Run: `sqlfluff lint path/to/sql/files/` or `sqlfluff fix path/to/sql/files/`

- **djLint**: A linter and formatter for Django templates
  - Configuration: `.djlintrc`
  - Run: `djlint --reformat .`

- **Pre-commit**: Git hooks to enforce code quality
  - Configuration: `.pre-commit-config.yaml`
  - Install: `pre-commit install`
  - Run: `pre-commit run --all-files`

For detailed information about these tools, see [CODE_QUALITY.md](CODE_QUALITY.md).

## Development Setup

1. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Install development dependencies:
   ```bash
   pip install -r requirements-dev.txt
   ```

4. Set up pre-commit hooks:
   ```bash
   pre-commit install
   ```

5. Run migrations:
   ```bash
   python manage.py migrate
   ```

6. Start the development server:
   ```bash
   python manage.py runserver
   ```

## Tag System

The tag system allows for dynamic creation of tags with multiple conditions. Tags can be used to categorize and filter Instagram accounts based on various criteria.

### Tag Models

- `DynamicTag`: The main tag model
- `TagCategory`: Categories for organizing tags
- `TagGroup`: Groups of related tags
- `CampaignTagRule`: Rules for applying tags to accounts
- `CampaignTagCondition`: Conditions for tag rules

### Tag Creation Process

1. User fills out the tag form with name, description, etc.
2. User adds one or more conditions using the condition builder
3. Form is submitted with the tag data and conditions JSON
4. Server creates the tag and associated rule and conditions

## Troubleshooting

If you encounter issues with tag creation:

1. Check the server logs for error messages
2. Verify that the conditions JSON is properly formatted
3. Ensure that the tag form is properly submitting all required fields
4. Check the database to see if the tag, rule, and conditions were created
