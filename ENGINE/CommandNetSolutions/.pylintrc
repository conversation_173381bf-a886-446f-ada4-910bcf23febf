[MASTER]
# Specify a configuration file.
rcfile=.pylintrc

# Python code to execute, usually for sys.path manipulation such as pygtk.require().
init-hook='import sys; import os; sys.path.append(os.path.dirname(os.path.abspath(__file__)))'

# Use multiple processes to speed up Pylint.
jobs=0

# List of plugins (as comma separated values of python modules names) to load.
load-plugins=pylint_django

# <PERSON><PERSON> collected data for later comparisons.
persistent=yes

# Allow loading of arbitrary C extensions.
unsafe-load-any-extension=no

# A comma-separated list of package or module names from where C extensions may be loaded.
extension-pkg-whitelist=

# Allow optimization of some AST trees.
optimize-ast=yes

# Django settings module for the project
django-settings-module=CommandNetSolutions.settings

[MESSAGES CONTROL]
# Only show warnings with the listed confidence levels.
confidence=HIGH,INFERENCE,INFERENCE_FAILURE,UNDEFINED

# Disable the message, report, category or checker with the given id(s).
disable=missing-docstring,
        invalid-name,
        too-many-locals,
        too-many-arguments,
        too-many-instance-attributes,
        too-many-public-methods,
        too-few-public-methods,
        protected-access,
        no-self-use,
        duplicate-code,
        line-too-long,
        too-many-lines,
        fixme,
        broad-except,
        no-member,
        unused-argument,
        no-else-return,
        import-outside-toplevel,
        consider-using-f-string

[REPORTS]
# Set the output format.
output-format=text

# Tells whether to display a full report or only the messages.
reports=no

# Python expression which should return a note less than 10.
evaluation=10.0 - ((float(5 * error + warning + refactor + convention) / statement) * 10)

[FORMAT]
# Maximum number of characters on a single line.
max-line-length=100

# Regexp for a line that is allowed to be longer than the limit.
ignore-long-lines=^\s*(# )?<?https?://\S+>?$

# Allow the body of an if to be on the same line as the test if there is no else.
single-line-if-stmt=no

# List of optional constructs for which whitespace checking is disabled.
no-space-check=trailing-comma,dict-separator

# Maximum number of lines in a module.
max-module-lines=1000

# String used as indentation unit.
indent-string='    '

# Number of spaces of indent required inside a hanging or continued line.
indent-after-paren=4

# Expected format of line ending.
expected-line-ending-format=LF

[BASIC]
# Good variable names which should always be accepted, separated by a comma.
good-names=i,j,k,ex,Run,_,pk,id

# Bad variable names which should always be refused, separated by a comma.
bad-names=foo,bar,baz,toto,tutu,tata

# Colon-delimited sets of names that determine each other's naming style when the name regexes allow several styles.
name-group=

# Include a hint for the correct naming format with invalid-name.
include-naming-hint=yes

# Regular expression matching correct function names.
function-rgx=[a-z_][a-z0-9_]{2,30}$

# Naming hint for function names.
function-name-hint=[a-z_][a-z0-9_]{2,30}$

# Regular expression matching correct variable names.
variable-rgx=[a-z_][a-z0-9_]{2,30}$

# Naming hint for variable names.
variable-name-hint=[a-z_][a-z0-9_]{2,30}$

# Regular expression matching correct constant names.
const-rgx=(([A-Z_][A-Z0-9_]*)|(__.*__))$

# Naming hint for constant names.
const-name-hint=(([A-Z_][A-Z0-9_]*)|(__.*__))$

# Regular expression matching correct attribute names.
attr-rgx=[a-z_][a-z0-9_]{2,30}$

# Naming hint for attribute names.
attr-name-hint=[a-z_][a-z0-9_]{2,30}$

# Regular expression matching correct argument names.
argument-rgx=[a-z_][a-z0-9_]{2,30}$

# Naming hint for argument names.
argument-name-hint=[a-z_][a-z0-9_]{2,30}$

# Regular expression matching correct class attribute names.
class-attribute-rgx=([A-Za-z_][A-Za-z0-9_]{2,30}|(__.*__))$

# Naming hint for class attribute names.
class-attribute-name-hint=([A-Za-z_][A-Za-z0-9_]{2,30}|(__.*__))$

# Regular expression matching correct inline iteration names.
inlinevar-rgx=[A-Za-z_][A-Za-z0-9_]*$

# Naming hint for inline iteration names.
inlinevar-name-hint=[A-Za-z_][A-Za-z0-9_]*$

# Regular expression matching correct class names.
class-rgx=[A-Z_][a-zA-Z0-9]+$

# Naming hint for class names.
class-name-hint=[A-Z_][a-zA-Z0-9]+$

# Regular expression matching correct module names.
module-rgx=(([a-z_][a-z0-9_]*)|([A-Z][a-zA-Z0-9]+))$

# Naming hint for module names.
module-name-hint=(([a-z_][a-z0-9_]*)|([A-Z][a-zA-Z0-9]+))$

# Regular expression matching correct method names.
method-rgx=[a-z_][a-z0-9_]{2,30}$

# Naming hint for method names.
method-name-hint=[a-z_][a-z0-9_]{2,30}$

# Regular expression which should only match function or class names that do not require a docstring.
no-docstring-rgx=^_

# Minimum line length for functions/classes that require docstrings, shorter ones are exempt.
docstring-min-length=-1

[SIMILARITIES]
# Minimum lines number of a similarity.
min-similarity-lines=4

# Ignore comments when computing similarities.
ignore-comments=yes

# Ignore docstrings when computing similarities.
ignore-docstrings=yes

# Ignore imports when computing similarities.
ignore-imports=yes

[TYPECHECK]
# Tells whether missing members accessed in mixin class should be ignored.
ignore-mixin-members=yes

# List of module names for which member attributes should not be checked.
ignored-modules=

# List of classes names for which member attributes should not be checked.
ignored-classes=SQLObject

# List of members which are set dynamically and missed by pylint inference system.
generated-members=REQUEST,acl_users,aq_parent,objects,DoesNotExist,id,pk,_meta,base_fields,context

[VARIABLES]
# Tells whether we should check for unused import in __init__ files.
init-import=no

# A regular expression matching the name of dummy variables.
dummy-variables-rgx=_$|dummy

# List of additional names supposed to be defined in builtins.
additional-builtins=

# List of strings which can identify a callback function by name.
callbacks=cb_,_cb

[DESIGN]
# Maximum number of arguments for function / method.
max-args=5

# Argument names that match this expression will be ignored.
ignored-argument-names=_.*

# Maximum number of locals for function / method body.
max-locals=15

# Maximum number of return / yield for function / method body.
max-returns=6

# Maximum number of branch for function / method body.
max-branches=12

# Maximum number of statements in function / method body.
max-statements=50

# Maximum number of parents for a class.
max-parents=7

# Maximum number of attributes for a class.
max-attributes=7

# Minimum number of public methods for a class.
min-public-methods=2

# Maximum number of public methods for a class.
max-public-methods=20

[CLASSES]
# List of method names used to declare (i.e. assign) instance attributes.
defining-attr-methods=__init__,__new__,setUp

# List of valid names for the first argument in a class method.
valid-classmethod-first-arg=cls

# List of valid names for the first argument in a metaclass class method.
valid-metaclass-classmethod-first-arg=mcs

# List of member names, which should be excluded from the protected access warning.
exclude-protected=_asdict,_fields,_replace,_source,_make

[IMPORTS]
# Deprecated modules which should not be used, separated by a comma.
deprecated-modules=regsub,TERMIOS,Bastion,rexec

# Create a graph of every (i.e. internal and external) dependencies in the given file.
import-graph=

# Create a graph of external dependencies in the given file.
ext-import-graph=

# Create a graph of internal dependencies in the given file.
int-import-graph=

[EXCEPTIONS]
# Exceptions that will emit a warning when being caught.
overgeneral-exceptions=Exception
