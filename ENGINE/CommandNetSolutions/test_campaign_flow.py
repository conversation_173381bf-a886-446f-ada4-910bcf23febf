#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to test the campaign flow from A-Z, including system tags.

This script:
1. Creates system tags
2. Creates a test campaign
3. Adds targets to the campaign
4. Launches the campaign
5. Simulates account collection
6. Applies tags to accounts
7. Generates a whitelist

Usage:
    python test_campaign_flow.py
"""
import os
import sys
import uuid
import random
import datetime
from django.core.management import call_command
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import transaction
from django.contrib.contenttypes.models import ContentType

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CommandNetSolutions.settings')
import django
django.setup()

# Import models
from campaigns.models import (
    Campaign, LocationTarget, UsernameTarget, DynamicTag,
    TagGroup, TagCategory, CampaignTag, TagAnalysisResult
)
from instagram.models import Accounts, WhiteListEntry
from taggit.models import Tag
from instagram.models import CustomTaggedItem

def create_system_tags():
    """Create system tags using the management command"""
    print("Creating system tags...")
    call_command('create_system_tags')
    
    # Verify system tags were created
    system_tags = DynamicTag.objects.filter(is_system=True)
    print(f"Created {system_tags.count()} system tags:")
    for tag in system_tags:
        print(f"  - {tag.name}: {tag.description}")
    
    return system_tags

def create_test_campaign():
    """Create a test campaign"""
    print("\nCreating test campaign...")
    
    # Get or create a test user
    user, created = User.objects.get_or_create(
        username='test_user',
        defaults={
            'email': '<EMAIL>',
            'is_staff': True
        }
    )
    
    if created:
        user.set_password('password')
        user.save()
        print("Created test user: test_user")
    
    # Create a campaign
    campaign = Campaign.objects.create(
        id=uuid.uuid4(),
        name='Test Campaign with System Tags',
        description='A test campaign to demonstrate system tags',
        creator=user,
        status='draft',
        target_type='mixed',
        audience_type='profile'
    )
    
    print(f"Created campaign: {campaign.name} (ID: {campaign.id})")
    return campaign

def add_targets_to_campaign(campaign):
    """Add location and username targets to the campaign"""
    print("\nAdding targets to campaign...")
    
    # Add a location target
    location = LocationTarget.objects.create(
        id=uuid.uuid4(),
        campaign=campaign,
        country='United States',
        city='New York',
        location_id='123456789'
    )
    print(f"Added location target: {location.city}, {location.country}")
    
    # Add username targets
    usernames = ['instagram', 'nike', 'natgeo', 'apple']
    for username in usernames:
        target = UsernameTarget.objects.create(
            id=uuid.uuid4(),
            campaign=campaign,
            username=username,
            audience_type='profile'
        )
        print(f"Added username target: {target.username}")
    
    return True

def assign_tags_to_campaign(campaign, system_tags):
    """Assign system tags to the campaign"""
    print("\nAssigning system tags to campaign...")
    
    # Assign all system tags to the campaign
    for tag in system_tags:
        campaign_tag = CampaignTag.objects.create(
            id=uuid.uuid4(),
            campaign=campaign,
            tag=tag,
            is_required=(random.random() > 0.5)  # 50% chance of being required
        )
        print(f"Assigned tag '{tag.name}' to campaign (Required: {campaign_tag.is_required})")
    
    return True

def launch_campaign(campaign):
    """Launch the campaign"""
    print("\nLaunching campaign...")
    
    # Update campaign status
    campaign.status = 'running'
    campaign.airflow_dag_id = f"campaign_{campaign.id}"
    campaign.airflow_run_id = f"run_{uuid.uuid4()}"
    campaign.save()
    
    print(f"Campaign '{campaign.name}' launched with status: {campaign.status}")
    return True

def simulate_account_collection(campaign):
    """Simulate account collection"""
    print("\nSimulating account collection...")
    
    # Create dummy accounts
    accounts = []
    account_data = [
        {
            'username': 'user1',
            'full_name': 'User One',
            'bio': 'Digital marketer and content creator. Love photography and travel.',
            'followers': 15000,
            'following': 500,
            'number_of_posts': 250,
            'engagement_rate': 0.06,
            'account_type': 'business',
            'is_verified': True
        },
        {
            'username': 'user2',
            'full_name': 'User Two',
            'bio': 'Fitness enthusiast. Personal trainer. Healthy lifestyle advocate.',
            'followers': 8000,
            'following': 600,
            'number_of_posts': 120,
            'engagement_rate': 0.04,
            'account_type': 'personal',
            'is_verified': False
        },
        {
            'username': 'user3',
            'full_name': 'User Three',
            'bio': 'Tech blogger and software developer. Coding is my passion.',
            'followers': 5000,
            'following': 300,
            'number_of_posts': 80,
            'engagement_rate': 0.03,
            'account_type': 'personal',
            'is_verified': False
        },
        {
            'username': 'user4',
            'full_name': 'User Four',
            'bio': 'Fashion designer and stylist. Follow for daily outfit inspiration.',
            'followers': 25000,
            'following': 1200,
            'number_of_posts': 500,
            'engagement_rate': 0.07,
            'account_type': 'business',
            'is_verified': True
        },
        {
            'username': 'user5',
            'full_name': 'User Five',
            'bio': 'Food blogger. Sharing recipes and restaurant reviews.',
            'followers': 3000,
            'following': 800,
            'number_of_posts': 50,
            'engagement_rate': 0.02,
            'account_type': 'personal',
            'is_verified': False
        }
    ]
    
    for data in account_data:
        account, created = Accounts.objects.update_or_create(
            username=data['username'],
            defaults={
                'full_name': data['full_name'],
                'bio': data['bio'],
                'followers': data['followers'],
                'following': data['following'],
                'number_of_posts': data['number_of_posts'],
                'engagement_rate': data['engagement_rate'],
                'account_type': data['account_type'],
                'is_verified': data['is_verified'],
                'campaign_id': str(campaign.id),
                'collection_date': timezone.now()
            }
        )
        accounts.append(account)
        print(f"{'Created' if created else 'Updated'} account: {account.username}")
    
    return accounts

def apply_tags_to_accounts(campaign, accounts, system_tags):
    """Apply system tags to accounts"""
    print("\nApplying system tags to accounts...")
    
    # Get content type for Accounts model
    content_type = ContentType.objects.get_for_model(Accounts)
    
    # Apply tags to accounts
    for account in accounts:
        # Check each system tag against the account
        for tag in system_tags:
            matched = False
            confidence_score = 0.0
            match_details = {}
            
            # Simple matching logic based on tag field and pattern
            if tag.field == 'engagement_rate' and 'engagement_rate' in tag.pattern:
                if '>' in tag.pattern:
                    threshold = float(tag.pattern.split('>')[-1].strip())
                    matched = account.engagement_rate > threshold
                    confidence_score = min(1.0, account.engagement_rate / threshold) if matched else 0.0
                    match_details = {
                        'field': 'engagement_rate',
                        'value': account.engagement_rate,
                        'threshold': threshold
                    }
            
            elif tag.field == 'followers' and 'followers' in tag.pattern:
                if '>' in tag.pattern:
                    threshold = int(tag.pattern.split('>')[-1].strip())
                    matched = account.followers > threshold
                    confidence_score = min(1.0, account.followers / threshold) if matched else 0.0
                    match_details = {
                        'field': 'followers',
                        'value': account.followers,
                        'threshold': threshold
                    }
            
            elif tag.field == 'number_of_posts' and 'number_of_posts' in tag.pattern:
                if '>' in tag.pattern:
                    threshold = int(tag.pattern.split('>')[-1].strip())
                    matched = account.number_of_posts > threshold
                    confidence_score = min(1.0, account.number_of_posts / threshold) if matched else 0.0
                    match_details = {
                        'field': 'number_of_posts',
                        'value': account.number_of_posts,
                        'threshold': threshold
                    }
            
            elif tag.field == 'is_verified' and 'is_verified' in tag.pattern:
                matched = account.is_verified
                confidence_score = 1.0 if matched else 0.0
                match_details = {
                    'field': 'is_verified',
                    'value': account.is_verified
                }
            
            elif tag.field == 'account_type' and 'account_type' in tag.pattern:
                if '==' in tag.pattern:
                    account_type = tag.pattern.split('==')[-1].strip().replace("'", "").replace('"', '')
                    matched = account.account_type == account_type
                    confidence_score = 1.0 if matched else 0.0
                    match_details = {
                        'field': 'account_type',
                        'value': account.account_type,
                        'expected': account_type
                    }
            
            # Create tag analysis result
            if matched:
                TagAnalysisResult.objects.create(
                    id=uuid.uuid4(),
                    account_id=account.username,
                    campaign=campaign,
                    tag=tag,
                    matched=True,
                    confidence_score=confidence_score,
                    match_details=match_details
                )
                
                # Create taggit Tag and CustomTaggedItem
                taggit_tag, _ = Tag.objects.get_or_create(name=tag.name)
                
                CustomTaggedItem.objects.get_or_create(
                    tag=taggit_tag,
                    content_type=content_type,
                    object_id=account.username
                )
                
                print(f"Applied tag '{tag.name}' to account '{account.username}' (Confidence: {confidence_score:.2f})")
    
    return True

def generate_whitelist(campaign, accounts):
    """Generate whitelist based on tag matches"""
    print("\nGenerating whitelist...")
    
    # Get required tags for this campaign
    required_tags = CampaignTag.objects.filter(
        campaign=campaign,
        is_required=True
    ).values_list('tag__name', flat=True)
    
    print(f"Required tags for whitelist: {', '.join(required_tags)}")
    
    # Get content type for Accounts model
    content_type = ContentType.objects.get_for_model(Accounts)
    
    # Check each account for required tags
    for account in accounts:
        # Get all tags for this account
        account_tags = CustomTaggedItem.objects.filter(
            content_type=content_type,
            object_id=account.username
        ).values_list('tag__name', flat=True)
        
        # Check if account has all required tags
        has_all_required = all(tag in account_tags for tag in required_tags)
        
        if has_all_required:
            # Add to whitelist
            whitelist_entry, created = WhiteListEntry.objects.get_or_create(
                account=account,
                defaults={
                    'added_by': User.objects.first(),
                    'reason': f"Matched all required tags: {', '.join(required_tags)}",
                    'campaign_id': str(campaign.id)
                }
            )
            
            print(f"{'Added' if created else 'Updated'} whitelist entry for account '{account.username}'")
    
    # Count whitelist entries
    whitelist_count = WhiteListEntry.objects.filter(campaign_id=str(campaign.id)).count()
    print(f"Total accounts whitelisted: {whitelist_count}")
    
    return True

def main():
    """Main function to run the test"""
    print("Starting campaign flow test with system tags...\n")
    
    # Create system tags
    system_tags = create_system_tags()
    
    # Create test campaign
    campaign = create_test_campaign()
    
    # Add targets to campaign
    add_targets_to_campaign(campaign)
    
    # Assign system tags to campaign
    assign_tags_to_campaign(campaign, system_tags)
    
    # Launch campaign
    launch_campaign(campaign)
    
    # Simulate account collection
    accounts = simulate_account_collection(campaign)
    
    # Apply tags to accounts
    apply_tags_to_accounts(campaign, accounts, system_tags)
    
    # Generate whitelist
    generate_whitelist(campaign, accounts)
    
    print("\nCampaign flow test completed successfully!")

if __name__ == "__main__":
    main()
