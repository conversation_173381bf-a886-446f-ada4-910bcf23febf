#!/bin/bash

# Comprehensive Whitelist Testing Script
# This script provides an easy way to test the whitelist functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
NUM_ACCOUNTS=100
CLEAR_EXISTING=true
CAMPAIGN_ID=""

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to show usage
show_usage() {
    echo "🧪 Comprehensive Whitelist Testing Script"
    echo "=========================================="
    echo ""
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -c, --campaign-id ID     Campaign ID to test (UUID format)"
    echo "  -n, --num-accounts NUM   Number of accounts to simulate (default: 100)"
    echo "  -k, --keep-data         Keep existing data (don't clear)"
    echo "  -l, --list-campaigns    List available campaigns"
    echo "  -h, --help              Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 --list-campaigns"
    echo "  $0 --campaign-id 7a946308-771a-4ccb-b359-8b08ce144943"
    echo "  $0 --campaign-id 7a946308-771a-4ccb-b359-8b08ce144943 --num-accounts 200"
    echo "  $0 --campaign-id 7a946308-771a-4ccb-b359-8b08ce144943 --keep-data"
}

# Function to list campaigns
list_campaigns() {
    print_info "Available Campaigns:"
    echo "===================="
    
    source venv/bin/activate
    python manage.py shell -c "
from campaigns.models import Campaign
campaigns = Campaign.objects.all()
for c in campaigns:
    print(f'ID: {c.id}')
    print(f'Name: {c.name}')
    print(f'Status: {c.status}')
    print('-' * 40)
"
}

# Function to run whitelist simulation
run_simulation() {
    local campaign_id="$1"
    local num_accounts="$2"
    local clear_existing="$3"
    
    print_info "Running Whitelist Testing Simulation"
    echo "====================================="
    echo "Campaign ID: $campaign_id"
    echo "Number of accounts: $num_accounts"
    echo "Clear existing data: $clear_existing"
    echo ""
    
    # Activate virtual environment
    source venv/bin/activate
    
    # Build command
    local cmd="python manage.py simulate_whitelist_testing $campaign_id --num-accounts $num_accounts"
    if [ "$clear_existing" = true ]; then
        cmd="$cmd --clear-existing"
    fi
    
    print_info "Running command: $cmd"
    echo ""
    
    # Run the simulation
    if $cmd; then
        print_success "Simulation completed successfully!"
        echo ""
        print_info "Next Steps:"
        echo "1. View the Whitelist Analytics Dashboard:"
        echo "   http://127.0.0.1:8001/campaigns/$campaign_id/simulation/"
        echo ""
        echo "2. Check the campaign detail page:"
        echo "   http://127.0.0.1:8001/campaigns/$campaign_id/"
        echo ""
        echo "3. Review the generated data in the admin interface"
        echo ""
        print_success "Whitelist testing system is working correctly!"
        echo "You can now test tag filtering, whitelist generation, and analytics."
    else
        print_error "Simulation failed!"
        echo ""
        print_warning "Troubleshooting Tips:"
        echo "1. Make sure Django server is running"
        echo "2. Check database connections"
        echo "3. Verify campaign has valid configuration"
        echo "4. Check Django logs for detailed errors"
        return 1
    fi
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -c|--campaign-id)
            CAMPAIGN_ID="$2"
            shift 2
            ;;
        -n|--num-accounts)
            NUM_ACCOUNTS="$2"
            shift 2
            ;;
        -k|--keep-data)
            CLEAR_EXISTING=false
            shift
            ;;
        -l|--list-campaigns)
            list_campaigns
            exit 0
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    print_error "Virtual environment not found. Please make sure you're in the Django project directory."
    exit 1
fi

# If no campaign ID provided, show interactive selection
if [ -z "$CAMPAIGN_ID" ]; then
    print_info "No campaign ID provided. Listing available campaigns..."
    echo ""
    list_campaigns
    echo ""
    print_info "Please run the script again with --campaign-id option"
    echo "Example: $0 --campaign-id YOUR_CAMPAIGN_ID"
    exit 0
fi

# Validate campaign ID format (basic UUID check)
if [[ ! $CAMPAIGN_ID =~ ^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$ ]]; then
    print_error "Invalid campaign ID format. Expected UUID format."
    exit 1
fi

# Run the simulation
run_simulation "$CAMPAIGN_ID" "$NUM_ACCOUNTS" "$CLEAR_EXISTING"
