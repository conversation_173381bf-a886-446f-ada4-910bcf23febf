[tool.ruff]
# Enable flake8-bugbear (`B`) rules.
select = [
    "E",  # pycodestyle errors
    "F",  # pyflakes
    "B",  # flake8-bugbear
    "I",  # isort
    "C4", # flake8-comprehensions
    "UP", # pyupgrade
    "N",  # pep8-naming
    "COM", # flake8-commas
    "RET", # flake8-return
    "SIM", # flake8-simplify
    "PL",  # pylint
]

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "migrations",
]

# Same as Black.
line-length = 100

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

# Assume Python 3.10.
target-version = "py310"

[tool.ruff.mccabe]
# Unlike Flake8, default to a complexity level of 10.
max-complexity = 10

[tool.ruff.isort]
known-third-party = ["django", "rest_framework"]
section-order = ["future", "standard-library", "django", "third-party", "first-party", "local-folder"]

[tool.ruff.flake8-quotes]
docstring-quotes = "double"
inline-quotes = "single"

[tool.ruff.format]
# Like Black, use double quotes for multiline strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

[tool.pylint.main]
# Specify a configuration file.
rcfile = ".pylintrc"
# Python code to execute, usually for sys.path manipulation such as pygtk.require().
init-hook = "import sys; import os; sys.path.append(os.path.dirname(os.path.abspath('__file__')))"
# Use multiple processes to speed up Pylint.
jobs = 0
# List of plugins (as comma separated values of python modules names) to load.
load-plugins = "pylint_django"
# Pickle collected data for later comparisons.
persistent = true
# Allow loading of arbitrary C extensions.
unsafe-load-any-extension = false
# A comma-separated list of package or module names from where C extensions may be loaded.
extension-pkg-whitelist = ""
# Allow optimization of some AST trees.
optimize-ast = true

[tool.pylint.messages_control]
# Only show warnings with the listed confidence levels.
confidence = ["HIGH", "INFERENCE", "INFERENCE_FAILURE", "UNDEFINED"]
# Disable the message, report, category or checker with the given id(s).
disable = [
    "missing-docstring",
    "invalid-name",
    "too-many-locals",
    "too-many-arguments",
    "too-many-instance-attributes",
    "too-many-public-methods",
    "too-few-public-methods",
    "protected-access",
    "no-self-use",
    "duplicate-code",
    "line-too-long",
    "too-many-lines",
    "fixme",
    "broad-except",
    "no-member",
    "unused-argument",
    "no-else-return",
    "import-outside-toplevel",
    "consider-using-f-string",
]

[tool.pylint.reports]
# Set the output format.
output-format = "text"
# Tells whether to display a full report or only the messages.
reports = false
# Python expression which should return a note less than 10.
evaluation = "10.0 - ((float(5 * error + warning + refactor + convention) / statement) * 10)"

[tool.pylint.format]
# Maximum number of characters on a single line.
max-line-length = 100
# Regexp for a line that is allowed to be longer than the limit.
ignore-long-lines = "^\\s*(# )?<?https?://\\S+>?$"
# Allow the body of an if to be on the same line as the test if there is no else.
single-line-if-stmt = false
# List of optional constructs for which whitespace checking is disabled.
no-space-check = ["trailing-comma", "dict-separator"]
# Maximum number of lines in a module.
max-module-lines = 1000
# String used as indentation unit.
indent-string = "    "
# Number of spaces of indent required inside a hanging or continued line.
indent-after-paren = 4
# Expected format of line ending.
expected-line-ending-format = "LF"

[tool.pylint.basic]
# Good variable names which should always be accepted, separated by a comma.
good-names = ["i", "j", "k", "ex", "Run", "_", "pk", "id"]
# Bad variable names which should always be refused, separated by a comma.
bad-names = ["foo", "bar", "baz", "toto", "tutu", "tata"]
# Colon-delimited sets of names that determine each other's naming style when the name regexes allow several styles.
name-group = []
# Include a hint for the correct naming format with invalid-name.
include-naming-hint = true
# Regular expression matching correct function names.
function-rgx = "[a-z_][a-z0-9_]{2,30}$"
# Naming hint for function names.
function-name-hint = "[a-z_][a-z0-9_]{2,30}$"
# Regular expression matching correct variable names.
variable-rgx = "[a-z_][a-z0-9_]{2,30}$"
# Naming hint for variable names.
variable-name-hint = "[a-z_][a-z0-9_]{2,30}$"
# Regular expression matching correct constant names.
const-rgx = "(([A-Z_][A-Z0-9_]*)|(__.*__))$"
# Naming hint for constant names.
const-name-hint = "(([A-Z_][A-Z0-9_]*)|(__.*__))$"
# Regular expression matching correct attribute names.
attr-rgx = "[a-z_][a-z0-9_]{2,30}$"
# Naming hint for attribute names.
attr-name-hint = "[a-z_][a-z0-9_]{2,30}$"
# Regular expression matching correct argument names.
argument-rgx = "[a-z_][a-z0-9_]{2,30}$"
# Naming hint for argument names.
argument-name-hint = "[a-z_][a-z0-9_]{2,30}$"
# Regular expression matching correct class attribute names.
class-attribute-rgx = "([A-Za-z_][A-Za-z0-9_]{2,30}|(__.*__))$"
# Naming hint for class attribute names.
class-attribute-name-hint = "([A-Za-z_][A-Za-z0-9_]{2,30}|(__.*__))$"
# Regular expression matching correct inline iteration names.
inlinevar-rgx = "[A-Za-z_][A-Za-z0-9_]*$"
# Naming hint for inline iteration names.
inlinevar-name-hint = "[A-Za-z_][A-Za-z0-9_]*$"
# Regular expression matching correct class names.
class-rgx = "[A-Z_][a-zA-Z0-9]+$"
# Naming hint for class names.
class-name-hint = "[A-Z_][a-zA-Z0-9]+$"
# Regular expression matching correct module names.
module-rgx = "(([a-z_][a-z0-9_]*)|([A-Z][a-zA-Z0-9]+))$"
# Naming hint for module names.
module-name-hint = "(([a-z_][a-z0-9_]*)|([A-Z][a-zA-Z0-9]+))$"
# Regular expression matching correct method names.
method-rgx = "[a-z_][a-z0-9_]{2,30}$"
# Naming hint for method names.
method-name-hint = "[a-z_][a-z0-9_]{2,30}$"
# Regular expression which should only match function or class names that do not require a docstring.
no-docstring-rgx = "^_"
# Minimum line length for functions/classes that require docstrings, shorter ones are exempt.
docstring-min-length = -1

# SQLFluff configuration is in .sqlfluff file
