#!/bin/bash
# Script to run linters on the campaigns app

echo "Running linters on the campaigns app..."

# Set the base directory
BASE_DIR="ENGINE/CommandNetSolutions"
CAMPAIGNS_DIR="$BASE_DIR/campaigns"

# Check if we're in the right directory
if [ ! -d "$BASE_DIR" ]; then
    echo "Error: $BASE_DIR directory not found. Make sure you're running this script from the repository root."
    exit 1
fi

# Activate virtual environment if it exists
if [ -d "venv" ]; then
    echo "Activating virtual environment..."
    source venv/bin/activate
fi

echo "----------------------------------------"
echo "Running Ruff on campaigns app..."
echo "----------------------------------------"
ruff check $CAMPAIGNS_DIR

echo "----------------------------------------"
echo "Running Pylint on campaigns app..."
echo "----------------------------------------"
pylint --rcfile=$BASE_DIR/.pylintrc --load-plugins=pylint_django --django-settings-module=CommandNetSolutions.settings $CAMPAIGNS_DIR

echo "----------------------------------------"
echo "Running SQLFluff on campaigns SQL files..."
echo "----------------------------------------"
if [ -d "$CAMPAIGNS_DIR/sql" ]; then
    sqlfluff lint $CAMPAIGNS_DIR/sql/
else
    echo "No SQL directory found in campaigns app."
fi

echo "----------------------------------------"
echo "Running djLint on campaigns templates..."
echo "----------------------------------------"
if [ -d "$CAMPAIGNS_DIR/templates" ]; then
    djlint --check $CAMPAIGNS_DIR/templates/
else
    echo "No templates directory found in campaigns app."
fi

echo "----------------------------------------"
echo "Linting complete!"
echo "----------------------------------------"
