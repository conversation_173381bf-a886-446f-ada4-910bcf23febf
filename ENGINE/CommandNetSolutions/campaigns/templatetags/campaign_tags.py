from django import template

register = template.Library()

@register.filter
def replace_underscore(value):
    """
    Replace all underscores with spaces.

    Usage: {{ value|replace_underscore }}
    """
    if value is None:
        return ''
    return value.replace('_', ' ')

@register.filter
def multiply(value, arg):
    """
    Multiply the value by the argument.

    Usage: {{ value|multiply:2 }}
    """
    try:
        return float(value) * float(arg)
    except (ValueError, TypeError):
        return 0
