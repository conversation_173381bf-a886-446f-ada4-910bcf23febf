"""
Main models for the campaigns app.

This file contains the main models for the campaigns app, including Campaign, LocationTarget, UsernameTarget, etc.
"""
from django.db import models
from django.utils import timezone
from django.contrib.auth.models import User
import uuid

class CampaignQuerySet(models.QuerySet):
    """
    Custom QuerySet for Campaign model to handle missing fields.
    """
    def all(self):
        # Explicitly select only the fields that exist in the database
        return super().all().only(
            'id', 'name', 'description', 'dmp_conf', 'status',
            'target_type', 'audience_type', 'created_at', 'updated_at',
            'airflow_run_id', 'creator_id', 'is_favorite', 'airflow_dag_id'
        )

class CampaignManager(models.Manager):
    """
    Custom manager for Campaign model.
    """
    def get_queryset(self):
        return CampaignQuerySet(self.model, using=self._db)

class Campaign(models.Model):
    """
    Model to store campaign information.
    """
    # Use custom manager
    objects = CampaignManager()
    CAMPAIGN_STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('stopped', 'Stopped'),
    ]

    TARGET_TYPE_CHOICES = [
        ('location', 'Location Based'),
        ('username', 'Username Based'),
        ('mixed', 'Mixed (Both Types)'),
    ]

    AUDIENCE_TYPE_CHOICES = [
        ('profile', 'Profile Only'),
        ('followers', 'Followers Only'),
        ('following', 'Following Only'),
        ('both', 'Followers and Following'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=120)
    description = models.TextField(blank=True, null=True)
    dmp_conf = models.JSONField(default=dict)
    status = models.CharField(max_length=10, choices=CAMPAIGN_STATUS_CHOICES, default='draft')
    target_type = models.CharField(max_length=20, choices=TARGET_TYPE_CHOICES, default='location')
    audience_type = models.CharField(max_length=20, choices=AUDIENCE_TYPE_CHOICES, default='profile')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    airflow_run_id = models.CharField(max_length=200, default='pending')
    creator = models.ForeignKey(User, on_delete=models.DO_NOTHING, null=True)
    is_favorite = models.BooleanField(default=False, help_text="Mark as favorite to easily reuse this campaign's settings")

    # Additional fields we want to use but aren't in the existing schema
    airflow_dag_id = models.CharField(max_length=255, null=True, blank=True)

    # CEP-related fields
    is_in_cep = models.BooleanField(
        default=False,
        help_text="Indicates if this campaign is currently being used in a CEP workflow"
    )

    def __init__(self, *args, **kwargs):
        # Remove scheduled_start and scheduled_end if they're in kwargs
        if 'scheduled_start' in kwargs:
            del kwargs['scheduled_start']
        if 'scheduled_end' in kwargs:
            del kwargs['scheduled_end']
        super().__init__(*args, **kwargs)

    def __str__(self):
        return self.name

    def can_pause(self):
        """Check if the campaign can be paused."""
        return self.status == 'running' and self.airflow_dag_id and self.airflow_run_id != 'pending'

    def can_resume(self):
        """Check if the campaign can be resumed."""
        return self.status == 'paused' and self.airflow_dag_id and self.airflow_run_id != 'pending'

    def can_stop(self):
        """Check if the campaign can be stopped."""
        return self.status in ['running', 'paused', 'pending'] and self.airflow_dag_id and self.airflow_run_id != 'pending'

    def pause(self):
        """Pause the campaign."""
        if self.can_pause():
            self.status = 'paused'
            self.save(update_fields=['status', 'updated_at'])
            return True
        return False

    def resume(self):
        """Resume the campaign."""
        if self.can_resume():
            self.status = 'running'
            self.save(update_fields=['status', 'updated_at'])
            return True
        return False

    def stop(self):
        """Stop the campaign."""
        if self.can_stop():
            self.status = 'stopped'
            self.save(update_fields=['status', 'updated_at'])
            return True
        return False

    class Meta:
        db_table = 'campaigns_campaign'
        verbose_name = "Campaign"
        verbose_name_plural = "Campaigns"
        # Specify the fields to include in the database query
        managed = True
        default_permissions = ('add', 'change', 'delete', 'view')


class LocationTarget(models.Model):
    """
    Model to store location-based campaign targets.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='location_targets')
    country = models.CharField(max_length=100)
    city = models.CharField(max_length=100)
    location_id = models.CharField(max_length=100)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.city}, {self.country}"

    class Meta:
        db_table = 'campaigns_locationtarget'


class UsernameTarget(models.Model):
    """
    Model to store username-based campaign targets.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='username_targets')
    username = models.CharField(max_length=255)
    audience_type = models.CharField(
        max_length=20,
        choices=Campaign.AUDIENCE_TYPE_CHOICES,
        default='profile'
    )
    processed = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.username} ({self.get_audience_type_display()})"

    class Meta:
        db_table = 'campaigns_usernametarget'


class TagAnalysisResult(models.Model):
    """
    Model to store detailed tag analysis results for accounts.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    account_id = models.CharField(max_length=255, default='unknown')
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='tag_analyses')
    tag = models.ForeignKey('DynamicTag', on_delete=models.CASCADE, related_name='analyses')
    matched = models.BooleanField(default=False, help_text="Whether the tag matched the account")
    confidence_score = models.FloatField(default=0.0, help_text="Confidence score for the match (0.0-1.0)")
    match_details = models.JSONField(default=dict, blank=True, help_text="Detailed information about the match")
    analysis_mode = models.CharField(max_length=20, default='manual', help_text="Analysis mode used for this result")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Analysis of {self.tag.name} for account {self.account_id}"

    class Meta:
        db_table = 'campaigns_tag_analysis_result'
        verbose_name = "Tag Analysis Result"
        verbose_name_plural = "Tag Analysis Results"
        unique_together = ('account_id', 'campaign', 'tag')
        indexes = [
            models.Index(fields=['account_id', 'campaign']),
            models.Index(fields=['tag', 'matched']),
            models.Index(fields=['created_at']),
        ]


class CampaignROI(models.Model):
    """
    Model to track campaign ROI metrics.
    """
    COST_TYPE_CHOICES = [
        ('fixed', 'Fixed Cost'),
        ('hourly', 'Hourly Rate'),
        ('per_account', 'Per Account'),
        ('per_whitelist', 'Per Whitelist Entry')
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    campaign = models.OneToOneField(Campaign, on_delete=models.CASCADE, related_name='roi')
    cost_type = models.CharField(max_length=20, choices=COST_TYPE_CHOICES, default='fixed')
    cost_value = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    total_cost = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    estimated_value = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    actual_value = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    roi_percentage = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    conversion_rate = models.DecimalField(max_digits=5, decimal_places=2, default=0.00)
    cost_per_account = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    cost_per_whitelist = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"ROI for {self.campaign.name}"

    def calculate_roi(self):
        """Calculate ROI based on costs and values."""
        if self.total_cost > 0:
            self.roi_percentage = ((self.actual_value - self.total_cost) / self.total_cost) * 100
        else:
            self.roi_percentage = 0
        return self.roi_percentage

    def calculate_costs(self, result=None):
        """Calculate total costs based on cost type and campaign results."""
        if not result:
            try:
                result = self.campaign.results.first()
            except:
                return 0

        if not result:
            return 0

        if self.cost_type == 'fixed':
            self.total_cost = self.cost_value
        elif self.cost_type == 'hourly':
            hours = result.analysis_duration / 3600  # Convert seconds to hours
            self.total_cost = self.cost_value * hours
        elif self.cost_type == 'per_account':
            self.total_cost = self.cost_value * result.total_accounts_processed
        elif self.cost_type == 'per_whitelist':
            self.total_cost = self.cost_value * result.total_accounts_whitelisted

        # Calculate derived metrics
        if result.total_accounts_processed > 0:
            self.cost_per_account = self.total_cost / result.total_accounts_processed

        if result.total_accounts_whitelisted > 0:
            self.cost_per_whitelist = self.total_cost / result.total_accounts_whitelisted
            self.conversion_rate = (result.total_accounts_whitelisted / result.total_accounts_processed) * 100

        return self.total_cost

    class Meta:
        db_table = 'campaigns_campaign_roi'
        verbose_name = "Campaign ROI"
        verbose_name_plural = "Campaign ROIs"


class CampaignResult(models.Model):
    """
    Model to store campaign results and analytics.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    campaign = models.ForeignKey(Campaign, on_delete=models.CASCADE, related_name='results')
    total_accounts_found = models.IntegerField(default=0)
    total_accounts_processed = models.IntegerField(default=0)
    total_accounts_pending = models.IntegerField(default=0)
    total_accounts_tagged = models.IntegerField(default=0, help_text="Number of accounts that received at least one tag")
    total_accounts_whitelisted = models.IntegerField(default=0, help_text="Number of accounts added to whitelist")
    average_confidence_score = models.FloatField(default=0.0, help_text="Average confidence score across all tag matches")
    analysis_duration = models.FloatField(default=0.0, help_text="Total analysis duration in seconds")
    engagement_rate = models.FloatField(default=0.0, help_text="Average engagement rate of processed accounts")
    quality_score = models.FloatField(default=0.0, help_text="Overall quality score of the campaign results")
    last_processed_at = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Results for {self.campaign.name}"

    def calculate_quality_score(self):
        """Calculate a quality score based on multiple metrics."""
        # Weighted formula for quality score
        confidence_weight = 0.4
        engagement_weight = 0.3
        whitelist_weight = 0.3

        # Calculate whitelist ratio
        whitelist_ratio = 0
        if self.total_accounts_processed > 0:
            whitelist_ratio = self.total_accounts_whitelisted / self.total_accounts_processed

        # Calculate quality score (0-100)
        self.quality_score = (
            (self.average_confidence_score * confidence_weight) +
            (self.engagement_rate * engagement_weight) +
            (whitelist_ratio * whitelist_weight)
        ) * 100

        return self.quality_score

    class Meta:
        db_table = 'campaigns_campaignresult'
        verbose_name = "Campaign Result"
        verbose_name_plural = "Campaign Results"


class TagCategory(models.Model):
    """
    Model to organize tags into hierarchical categories.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    parent = models.ForeignKey('self', on_delete=models.CASCADE, null=True, blank=True, related_name='children')
    color = models.CharField(max_length=20, default='#6c757d', help_text="Color code for visual representation")
    icon = models.CharField(max_length=50, blank=True, null=True, help_text="Icon name (e.g., 'tag', 'user', etc.)")
    priority = models.IntegerField(default=0, help_text="Higher priority categories appear first")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'campaigns_tag_category'
        verbose_name = "Tag Category"
        verbose_name_plural = "Tag Categories"
        ordering = ['-priority', 'name']


class TagGroup(models.Model):
    """
    Model to group related tags together.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    color = models.CharField(max_length=20, default='#6c757d', help_text="Color code for visual representation")
    is_global = models.BooleanField(default=False, help_text="If true, this tag group is available to all campaigns")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    creator = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    parent = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True)
    icon = models.CharField(max_length=50, null=True, blank=True, help_text="Icon name (e.g., 'tag', 'folder', etc.)")
    priority = models.IntegerField(default=0, help_text="Higher priority groups appear first")

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'campaigns_tag_group'
        verbose_name = "Tag Group"
        verbose_name_plural = "Tag Groups"
        ordering = ['-priority', 'name']


class DynamicTag(models.Model):
    """
    Model to store dynamic tag definitions that can be used in campaigns.
    """
    TAG_TYPE_CHOICES = [
        ('keyword', 'Keyword Match'),
        ('regex', 'Regular Expression'),
        ('sentiment', 'Sentiment Analysis'),
        ('category', 'Category Classification'),
        ('ml', 'Machine Learning'),
        ('nlp', 'Natural Language Processing')
    ]

    CONFIDENCE_LEVEL_CHOICES = [
        ('high', 'High'),
        ('medium', 'Medium'),
        ('low', 'Low')
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True, null=True)
    category = models.ForeignKey(TagCategory, on_delete=models.SET_NULL, null=True, blank=True, related_name='tags')
    tag_group = models.ForeignKey(TagGroup, on_delete=models.SET_NULL, null=True, blank=True, related_name='tags')
    tag_type = models.CharField(max_length=20, choices=TAG_TYPE_CHOICES, default='keyword')
    pattern = models.TextField(help_text="Pattern to match (keyword, regex, etc.)")
    field = models.CharField(max_length=50, help_text="Account field to analyze (bio, interests, etc.)")
    is_global = models.BooleanField(default=False, help_text="If true, this tag is available to all campaigns")
    is_system = models.BooleanField(default=False, help_text="If true, this tag is a system tag and cannot be deleted")
    # confidence_level field removed as per requirements
    weight = models.FloatField(default=1.0, help_text="Weight factor for scoring calculations")
    related_tags = models.ManyToManyField('self', blank=True, symmetrical=False,
                                         related_name='related_to',
                                         help_text="Tags that are related to this tag")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    def delete(self, *args, **kwargs):
        """
        Override the delete method to prevent deletion of system tags.
        """
        if self.is_system:
            raise ValueError(f"Cannot delete system tag '{self.name}'")
        return super().delete(*args, **kwargs)

    class Meta:
        db_table = 'campaigns_dynamic_tag'
        verbose_name = "Dynamic Tag"
        verbose_name_plural = "Dynamic Tags"
        ordering = ['-is_system', 'category__name', 'name']


class CampaignTag(models.Model):
    """
    Model to associate tags with campaigns.
    This model replaces the functionality of the removed CampaignAnalysisSettings model
    for connecting campaigns to tags.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    campaign = models.ForeignKey(
        Campaign,
        on_delete=models.CASCADE,
        related_name='campaign_tags'
    )
    tag = models.ForeignKey(
        DynamicTag,
        on_delete=models.CASCADE,
        related_name='campaign_assignments'
    )
    is_required = models.BooleanField(
        default=False,
        help_text="If true, this tag is required for whitelist inclusion"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.tag.name} for {self.campaign.name}"

    class Meta:
        db_table = 'campaigns_campaign_tag'
        verbose_name = "Campaign Tag"
        verbose_name_plural = "Campaign Tags"
        unique_together = ('campaign', 'tag')
        ordering = ['tag__name']


class TagMetrics(models.Model):
    """
    Model to store metrics and effectiveness data for tags.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    tag = models.OneToOneField(DynamicTag, on_delete=models.CASCADE, related_name='metrics')
    usage_count = models.IntegerField(default=0, help_text="Number of times this tag has been used")
    match_count = models.IntegerField(default=0, help_text="Number of accounts that matched this tag")
    conversion_rate = models.FloatField(default=0.0, help_text="Percentage of matches that led to conversions")
    precision = models.FloatField(default=0.0, help_text="Precision score (true positives / (true positives + false positives))")
    recall = models.FloatField(default=0.0, help_text="Recall score (true positives / (true positives + false negatives))")
    f1_score = models.FloatField(default=0.0, help_text="F1 score (2 * precision * recall / (precision + recall))")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Metrics for {self.tag.name}"

    class Meta:
        db_table = 'campaigns_tag_metrics'
        verbose_name = "Tag Metrics"
        verbose_name_plural = "Tag Metrics"


class CampaignTagRule(models.Model):
    """
    Model to store tag rules for campaigns.
    """
    LOGIC_CHOICES = [
        ('all', 'All conditions must match'),
        ('any', 'Any condition can match')
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=100, blank=True, null=True)
    tag = models.CharField(max_length=50, blank=True, null=True)
    description = models.TextField(blank=True, null=True)
    active = models.BooleanField(default=True)
    is_global = models.BooleanField(default=False, help_text='If true, this tag is available to all campaigns')
    logic = models.CharField(max_length=10, choices=LOGIC_CHOICES, default='all',
                            help_text='Determines how conditions are combined')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'campaigns_tag_rule'
        verbose_name = 'Campaign Tag Rule'
        verbose_name_plural = 'Campaign Tag Rules'
        ordering = ['name']


class CampaignTagCondition(models.Model):
    """
    Model to store conditions for tag rules.
    """
    FIELD_TYPE_CHOICES = [
        ('string', 'String'),
        ('number', 'Number'),
        ('boolean', 'Boolean'),
        ('array', 'Array'),
        ('date', 'Date')
    ]

    OPERATOR_CHOICES = [
        # String operators
        ('contains', 'Contains'),
        ('not_contains', 'Does Not Contain'),
        ('equals', 'Equals'),
        ('not_equals', 'Does Not Equal'),
        ('starts_with', 'Starts With'),
        ('ends_with', 'Ends With'),
        ('matches', 'Matches Regex'),
        ('isnull', 'Is Null'),
        ('notnull', 'Is Not Null'),
        ('isempty', 'Is Empty'),
        ('isnotempty', 'Is Not Empty'),

        # Number operators
        ('gt', 'Greater Than'),
        ('gte', 'Greater Than or Equal'),
        ('lt', 'Less Than'),
        ('lte', 'Less Than or Equal'),
        ('between', 'Between'),

        # Boolean operators
        ('is_true', 'Is True'),
        ('is_false', 'Is False'),

        # Array operators
        ('contains_any', 'Contains Any'),
        ('contains_all', 'Contains All'),
        ('not_contains_any', 'Does Not Contain Any'),
        ('length', 'Has Length'),

        # Date operators
        ('before', 'Before'),
        ('after', 'After'),
        ('between_dates', 'Between Dates'),
        ('older_than', 'Older Than'),
        ('newer_than', 'Newer Than')
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    rule = models.ForeignKey(CampaignTagRule, on_delete=models.CASCADE, related_name='conditions')
    field = models.CharField(max_length=50)
    field_type = models.CharField(max_length=10, choices=FIELD_TYPE_CHOICES, default='string')
    operator = models.CharField(max_length=20, choices=OPERATOR_CHOICES)
    value = models.JSONField(help_text='Value to compare against (stored as JSON)')
    score = models.IntegerField(default=1, help_text='Score contribution for this condition')
    required = models.BooleanField(default=True, help_text='If true, this condition must pass for the rule to match')
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.field} {self.operator}"

    class Meta:
        db_table = 'campaigns_tag_condition'
        verbose_name = 'Tag Rule Condition'
        verbose_name_plural = 'Tag Rule Conditions'


# Create TagRuleCondition as an alias for CampaignTagCondition for backward compatibility
# This resolves the import error in views_original.py and other files
class TagRuleCondition(CampaignTagCondition):
    """
    Alias for CampaignTagCondition to maintain backward compatibility.
    This model inherits all fields and methods from CampaignTagCondition.
    """
    class Meta:
        proxy = True
        verbose_name = 'Tag Rule Condition'
        verbose_name_plural = 'Tag Rule Conditions'





class Notification(models.Model):
    """
    Model to store user notifications.
    """
    NOTIFICATION_TYPE_CHOICES = [
        ('info', 'Information'),
        ('success', 'Success'),
        ('warning', 'Warning'),
        ('error', 'Error')
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    title = models.CharField(max_length=255)
    message = models.TextField()
    notification_type = models.CharField(max_length=20, choices=NOTIFICATION_TYPE_CHOICES, default='info')
    link = models.CharField(max_length=255, blank=True, null=True)
    data = models.JSONField(default=dict, blank=True)
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.title} ({self.user.username})"

    def mark_as_read(self):
        """Mark notification as read."""
        self.is_read = True
        self.read_at = timezone.now()
        self.save(update_fields=['is_read', 'read_at'])

    class Meta:
        db_table = 'campaigns_user_notification'
        verbose_name = "User Notification"
        verbose_name_plural = "User Notifications"
        ordering = ['-created_at']
