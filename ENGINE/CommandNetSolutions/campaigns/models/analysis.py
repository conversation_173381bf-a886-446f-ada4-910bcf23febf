"""
Models for campaign analysis settings.
"""
import uuid
from django.db import models
from django.utils import timezone
from .main import Campaign, DynamicTag


class CampaignAnalysisSettings(models.Model):
    """
    Model to store campaign analysis settings.
    """
    ANALYSIS_FREQUENCY_CHOICES = [
        ('immediate', 'After Collection'),
        ('hourly', 'Hourly'),
        ('daily', 'Daily'),
        ('manual', 'Manual Only'),
    ]

    ANALYSIS_MODE_CHOICES = [
        ('basic', 'Basic Analysis'),
        ('advanced', 'Advanced Analysis with ML'),
        ('comprehensive', 'Comprehensive Analysis with NLP'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    campaign = models.OneToOneField(
        Campaign, 
        on_delete=models.CASCADE, 
        related_name='analysis_settings'
    )
    auto_analyze = models.BooleanField(
        default=True,
        help_text="Automatically analyze accounts after collection"
    )
    analysis_frequency = models.CharField(
        max_length=20,
        choices=ANALYSIS_FREQUENCY_CHOICES,
        default='immediate',
        help_text="How often to analyze accounts"
    )
    analysis_mode = models.CharField(
        max_length=20,
        choices=ANALYSIS_MODE_CHOICES,
        default='basic',
        help_text="Determines the depth and methods used for analysis"
    )
    min_followers = models.IntegerField(
        blank=True, 
        null=True,
        help_text="Minimum number of followers for analysis (leave blank for no minimum)"
    )
    max_followers = models.IntegerField(
        blank=True, 
        null=True,
        help_text="Maximum number of followers for analysis (leave blank for no maximum)"
    )
    enable_tagging = models.BooleanField(
        default=True,
        help_text="Enable automatic tagging of accounts"
    )
    active_workflows = models.JSONField(
        default=dict,
        help_text="Currently active workflows for this campaign"
    )
    completed_workflows = models.JSONField(
        default=dict,
        help_text="Completed workflows for this campaign"
    )
    workflow_statistics = models.JSONField(
        default=dict,
        help_text="Statistics for workflow execution"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Analysis Settings for {self.campaign.name}"

    class Meta:
        db_table = 'campaigns_analysis_settings'
        verbose_name = "Campaign Analysis Settings"
        verbose_name_plural = "Campaign Analysis Settings"


class CampaignAnalysisTag(models.Model):
    """
    Model to associate tags with campaign analysis settings.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    analysis_settings = models.ForeignKey(
        CampaignAnalysisSettings,
        on_delete=models.CASCADE,
        related_name='analysis_tags'
    )
    tag = models.ForeignKey(
        DynamicTag,
        on_delete=models.CASCADE,
        related_name='analysis_settings'
    )
    is_required = models.BooleanField(
        default=False,
        help_text="If true, accounts must match this tag to be included in the whitelist"
    )
    weight = models.FloatField(
        default=1.0,
        help_text="Weight factor for this tag in the analysis"
    )
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.tag.name} for {self.analysis_settings.campaign.name}"

    class Meta:
        db_table = 'campaigns_analysis_tag'
        verbose_name = "Campaign Analysis Tag"
        verbose_name_plural = "Campaign Analysis Tags"
        unique_together = ('analysis_settings', 'tag')
