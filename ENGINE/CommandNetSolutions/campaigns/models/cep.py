"""
Models for CEP (Customer Engagement Process) functionality.

This file contains models related to the CEP workflow management,
including the CEPWorkflow model that enforces the one-active-workflow constraint.
"""
import uuid
from django.db import models
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.db.models.signals import pre_save, post_save
from django.dispatch import receiver

from campaigns.models.main import Campaign

class CEPWorkflow(models.Model):
    """
    Model to track CEP (Customer Engagement Process) workflows.
    
    This model enforces the constraint that only one active CEP workflow
    can exist at any time across the entire system.
    """
    SUBSCRIPTION_TIER_CHOICES = [
        ('bronze', 'Bronze'),
        ('silver', 'Silver'),
        ('gold', 'Gold'),
    ]
    
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('running', 'Running'),
        ('paused', 'Paused'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('stopped', 'Stopped'),
    ]
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    campaign = models.OneToOneField(
        Campaign, 
        on_delete=models.CASCADE, 
        related_name='cep_workflow',
        help_text="Campaign associated with this CEP workflow"
    )
    subscription_tier = models.CharField(
        max_length=10, 
        choices=SUBSCRIPTION_TIER_CHOICES,
        default='bronze',
        help_text="Subscription tier determining available engagement actions"
    )
    status = models.CharField(
        max_length=10, 
        choices=STATUS_CHOICES,
        default='pending',
        help_text="Current status of the CEP workflow"
    )
    airflow_dag_id = models.CharField(
        max_length=255, 
        null=True, 
        blank=True,
        help_text="Airflow DAG ID for this CEP workflow"
    )
    airflow_run_id = models.CharField(
        max_length=255, 
        null=True, 
        blank=True,
        help_text="Airflow run ID for this CEP workflow"
    )
    follow_progress = models.FloatField(
        default=0.0,
        help_text="Progress percentage for follow actions (0-100)"
    )
    like_progress = models.FloatField(
        default=0.0,
        help_text="Progress percentage for like actions (0-100)"
    )
    comment_progress = models.FloatField(
        default=0.0,
        help_text="Progress percentage for comment actions (0-100)"
    )
    dm_progress = models.FloatField(
        default=0.0,
        help_text="Progress percentage for direct message actions (0-100)"
    )
    whitelist_count = models.IntegerField(
        default=0,
        help_text="Number of accounts in the whitelist"
    )
    started_at = models.DateTimeField(
        null=True, 
        blank=True,
        help_text="When the CEP workflow was started"
    )
    completed_at = models.DateTimeField(
        null=True, 
        blank=True,
        help_text="When the CEP workflow was completed"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'campaigns_cep_workflow'
        verbose_name = "CEP Workflow"
        verbose_name_plural = "CEP Workflows"
        
    def __str__(self):
        return f"CEP Workflow for {self.campaign.name} ({self.get_status_display()})"
    
    def clean(self):
        """
        Validate that only one active CEP workflow exists.
        """
        # Check if there's already an active CEP workflow (excluding self)
        active_workflows = CEPWorkflow.objects.filter(
            status__in=['pending', 'running', 'paused']
        ).exclude(pk=self.pk)
        
        if active_workflows.exists() and self.status in ['pending', 'running', 'paused']:
            active_workflow = active_workflows.first()
            raise ValidationError(
                f"Only one active CEP workflow is allowed. "
                f"There is already an active workflow for campaign '{active_workflow.campaign.name}'."
            )
        
        # Check if the campaign has a non-empty whitelist
        from instagram.models import WhiteListEntry, Accounts
        whitelist_count = WhiteListEntry.objects.filter(
            account__campaign_id=str(self.campaign.id)
        ).count()
        
        if whitelist_count == 0 and self.status in ['pending', 'running']:
            raise ValidationError(
                "Campaign must have at least one account in the whitelist to start a CEP workflow."
            )
        
        self.whitelist_count = whitelist_count
    
    def save(self, *args, **kwargs):
        """
        Save the CEP workflow with validation.
        """
        self.clean()
        
        # Set started_at when transitioning to running
        if self.status == 'running' and not self.started_at:
            self.started_at = timezone.now()
            
        # Set completed_at when transitioning to completed, failed, or stopped
        if self.status in ['completed', 'failed', 'stopped'] and not self.completed_at:
            self.completed_at = timezone.now()
            
        super().save(*args, **kwargs)
    
    def start(self):
        """
        Start the CEP workflow.
        """
        if self.status == 'pending':
            self.status = 'running'
            self.started_at = timezone.now()
            self.save()
            return True
        return False
    
    def pause(self):
        """
        Pause the CEP workflow.
        """
        if self.status == 'running':
            self.status = 'paused'
            self.save()
            return True
        return False
    
    def resume(self):
        """
        Resume the CEP workflow.
        """
        if self.status == 'paused':
            self.status = 'running'
            self.save()
            return True
        return False
    
    def stop(self):
        """
        Stop the CEP workflow.
        """
        if self.status in ['pending', 'running', 'paused']:
            self.status = 'stopped'
            self.completed_at = timezone.now()
            self.save()
            return True
        return False
    
    def update_progress(self, action_type, progress):
        """
        Update the progress for a specific action type.
        
        Args:
            action_type (str): Type of action ('follow', 'like', 'comment', 'dm')
            progress (float): Progress percentage (0-100)
        """
        if action_type == 'follow':
            self.follow_progress = progress
        elif action_type == 'like':
            self.like_progress = progress
        elif action_type == 'comment':
            self.comment_progress = progress
        elif action_type == 'dm':
            self.dm_progress = progress
            
        self.save(update_fields=[f'{action_type}_progress', 'updated_at'])
    
    @property
    def overall_progress(self):
        """
        Calculate the overall progress across all action types.
        
        Returns:
            float: Overall progress percentage (0-100)
        """
        # Determine which actions are available based on subscription tier
        if self.subscription_tier == 'bronze':
            # Bronze: Only follow and like
            return (self.follow_progress + self.like_progress) / 2
        elif self.subscription_tier == 'silver':
            # Silver: Follow, like, and comment
            return (self.follow_progress + self.like_progress + self.comment_progress) / 3
        else:
            # Gold: All actions
            return (self.follow_progress + self.like_progress + self.comment_progress + self.dm_progress) / 4

# Add is_in_cep field to Campaign model through a migration
# This will be done in a separate migration file

# Signal to update Campaign.is_in_cep when CEPWorkflow status changes
@receiver(post_save, sender=CEPWorkflow)
def update_campaign_cep_status(sender, instance, **kwargs):
    """
    Update the campaign's is_in_cep field when CEPWorkflow status changes.
    """
    campaign = instance.campaign
    
    # Set is_in_cep to True if workflow is active, False otherwise
    is_active = instance.status in ['pending', 'running', 'paused']
    
    # Only update if necessary to avoid infinite recursion
    if hasattr(campaign, 'is_in_cep') and campaign.is_in_cep != is_active:
        campaign.is_in_cep = is_active
        campaign.save(update_fields=['is_in_cep', 'updated_at'])
