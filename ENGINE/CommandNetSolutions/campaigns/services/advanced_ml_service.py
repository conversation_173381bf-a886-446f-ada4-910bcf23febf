"""
Advanced Machine Learning Service for enhanced account analysis.
"""
import os
import json
import logging
import numpy as np
import pickle
from django.conf import settings
from django.utils import timezone
from campaigns.services.ml_service import MLService

logger = logging.getLogger(__name__)

# Define model directory
MODEL_DIR = os.path.join(settings.BASE_DIR, 'campaigns', 'ml_models')
os.makedirs(MODEL_DIR, exist_ok=True)


class AdvancedMLService(MLService):
    """
    Advanced Machine Learning Service for enhanced account analysis.
    Extends the basic MLService with more sophisticated models and features.
    """
    
    def __init__(self):
        """Initialize the advanced ML service."""
        super().__init__()
        self.models = {}
        self.vectorizers = {}
        self.load_models()
    
    def load_models(self):
        """Load ML models from disk."""
        try:
            # Load sentiment analysis model
            sentiment_model_path = os.path.join(MODEL_DIR, 'sentiment_model.pkl')
            sentiment_vectorizer_path = os.path.join(MODEL_DIR, 'sentiment_vectorizer.pkl')
            
            if os.path.exists(sentiment_model_path) and os.path.exists(sentiment_vectorizer_path):
                with open(sentiment_model_path, 'rb') as f:
                    self.models['sentiment'] = pickle.load(f)
                
                with open(sentiment_vectorizer_path, 'rb') as f:
                    self.vectorizers['sentiment'] = pickle.load(f)
                
                logger.info("Loaded sentiment analysis model")
            
            # Load engagement prediction model
            engagement_model_path = os.path.join(MODEL_DIR, 'engagement_model.pkl')
            engagement_vectorizer_path = os.path.join(MODEL_DIR, 'engagement_vectorizer.pkl')
            
            if os.path.exists(engagement_model_path) and os.path.exists(engagement_vectorizer_path):
                with open(engagement_model_path, 'rb') as f:
                    self.models['engagement'] = pickle.load(f)
                
                with open(engagement_vectorizer_path, 'rb') as f:
                    self.vectorizers['engagement'] = pickle.load(f)
                
                logger.info("Loaded engagement prediction model")
            
            # Load audience quality model
            quality_model_path = os.path.join(MODEL_DIR, 'quality_model.pkl')
            quality_vectorizer_path = os.path.join(MODEL_DIR, 'quality_vectorizer.pkl')
            
            if os.path.exists(quality_model_path) and os.path.exists(quality_vectorizer_path):
                with open(quality_model_path, 'rb') as f:
                    self.models['quality'] = pickle.load(f)
                
                with open(quality_vectorizer_path, 'rb') as f:
                    self.vectorizers['quality'] = pickle.load(f)
                
                logger.info("Loaded audience quality model")
        except Exception as e:
            logger.exception(f"Error loading ML models: {str(e)}")
    
    def analyze_sentiment(self, text):
        """
        Analyze sentiment of text.
        
        Args:
            text (str): Text to analyze
            
        Returns:
            dict: Sentiment analysis result
        """
        if 'sentiment' not in self.models or 'sentiment' not in self.vectorizers:
            # Fall back to basic sentiment analysis
            return super().analyze_sentiment(text)
        
        try:
            # Preprocess text
            if not text:
                return {
                    'sentiment': 'neutral',
                    'score': 0.5,
                    'confidence': 0.0
                }
            
            # Vectorize text
            features = self.vectorizers['sentiment'].transform([text])
            
            # Predict sentiment
            sentiment_proba = self.models['sentiment'].predict_proba(features)[0]
            sentiment_idx = np.argmax(sentiment_proba)
            sentiment_score = sentiment_proba[sentiment_idx]
            
            # Map index to sentiment
            sentiments = ['negative', 'neutral', 'positive']
            sentiment = sentiments[sentiment_idx]
            
            # Calculate normalized score (0-1)
            normalized_score = 0.0
            if sentiment == 'negative':
                normalized_score = 0.0 + (0.33 * sentiment_score)
            elif sentiment == 'neutral':
                normalized_score = 0.33 + (0.33 * sentiment_score)
            else:
                normalized_score = 0.67 + (0.33 * sentiment_score)
            
            return {
                'sentiment': sentiment,
                'score': normalized_score,
                'confidence': sentiment_score
            }
        except Exception as e:
            logger.exception(f"Error analyzing sentiment: {str(e)}")
            # Fall back to basic sentiment analysis
            return super().analyze_sentiment(text)
    
    def predict_engagement(self, account_data):
        """
        Predict engagement rate for an account.
        
        Args:
            account_data (dict): Account data
            
        Returns:
            dict: Engagement prediction result
        """
        if 'engagement' not in self.models or 'engagement' not in self.vectorizers:
            # Fall back to basic engagement prediction
            return super().predict_engagement(account_data)
        
        try:
            # Extract features
            features = self._extract_account_features(account_data)
            
            # Vectorize features
            feature_vector = self.vectorizers['engagement'].transform([features])
            
            # Predict engagement
            engagement_rate = self.models['engagement'].predict(feature_vector)[0]
            confidence = 0.8  # Fixed confidence for now
            
            return {
                'engagement_rate': float(engagement_rate),
                'confidence': confidence
            }
        except Exception as e:
            logger.exception(f"Error predicting engagement: {str(e)}")
            # Fall back to basic engagement prediction
            return super().predict_engagement(account_data)
    
    def assess_account_quality(self, account_data):
        """
        Assess overall quality of an account.
        
        Args:
            account_data (dict): Account data
            
        Returns:
            dict: Quality assessment result
        """
        if 'quality' not in self.models or 'quality' not in self.vectorizers:
            # Fall back to basic quality assessment
            return super().assess_account_quality(account_data)
        
        try:
            # Extract features
            features = self._extract_account_features(account_data)
            
            # Vectorize features
            feature_vector = self.vectorizers['quality'].transform([features])
            
            # Predict quality
            quality_proba = self.models['quality'].predict_proba(feature_vector)[0]
            quality_idx = np.argmax(quality_proba)
            quality_score = quality_proba[quality_idx]
            
            # Map index to quality level
            quality_levels = ['low', 'medium', 'high']
            quality = quality_levels[quality_idx]
            
            # Calculate normalized score (0-1)
            normalized_score = 0.0
            if quality == 'low':
                normalized_score = 0.0 + (0.33 * quality_score)
            elif quality == 'medium':
                normalized_score = 0.33 + (0.33 * quality_score)
            else:
                normalized_score = 0.67 + (0.33 * quality_score)
            
            return {
                'quality': quality,
                'score': normalized_score,
                'confidence': quality_score
            }
        except Exception as e:
            logger.exception(f"Error assessing account quality: {str(e)}")
            # Fall back to basic quality assessment
            return super().assess_account_quality(account_data)
    
    def detect_fake_followers(self, account_data):
        """
        Detect fake followers in an account.
        
        Args:
            account_data (dict): Account data
            
        Returns:
            dict: Fake follower detection result
        """
        try:
            # Extract relevant metrics
            followers = account_data.get('followers', 0)
            following = account_data.get('following', 0)
            posts = account_data.get('posts', 0)
            engagement = account_data.get('engagement_rate', 0.0)
            
            # Calculate follower-following ratio
            if following > 0:
                follower_ratio = followers / following
            else:
                follower_ratio = followers
            
            # Calculate post frequency
            account_age_days = account_data.get('account_age_days', 365)
            if account_age_days > 0:
                post_frequency = posts / account_age_days
            else:
                post_frequency = 0
            
            # Calculate fake follower probability
            fake_probability = 0.0
            
            # Suspicious patterns
            if followers > 1000 and engagement < 0.01:
                fake_probability += 0.3
            
            if followers > 5000 and posts < 10:
                fake_probability += 0.2
            
            if follower_ratio > 10 and engagement < 0.02:
                fake_probability += 0.2
            
            if post_frequency < 0.05 and followers > 1000:
                fake_probability += 0.1
            
            # Cap probability at 1.0
            fake_probability = min(fake_probability, 1.0)
            
            # Determine result
            if fake_probability > 0.7:
                result = 'high'
            elif fake_probability > 0.4:
                result = 'medium'
            else:
                result = 'low'
            
            return {
                'fake_follower_probability': fake_probability,
                'risk_level': result,
                'confidence': 0.8
            }
        except Exception as e:
            logger.exception(f"Error detecting fake followers: {str(e)}")
            return {
                'fake_follower_probability': 0.0,
                'risk_level': 'unknown',
                'confidence': 0.0
            }
    
    def predict_content_performance(self, content_data):
        """
        Predict performance of content.
        
        Args:
            content_data (dict): Content data
            
        Returns:
            dict: Content performance prediction
        """
        try:
            # Extract relevant features
            caption = content_data.get('caption', '')
            hashtags = content_data.get('hashtags', [])
            image_type = content_data.get('image_type', 'photo')
            post_time = content_data.get('post_time', '')
            
            # Analyze caption sentiment
            sentiment_result = self.analyze_sentiment(caption)
            sentiment_score = sentiment_result.get('score', 0.5)
            
            # Calculate hashtag effectiveness
            hashtag_count = len(hashtags)
            hashtag_score = 0.0
            
            if 0 < hashtag_count <= 5:
                hashtag_score = 0.7
            elif 5 < hashtag_count <= 15:
                hashtag_score = 1.0
            elif 15 < hashtag_count <= 30:
                hashtag_score = 0.8
            else:
                hashtag_score = 0.5
            
            # Calculate image type score
            image_type_score = 0.0
            
            if image_type == 'carousel':
                image_type_score = 1.0
            elif image_type == 'video':
                image_type_score = 0.9
            elif image_type == 'photo':
                image_type_score = 0.7
            else:
                image_type_score = 0.5
            
            # Calculate time score
            time_score = 0.8  # Default
            
            # Calculate overall performance score
            performance_score = (
                sentiment_score * 0.3 +
                hashtag_score * 0.3 +
                image_type_score * 0.3 +
                time_score * 0.1
            )
            
            # Predict engagement rate
            base_engagement = 0.03  # 3% base engagement rate
            predicted_engagement = base_engagement * performance_score * 2
            
            # Determine performance level
            if performance_score > 0.8:
                performance_level = 'high'
            elif performance_score > 0.5:
                performance_level = 'medium'
            else:
                performance_level = 'low'
            
            return {
                'performance_score': performance_score,
                'performance_level': performance_level,
                'predicted_engagement': predicted_engagement,
                'factors': {
                    'sentiment': sentiment_result.get('sentiment'),
                    'hashtags': {
                        'count': hashtag_count,
                        'effectiveness': hashtag_score
                    },
                    'content_type': {
                        'type': image_type,
                        'effectiveness': image_type_score
                    }
                }
            }
        except Exception as e:
            logger.exception(f"Error predicting content performance: {str(e)}")
            return {
                'performance_score': 0.5,
                'performance_level': 'medium',
                'predicted_engagement': 0.03,
                'factors': {}
            }
    
    def analyze_audience(self, audience_data):
        """
        Analyze audience demographics and interests.
        
        Args:
            audience_data (list): List of audience accounts
            
        Returns:
            dict: Audience analysis result
        """
        try:
            if not audience_data:
                return {
                    'demographics': {},
                    'interests': {},
                    'quality': {
                        'score': 0.0,
                        'level': 'unknown'
                    }
                }
            
            # Extract demographics
            age_groups = {'18-24': 0, '25-34': 0, '35-44': 0, '45+': 0, 'unknown': 0}
            genders = {'male': 0, 'female': 0, 'unknown': 0}
            locations = {}
            
            # Extract interests
            interests = {}
            
            # Calculate quality metrics
            engagement_rates = []
            follower_counts = []
            quality_scores = []
            
            # Process audience data
            for account in audience_data:
                # Demographics
                age_group = account.get('age_group', 'unknown')
                gender = account.get('gender', 'unknown')
                location = account.get('location', 'unknown')
                
                if age_group in age_groups:
                    age_groups[age_group] += 1
                else:
                    age_groups['unknown'] += 1
                
                if gender in genders:
                    genders[gender] += 1
                else:
                    genders['unknown'] += 1
                
                if location:
                    locations[location] = locations.get(location, 0) + 1
                
                # Interests
                account_interests = account.get('interests', [])
                for interest in account_interests:
                    interests[interest] = interests.get(interest, 0) + 1
                
                # Quality metrics
                engagement_rates.append(account.get('engagement_rate', 0.0))
                follower_counts.append(account.get('followers', 0))
                
                # Assess account quality
                quality_result = self.assess_account_quality(account)
                quality_scores.append(quality_result.get('score', 0.5))
            
            # Calculate percentages for demographics
            total_accounts = len(audience_data)
            
            age_distribution = {k: (v / total_accounts) * 100 for k, v in age_groups.items() if v > 0}
            gender_distribution = {k: (v / total_accounts) * 100 for k, v in genders.items() if v > 0}
            
            # Sort locations by count
            sorted_locations = sorted(locations.items(), key=lambda x: x[1], reverse=True)
            top_locations = dict(sorted_locations[:10])
            location_distribution = {k: (v / total_accounts) * 100 for k, v in top_locations.items()}
            
            # Sort interests by count
            sorted_interests = sorted(interests.items(), key=lambda x: x[1], reverse=True)
            top_interests = dict(sorted_interests[:20])
            interest_distribution = {k: (v / total_accounts) * 100 for k, v in top_interests.items()}
            
            # Calculate average metrics
            avg_engagement = sum(engagement_rates) / len(engagement_rates) if engagement_rates else 0
            avg_followers = sum(follower_counts) / len(follower_counts) if follower_counts else 0
            avg_quality = sum(quality_scores) / len(quality_scores) if quality_scores else 0
            
            # Determine audience quality level
            if avg_quality > 0.8:
                quality_level = 'high'
            elif avg_quality > 0.5:
                quality_level = 'medium'
            else:
                quality_level = 'low'
            
            return {
                'demographics': {
                    'age': age_distribution,
                    'gender': gender_distribution,
                    'location': location_distribution
                },
                'interests': interest_distribution,
                'quality': {
                    'score': avg_quality,
                    'level': quality_level,
                    'metrics': {
                        'avg_engagement': avg_engagement,
                        'avg_followers': avg_followers
                    }
                }
            }
        except Exception as e:
            logger.exception(f"Error analyzing audience: {str(e)}")
            return {
                'demographics': {},
                'interests': {},
                'quality': {
                    'score': 0.0,
                    'level': 'unknown'
                }
            }
    
    def _extract_account_features(self, account_data):
        """
        Extract features from account data for ML models.
        
        Args:
            account_data (dict): Account data
            
        Returns:
            str: Feature string
        """
        # Extract basic metrics
        followers = account_data.get('followers', 0)
        following = account_data.get('following', 0)
        posts = account_data.get('posts', 0)
        
        # Calculate derived metrics
        if following > 0:
            follower_ratio = followers / following
        else:
            follower_ratio = 0
        
        if followers > 0:
            post_ratio = posts / followers
        else:
            post_ratio = 0
        
        # Extract text data
        bio = account_data.get('bio', '')
        username = account_data.get('username', '')
        
        # Combine features
        features = f"{bio} {username} followers:{followers} following:{following} posts:{posts} "
        features += f"follower_ratio:{follower_ratio:.2f} post_ratio:{post_ratio:.2f}"
        
        return features
    
    def train_sentiment_model(self, training_data):
        """
        Train sentiment analysis model.
        
        Args:
            training_data (list): List of (text, sentiment) tuples
            
        Returns:
            bool: Success status
        """
        try:
            from sklearn.feature_extraction.text import TfidfVectorizer
            from sklearn.ensemble import RandomForestClassifier
            
            # Extract texts and labels
            texts = [item[0] for item in training_data]
            labels = [item[1] for item in training_data]
            
            # Create vectorizer
            vectorizer = TfidfVectorizer(max_features=5000, ngram_range=(1, 2))
            X = vectorizer.fit_transform(texts)
            
            # Train model
            model = RandomForestClassifier(n_estimators=100, random_state=42)
            model.fit(X, labels)
            
            # Save model and vectorizer
            with open(os.path.join(MODEL_DIR, 'sentiment_model.pkl'), 'wb') as f:
                pickle.dump(model, f)
            
            with open(os.path.join(MODEL_DIR, 'sentiment_vectorizer.pkl'), 'wb') as f:
                pickle.dump(vectorizer, f)
            
            # Update in-memory models
            self.models['sentiment'] = model
            self.vectorizers['sentiment'] = vectorizer
            
            return True
        except Exception as e:
            logger.exception(f"Error training sentiment model: {str(e)}")
            return False
