"""
Airflow service for interacting with Airflow API.

This service provides methods for:
1. Triggering DAGs with proper configuration
2. Monitoring DAG execution status
3. Retrieving DAG run results
4. Managing DAG schedules
"""
import json
import requests
import logging
import time
from datetime import datetime, timedelta
from django.conf import settings
from django.utils import timezone
from django.db import transaction

logger = logging.getLogger(__name__)

class AirflowService:
    """
    Service for interacting with Airflow API.
    """
    def __init__(self):
        """Initialize the Airflow service."""
        self.base_url = getattr(settings, 'AIRFLOW_API_URL', 'http://localhost:8080/api/v1')
        self.username = getattr(settings, 'AIRFLOW_USERNAME', 'airflow')
        self.password = getattr(settings, 'AIRFLOW_PASSWORD', 'airflow')
        self.timeout = getattr(settings, 'AIRFLOW_API_TIMEOUT', 10)
        self.max_retries = getattr(settings, 'AIRFLOW_API_MAX_RETRIES', 3)
        self.retry_delay = getattr(settings, 'AIRFLOW_API_RETRY_DELAY', 2)

    def _make_request(self, method, endpoint, data=None, params=None, retry_count=0):
        """
        Make a request to the Airflow API with retry logic.

        Args:
            method (str): HTTP method (GET, POST, etc.)
            endpoint (str): API endpoint
            data (dict): Request data for POST/PUT requests
            params (dict): Query parameters for GET requests
            retry_count (int): Current retry count

        Returns:
            dict: Response data or error information
        """
        url = f"{self.base_url}/{endpoint.lstrip('/')}"

        try:
            response = requests.request(
                method,
                url,
                json=data,
                params=params,
                auth=(self.username, self.password),
                headers={"Content-Type": "application/json"},
                timeout=self.timeout
            )

            # Check if request was successful
            if response.status_code in (200, 201, 204):
                try:
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'data': response.json() if response.content else {}
                    }
                except ValueError:
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'data': {}
                    }
            else:
                # Log error
                logger.error(f"Airflow API error: {response.status_code} - {response.text}")

                # Retry on server errors (5xx) or rate limiting (429)
                if response.status_code in (429, 500, 502, 503, 504) and retry_count < self.max_retries:
                    retry_count += 1
                    logger.info(f"Retrying request ({retry_count}/{self.max_retries})...")
                    time.sleep(self.retry_delay)
                    return self._make_request(method, endpoint, data, params, retry_count)

                # Return error response
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': response.text
                }

        except requests.exceptions.RequestException as e:
            logger.error(f"Airflow API request error: {str(e)}")

            # Retry on connection errors
            if retry_count < self.max_retries:
                retry_count += 1
                logger.info(f"Retrying request ({retry_count}/{self.max_retries})...")
                time.sleep(self.retry_delay)
                return self._make_request(method, endpoint, data, params, retry_count)

            # Return error response
            return {
                'success': False,
                'status_code': 0,
                'error': str(e)
            }

    def trigger_dag(self, dag_id, conf=None, run_id=None, execution_date=None, wait_for_completion=False, timeout=300):
        """
        Trigger a DAG run with optional configuration.

        Args:
            dag_id (str): The ID of the DAG to trigger
            conf (dict, optional): Configuration parameters to pass to the DAG
            run_id (str, optional): Custom run ID for the DAG run
            execution_date (str, optional): Execution date for the DAG run
            wait_for_completion (bool): Whether to wait for the DAG to complete
            timeout (int): Maximum time to wait for completion in seconds

        Returns:
            dict: Response with DAG run information
        """
        # Generate a unique run ID if not provided
        if not run_id:
            run_id = f"manual_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Prepare payload
        payload = {
            "dag_run_id": run_id,
            "logical_date": execution_date or datetime.now().isoformat(),
        }

        # Add configuration if provided
        if conf:
            payload["conf"] = conf

        # Trigger DAG run
        endpoint = f"dags/{dag_id}/dagRuns"
        response = self._make_request('POST', endpoint, data=payload)

        # Check if request was successful
        if not response['success']:
            logger.error(f"Failed to trigger DAG {dag_id}: {response.get('error', 'Unknown error')}")
            return {
                'success': False,
                'error': response.get('error', 'Failed to trigger DAG'),
                'dag_id': dag_id,
                'run_id': run_id
            }

        # Get DAG run information
        dag_run = response['data']
        logger.info(f"Successfully triggered DAG {dag_id} with run ID {run_id}")

        # Wait for completion if requested
        if wait_for_completion:
            return self.wait_for_dag_completion(dag_id, run_id, timeout)

        return {
            'success': True,
            'dag_id': dag_id,
            'run_id': dag_run.get('dag_run_id', run_id),
            'state': dag_run.get('state', 'queued'),
            'execution_date': dag_run.get('execution_date', execution_date),
            'start_date': dag_run.get('start_date'),
            'end_date': dag_run.get('end_date')
        }

    def get_dag_run(self, dag_id, run_id):
        """
        Get information about a specific DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to check

        Returns:
            dict: DAG run information
        """
        endpoint = f"dags/{dag_id}/dagRuns/{run_id}"
        response = self._make_request('GET', endpoint)

        # Check if request was successful
        if not response['success']:
            logger.error(f"Failed to get DAG run {dag_id}/{run_id}: {response.get('error', 'Unknown error')}")
            return {
                'success': False,
                'error': response.get('error', 'Failed to get DAG run'),
                'dag_id': dag_id,
                'run_id': run_id
            }

        # Get DAG run information
        dag_run = response['data']

        return {
            'success': True,
            'dag_id': dag_id,
            'run_id': run_id,
            'state': dag_run.get('state'),
            'execution_date': dag_run.get('execution_date'),
            'start_date': dag_run.get('start_date'),
            'end_date': dag_run.get('end_date'),
            'data': dag_run
        }

    def get_dag_run_status(self, dag_id, run_id):
        """
        Get the status of a DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to check

        Returns:
            str: Status of the DAG run or None if failed
        """
        result = self.get_dag_run(dag_id, run_id)

        if result['success']:
            return result['state']

        return None

    def wait_for_dag_completion(self, dag_id, run_id, timeout=300, check_interval=5):
        """
        Wait for a DAG run to complete.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to check
            timeout (int): Maximum time to wait in seconds
            check_interval (int): Time between status checks in seconds

        Returns:
            dict: DAG run information
        """
        logger.info(f"Waiting for DAG {dag_id}/{run_id} to complete (timeout: {timeout}s)")

        start_time = time.time()
        elapsed_time = 0

        while elapsed_time < timeout:
            # Get DAG run status
            result = self.get_dag_run(dag_id, run_id)

            if not result['success']:
                logger.error(f"Failed to get DAG run status: {result.get('error', 'Unknown error')}")
                return result

            # Check if DAG run has completed
            state = result['state']
            if state in ('success', 'failed', 'skipped'):
                logger.info(f"DAG {dag_id}/{run_id} completed with state: {state}")
                return result

            # Wait before checking again
            time.sleep(check_interval)
            elapsed_time = time.time() - start_time

        logger.warning(f"Timeout waiting for DAG {dag_id}/{run_id} to complete")
        return {
            'success': False,
            'error': 'Timeout waiting for DAG to complete',
            'dag_id': dag_id,
            'run_id': run_id,
            'state': state if 'state' in locals() else 'unknown'
        }

    def get_dag_run_tasks(self, dag_id, run_id):
        """
        Get tasks for a specific DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to check

        Returns:
            dict: DAG run tasks
        """
        endpoint = f"dags/{dag_id}/dagRuns/{run_id}/taskInstances"
        response = self._make_request('GET', endpoint)

        # Check if request was successful
        if not response['success']:
            logger.error(f"Failed to get DAG run tasks {dag_id}/{run_id}: {response.get('error', 'Unknown error')}")
            return {
                'success': False,
                'error': response.get('error', 'Failed to get DAG run tasks'),
                'dag_id': dag_id,
                'run_id': run_id
            }

        # Get task instances
        task_instances = response['data'].get('task_instances', [])

        return {
            'success': True,
            'dag_id': dag_id,
            'run_id': run_id,
            'task_instances': task_instances
        }

    def get_dag_run_logs(self, dag_id, run_id, task_id, try_number=1):
        """
        Get logs for a specific task in a DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID
            task_id (str): The task ID
            try_number (int): The try number

        Returns:
            dict: Task logs
        """
        endpoint = f"dags/{dag_id}/dagRuns/{run_id}/taskInstances/{task_id}/logs/{try_number}"
        response = self._make_request('GET', endpoint)

        # Check if request was successful
        if not response['success']:
            logger.error(f"Failed to get task logs {dag_id}/{run_id}/{task_id}: {response.get('error', 'Unknown error')}")
            return {
                'success': False,
                'error': response.get('error', 'Failed to get task logs'),
                'dag_id': dag_id,
                'run_id': run_id,
                'task_id': task_id
            }

        # Get logs
        logs = response['data']

        return {
            'success': True,
            'dag_id': dag_id,
            'run_id': run_id,
            'task_id': task_id,
            'logs': logs
        }

    def get_dag_run_xcom(self, dag_id, run_id, task_id, key=None):
        """
        Get XCom values for a specific task in a DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID
            task_id (str): The task ID
            key (str, optional): The XCom key to retrieve

        Returns:
            dict: XCom values
        """
        endpoint = f"dags/{dag_id}/dagRuns/{run_id}/taskInstances/{task_id}/xcomEntries"
        if key:
            endpoint = f"{endpoint}/{key}"

        response = self._make_request('GET', endpoint)

        # Check if request was successful
        if not response['success']:
            logger.error(f"Failed to get XCom values {dag_id}/{run_id}/{task_id}: {response.get('error', 'Unknown error')}")
            return {
                'success': False,
                'error': response.get('error', 'Failed to get XCom values'),
                'dag_id': dag_id,
                'run_id': run_id,
                'task_id': task_id
            }

        # Get XCom values
        xcom_values = response['data']

        return {
            'success': True,
            'dag_id': dag_id,
            'run_id': run_id,
            'task_id': task_id,
            'key': key,
            'xcom_values': xcom_values
        }

    def trigger_campaign_data_collection(self, campaign_id, target_type, audience_type, location_targets=None, username_targets=None, additional_params=None, wait_for_completion=False):
        """
        Trigger the campaign_data_collection DAG.

        Args:
            campaign_id (str): Campaign ID
            target_type (str): Target type ('location', 'username', or 'mixed')
            audience_type (str): Audience type ('profile', 'followers', 'following', or 'both')
            location_targets (list, optional): List of location targets (required for 'location' or 'mixed' target types)
            username_targets (list, optional): List of username targets (required for 'username' or 'mixed' target types)
            additional_params (dict, optional): Additional parameters to pass to the DAG
            wait_for_completion (bool): Whether to wait for the DAG to complete

        Returns:
            dict: Response with DAG run information
        """
        # Validate inputs
        if target_type == 'location' and not location_targets:
            raise ValueError("Location targets are required for location-based campaigns")

        if target_type == 'username' and not username_targets:
            raise ValueError("Username targets are required for username-based campaigns")

        if target_type == 'mixed' and not (location_targets or username_targets):
            raise ValueError("At least one type of target (location or username) is required for mixed campaigns")

        # Prepare configuration
        conf = {
            'campaign_id': str(campaign_id),
            'target_type': target_type,
            'audience_type': audience_type
        }

        # Add targets based on target type
        if target_type in ['location', 'mixed'] and location_targets:
            conf['location_targets'] = location_targets

        if target_type in ['username', 'mixed'] and username_targets:
            conf['username_targets'] = username_targets

        # Add additional parameters if provided
        if additional_params:
            for key, value in additional_params.items():
                if key not in conf:  # Don't overwrite existing keys
                    conf[key] = value

        # Trigger DAG
        return self.trigger_dag('campaign_data_collection', conf, wait_for_completion=wait_for_completion)

    def trigger_campaign_tagging(self, campaign_id, analysis_settings, workflow_tracking=None, wait_for_completion=False):
        """
        Trigger the campaign_tagging DAG.

        Args:
            campaign_id (str): Campaign ID
            analysis_settings (dict): Analysis settings including:
                - enable_tagging (bool): Whether to enable tagging
                - target_tags (list): List of target tags to apply
                - min_followers (int): Minimum number of followers
                - max_followers (int): Maximum number of followers
            workflow_tracking (dict, optional): Workflow tracking information
            wait_for_completion (bool): Whether to wait for the DAG to complete

        Returns:
            dict: Response with DAG run information
        """
        # Prepare configuration
        conf = {
            'campaign_id': str(campaign_id),
            'analysis_settings': analysis_settings
        }

        # Add workflow tracking information if provided
        if workflow_tracking:
            # Make sure analysis_settings is a dictionary
            if not isinstance(conf['analysis_settings'], dict):
                conf['analysis_settings'] = {}

            # Add workflow tracking information
            if 'active_workflows' in workflow_tracking:
                conf['analysis_settings']['active_workflows'] = workflow_tracking['active_workflows']

            if 'completed_workflows' in workflow_tracking:
                conf['analysis_settings']['completed_workflows'] = workflow_tracking['completed_workflows']

            if 'workflow_statistics' in workflow_tracking:
                conf['analysis_settings']['workflow_statistics'] = workflow_tracking['workflow_statistics']

        # Trigger DAG
        return self.trigger_dag('campaign_tagging', conf, wait_for_completion=wait_for_completion)

    def trigger_campaign_cep(self, campaign_id, workflow_type, batch_size=10, max_actions=50,
                           delay_between_actions=60, custom_params=None, wait_for_completion=False):
        """
        Trigger the campaign_cep DAG.

        Args:
            campaign_id (str): Campaign ID
            workflow_type (str): Workflow type ('follow', 'like', 'comment', 'dm', or 'all')
            batch_size (int): Number of accounts to process in each batch
            max_actions (int): Maximum number of actions to perform
            delay_between_actions (int): Delay between actions in seconds
            custom_params (dict, optional): Custom parameters for specific workflow types:
                - message_template (str): Template for DM messages
                - comment_template (str): Template for comments
                - additional workflow-specific parameters
            wait_for_completion (bool): Whether to wait for the DAG to complete

        Returns:
            dict: Response with DAG run information
        """
        # Validate inputs
        if workflow_type not in ['follow', 'like', 'comment', 'dm', 'all']:
            raise ValueError(f"Invalid workflow_type: {workflow_type}. Must be one of: follow, like, comment, dm, all")

        # Prepare configuration
        conf = {
            'campaign_id': str(campaign_id),
            'workflow_type': workflow_type,
            'batch_size': batch_size,
            'max_actions': max_actions,
            'delay_between_actions': delay_between_actions
        }

        # Add custom parameters if provided
        if custom_params:
            # Add message template for DM workflows
            if workflow_type == 'dm' and 'message_template' in custom_params:
                conf['message_template'] = custom_params['message_template']

            # Add comment template for comment workflows
            if workflow_type == 'comment' and 'comment_template' in custom_params:
                conf['comment_template'] = custom_params['comment_template']

            # Add any other custom parameters
            conf['custom_params'] = {k: v for k, v in custom_params.items()
                                    if k not in ['message_template', 'comment_template']}

        # Trigger DAG
        return self.trigger_dag('campaign_cep', conf, wait_for_completion=wait_for_completion)

    def pause_dag_run(self, dag_id, run_id):
        """
        Pause a DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to pause

        Returns:
            dict: Response with pause status
        """
        endpoint = f"dags/{dag_id}/dagRuns/{run_id}/actions/pause"
        response = self._make_request('POST', endpoint)

        # Check if request was successful
        if not response['success']:
            logger.error(f"Failed to pause DAG run {dag_id}/{run_id}: {response.get('error', 'Unknown error')}")
            return {
                'success': False,
                'error': response.get('error', 'Failed to pause DAG run'),
                'dag_id': dag_id,
                'run_id': run_id
            }

        logger.info(f"Successfully paused DAG run {dag_id}/{run_id}")
        return {
            'success': True,
            'dag_id': dag_id,
            'run_id': run_id,
            'message': 'DAG run paused successfully'
        }

    def resume_dag_run(self, dag_id, run_id):
        """
        Resume a paused DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to resume

        Returns:
            dict: Response with resume status
        """
        endpoint = f"dags/{dag_id}/dagRuns/{run_id}/actions/resume"
        response = self._make_request('POST', endpoint)

        # Check if request was successful
        if not response['success']:
            logger.error(f"Failed to resume DAG run {dag_id}/{run_id}: {response.get('error', 'Unknown error')}")
            return {
                'success': False,
                'error': response.get('error', 'Failed to resume DAG run'),
                'dag_id': dag_id,
                'run_id': run_id
            }

        logger.info(f"Successfully resumed DAG run {dag_id}/{run_id}")
        return {
            'success': True,
            'dag_id': dag_id,
            'run_id': run_id,
            'message': 'DAG run resumed successfully'
        }

    def stop_dag_run(self, dag_id, run_id):
        """
        Stop a DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to stop

        Returns:
            dict: Response with stop status
        """
        endpoint = f"dags/{dag_id}/dagRuns/{run_id}/actions/cancel"
        response = self._make_request('POST', endpoint)

        # Check if request was successful
        if not response['success']:
            logger.error(f"Failed to stop DAG run {dag_id}/{run_id}: {response.get('error', 'Unknown error')}")
            return {
                'success': False,
                'error': response.get('error', 'Failed to stop DAG run'),
                'dag_id': dag_id,
                'run_id': run_id
            }

        logger.info(f"Successfully stopped DAG run {dag_id}/{run_id}")
        return {
            'success': True,
            'dag_id': dag_id,
            'run_id': run_id,
            'message': 'DAG run stopped successfully'
        }

    def update_campaign_with_dag_status(self, campaign_id, dag_id, run_id):
        """
        Update campaign with DAG run status.

        Args:
            campaign_id (str): Campaign ID
            dag_id (str): DAG ID
            run_id (str): DAG run ID

        Returns:
            dict: Updated campaign information
        """
        from campaigns.models import Campaign

        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)

            # Get DAG run status
            dag_run = self.get_dag_run(dag_id, run_id)

            if not dag_run['success']:
                logger.error(f"Failed to get DAG run status: {dag_run.get('error', 'Unknown error')}")
                return {
                    'success': False,
                    'error': dag_run.get('error', 'Failed to get DAG run status'),
                    'campaign_id': campaign_id
                }

            # Update campaign status based on DAG run state
            state = dag_run['state']

            with transaction.atomic():
                # Update campaign fields
                campaign.airflow_dag_id = dag_id
                campaign.airflow_run_id = run_id

                # Update campaign status
                if state == 'success':
                    campaign.status = 'completed'
                elif state == 'failed':
                    campaign.status = 'failed'
                elif state == 'running':
                    campaign.status = 'running'
                else:
                    campaign.status = 'pending'

                campaign.save()

            return {
                'success': True,
                'campaign_id': str(campaign.id),
                'status': campaign.status,
                'dag_id': dag_id,
                'run_id': run_id,
                'dag_state': state
            }

        except Campaign.DoesNotExist:
            logger.error(f"Campaign not found: {campaign_id}")
            return {
                'success': False,
                'error': 'Campaign not found',
                'campaign_id': campaign_id
            }

        except Exception as e:
            logger.exception(f"Error updating campaign with DAG status: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'campaign_id': campaign_id
            }
