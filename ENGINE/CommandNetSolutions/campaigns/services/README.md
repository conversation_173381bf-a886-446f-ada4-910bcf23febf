# Campaign Services

This directory contains services for the campaigns app.

## Workflow Services

### PyFlow Service

The PyFlow service is responsible for creating and running PyFlow workflows for campaigns. It provides methods for creating and running workflows for account collection, account analysis, and engagement actions.

### Dummy PyFlow Service

The dummy PyFlow service is a drop-in replacement for the real PyFlow service that simulates PyFlow workflows without requiring the actual PyFlow system. It's useful for development and testing.

To use the dummy PyFlow service, set the `USE_DUMMY_PYFLOW` setting to `True` in your settings file:

```python
# In settings.py
USE_DUMMY_PYFLOW = True  # Set to False in production to use the real PyFlow service
```

### PyFlow Factory

The PyFlow factory is responsible for creating the appropriate PyFlow service instance based on the `USE_DUMMY_PYFLOW` setting. It provides a single entry point for getting a PyFlow service instance.

```python
from campaigns.services.pyflow_factory import PyFlowFactory

# Get the appropriate PyFlow service instance
pyflow_service = PyFlowFactory.create_pyflow_service()

# Use the PyFlow service
result = pyflow_service.create_and_run_collection_workflow(
    campaign_id='123',
    targets=[...]
)
```

### Workflow Service

The workflow service provides a higher-level interface for creating and running workflows for campaigns. It uses the PyFlow factory to get the appropriate PyFlow service instance.

```python
from campaigns.services.workflow_service import WorkflowService

# Create a workflow service instance
workflow_service = WorkflowService()

# Run a collection workflow
result = workflow_service.run_collection_workflow(
    campaign_id='123',
    targets=[...]
)
```

## API Endpoints

The following API endpoints are available for managing workflows:

- `POST /api/campaigns/<campaign_id>/workflows/collection/`: Run a collection workflow for a campaign
- `POST /api/campaigns/<campaign_id>/workflows/analysis/`: Run an analysis workflow for a campaign
- `POST /api/campaigns/<campaign_id>/workflows/engagement/`: Run an engagement workflow for a campaign
- `GET /api/campaigns/<campaign_id>/workflows/`: List workflows for a campaign
- `GET /api/workflows/<workflow_execution_id>/`: Get the status of a workflow execution

## Testing

To test the dummy workflow implementation, run the following command:

```bash
python manage.py test campaigns.tests.test_dummy_workflow
```
