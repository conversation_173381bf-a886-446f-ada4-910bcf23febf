"""
Mock Resource Manager Service for testing the Resource Manager dashboard.
"""
from django.utils import timezone
import random
import uuid
import time
import threading
import json

class MockResourceManagerService:
    """
    Mock implementation of the ResourceManagerService for testing.
    """
    def __init__(self):
        self.lock = threading.RLock()
        self.running_workflows = {}
        self.queued_workflows = {}
        self.max_queue_size = 10
        self.api_usage = {
            'instagram': {
                'current': random.randint(20, 40),
                'limit': 100,
                'reset_in': random.randint(1800, 3600)
            },
            'graph': {
                'current': random.randint(50, 70),
                'limit': 100,
                'reset_in': random.randint(1200, 2400)
            }
        }
        
        # Generate some sample workflows
        self._generate_sample_workflows()
    
    def _generate_sample_workflows(self):
        """Generate sample workflows for testing."""
        # Generate a running workflow
        workflow_id = str(uuid.uuid4())
        self.running_workflows[workflow_id] = {
            'id': workflow_id,
            'campaign_id': 'Summer2025',
            'workflow_type': 'collection',
            'priority': 5,
            'status': 'running',
            'start_time': timezone.now(),
            'submission_time': timezone.now() - timezone.timedelta(minutes=5),
            'paused': False,
            'resource_usage': {
                'cpu': 25,
                'memory': 40,
                'network': 15
            },
            'api_usage': {
                'instagram': 15,
                'graph': 25
            }
        }
        
        # Generate some queued workflows
        for i in range(3):
            workflow_id = str(uuid.uuid4())
            self.queued_workflows[workflow_id] = {
                'id': workflow_id,
                'campaign_id': f'Campaign{i+1}',
                'workflow_type': random.choice(['collection', 'analysis']),
                'priority': random.randint(1, 10),
                'status': 'queued',
                'submission_time': timezone.now() - timezone.timedelta(minutes=random.randint(10, 120)),
                'paused': False
            }
    
    def get_system_resources(self):
        """
        Get system resources.
        
        Returns:
            dict: System resources
        """
        # Determine bot worker status based on running workflows
        bot_status = 'available'
        active_workflow = None
        
        with self.lock:
            running_count = len([w for w in self.running_workflows.values() if not w.get('paused', False)])
            if running_count > 0:
                bot_status = 'busy'
                # Get the first running workflow as the active one
                for workflow_id, info in self.running_workflows.items():
                    if not info.get('paused', False):
                        active_workflow = {
                            'id': workflow_id,
                            'campaign_id': info.get('campaign_id'),
                            'workflow_type': info.get('workflow_type'),
                            'priority': info.get('priority'),
                            'start_time': info.get('start_time')
                        }
                        break
        
        # Get queue information
        queue_total = len(self.queued_workflows)
        
        # Get Redis status (mock)
        redis_status = {
            'available': True,
            'error': None
        }
        
        return {
            'bot_worker': {
                'status': bot_status,
                'active_workflow': active_workflow
            },
            'queue': {
                'total': queue_total,
                'max_size': self.max_queue_size
            },
            'api_usage': self.api_usage,
            'redis_status': redis_status
        }
    
    def _pause_workflow(self, workflow_id):
        """
        Pause a workflow.
        
        Args:
            workflow_id (str): Workflow ID
        """
        with self.lock:
            if workflow_id in self.running_workflows:
                self.running_workflows[workflow_id]['paused'] = True
    
    def _resume_workflow(self, workflow_id):
        """
        Resume a workflow.
        
        Args:
            workflow_id (str): Workflow ID
        """
        with self.lock:
            if workflow_id in self.running_workflows:
                self.running_workflows[workflow_id]['paused'] = False
    
    def _remove_from_queue(self, workflow_id):
        """
        Remove a workflow from the queue.
        
        Args:
            workflow_id (str): Workflow ID
        """
        with self.lock:
            if workflow_id in self.queued_workflows:
                del self.queued_workflows[workflow_id]
    
    def _resume_all_workflows(self):
        """
        Resume all paused workflows.
        """
        with self.lock:
            for workflow_id in self.running_workflows:
                self.running_workflows[workflow_id]['paused'] = False
    
    def get_workflow_details(self, workflow_id):
        """
        Get workflow details.
        
        Args:
            workflow_id (str): Workflow ID
            
        Returns:
            dict: Workflow details
        """
        with self.lock:
            if workflow_id in self.running_workflows:
                workflow = self.running_workflows[workflow_id].copy()
                # Add some additional details for the modal
                workflow['progress'] = random.randint(10, 90)
                workflow['processed_items'] = int(workflow['progress'] * 0.1)
                workflow['total_items'] = 100
                workflow['duration'] = random.randint(60, 300)
                return workflow
            elif workflow_id in self.queued_workflows:
                workflow = self.queued_workflows[workflow_id].copy()
                # Add some additional details for the modal
                workflow['progress'] = 0
                workflow['processed_items'] = 0
                workflow['total_items'] = 100
                workflow['duration'] = 0
                return workflow
            
        return None

# Create a singleton instance
mock_resource_manager = MockResourceManagerService()
