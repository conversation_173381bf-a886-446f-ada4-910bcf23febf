"""
Notification Service for sending notifications to users.
"""
import logging
import json
from datetime import datetime
from django.conf import settings
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils import timezone
from django.contrib.auth import get_user_model

logger = logging.getLogger(__name__)

User = get_user_model()


class NotificationService:
    """
    Service for sending notifications to users.
    Provides methods for sending emails, in-app notifications, and webhooks.
    """
    
    def __init__(self):
        """Initialize the notification service."""
        self.email_enabled = getattr(settings, 'EMAIL_ENABLED', False)
        self.webhook_enabled = getattr(settings, 'WEBHOOK_ENABLED', False)
        self.slack_enabled = getattr(settings, 'SLACK_ENABLED', False)
    
    def send_email(self, recipients, subject, template, context=None, from_email=None):
        """
        Send an email notification.
        
        Args:
            recipients (list): List of email addresses
            subject (str): Email subject
            template (str): Email template name
            context (dict): Template context
            from_email (str): Sender email address
            
        Returns:
            dict: Email sending result
        """
        if not self.email_enabled:
            logger.warning("Email notifications are disabled")
            return {
                'success': False,
                'error': 'Email notifications are disabled'
            }
        
        if not recipients:
            logger.error("No recipients specified")
            return {
                'success': False,
                'error': 'No recipients specified'
            }
        
        try:
            # Set default context
            if context is None:
                context = {}
            
            # Set default from email
            if from_email is None:
                from_email = getattr(settings, 'DEFAULT_FROM_EMAIL', '<EMAIL>')
            
            # Render email content
            html_content = render_to_string(f"emails/{template}.html", context)
            text_content = render_to_string(f"emails/{template}.txt", context)
            
            # Send email
            send_mail(
                subject=subject,
                message=text_content,
                from_email=from_email,
                recipient_list=recipients,
                html_message=html_content,
                fail_silently=False
            )
            
            return {
                'success': True,
                'recipients': recipients,
                'subject': subject
            }
        except Exception as e:
            logger.exception(f"Error sending email: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def send_in_app_notification(self, user_ids, title, message, notification_type='info', link=None, data=None):
        """
        Send an in-app notification.
        
        Args:
            user_ids (list): List of user IDs
            title (str): Notification title
            message (str): Notification message
            notification_type (str): Notification type (info, success, warning, error)
            link (str): Optional link to include in the notification
            data (dict): Optional additional data
            
        Returns:
            dict: Notification sending result
        """
        from campaigns.models import Notification
        
        if not user_ids:
            logger.error("No users specified")
            return {
                'success': False,
                'error': 'No users specified'
            }
        
        try:
            # Create notifications
            notifications = []
            
            for user_id in user_ids:
                try:
                    user = User.objects.get(id=user_id)
                    
                    notification = Notification.objects.create(
                        user=user,
                        title=title,
                        message=message,
                        notification_type=notification_type,
                        link=link,
                        data=data or {}
                    )
                    
                    notifications.append(notification)
                except User.DoesNotExist:
                    logger.warning(f"User not found: {user_id}")
            
            return {
                'success': True,
                'notifications': [str(n.id) for n in notifications],
                'count': len(notifications)
            }
        except Exception as e:
            logger.exception(f"Error sending in-app notification: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def send_webhook(self, webhook_url, event_type, payload):
        """
        Send a webhook notification.
        
        Args:
            webhook_url (str): Webhook URL
            event_type (str): Event type
            payload (dict): Webhook payload
            
        Returns:
            dict: Webhook sending result
        """
        if not self.webhook_enabled:
            logger.warning("Webhook notifications are disabled")
            return {
                'success': False,
                'error': 'Webhook notifications are disabled'
            }
        
        if not webhook_url:
            logger.error("No webhook URL specified")
            return {
                'success': False,
                'error': 'No webhook URL specified'
            }
        
        try:
            import requests
            
            # Prepare webhook data
            webhook_data = {
                'event_type': event_type,
                'timestamp': datetime.now().isoformat(),
                'payload': payload
            }
            
            # Send webhook
            response = requests.post(
                webhook_url,
                json=webhook_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code in (200, 201, 202, 204):
                return {
                    'success': True,
                    'status_code': response.status_code
                }
            else:
                logger.error(f"Webhook failed: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f"Webhook failed: {response.status_code}",
                    'response': response.text
                }
        except Exception as e:
            logger.exception(f"Error sending webhook: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def send_slack_notification(self, channel, message, attachments=None):
        """
        Send a Slack notification.
        
        Args:
            channel (str): Slack channel
            message (str): Message text
            attachments (list): Optional message attachments
            
        Returns:
            dict: Slack notification result
        """
        if not self.slack_enabled:
            logger.warning("Slack notifications are disabled")
            return {
                'success': False,
                'error': 'Slack notifications are disabled'
            }
        
        try:
            from slack_sdk import WebClient
            from slack_sdk.errors import SlackApiError
            
            # Get Slack token
            slack_token = getattr(settings, 'SLACK_API_TOKEN', None)
            
            if not slack_token:
                logger.error("Slack API token not configured")
                return {
                    'success': False,
                    'error': 'Slack API token not configured'
                }
            
            # Initialize Slack client
            client = WebClient(token=slack_token)
            
            # Send message
            response = client.chat_postMessage(
                channel=channel,
                text=message,
                attachments=attachments
            )
            
            return {
                'success': True,
                'channel': channel,
                'ts': response['ts']
            }
        except SlackApiError as e:
            logger.error(f"Slack API error: {e.response['error']}")
            return {
                'success': False,
                'error': e.response['error']
            }
        except Exception as e:
            logger.exception(f"Error sending Slack notification: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def notify_campaign_status(self, campaign_id, status, details=None):
        """
        Send notifications about campaign status.
        
        Args:
            campaign_id (str): Campaign ID
            status (str): Campaign status
            details (dict): Optional status details
            
        Returns:
            dict: Notification results
        """
        from campaigns.models import Campaign
        
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)
            
            # Get creator
            creator = campaign.creator
            
            if not creator:
                logger.warning(f"Campaign has no creator: {campaign_id}")
                return {
                    'success': False,
                    'error': 'Campaign has no creator'
                }
            
            # Prepare notification data
            title = f"Campaign {status.title()}"
            message = f"Your campaign '{campaign.name}' is now {status}."
            
            if details:
                if 'message' in details:
                    message = details['message']
            
            # Send in-app notification
            in_app_result = self.send_in_app_notification(
                [creator.id],
                title,
                message,
                notification_type='info',
                link=f"/campaigns/{campaign_id}/",
                data={
                    'campaign_id': str(campaign_id),
                    'status': status,
                    'details': details or {}
                }
            )
            
            # Send email notification if enabled
            email_result = None
            if self.email_enabled and creator.email:
                email_result = self.send_email(
                    [creator.email],
                    title,
                    'campaign_status',
                    {
                        'campaign': campaign,
                        'status': status,
                        'details': details or {},
                        'user': creator
                    }
                )
            
            return {
                'success': in_app_result.get('success', False),
                'in_app': in_app_result,
                'email': email_result
            }
        except Campaign.DoesNotExist:
            logger.error(f"Campaign not found: {campaign_id}")
            return {
                'success': False,
                'error': 'Campaign not found'
            }
        except Exception as e:
            logger.exception(f"Error sending campaign status notification: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def notify_analysis_complete(self, campaign_id, results):
        """
        Send notifications about completed analysis.
        
        Args:
            campaign_id (str): Campaign ID
            results (dict): Analysis results
            
        Returns:
            dict: Notification results
        """
        from campaigns.models import Campaign
        
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)
            
            # Get creator
            creator = campaign.creator
            
            if not creator:
                logger.warning(f"Campaign has no creator: {campaign_id}")
                return {
                    'success': False,
                    'error': 'Campaign has no creator'
                }
            
            # Prepare notification data
            title = "Analysis Complete"
            
            # Format message based on results
            total = results.get('total', 0)
            white_listed = results.get('white_listed', 0)
            percentage = results.get('percentage', 0)
            
            message = f"Analysis of '{campaign.name}' is complete. "
            message += f"{white_listed} of {total} accounts ({percentage:.1f}%) were added to the whitelist."
            
            # Send in-app notification
            in_app_result = self.send_in_app_notification(
                [creator.id],
                title,
                message,
                notification_type='success',
                link=f"/campaigns/{campaign_id}/results/",
                data={
                    'campaign_id': str(campaign_id),
                    'results': results
                }
            )
            
            # Send email notification if enabled
            email_result = None
            if self.email_enabled and creator.email:
                email_result = self.send_email(
                    [creator.email],
                    title,
                    'analysis_complete',
                    {
                        'campaign': campaign,
                        'results': results,
                        'user': creator
                    }
                )
            
            return {
                'success': in_app_result.get('success', False),
                'in_app': in_app_result,
                'email': email_result
            }
        except Campaign.DoesNotExist:
            logger.error(f"Campaign not found: {campaign_id}")
            return {
                'success': False,
                'error': 'Campaign not found'
            }
        except Exception as e:
            logger.exception(f"Error sending analysis complete notification: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
