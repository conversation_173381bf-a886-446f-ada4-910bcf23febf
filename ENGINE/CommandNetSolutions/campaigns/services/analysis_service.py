"""
Service for analysis operations.
"""
from campaigns.interfaces.analysis_service import AnalysisServiceInterface
from campaigns.domains.analysis import AnalysisDomain
from campaigns.tasks import analyze_campaign_accounts_task


class AnalysisService(AnalysisServiceInterface):
    """
    Service for analysis operations.
    Coordinates between domain, repository, and asynchronous tasks.
    """
    
    def __init__(self, repository, tagging_system=None):
        """
        Initialize service.
        
        Args:
            repository (CampaignRepository): Campaign repository
            tagging_system (AutoTaggingSystem): Optional tagging system
        """
        self.repository = repository
        self.tagging_system = tagging_system
        self.domain = AnalysisDomain(repository, tagging_system)
    
    def analyze_campaign(self, campaign_id, options=None):
        """
        Analyze campaign accounts.
        
        Args:
            campaign_id (uuid): Campaign ID
            options (dict): Optional analysis options
            
        Returns:
            dict: Analysis results or task information
        """
        # Get options
        options = options or {}
        async_mode = options.get('async', True)
        
        if async_mode:
            # Launch asynchronous task
            task = analyze_campaign_accounts_task.delay(str(campaign_id))
            
            return {
                'task_id': task.id,
                'status': 'pending',
                'message': 'Analysis started in background'
            }
        else:
            # Run synchronously
            return self.domain.analyze_campaign(campaign_id, options)
    
    def get_analysis_stats(self, campaign_id):
        """
        Get analysis statistics for a campaign.
        
        Args:
            campaign_id (uuid): Campaign ID
            
        Returns:
            dict: Analysis statistics
        """
        return self.domain.get_analysis_stats(campaign_id)
    
    def apply_tags(self, account_id, tags):
        """
        Apply tags to account.
        
        Args:
            account_id (str): Account ID
            tags (list): List of tags to apply
            
        Returns:
            bool: Success status
        """
        from instagram.models import Accounts
        
        try:
            # Get account
            account = Accounts.objects.get(username=account_id)
            
            # Apply tags
            for tag in tags:
                account.tags.add(tag)
            
            return True
        except Exception as e:
            print(f"Error applying tags: {str(e)}")
            return False
    
    def get_task_status(self, task_id):
        """
        Get status of an analysis task.
        
        Args:
            task_id (str): Task ID
            
        Returns:
            dict: Task status information
        """
        from celery.result import AsyncResult
        
        task = AsyncResult(task_id)
        
        if task.state == 'PENDING':
            response = {
                'status': 'pending',
                'current': 0,
                'total': 1,
                'percent': 0,
            }
        elif task.state == 'PROGRESS':
            response = {
                'status': 'in_progress',
                'current': task.info.get('current', 0),
                'total': task.info.get('total', 1),
                'percent': int(100 * task.info.get('current', 0) / task.info.get('total', 1)),
            }
        elif task.state == 'SUCCESS':
            response = {
                'status': 'completed',
                'current': task.info.get('processed', 0),
                'total': task.info.get('total', 1),
                'percent': 100,
                'result': task.info,
            }
        else:
            # Something went wrong
            response = {
                'status': 'error',
                'error': str(task.info),
            }
        
        return response
