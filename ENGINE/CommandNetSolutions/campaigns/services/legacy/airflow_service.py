"""
Legacy Airflow service for backward compatibility.
"""
import json
import requests
import logging
from datetime import datetime
from django.conf import settings

logger = logging.getLogger(__name__)

class AirflowService:
    """
    Service for interacting with Airflow API.
    """
    def __init__(self):
        self.base_url = getattr(settings, 'AIRFLOW_API_URL', 'http://localhost:8080/api/v1')
        self.username = getattr(settings, 'AIRFLOW_USERNAME', 'airflow')
        self.password = getattr(settings, 'AIRFLOW_PASSWORD', 'airflow')

    def trigger_dag(self, dag_id, conf=None):
        """
        Trigger a DAG run with optional configuration.

        Args:
            dag_id (str): The ID of the DAG to trigger
            conf (dict, optional): Configuration parameters to pass to the DAG

        Returns:
            dict: Response from Airflow API or a mock response if Airflow is not available
        """
        url = f"{self.base_url}/dags/{dag_id}/dagRuns"

        # Generate a unique run ID
        run_id = f"manual_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        payload = {
            "dag_run_id": run_id,
            "logical_date": datetime.now().isoformat(),
        }

        if conf:
            payload["conf"] = conf

        try:
            response = requests.post(
                url,
                json=payload,
                auth=(self.username, self.password),
                headers={"Content-Type": "application/json"},
                timeout=5  # Add a timeout to prevent long waits
            )

            if response.status_code in (200, 201):
                return response.json()
            else:
                logger.error(f"Failed to trigger DAG {dag_id}: {response.status_code} - {response.text}")
                # Return a mock response with the run_id so the campaign can still be created
                logger.info(f"Returning mock response with run_id: {run_id}")
                return {"dag_run_id": run_id}

        except Exception as e:
            logger.exception(f"Error triggering DAG {dag_id}: {str(e)}")
            # Return a mock response with the run_id so the campaign can still be created
            logger.info(f"Returning mock response with run_id: {run_id} after exception")
            return {"dag_run_id": run_id}

    def get_dag_run_status(self, dag_id, run_id):
        """
        Get the status of a DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to check

        Returns:
            str: Status of the DAG run or None if failed
        """
        url = f"{self.base_url}/dags/{dag_id}/dagRuns/{run_id}"

        try:
            response = requests.get(
                url,
                auth=(self.username, self.password)
            )

            if response.status_code == 200:
                return response.json().get('state')
            else:
                logger.error(f"Failed to get DAG run status: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.exception(f"Error getting DAG run status: {str(e)}")
            return None

    def pause_dag_run(self, dag_id, run_id):
        """
        Pause a DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to pause

        Returns:
            dict: Response with pause status
        """
        url = f"{self.base_url}/dags/{dag_id}/dagRuns/{run_id}/actions/pause"

        try:
            response = requests.post(
                url,
                auth=(self.username, self.password),
                headers={"Content-Type": "application/json"},
                timeout=5
            )

            if response.status_code in (200, 201, 204):
                logger.info(f"Successfully paused DAG run {dag_id}/{run_id}")
                return {
                    'success': True,
                    'dag_id': dag_id,
                    'run_id': run_id,
                    'message': 'DAG run paused successfully'
                }
            else:
                logger.error(f"Failed to pause DAG run {dag_id}/{run_id}: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f"Failed to pause DAG run: {response.text}",
                    'dag_id': dag_id,
                    'run_id': run_id
                }

        except Exception as e:
            logger.exception(f"Error pausing DAG run: {str(e)}")
            return {
                'success': False,
                'error': f"Error pausing DAG run: {str(e)}",
                'dag_id': dag_id,
                'run_id': run_id
            }

    def resume_dag_run(self, dag_id, run_id):
        """
        Resume a paused DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to resume

        Returns:
            dict: Response with resume status
        """
        url = f"{self.base_url}/dags/{dag_id}/dagRuns/{run_id}/actions/resume"

        try:
            response = requests.post(
                url,
                auth=(self.username, self.password),
                headers={"Content-Type": "application/json"},
                timeout=5
            )

            if response.status_code in (200, 201, 204):
                logger.info(f"Successfully resumed DAG run {dag_id}/{run_id}")
                return {
                    'success': True,
                    'dag_id': dag_id,
                    'run_id': run_id,
                    'message': 'DAG run resumed successfully'
                }
            else:
                logger.error(f"Failed to resume DAG run {dag_id}/{run_id}: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f"Failed to resume DAG run: {response.text}",
                    'dag_id': dag_id,
                    'run_id': run_id
                }

        except Exception as e:
            logger.exception(f"Error resuming DAG run: {str(e)}")
            return {
                'success': False,
                'error': f"Error resuming DAG run: {str(e)}",
                'dag_id': dag_id,
                'run_id': run_id
            }

    def stop_dag_run(self, dag_id, run_id):
        """
        Stop a DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to stop

        Returns:
            dict: Response with stop status
        """
        url = f"{self.base_url}/dags/{dag_id}/dagRuns/{run_id}/actions/cancel"

        try:
            response = requests.post(
                url,
                auth=(self.username, self.password),
                headers={"Content-Type": "application/json"},
                timeout=5
            )

            if response.status_code in (200, 201, 204):
                logger.info(f"Successfully stopped DAG run {dag_id}/{run_id}")
                return {
                    'success': True,
                    'dag_id': dag_id,
                    'run_id': run_id,
                    'message': 'DAG run stopped successfully'
                }
            else:
                logger.error(f"Failed to stop DAG run {dag_id}/{run_id}: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f"Failed to stop DAG run: {response.text}",
                    'dag_id': dag_id,
                    'run_id': run_id
                }

        except Exception as e:
            logger.exception(f"Error stopping DAG run: {str(e)}")
            return {
                'success': False,
                'error': f"Error stopping DAG run: {str(e)}",
                'dag_id': dag_id,
                'run_id': run_id
            }
