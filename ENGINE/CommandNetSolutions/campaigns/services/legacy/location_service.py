"""
Legacy location service for backward compatibility.
"""
import csv
import os
import logging
from django.conf import settings

logger = logging.getLogger(__name__)

class LocationService:
    """
    Service for handling location data.
    """
    def __init__(self):
        self.locations_file = os.path.join(settings.BASE_DIR, 'campaigns', 'data', 'locations.csv')

    def search_locations(self, search_term):
        """
        Search for locations by country, city name, or location ID.

        Args:
            search_term (str): The search term to look for

        Returns:
            list: List of matching locations
        """
        results = []
        search_term = search_term.lower()

        # Check if search term is numeric (potential location ID)
        is_numeric_search = search_term.isdigit()

        try:
            if os.path.exists(self.locations_file):
                with open(self.locations_file, 'r', encoding='utf-8') as csvfile:
                    reader = csv.DictReader(csvfile)
                    for row in reader:
                        # Check if search matches location ID (exact or partial)
                        location_id_match = (
                            is_numeric_search and
                            search_term in row['location_id'].lower()
                        )

                        # Check if search matches city or country
                        name_match = (
                            search_term in row['country'].lower() or
                            search_term in row['city'].lower()
                        )

                        if location_id_match or name_match:
                            results.append(row)
            else:
                # If file doesn't exist, return some dummy data
                logger.warning(f"Locations file not found: {self.locations_file}")
                if 'new york' in search_term or 'united states' in search_term:
                    results.append({
                        'country': 'United States',
                        'city': 'New York',
                        'location_id': 'US_NY_123'
                    })
                elif 'london' in search_term or 'united kingdom' in search_term:
                    results.append({
                        'country': 'United Kingdom',
                        'city': 'London',
                        'location_id': 'UK_LDN_456'
                    })
        except Exception as e:
            logger.exception(f"Error searching locations: {str(e)}")

        return results

    def get_location_by_id(self, location_id):
        """
        Get location details by ID.

        Args:
            location_id (str): The location ID to look for

        Returns:
            dict: Location details or None if not found
        """
        try:
            if os.path.exists(self.locations_file):
                with open(self.locations_file, 'r', encoding='utf-8') as csvfile:
                    reader = csv.DictReader(csvfile)
                    for row in reader:
                        if row['location_id'] == location_id:
                            return row
            else:
                # If file doesn't exist, return some dummy data
                logger.warning(f"Locations file not found: {self.locations_file}")
                if location_id == 'US_NY_123':
                    return {
                        'country': 'United States',
                        'city': 'New York',
                        'location_id': 'US_NY_123'
                    }
                elif location_id == 'UK_LDN_456':
                    return {
                        'country': 'United Kingdom',
                        'city': 'London',
                        'location_id': 'UK_LDN_456'
                    }
        except Exception as e:
            logger.exception(f"Error getting location by ID: {str(e)}")

        return None
