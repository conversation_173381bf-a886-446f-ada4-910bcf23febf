"""
Service for analyzing campaign data.
"""
import logging
from django.utils import timezone

logger = logging.getLogger(__name__)


class CampaignAnalysisService:
    """
    Service for analyzing campaign data.
    """
    def __init__(self):
        """
        Initialize the service.
        """
        from instagram.tagging_engine import AutoTaggingSystem
        self.tagging_system = AutoTaggingSystem()

    def get_campaign_analysis_stats(self, campaign_id):
        """
        Get analysis statistics for a campaign.

        Args:
            campaign_id: UUID of the campaign

        Returns:
            dict: Analysis statistics
        """
        from instagram.models import Accounts, WhiteListEntry
        from campaigns.models import Campaign, CampaignResult

        try:
            campaign = Campaign.objects.get(id=campaign_id)

            # Get campaign result
            result, created = CampaignResult.objects.get_or_create(campaign=campaign)

            # Get total accounts collected by this campaign
            total_accounts = Accounts.objects.filter(campaign_id=str(campaign.id)).count()

            # Get white listed accounts
            white_listed = WhiteListEntry.objects.filter(
                account__campaign_id=str(campaign.id)
            ).count()

            return {
                'total': total_accounts,
                'processed': result.total_accounts_processed,
                'white_listed': white_listed,
                'percentage': (white_listed / total_accounts * 100) if total_accounts > 0 else 0,
                'last_processed_at': result.last_processed_at
            }

        except Exception as e:
            logger.exception(f"Error getting campaign analysis stats: {str(e)}")
            return {
                'total': 0,
                'processed': 0,
                'white_listed': 0,
                'percentage': 0,
                'last_processed_at': None,
                'error': str(e)
            }

    def analyze_campaign_accounts(self, campaign_id, batch_size=100):
        """
        Analyze accounts collected by a campaign.

        Args:
            campaign_id: UUID of the campaign
            batch_size: Number of accounts to process in each batch

        Returns:
            dict: Summary of analysis results
        """
        from instagram.models import Accounts, WhiteListEntry
        from campaigns.models import Campaign, CampaignResult

        try:
            campaign = Campaign.objects.get(id=campaign_id)

            # Get accounts collected by this campaign
            accounts = Accounts.objects.filter(campaign_id=str(campaign.id))

            total = accounts.count()
            processed = 0
            white_listed = 0

            # Get campaign analysis settings if they exist
            try:
                analysis_settings = campaign.analysis_settings
                min_followers = analysis_settings.min_followers
                max_followers = analysis_settings.max_followers

                # Apply follower filters if specified
                if min_followers is not None:
                    accounts = accounts.filter(followers__gte=min_followers)
                if max_followers is not None:
                    accounts = accounts.filter(followers__lte=max_followers)

                # Update total after filtering
                total = accounts.count()
            except:
                # If no analysis settings, proceed with all accounts
                pass

            # Process in batches to reduce memory usage
            for i in range(0, total, batch_size):
                batch = accounts[i:i+batch_size]

                for account in batch:
                    # Process account using the tagging system
                    result = self.tagging_system.process_account(account)

                    # Apply dynamic tags if enabled
                    dynamic_tags = []
                    try:
                        if hasattr(campaign, 'analysis_settings') and hasattr(campaign.analysis_settings, 'enable_dynamic_tagging') and campaign.analysis_settings.enable_dynamic_tagging and campaign.analysis_settings.dynamic_tags.exists():
                            dynamic_tags = self._process_dynamic_tags(account, campaign.analysis_settings.dynamic_tags.all())
                            # Add dynamic tags to the result
                            result['tags'].extend(dynamic_tags)
                    except Exception as e:
                        logger.warning(f"Error processing dynamic tags: {str(e)}")

                    # Update white list if needed
                    qualifies = bool(result.get('privileges'))
                    if qualifies:
                        WhiteListEntry.objects.update_or_create(
                            account=account,
                            defaults={
                                'tags': result['tags'],
                                'is_auto': True,
                                **result['privileges']
                            }
                        )
                        white_listed += 1
                    else:
                        # Remove from white list if it exists
                        WhiteListEntry.objects.filter(account=account).delete()

                    processed += 1

            # Update campaign results
            campaign_result, created = CampaignResult.objects.get_or_create(campaign=campaign)
            campaign_result.total_accounts_processed = processed
            campaign_result.last_processed_at = timezone.now()
            campaign_result.save()

            return {
                'total': total,
                'processed': processed,
                'white_listed': white_listed,
                'percentage': (white_listed / total * 100) if total > 0 else 0
            }

        except Exception as e:
            logger.exception(f"Error analyzing campaign accounts: {str(e)}")
            return {
                'total': 0,
                'processed': 0,
                'white_listed': 0,
                'percentage': 0,
                'error': str(e)
            }

    def _process_dynamic_tags(self, account, dynamic_tags):
        """
        Process dynamic tags for an account.

        Args:
            account: Account object
            dynamic_tags: QuerySet of DynamicTag objects

        Returns:
            list: List of tag names that match the account
        """
        matching_tags = []

        for tag in dynamic_tags:
            # Skip if no conditions
            if not tag.conditions:
                continue

            # Check if account matches all conditions
            matches = True
            for condition in tag.conditions:
                field = condition.get('field')
                operator = condition.get('operator')
                value = condition.get('value')

                if not field or not operator:
                    continue

                # Get account field value
                account_value = getattr(account, field, None)
                if account_value is None:
                    matches = False
                    break

                # Compare based on operator
                if operator == 'equals':
                    if str(account_value) != str(value):
                        matches = False
                        break
                elif operator == 'contains':
                    if str(value).lower() not in str(account_value).lower():
                        matches = False
                        break
                elif operator == 'greater_than':
                    try:
                        if float(account_value) <= float(value):
                            matches = False
                            break
                    except (ValueError, TypeError):
                        matches = False
                        break
                elif operator == 'less_than':
                    try:
                        if float(account_value) >= float(value):
                            matches = False
                            break
                    except (ValueError, TypeError):
                        matches = False
                        break

            # If all conditions match, add tag to result
            if matches:
                matching_tags.append(tag.name)

        return matching_tags
