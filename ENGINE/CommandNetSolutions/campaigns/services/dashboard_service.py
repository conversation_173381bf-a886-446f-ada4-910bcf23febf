"""
Dashboard service for real-time analysis metrics.
"""
import logging
import json
from django.utils import timezone
from django.db.models import Count, Avg, Sum, F, Q
from django.db import connection
from datetime import timedelta

logger = logging.getLogger(__name__)


class DashboardService:
    """
    Service for generating real-time dashboard metrics and visualizations.
    """
    
    def get_campaign_summary(self, campaign_id=None):
        """
        Get summary statistics for campaigns.
        
        Args:
            campaign_id (str, optional): Campaign ID to filter by
            
        Returns:
            dict: Campaign summary statistics
        """
        from campaigns.models import Campaign, CampaignResult
        
        try:
            # Base query
            campaigns = Campaign.objects.all()
            
            # Filter by campaign ID if provided
            if campaign_id:
                campaigns = campaigns.filter(id=campaign_id)
            
            # Get campaign counts by status
            status_counts = campaigns.values('status').annotate(count=Count('id'))
            status_summary = {item['status']: item['count'] for item in status_counts}
            
            # Get campaign counts by target type
            target_counts = campaigns.values('target_type').annotate(count=Count('id'))
            target_summary = {item['target_type']: item['count'] for item in target_counts}
            
            # Get total accounts collected and processed
            results = CampaignResult.objects.filter(campaign__in=campaigns)
            total_accounts_found = results.aggregate(total=Sum('total_accounts_found'))['total'] or 0
            total_accounts_processed = results.aggregate(total=Sum('total_accounts_processed'))['total'] or 0
            total_accounts_pending = results.aggregate(total=Sum('total_accounts_pending'))['total'] or 0
            
            # Calculate processing rate
            processing_rate = 0
            if total_accounts_found > 0:
                processing_rate = (total_accounts_processed / total_accounts_found) * 100
            
            return {
                'total_campaigns': campaigns.count(),
                'status_summary': status_summary,
                'target_summary': target_summary,
                'total_accounts_found': total_accounts_found,
                'total_accounts_processed': total_accounts_processed,
                'total_accounts_pending': total_accounts_pending,
                'processing_rate': processing_rate
            }
        except Exception as e:
            logger.exception(f"Error getting campaign summary: {str(e)}")
            return {
                'error': str(e)
            }
    
    def get_tag_performance(self, campaign_id=None, limit=10):
        """
        Get tag performance metrics.
        
        Args:
            campaign_id (str, optional): Campaign ID to filter by
            limit (int): Maximum number of tags to return
            
        Returns:
            dict: Tag performance metrics
        """
        from campaigns.models import TagAnalysisResult, TagMetrics
        
        try:
            # Get tag metrics
            metrics = TagMetrics.objects.all()
            
            # Get top performing tags by match count
            top_tags = metrics.order_by('-match_count')[:limit]
            
            # Get tag performance over time
            tag_results = TagAnalysisResult.objects.all()
            
            # Filter by campaign if provided
            if campaign_id:
                tag_results = tag_results.filter(campaign_id=campaign_id)
            
            # Get match counts by tag
            tag_matches = tag_results.filter(matched=True).values('tag__name').annotate(
                count=Count('id')
            ).order_by('-count')[:limit]
            
            # Get match counts by day
            last_30_days = timezone.now() - timedelta(days=30)
            daily_matches = tag_results.filter(
                created_at__gte=last_30_days,
                matched=True
            ).extra(
                select={'day': "DATE(created_at)"}
            ).values('day').annotate(
                count=Count('id')
            ).order_by('day')
            
            return {
                'top_tags': list(top_tags.values('tag__name', 'match_count', 'precision', 'recall', 'f1_score')),
                'tag_matches': list(tag_matches),
                'daily_matches': list(daily_matches),
                'total_tags': metrics.count(),
                'total_matches': tag_results.filter(matched=True).count()
            }
        except Exception as e:
            logger.exception(f"Error getting tag performance: {str(e)}")
            return {
                'error': str(e)
            }
    
    def get_account_insights(self, campaign_id=None):
        """
        Get insights about analyzed accounts.
        
        Args:
            campaign_id (str, optional): Campaign ID to filter by
            
        Returns:
            dict: Account insights
        """
        from instagram.models import Accounts, WhiteListEntry
        
        try:
            # Base query
            accounts = Accounts.objects.all()
            
            # Filter by campaign if provided
            if campaign_id:
                accounts = accounts.filter(campaign_id=campaign_id)
            
            # Get follower distribution
            follower_ranges = [
                {'min': 0, 'max': 1000, 'label': '0-1K'},
                {'min': 1001, 'max': 5000, 'label': '1K-5K'},
                {'min': 5001, 'max': 10000, 'label': '5K-10K'},
                {'min': 10001, 'max': 50000, 'label': '10K-50K'},
                {'min': 50001, 'max': 100000, 'label': '50K-100K'},
                {'min': 100001, 'max': 500000, 'label': '100K-500K'},
                {'min': 500001, 'max': 1000000, 'label': '500K-1M'},
                {'min': 1000001, 'max': None, 'label': '1M+'}
            ]
            
            follower_distribution = []
            for range_info in follower_ranges:
                query = Q(followers__gte=range_info['min'])
                if range_info['max']:
                    query &= Q(followers__lte=range_info['max'])
                
                count = accounts.filter(query).count()
                follower_distribution.append({
                    'label': range_info['label'],
                    'count': count
                })
            
            # Get whitelist stats
            whitelist_count = WhiteListEntry.objects.filter(account__in=accounts).count()
            whitelist_rate = 0
            if accounts.count() > 0:
                whitelist_rate = (whitelist_count / accounts.count()) * 100
            
            # Get account type distribution
            account_types = accounts.values('account_type').annotate(count=Count('id'))
            
            # Get average engagement rate
            avg_engagement = accounts.aggregate(
                avg_engagement=Avg(F('likes_count') + F('comments_count')) / Avg('followers')
            )['avg_engagement'] or 0
            
            return {
                'total_accounts': accounts.count(),
                'follower_distribution': follower_distribution,
                'whitelist_count': whitelist_count,
                'whitelist_rate': whitelist_rate,
                'account_types': list(account_types),
                'avg_engagement': avg_engagement
            }
        except Exception as e:
            logger.exception(f"Error getting account insights: {str(e)}")
            return {
                'error': str(e)
            }
    
    def get_analysis_metrics(self, campaign_id=None):
        """
        Get metrics about the analysis process.
        
        Args:
            campaign_id (str, optional): Campaign ID to filter by
            
        Returns:
            dict: Analysis metrics
        """
        from campaigns.models import CampaignResult, TagAnalysisResult
        
        try:
            # Base query for campaign results
            results = CampaignResult.objects.all()
            
            # Filter by campaign if provided
            if campaign_id:
                results = results.filter(campaign_id=campaign_id)
            
            # Get average analysis duration
            avg_duration = results.aggregate(avg=Avg('analysis_duration'))['avg'] or 0
            
            # Get average confidence score
            avg_confidence = results.aggregate(avg=Avg('average_confidence_score'))['avg'] or 0
            
            # Get tag analysis results
            tag_results = TagAnalysisResult.objects.all()
            
            # Filter by campaign if provided
            if campaign_id:
                tag_results = tag_results.filter(campaign_id=campaign_id)
            
            # Get analysis mode distribution
            mode_distribution = tag_results.values('analysis_mode').annotate(count=Count('id'))
            
            # Get confidence score distribution
            confidence_ranges = [
                {'min': 0.0, 'max': 0.2, 'label': '0-20%'},
                {'min': 0.2, 'max': 0.4, 'label': '20-40%'},
                {'min': 0.4, 'max': 0.6, 'label': '40-60%'},
                {'min': 0.6, 'max': 0.8, 'label': '60-80%'},
                {'min': 0.8, 'max': 1.0, 'label': '80-100%'}
            ]
            
            confidence_distribution = []
            for range_info in confidence_ranges:
                count = tag_results.filter(
                    confidence_score__gte=range_info['min'],
                    confidence_score__lte=range_info['max']
                ).count()
                confidence_distribution.append({
                    'label': range_info['label'],
                    'count': count
                })
            
            return {
                'total_analyses': tag_results.count(),
                'avg_duration': avg_duration,
                'avg_confidence': avg_confidence,
                'mode_distribution': list(mode_distribution),
                'confidence_distribution': confidence_distribution
            }
        except Exception as e:
            logger.exception(f"Error getting analysis metrics: {str(e)}")
            return {
                'error': str(e)
            }
    
    def get_dashboard_data(self, campaign_id=None):
        """
        Get comprehensive dashboard data.
        
        Args:
            campaign_id (str, optional): Campaign ID to filter by
            
        Returns:
            dict: Dashboard data
        """
        return {
            'campaign_summary': self.get_campaign_summary(campaign_id),
            'tag_performance': self.get_tag_performance(campaign_id),
            'account_insights': self.get_account_insights(campaign_id),
            'analysis_metrics': self.get_analysis_metrics(campaign_id),
            'timestamp': timezone.now().isoformat()
        }
