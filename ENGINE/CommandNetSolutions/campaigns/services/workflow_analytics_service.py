"""
Workflow Analytics Service for collecting and analyzing workflow metrics.

This service provides methods for:
1. Collecting workflow execution metrics
2. Generating analytics reports
3. Tracking workflow performance over time
4. Providing dashboard data
"""
import logging
import json
from datetime import datetime, timedelta
from django.db.models import Avg, <PERSON>, Su<PERSON>, <PERSON>, <PERSON>, F, Q
from django.utils import timezone
from django.db import transaction

logger = logging.getLogger(__name__)

class WorkflowAnalyticsService:
    """
    Service for collecting and analyzing workflow metrics.
    """
    
    def __init__(self):
        """Initialize the workflow analytics service."""
        pass
    
    def get_campaign_workflow_stats(self, campaign_id):
        """
        Get workflow statistics for a campaign.
        
        Args:
            campaign_id (str): Campaign ID
            
        Returns:
            dict: Workflow statistics
        """
        from campaigns.models.workflow import WorkflowExecution
        from campaigns.models import Campaign
        
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)
            
            # Get workflow executions
            executions = WorkflowExecution.objects.filter(campaign=campaign)
            
            # Calculate statistics
            total_executions = executions.count()
            completed_executions = executions.filter(status='completed').count()
            failed_executions = executions.filter(status='failed').count()
            running_executions = executions.filter(status='running').count()
            
            # Calculate average duration for completed workflows
            avg_duration = executions.filter(status='completed').aggregate(avg_duration=Avg('duration'))['avg_duration'] or 0
            
            # Calculate success rate
            success_rate = (completed_executions / total_executions * 100) if total_executions > 0 else 0
            
            # Get workflow types
            workflow_types = executions.values('workflow_type').annotate(count=Count('id')).order_by('-count')
            
            # Get latest execution
            latest_execution = executions.order_by('-start_time').first()
            
            # Get workflow type statistics
            workflow_type_stats = {}
            for wf_type in executions.values_list('workflow_type', flat=True).distinct():
                type_executions = executions.filter(workflow_type=wf_type)
                type_completed = type_executions.filter(status='completed').count()
                type_failed = type_executions.filter(status='failed').count()
                type_running = type_executions.filter(status='running').count()
                type_success_rate = (type_completed / type_executions.count() * 100) if type_executions.count() > 0 else 0
                type_avg_duration = type_executions.filter(status='completed').aggregate(avg_duration=Avg('duration'))['avg_duration'] or 0
                
                workflow_type_stats[wf_type] = {
                    'total': type_executions.count(),
                    'completed': type_completed,
                    'failed': type_failed,
                    'running': type_running,
                    'success_rate': type_success_rate,
                    'avg_duration': type_avg_duration
                }
            
            # Return statistics
            return {
                'success': True,
                'campaign_id': str(campaign.id),
                'campaign_name': campaign.name,
                'total_executions': total_executions,
                'completed_executions': completed_executions,
                'failed_executions': failed_executions,
                'running_executions': running_executions,
                'success_rate': success_rate,
                'avg_duration': avg_duration,
                'workflow_types': list(workflow_types),
                'workflow_type_stats': workflow_type_stats,
                'latest_execution': {
                    'id': str(latest_execution.id),
                    'workflow_type': latest_execution.workflow_type,
                    'status': latest_execution.status,
                    'start_time': latest_execution.start_time,
                    'end_time': latest_execution.end_time,
                    'duration': latest_execution.duration,
                    'progress': latest_execution.progress
                } if latest_execution else None
            }
        
        except Campaign.DoesNotExist:
            logger.error(f"Campaign not found: {campaign_id}")
            return {
                'success': False,
                'error': 'Campaign not found',
                'campaign_id': campaign_id
            }
        
        except Exception as e:
            logger.exception(f"Error getting campaign workflow stats: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'campaign_id': campaign_id
            }
    
    def get_workflow_execution_details(self, workflow_execution_id):
        """
        Get detailed information about a workflow execution.
        
        Args:
            workflow_execution_id (str): Workflow execution ID
            
        Returns:
            dict: Workflow execution details
        """
        from campaigns.models.workflow import WorkflowExecution, WorkflowProgressUpdate
        
        try:
            # Get workflow execution
            execution = WorkflowExecution.objects.get(id=workflow_execution_id)
            
            # Get progress updates
            progress_updates = WorkflowProgressUpdate.objects.filter(workflow_execution=execution).order_by('timestamp')
            
            # Calculate metrics
            duration = execution.duration
            if execution.status == 'running':
                # Calculate current duration for running workflows
                duration = (timezone.now() - execution.start_time).total_seconds()
            
            # Calculate processing rate (items per second)
            processing_rate = execution.processed_items / duration if duration > 0 else 0
            
            # Calculate success rate
            success_rate = (execution.successful_items / execution.processed_items * 100) if execution.processed_items > 0 else 0
            
            # Calculate failure rate
            failure_rate = (execution.failed_items / execution.processed_items * 100) if execution.processed_items > 0 else 0
            
            # Calculate estimated time remaining
            remaining_items = execution.total_items - execution.processed_items
            estimated_time_remaining = remaining_items / processing_rate if processing_rate > 0 else 0
            
            # Format progress updates for chart
            progress_chart_data = []
            for update in progress_updates:
                progress_chart_data.append({
                    'timestamp': update.timestamp.isoformat(),
                    'progress': update.progress,
                    'processed_items': update.processed_items,
                    'successful_items': update.successful_items,
                    'failed_items': update.failed_items
                })
            
            # Return execution details
            return {
                'success': True,
                'execution_id': str(execution.id),
                'campaign_id': str(execution.campaign.id),
                'campaign_name': execution.campaign.name,
                'workflow_type': execution.workflow_type,
                'workflow_name': execution.workflow_name,
                'status': execution.status,
                'start_time': execution.start_time.isoformat(),
                'end_time': execution.end_time.isoformat() if execution.end_time else None,
                'duration': duration,
                'progress': execution.progress,
                'total_items': execution.total_items,
                'processed_items': execution.processed_items,
                'successful_items': execution.successful_items,
                'failed_items': execution.failed_items,
                'processing_rate': processing_rate,
                'success_rate': success_rate,
                'failure_rate': failure_rate,
                'estimated_time_remaining': estimated_time_remaining,
                'error_message': execution.error_message,
                'parameters': execution.parameters,
                'results': execution.results,
                'progress_updates': progress_chart_data
            }
        
        except WorkflowExecution.DoesNotExist:
            logger.error(f"Workflow execution not found: {workflow_execution_id}")
            return {
                'success': False,
                'error': 'Workflow execution not found',
                'execution_id': workflow_execution_id
            }
        
        except Exception as e:
            logger.exception(f"Error getting workflow execution details: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'execution_id': workflow_execution_id
            }
    
    def get_system_workflow_stats(self, days=30):
        """
        Get system-wide workflow statistics.
        
        Args:
            days (int): Number of days to include in statistics
            
        Returns:
            dict: System workflow statistics
        """
        from campaigns.models.workflow import WorkflowExecution
        
        try:
            # Calculate date range
            end_date = timezone.now()
            start_date = end_date - timedelta(days=days)
            
            # Get workflow executions in date range
            executions = WorkflowExecution.objects.filter(start_time__gte=start_date, start_time__lte=end_date)
            
            # Calculate statistics
            total_executions = executions.count()
            completed_executions = executions.filter(status='completed').count()
            failed_executions = executions.filter(status='failed').count()
            running_executions = executions.filter(status='running').count()
            
            # Calculate average duration for completed workflows
            avg_duration = executions.filter(status='completed').aggregate(avg_duration=Avg('duration'))['avg_duration'] or 0
            
            # Calculate success rate
            success_rate = (completed_executions / total_executions * 100) if total_executions > 0 else 0
            
            # Get workflow types
            workflow_types = executions.values('workflow_type').annotate(count=Count('id')).order_by('-count')
            
            # Get daily execution counts
            daily_executions = []
            current_date = start_date.date()
            while current_date <= end_date.date():
                day_start = timezone.make_aware(datetime.combine(current_date, datetime.min.time()))
                day_end = timezone.make_aware(datetime.combine(current_date, datetime.max.time()))
                
                day_executions = executions.filter(start_time__gte=day_start, start_time__lte=day_end)
                day_completed = day_executions.filter(status='completed').count()
                day_failed = day_executions.filter(status='failed').count()
                
                daily_executions.append({
                    'date': current_date.isoformat(),
                    'total': day_executions.count(),
                    'completed': day_completed,
                    'failed': day_failed
                })
                
                current_date += timedelta(days=1)
            
            # Get workflow type statistics
            workflow_type_stats = {}
            for wf_type in executions.values_list('workflow_type', flat=True).distinct():
                type_executions = executions.filter(workflow_type=wf_type)
                type_completed = type_executions.filter(status='completed').count()
                type_failed = type_executions.filter(status='failed').count()
                type_running = type_executions.filter(status='running').count()
                type_success_rate = (type_completed / type_executions.count() * 100) if type_executions.count() > 0 else 0
                type_avg_duration = type_executions.filter(status='completed').aggregate(avg_duration=Avg('duration'))['avg_duration'] or 0
                
                workflow_type_stats[wf_type] = {
                    'total': type_executions.count(),
                    'completed': type_completed,
                    'failed': type_failed,
                    'running': type_running,
                    'success_rate': type_success_rate,
                    'avg_duration': type_avg_duration
                }
            
            # Return statistics
            return {
                'success': True,
                'date_range': {
                    'start_date': start_date.isoformat(),
                    'end_date': end_date.isoformat(),
                    'days': days
                },
                'total_executions': total_executions,
                'completed_executions': completed_executions,
                'failed_executions': failed_executions,
                'running_executions': running_executions,
                'success_rate': success_rate,
                'avg_duration': avg_duration,
                'workflow_types': list(workflow_types),
                'workflow_type_stats': workflow_type_stats,
                'daily_executions': daily_executions
            }
        
        except Exception as e:
            logger.exception(f"Error getting system workflow stats: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_campaign_dashboard_data(self, campaign_id):
        """
        Get dashboard data for a campaign.
        
        Args:
            campaign_id (str): Campaign ID
            
        Returns:
            dict: Dashboard data
        """
        from campaigns.models.workflow import WorkflowExecution
        from campaigns.models import Campaign, TagAnalysisResult
        from instagram.models import Accounts, WhiteListEntry
        
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)
            
            # Get workflow executions
            executions = WorkflowExecution.objects.filter(campaign=campaign)
            
            # Get collected accounts
            collected_accounts = Accounts.objects.filter(campaign=campaign)
            
            # Get tag analysis results
            tag_results = TagAnalysisResult.objects.filter(campaign=campaign)
            
            # Get whitelist entries
            whitelist_entries = WhiteListEntry.objects.filter(account__in=collected_accounts)
            
            # Calculate statistics
            total_accounts = collected_accounts.count()
            total_tagged = tag_results.filter(matched=True).values('account').distinct().count()
            total_whitelist = whitelist_entries.count()
            
            # Calculate workflow statistics
            total_executions = executions.count()
            completed_executions = executions.filter(status='completed').count()
            failed_executions = executions.filter(status='failed').count()
            running_executions = executions.filter(status='running').count()
            
            # Get workflow type counts
            workflow_type_counts = executions.values('workflow_type').annotate(count=Count('id')).order_by('-count')
            
            # Get tag distribution
            tag_distribution = tag_results.filter(matched=True).values('tag__name').annotate(count=Count('id')).order_by('-count')[:10]
            
            # Get recent executions
            recent_executions = executions.order_by('-start_time')[:5].values(
                'id', 'workflow_type', 'status', 'start_time', 'end_time', 'duration', 'progress'
            )
            
            # Return dashboard data
            return {
                'success': True,
                'campaign_id': str(campaign.id),
                'campaign_name': campaign.name,
                'campaign_status': campaign.status,
                'account_stats': {
                    'total_accounts': total_accounts,
                    'total_tagged': total_tagged,
                    'total_whitelist': total_whitelist,
                    'tagging_rate': (total_tagged / total_accounts * 100) if total_accounts > 0 else 0,
                    'whitelist_rate': (total_whitelist / total_accounts * 100) if total_accounts > 0 else 0
                },
                'workflow_stats': {
                    'total_executions': total_executions,
                    'completed_executions': completed_executions,
                    'failed_executions': failed_executions,
                    'running_executions': running_executions,
                    'success_rate': (completed_executions / total_executions * 100) if total_executions > 0 else 0
                },
                'workflow_type_counts': list(workflow_type_counts),
                'tag_distribution': list(tag_distribution),
                'recent_executions': list(recent_executions)
            }
        
        except Campaign.DoesNotExist:
            logger.error(f"Campaign not found: {campaign_id}")
            return {
                'success': False,
                'error': 'Campaign not found',
                'campaign_id': campaign_id
            }
        
        except Exception as e:
            logger.exception(f"Error getting campaign dashboard data: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'campaign_id': campaign_id
            }
    
    def get_system_dashboard_data(self):
        """
        Get system-wide dashboard data.
        
        Returns:
            dict: System dashboard data
        """
        from campaigns.models.workflow import WorkflowExecution
        from campaigns.models import Campaign
        from instagram.models import Accounts, WhiteListEntry
        
        try:
            # Get active campaigns
            active_campaigns = Campaign.objects.filter(status__in=['running', 'pending'])
            
            # Get recent workflow executions
            recent_executions = WorkflowExecution.objects.order_by('-start_time')[:10]
            
            # Get running workflow executions
            running_executions = WorkflowExecution.objects.filter(status='running')
            
            # Calculate statistics
            total_campaigns = Campaign.objects.count()
            active_campaign_count = active_campaigns.count()
            completed_campaign_count = Campaign.objects.filter(status='completed').count()
            
            total_accounts = Accounts.objects.count()
            total_whitelist = WhiteListEntry.objects.count()
            
            total_executions = WorkflowExecution.objects.count()
            completed_executions = WorkflowExecution.objects.filter(status='completed').count()
            failed_executions = WorkflowExecution.objects.filter(status='failed').count()
            running_execution_count = running_executions.count()
            
            # Get workflow type counts
            workflow_type_counts = WorkflowExecution.objects.values('workflow_type').annotate(count=Count('id')).order_by('-count')
            
            # Format recent executions
            recent_execution_data = []
            for execution in recent_executions:
                recent_execution_data.append({
                    'id': str(execution.id),
                    'campaign_id': str(execution.campaign.id),
                    'campaign_name': execution.campaign.name,
                    'workflow_type': execution.workflow_type,
                    'status': execution.status,
                    'start_time': execution.start_time.isoformat(),
                    'end_time': execution.end_time.isoformat() if execution.end_time else None,
                    'duration': execution.duration,
                    'progress': execution.progress
                })
            
            # Format running executions
            running_execution_data = []
            for execution in running_executions:
                running_execution_data.append({
                    'id': str(execution.id),
                    'campaign_id': str(execution.campaign.id),
                    'campaign_name': execution.campaign.name,
                    'workflow_type': execution.workflow_type,
                    'start_time': execution.start_time.isoformat(),
                    'progress': execution.progress,
                    'processed_items': execution.processed_items,
                    'total_items': execution.total_items
                })
            
            # Return dashboard data
            return {
                'success': True,
                'campaign_stats': {
                    'total_campaigns': total_campaigns,
                    'active_campaigns': active_campaign_count,
                    'completed_campaigns': completed_campaign_count
                },
                'account_stats': {
                    'total_accounts': total_accounts,
                    'total_whitelist': total_whitelist,
                    'whitelist_rate': (total_whitelist / total_accounts * 100) if total_accounts > 0 else 0
                },
                'workflow_stats': {
                    'total_executions': total_executions,
                    'completed_executions': completed_executions,
                    'failed_executions': failed_executions,
                    'running_executions': running_execution_count,
                    'success_rate': (completed_executions / total_executions * 100) if total_executions > 0 else 0
                },
                'workflow_type_counts': list(workflow_type_counts),
                'recent_executions': recent_execution_data,
                'running_executions': running_execution_data,
                'active_campaigns': [
                    {
                        'id': str(campaign.id),
                        'name': campaign.name,
                        'status': campaign.status,
                        'target_type': campaign.target_type,
                        'audience_type': campaign.audience_type,
                        'created_at': campaign.created_at.isoformat()
                    }
                    for campaign in active_campaigns
                ]
            }
        
        except Exception as e:
            logger.exception(f"Error getting system dashboard data: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
