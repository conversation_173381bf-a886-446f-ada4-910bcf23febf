"""
ROI Service for campaign performance analysis.
"""
import logging
from decimal import Decimal
from django.db import transaction
from django.utils import timezone

logger = logging.getLogger(__name__)


class ROIService:
    """
    Service for calculating and tracking campaign ROI metrics.
    """
    
    def __init__(self, repository=None):
        """
        Initialize the ROI service.
        
        Args:
            repository: Optional repository for data access
        """
        self.repository = repository
    
    def get_campaign_roi(self, campaign_id):
        """
        Get ROI metrics for a campaign.
        
        Args:
            campaign_id: UUID of the campaign
            
        Returns:
            dict: ROI metrics
        """
        from campaigns.models import Campaign, CampaignROI
        
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)
            
            # Get or create ROI
            roi, created = CampaignROI.objects.get_or_create(
                campaign=campaign,
                defaults={
                    'cost_type': 'fixed',
                    'cost_value': Decimal('0.00')
                }
            )
            
            # Get latest result
            result = campaign.results.first()
            
            # Calculate costs and ROI if we have results
            if result:
                roi.calculate_costs(result)
                roi.calculate_roi()
                roi.save()
            
            return {
                'id': str(roi.id),
                'campaign_id': str(campaign.id),
                'cost_type': roi.cost_type,
                'cost_value': float(roi.cost_value),
                'total_cost': float(roi.total_cost),
                'estimated_value': float(roi.estimated_value),
                'actual_value': float(roi.actual_value),
                'roi_percentage': float(roi.roi_percentage),
                'conversion_rate': float(roi.conversion_rate),
                'cost_per_account': float(roi.cost_per_account),
                'cost_per_whitelist': float(roi.cost_per_whitelist),
                'notes': roi.notes
            }
            
        except Exception as e:
            logger.exception(f"Error getting campaign ROI: {str(e)}")
            return {
                'error': str(e)
            }
    
    def update_campaign_roi(self, campaign_id, data):
        """
        Update ROI metrics for a campaign.
        
        Args:
            campaign_id: UUID of the campaign
            data: Dict with ROI data to update
            
        Returns:
            dict: Updated ROI metrics
        """
        from campaigns.models import Campaign, CampaignROI
        
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)
            
            # Get or create ROI
            roi, created = CampaignROI.objects.get_or_create(
                campaign=campaign,
                defaults={
                    'cost_type': 'fixed',
                    'cost_value': Decimal('0.00')
                }
            )
            
            # Update fields
            with transaction.atomic():
                if 'cost_type' in data:
                    roi.cost_type = data['cost_type']
                
                if 'cost_value' in data:
                    roi.cost_value = Decimal(str(data['cost_value']))
                
                if 'estimated_value' in data:
                    roi.estimated_value = Decimal(str(data['estimated_value']))
                
                if 'actual_value' in data:
                    roi.actual_value = Decimal(str(data['actual_value']))
                
                if 'notes' in data:
                    roi.notes = data['notes']
                
                # Get latest result
                result = campaign.results.first()
                
                # Calculate costs and ROI
                roi.calculate_costs(result)
                roi.calculate_roi()
                roi.save()
            
            return {
                'id': str(roi.id),
                'campaign_id': str(campaign.id),
                'cost_type': roi.cost_type,
                'cost_value': float(roi.cost_value),
                'total_cost': float(roi.total_cost),
                'estimated_value': float(roi.estimated_value),
                'actual_value': float(roi.actual_value),
                'roi_percentage': float(roi.roi_percentage),
                'conversion_rate': float(roi.conversion_rate),
                'cost_per_account': float(roi.cost_per_account),
                'cost_per_whitelist': float(roi.cost_per_whitelist),
                'notes': roi.notes
            }
            
        except Exception as e:
            logger.exception(f"Error updating campaign ROI: {str(e)}")
            return {
                'error': str(e)
            }
    
    def calculate_campaign_quality(self, campaign_id):
        """
        Calculate quality score for a campaign.
        
        Args:
            campaign_id: UUID of the campaign
            
        Returns:
            dict: Quality metrics
        """
        from campaigns.models import Campaign
        from instagram.models import Accounts
        
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)
            
            # Get latest result
            result = campaign.results.first()
            
            if not result:
                return {
                    'quality_score': 0,
                    'error': 'No results available'
                }
            
            # Calculate engagement rate if not already set
            if result.engagement_rate == 0:
                accounts = Accounts.objects.filter(campaign=campaign)
                
                if accounts.exists():
                    total_engagement = 0
                    count = 0
                    
                    for account in accounts:
                        if account.followers > 0:
                            engagement = (account.likes_count + account.comments_count) / account.followers
                            total_engagement += engagement
                            count += 1
                    
                    if count > 0:
                        result.engagement_rate = total_engagement / count
                        result.save()
            
            # Calculate quality score
            quality_score = result.calculate_quality_score()
            result.save()
            
            return {
                'quality_score': quality_score,
                'engagement_rate': result.engagement_rate,
                'confidence_score': result.average_confidence_score,
                'whitelist_ratio': result.total_accounts_whitelisted / result.total_accounts_processed if result.total_accounts_processed > 0 else 0
            }
            
        except Exception as e:
            logger.exception(f"Error calculating campaign quality: {str(e)}")
            return {
                'error': str(e)
            }
    
    def get_roi_comparison(self, campaign_ids=None):
        """
        Get ROI comparison for multiple campaigns.
        
        Args:
            campaign_ids: List of campaign UUIDs to compare
            
        Returns:
            dict: ROI comparison data
        """
        from campaigns.models import Campaign, CampaignROI
        
        try:
            # Get campaigns
            campaigns = Campaign.objects.all()
            
            if campaign_ids:
                campaigns = campaigns.filter(id__in=campaign_ids)
            
            # Get ROI data
            roi_data = []
            
            for campaign in campaigns:
                try:
                    roi = CampaignROI.objects.get(campaign=campaign)
                    result = campaign.results.first()
                    
                    if result:
                        roi_data.append({
                            'campaign_id': str(campaign.id),
                            'campaign_name': campaign.name,
                            'roi_percentage': float(roi.roi_percentage),
                            'total_cost': float(roi.total_cost),
                            'actual_value': float(roi.actual_value),
                            'cost_per_whitelist': float(roi.cost_per_whitelist),
                            'conversion_rate': float(roi.conversion_rate),
                            'quality_score': float(result.quality_score),
                            'accounts_processed': result.total_accounts_processed,
                            'accounts_whitelisted': result.total_accounts_whitelisted
                        })
                except CampaignROI.DoesNotExist:
                    pass
            
            # Sort by ROI percentage
            roi_data.sort(key=lambda x: x['roi_percentage'], reverse=True)
            
            # Calculate averages
            avg_roi = sum(item['roi_percentage'] for item in roi_data) / len(roi_data) if roi_data else 0
            avg_cost = sum(item['total_cost'] for item in roi_data) / len(roi_data) if roi_data else 0
            avg_value = sum(item['actual_value'] for item in roi_data) / len(roi_data) if roi_data else 0
            avg_conversion = sum(item['conversion_rate'] for item in roi_data) / len(roi_data) if roi_data else 0
            
            return {
                'campaigns': roi_data,
                'averages': {
                    'roi_percentage': avg_roi,
                    'total_cost': avg_cost,
                    'actual_value': avg_value,
                    'conversion_rate': avg_conversion
                },
                'count': len(roi_data)
            }
            
        except Exception as e:
            logger.exception(f"Error getting ROI comparison: {str(e)}")
            return {
                'error': str(e)
            }
    
    def get_roi_trends(self, campaign_id, period='monthly'):
        """
        Get ROI trends over time for a campaign.
        
        Args:
            campaign_id: UUID of the campaign
            period: Time period for grouping ('daily', 'weekly', 'monthly')
            
        Returns:
            dict: ROI trend data
        """
        from campaigns.models import Campaign, CampaignROI
        from instagram.models import WhiteListEntry
        from django.db.models import Count
        from django.db.models.functions import TruncDay, TruncWeek, TruncMonth
        
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)
            
            # Get ROI
            try:
                roi = CampaignROI.objects.get(campaign=campaign)
            except CampaignROI.DoesNotExist:
                return {
                    'error': 'ROI data not found'
                }
            
            # Get whitelist entries by time period
            if period == 'daily':
                trunc_func = TruncDay
                date_format = '%Y-%m-%d'
            elif period == 'weekly':
                trunc_func = TruncWeek
                date_format = '%Y-%m-%d'
            else:  # monthly
                trunc_func = TruncMonth
                date_format = '%Y-%m'
            
            whitelist_trend = WhiteListEntry.objects.filter(
                account__campaign=campaign
            ).annotate(
                period=trunc_func('created_at')
            ).values('period').annotate(
                count=Count('id')
            ).order_by('period')
            
            # Calculate cumulative values and ROI for each period
            trend_data = []
            cumulative_count = 0
            
            for item in whitelist_trend:
                cumulative_count += item['count']
                period_cost = roi.cost_value * cumulative_count if roi.cost_type == 'per_whitelist' else roi.cost_value
                period_value = roi.actual_value * (cumulative_count / roi.campaign.results.first().total_accounts_whitelisted) if roi.campaign.results.first().total_accounts_whitelisted > 0 else 0
                period_roi = ((period_value - period_cost) / period_cost * 100) if period_cost > 0 else 0
                
                trend_data.append({
                    'period': item['period'].strftime(date_format),
                    'new_whitelists': item['count'],
                    'cumulative_whitelists': cumulative_count,
                    'cost': float(period_cost),
                    'value': float(period_value),
                    'roi': float(period_roi)
                })
            
            return {
                'campaign_id': str(campaign.id),
                'campaign_name': campaign.name,
                'period': period,
                'trend': trend_data
            }
            
        except Exception as e:
            logger.exception(f"Error getting ROI trends: {str(e)}")
            return {
                'error': str(e)
            }
