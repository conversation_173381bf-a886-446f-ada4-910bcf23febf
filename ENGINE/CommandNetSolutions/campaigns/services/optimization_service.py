"""
Optimization Service for improving system performance.
"""
import logging
import time
from functools import wraps
from django.db import connection, reset_queries
from django.conf import settings
from django.core.cache import cache
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from django.db.models import Count, Q, Avg, Sum, F, Prefetch

logger = logging.getLogger(__name__)


def query_debugger(func):
    """
    Debug database queries for a function.
    
    This decorator prints the number of queries executed and the time taken.
    Only works when DEBUG is True.
    """
    @wraps(func)
    def wrapper(*args, **kwargs):
        reset_queries()
        
        start = time.time()
        result = func(*args, **kwargs)
        end = time.time()
        
        if settings.DEBUG:
            queries = len(connection.queries)
            logger.debug(f"Function: {func.__name__}")
            logger.debug(f"Number of Queries: {queries}")
            logger.debug(f"Execution Time: {(end - start):.3f}s")
        
        return result
    return wrapper


def cache_result(timeout=300, key_prefix=''):
    """
    Cache the result of a function.
    
    Args:
        timeout (int): Cache timeout in seconds
        key_prefix (str): Prefix for cache key
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            cache_key = f"{key_prefix}_{func.__name__}"
            
            # Add args and kwargs to cache key
            if args:
                cache_key += f"_{hash(args)}"
            if kwargs:
                cache_key += f"_{hash(frozenset(kwargs.items()))}"
            
            # Try to get from cache
            cached_result = cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Call function and cache result
            result = func(*args, **kwargs)
            cache.set(cache_key, result, timeout)
            
            return result
        return wrapper
    return decorator


class OptimizationService:
    """
    Service for optimizing system performance.
    Provides methods for optimizing database queries, caching, and more.
    """
    
    @staticmethod
    def optimize_campaign_query(queryset):
        """
        Optimize a campaign queryset.
        
        Args:
            queryset: Campaign queryset
            
        Returns:
            Optimized queryset
        """
        # Prefetch related objects
        return queryset.prefetch_related(
            'location_targets',
            'username_targets',
            Prefetch(
                'analysis_settings',
                queryset=queryset.model.analysis_settings.related.related_model.objects.select_related('scoring_profile')
            )
        ).select_related('creator')
    
    @staticmethod
    def optimize_tag_query(queryset):
        """
        Optimize a tag queryset.
        
        Args:
            queryset: Tag queryset
            
        Returns:
            Optimized queryset
        """
        # Prefetch related objects
        return queryset.select_related('category')
    
    @staticmethod
    def optimize_notification_query(queryset):
        """
        Optimize a notification queryset.
        
        Args:
            queryset: Notification queryset
            
        Returns:
            Optimized queryset
        """
        # Select related objects
        return queryset.select_related('user')
    
    @staticmethod
    def get_campaign_stats(campaign_id):
        """
        Get optimized campaign statistics.
        
        Args:
            campaign_id: Campaign ID
            
        Returns:
            Campaign statistics
        """
        from campaigns.models import Campaign, CampaignResult
        
        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)
        
        # Get results with optimized query
        results = CampaignResult.objects.filter(campaign=campaign).aggregate(
            total_accounts=Count('id'),
            total_processed=Count('id', filter=Q(processed=True)),
            total_tagged=Count('id', filter=Q(tags__isnull=False), distinct=True),
            total_whitelisted=Count('id', filter=Q(whitelisted=True)),
            avg_confidence=Avg('confidence_score'),
            avg_engagement=Avg('engagement_rate'),
            avg_quality=Avg('quality_score')
        )
        
        return {
            'campaign': {
                'id': str(campaign.id),
                'name': campaign.name,
                'status': campaign.status
            },
            'stats': results
        }
    
    @staticmethod
    def get_tag_stats(campaign_id):
        """
        Get optimized tag statistics for a campaign.
        
        Args:
            campaign_id: Campaign ID
            
        Returns:
            Tag statistics
        """
        from campaigns.models import Campaign, CampaignResult, DynamicTag
        from django.db.models import Count, Avg, Case, When, IntegerField
        
        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)
        
        # Get tag statistics with optimized query
        tag_stats = DynamicTag.objects.filter(
            campaignresult__campaign=campaign
        ).annotate(
            match_count=Count('campaignresult', distinct=True),
            whitelist_count=Count(
                'campaignresult',
                filter=Q(campaignresult__whitelisted=True),
                distinct=True
            ),
            avg_confidence=Avg('campaignresult__tag_matches__confidence'),
            whitelist_rate=Case(
                When(match_count=0, then=0),
                default=F('whitelist_count') * 100.0 / F('match_count'),
                output_field=IntegerField()
            )
        ).values(
            'id', 'name', 'match_count', 'whitelist_count', 'avg_confidence', 'whitelist_rate'
        )
        
        return list(tag_stats)
    
    @staticmethod
    def apply_caching(viewset, methods=None, timeout=300):
        """
        Apply caching to a viewset.
        
        Args:
            viewset: ViewSet class
            methods: List of methods to cache (default: ['list', 'retrieve'])
            timeout: Cache timeout in seconds
            
        Returns:
            ViewSet class with caching applied
        """
        methods = methods or ['list', 'retrieve']
        
        # Apply cache_page decorator to specified methods
        for method_name in methods:
            if hasattr(viewset, method_name):
                method = getattr(viewset, method_name)
                cached_method = method_decorator(cache_page(timeout))(method)
                setattr(viewset, method_name, cached_method)
        
        return viewset
    
    @staticmethod
    def optimize_bulk_operations(model, objects, batch_size=100):
        """
        Optimize bulk operations.
        
        Args:
            model: Model class
            objects: List of objects to create/update
            batch_size: Batch size
            
        Returns:
            List of created/updated objects
        """
        # Split objects into batches
        batches = [objects[i:i + batch_size] for i in range(0, len(objects), batch_size)]
        
        # Process batches
        results = []
        for batch in batches:
            # Determine operation type
            if all(obj.pk is None for obj in batch):
                # Bulk create
                created = model.objects.bulk_create(batch)
                results.extend(created)
            else:
                # Bulk update
                fields = [f.name for f in model._meta.fields if f.name != 'id']
                model.objects.bulk_update(batch, fields)
                results.extend(batch)
        
        return results
