"""
Service for tag operations.
"""
from campaigns.domains.tag import TagDomain


class TagService:
    """
    Service for tag operations.
    Coordinates between domain and repository.
    """
    
    def __init__(self, repository):
        """
        Initialize service.
        
        Args:
            repository (TagRepository): Tag repository
        """
        self.repository = repository
        self.domain = TagDomain(repository)
    
    def create_tag(self, tag_data):
        """
        Create a new tag.
        
        Args:
            tag_data (dict): Tag data
            
        Returns:
            DynamicTag: Created tag
        """
        return self.domain.create_tag(tag_data)
    
    def update_tag(self, tag_id, tag_data):
        """
        Update an existing tag.
        
        Args:
            tag_id (uuid): Tag ID
            tag_data (dict): Updated tag data
            
        Returns:
            DynamicTag: Updated tag
        """
        return self.domain.update_tag(tag_id, tag_data)
    
    def delete_tag(self, tag_id):
        """
        Delete a tag.
        
        Args:
            tag_id (uuid): Tag ID
        """
        return self.domain.delete_tag(tag_id)
    
    def get_tag(self, tag_id):
        """
        Get tag by ID.
        
        Args:
            tag_id (uuid): Tag ID
            
        Returns:
            DynamicTag: Tag instance
        """
        return self.repository.get_by_id(tag_id)
    
    def list_tags(self, filters=None, order_by=None):
        """
        List tags with optional filtering and ordering.
        
        Args:
            filters (dict): Optional filters
            order_by (str): Optional ordering field
            
        Returns:
            QuerySet: Tag queryset
        """
        return self.repository.get_all(filters, order_by, with_usage=True)
    
    def assign_tag_to_campaign(self, tag_id, campaign_id):
        """
        Assign tag to campaign.
        
        Args:
            tag_id (uuid): Tag ID
            campaign_id (uuid): Campaign ID
        """
        return self.domain.assign_tag_to_campaign(tag_id, campaign_id)
    
    def remove_tag_from_campaign(self, tag_id, campaign_id):
        """
        Remove tag from campaign.
        
        Args:
            tag_id (uuid): Tag ID
            campaign_id (uuid): Campaign ID
        """
        return self.domain.remove_tag_from_campaign(tag_id, campaign_id)
