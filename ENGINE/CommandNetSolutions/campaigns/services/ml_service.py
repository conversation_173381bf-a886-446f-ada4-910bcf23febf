"""
Machine Learning service for advanced tag analysis.
"""
import logging
import numpy as np
from django.conf import settings
import os
import json
import pickle
from datetime import datetime

logger = logging.getLogger(__name__)

# Define model directory
ML_MODELS_DIR = os.path.join(settings.BASE_DIR, 'campaigns', 'ml_models')
os.makedirs(ML_MODELS_DIR, exist_ok=True)


class MLService:
    """
    Service for machine learning-based tag analysis.
    Provides methods for training, evaluating, and using ML models for tag matching.
    """
    
    def __init__(self):
        """Initialize the ML service."""
        self.models = {}
        self.vectorizers = {}
        self.model_metadata = {}
        self._load_models()
    
    def _load_models(self):
        """Load all available ML models from disk."""
        if not os.path.exists(ML_MODELS_DIR):
            logger.warning(f"ML models directory not found: {ML_MODELS_DIR}")
            return
        
        # Load model metadata
        metadata_path = os.path.join(ML_MODELS_DIR, 'model_metadata.json')
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, 'r') as f:
                    self.model_metadata = json.load(f)
            except Exception as e:
                logger.error(f"Error loading model metadata: {str(e)}")
                self.model_metadata = {}
        
        # Load models
        for tag_id in self.model_metadata:
            try:
                model_path = os.path.join(ML_MODELS_DIR, f"{tag_id}_model.pkl")
                vectorizer_path = os.path.join(ML_MODELS_DIR, f"{tag_id}_vectorizer.pkl")
                
                if os.path.exists(model_path) and os.path.exists(vectorizer_path):
                    with open(model_path, 'rb') as f:
                        self.models[tag_id] = pickle.load(f)
                    
                    with open(vectorizer_path, 'rb') as f:
                        self.vectorizers[tag_id] = pickle.load(f)
                    
                    logger.info(f"Loaded ML model for tag {tag_id}")
            except Exception as e:
                logger.error(f"Error loading model for tag {tag_id}: {str(e)}")
    
    def _save_model(self, tag_id, model, vectorizer, metadata):
        """
        Save a model and its metadata to disk.
        
        Args:
            tag_id (str): Tag ID
            model: Trained model
            vectorizer: Feature vectorizer
            metadata (dict): Model metadata
        """
        try:
            # Save model
            model_path = os.path.join(ML_MODELS_DIR, f"{tag_id}_model.pkl")
            with open(model_path, 'wb') as f:
                pickle.dump(model, f)
            
            # Save vectorizer
            vectorizer_path = os.path.join(ML_MODELS_DIR, f"{tag_id}_vectorizer.pkl")
            with open(vectorizer_path, 'wb') as f:
                pickle.dump(vectorizer, f)
            
            # Update metadata
            self.model_metadata[tag_id] = metadata
            
            # Save metadata
            metadata_path = os.path.join(ML_MODELS_DIR, 'model_metadata.json')
            with open(metadata_path, 'w') as f:
                json.dump(self.model_metadata, f)
            
            # Update in-memory models
            self.models[tag_id] = model
            self.vectorizers[tag_id] = vectorizer
            
            logger.info(f"Saved ML model for tag {tag_id}")
            return True
        except Exception as e:
            logger.error(f"Error saving model for tag {tag_id}: {str(e)}")
            return False
    
    def train_model(self, tag_id, positive_examples, negative_examples, tag_field):
        """
        Train a machine learning model for a tag.
        
        Args:
            tag_id (str): Tag ID
            positive_examples (list): List of text examples that should match the tag
            negative_examples (list): List of text examples that should not match the tag
            tag_field (str): Field to analyze (bio, interests, etc.)
            
        Returns:
            dict: Training results
        """
        try:
            from sklearn.feature_extraction.text import TfidfVectorizer
            from sklearn.ensemble import RandomForestClassifier
            from sklearn.model_selection import train_test_split
            from sklearn.metrics import precision_score, recall_score, f1_score, accuracy_score
            
            # Prepare data
            X = positive_examples + negative_examples
            y = [1] * len(positive_examples) + [0] * len(negative_examples)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
            
            # Create vectorizer
            vectorizer = TfidfVectorizer(max_features=1000, stop_words='english')
            X_train_vec = vectorizer.fit_transform(X_train)
            X_test_vec = vectorizer.transform(X_test)
            
            # Train model
            model = RandomForestClassifier(n_estimators=100, random_state=42)
            model.fit(X_train_vec, y_train)
            
            # Evaluate model
            y_pred = model.predict(X_test_vec)
            accuracy = accuracy_score(y_test, y_pred)
            precision = precision_score(y_test, y_pred, zero_division=0)
            recall = recall_score(y_test, y_pred, zero_division=0)
            f1 = f1_score(y_test, y_pred, zero_division=0)
            
            # Save model
            metadata = {
                'field': tag_field,
                'training_examples': len(X),
                'positive_examples': len(positive_examples),
                'negative_examples': len(negative_examples),
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'created_at': datetime.now().isoformat(),
                'algorithm': 'RandomForestClassifier'
            }
            
            self._save_model(tag_id, model, vectorizer, metadata)
            
            return {
                'success': True,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'training_examples': len(X)
            }
        except Exception as e:
            logger.exception(f"Error training model for tag {tag_id}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def analyze_text(self, tag_id, text):
        """
        Analyze text using a trained model.
        
        Args:
            tag_id (str): Tag ID
            text (str): Text to analyze
            
        Returns:
            dict: Analysis results
        """
        if tag_id not in self.models or tag_id not in self.vectorizers:
            return {
                'matched': False,
                'confidence': 0.0,
                'error': 'Model not found'
            }
        
        try:
            # Vectorize text
            text_vec = self.vectorizers[tag_id].transform([text])
            
            # Get prediction
            prediction = self.models[tag_id].predict(text_vec)[0]
            
            # Get confidence
            probabilities = self.models[tag_id].predict_proba(text_vec)[0]
            confidence = probabilities[1] if prediction == 1 else 1 - probabilities[1]
            
            return {
                'matched': prediction == 1,
                'confidence': float(confidence),
                'metadata': self.model_metadata.get(tag_id, {})
            }
        except Exception as e:
            logger.exception(f"Error analyzing text with tag {tag_id}: {str(e)}")
            return {
                'matched': False,
                'confidence': 0.0,
                'error': str(e)
            }
    
    def analyze_account(self, tag, account):
        """
        Analyze an account using a trained model.
        
        Args:
            tag (DynamicTag): Tag to use for analysis
            account (Accounts): Account to analyze
            
        Returns:
            dict: Analysis results
        """
        # Get field value
        field_value = getattr(account, tag.field, None)
        
        # Skip if field value is None
        if field_value is None:
            return {
                'matched': False,
                'confidence': 0.0,
                'error': f'Field {tag.field} not found'
            }
        
        # Convert to string if it's not already
        if isinstance(field_value, list):
            # For array fields like interests, locations, etc.
            field_value = ' '.join(str(item) for item in field_value if item)
        else:
            field_value = str(field_value)
        
        # Analyze text
        return self.analyze_text(str(tag.id), field_value)
    
    def get_model_info(self, tag_id):
        """
        Get information about a trained model.
        
        Args:
            tag_id (str): Tag ID
            
        Returns:
            dict: Model information
        """
        if tag_id not in self.model_metadata:
            return {
                'exists': False,
                'error': 'Model not found'
            }
        
        return {
            'exists': True,
            **self.model_metadata[tag_id]
        }
    
    def delete_model(self, tag_id):
        """
        Delete a trained model.
        
        Args:
            tag_id (str): Tag ID
            
        Returns:
            bool: Success status
        """
        try:
            # Delete model file
            model_path = os.path.join(ML_MODELS_DIR, f"{tag_id}_model.pkl")
            if os.path.exists(model_path):
                os.remove(model_path)
            
            # Delete vectorizer file
            vectorizer_path = os.path.join(ML_MODELS_DIR, f"{tag_id}_vectorizer.pkl")
            if os.path.exists(vectorizer_path):
                os.remove(vectorizer_path)
            
            # Remove from metadata
            if tag_id in self.model_metadata:
                del self.model_metadata[tag_id]
            
            # Save metadata
            metadata_path = os.path.join(ML_MODELS_DIR, 'model_metadata.json')
            with open(metadata_path, 'w') as f:
                json.dump(self.model_metadata, f)
            
            # Remove from in-memory models
            if tag_id in self.models:
                del self.models[tag_id]
            
            if tag_id in self.vectorizers:
                del self.vectorizers[tag_id]
            
            logger.info(f"Deleted ML model for tag {tag_id}")
            return True
        except Exception as e:
            logger.error(f"Error deleting model for tag {tag_id}: {str(e)}")
            return False
