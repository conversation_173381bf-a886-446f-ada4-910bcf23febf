"""
Workflow service for managing campaign workflows.

This service provides methods for creating and running workflows for campaigns.
It uses the PyFlowFactory to get the appropriate PyFlow service implementation.
"""
import logging
from django.db import transaction

from campaigns.models import Campaign
from campaigns.models.workflow import WorkflowExecution
from campaigns.services.pyflow_factory import PyFlowFactory

logger = logging.getLogger(__name__)

class WorkflowService:
    """
    Service for managing campaign workflows.
    
    This service provides methods for creating and running workflows for campaigns.
    It uses the PyFlowFactory to get the appropriate PyFlow service implementation.
    """
    
    def __init__(self):
        """Initialize the workflow service."""
        # Get the appropriate PyFlow service implementation
        self.pyflow_service = PyFlowFactory.create_pyflow_service()
    
    def run_collection_workflow(self, campaign_id, targets=None, options=None):
        """
        Run a collection workflow for a campaign.
        
        Args:
            campaign_id (str): Campaign ID
            targets (list, optional): List of targets (locations or usernames)
            options (dict, optional): Additional options
            
        Returns:
            dict: Result of workflow execution
        """
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)
            
            # Get targets if not provided
            if targets is None:
                targets = self._get_campaign_targets(campaign)
            
            # Run workflow
            result = self.pyflow_service.create_and_run_collection_workflow(
                campaign_id=str(campaign.id),
                targets=targets,
                options=options
            )
            
            # Update campaign status if workflow started successfully
            if result.get('success'):
                with transaction.atomic():
                    campaign.status = 'running'
                    campaign.save(update_fields=['status'])
            
            return result
            
        except Campaign.DoesNotExist:
            logger.error(f"Campaign {campaign_id} not found")
            return {
                'success': False,
                'error': f"Campaign {campaign_id} not found"
            }
        except Exception as e:
            logger.exception(f"Error running collection workflow: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def run_analysis_workflow(self, campaign_id, analysis_settings=None, options=None):
        """
        Run an analysis workflow for a campaign.
        
        Args:
            campaign_id (str): Campaign ID
            analysis_settings (dict, optional): Analysis settings
            options (dict, optional): Additional options
            
        Returns:
            dict: Result of workflow execution
        """
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)
            
            # Get analysis settings if not provided
            if analysis_settings is None:
                analysis_settings = self._get_campaign_analysis_settings(campaign)
            
            # Run workflow
            result = self.pyflow_service.create_and_run_analysis_workflow(
                campaign_id=str(campaign.id),
                analysis_settings=analysis_settings,
                options=options
            )
            
            # Update campaign status if workflow started successfully
            if result.get('success'):
                with transaction.atomic():
                    campaign.status = 'analyzing'
                    campaign.save(update_fields=['status'])
            
            return result
            
        except Campaign.DoesNotExist:
            logger.error(f"Campaign {campaign_id} not found")
            return {
                'success': False,
                'error': f"Campaign {campaign_id} not found"
            }
        except Exception as e:
            logger.exception(f"Error running analysis workflow: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def run_engagement_workflow(self, campaign_id, workflow_type, accounts=None, options=None):
        """
        Run an engagement workflow for a campaign.
        
        Args:
            campaign_id (str): Campaign ID
            workflow_type (str): Type of engagement workflow ('follow', 'like', 'comment', 'dm', 'cep')
            accounts (list, optional): List of accounts to engage with
            options (dict, optional): Additional options
            
        Returns:
            dict: Result of workflow execution
        """
        try:
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)
            
            # Get accounts if not provided
            if accounts is None:
                accounts = self._get_campaign_whitelist(campaign)
            
            # Run workflow
            result = self.pyflow_service.create_and_run_engagement_workflow(
                campaign_id=str(campaign.id),
                workflow_type=workflow_type,
                accounts=accounts,
                options=options
            )
            
            # Update campaign status if workflow started successfully
            if result.get('success'):
                with transaction.atomic():
                    campaign.status = 'engaging'
                    campaign.save(update_fields=['status'])
            
            return result
            
        except Campaign.DoesNotExist:
            logger.error(f"Campaign {campaign_id} not found")
            return {
                'success': False,
                'error': f"Campaign {campaign_id} not found"
            }
        except Exception as e:
            logger.exception(f"Error running engagement workflow: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_workflow_status(self, workflow_execution_id):
        """
        Get the status of a workflow execution.
        
        Args:
            workflow_execution_id (str): Workflow execution ID
            
        Returns:
            dict: Status of the workflow execution
        """
        return self.pyflow_service.get_workflow_status(workflow_execution_id)
    
    def _get_campaign_targets(self, campaign):
        """
        Get targets for a campaign.
        
        Args:
            campaign (Campaign): Campaign object
            
        Returns:
            list: List of targets
        """
        targets = []
        
        # Add location targets
        for location_target in campaign.location_targets.all():
            targets.append({
                'type': 'location',
                'location_id': location_target.location_id,
                'location_name': location_target.location_name
            })
        
        # Add username targets
        for username_target in campaign.username_targets.all():
            targets.append({
                'type': 'username',
                'username': username_target.username,
                'audience_type': username_target.audience_type
            })
        
        return targets
    
    def _get_campaign_analysis_settings(self, campaign):
        """
        Get analysis settings for a campaign.
        
        Args:
            campaign (Campaign): Campaign object
            
        Returns:
            dict: Analysis settings
        """
        try:
            # Get analysis settings
            settings = campaign.analysis_settings
            
            return {
                'min_followers': settings.min_followers,
                'max_followers': settings.max_followers,
                'target_tags': settings.target_tags,
                'enable_tagging': settings.enable_tagging
            }
        except Exception:
            # Return default settings
            return {
                'min_followers': None,
                'max_followers': None,
                'target_tags': [],
                'enable_tagging': True
            }
    
    def _get_campaign_whitelist(self, campaign):
        """
        Get whitelist for a campaign.
        
        Args:
            campaign (Campaign): Campaign object
            
        Returns:
            list: List of whitelisted accounts
        """
        # This is a placeholder implementation
        # In a real implementation, you would query the database for whitelisted accounts
        return []
