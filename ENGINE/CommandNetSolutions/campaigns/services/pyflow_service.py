"""
PyFlow Integration Service for workflow automation.
"""
import os
import json
import uuid
import logging
import subprocess
from pathlib import Path
from django.conf import settings
from django.utils import timezone
from django.db import transaction

logger = logging.getLogger(__name__)

# Define PyFlow paths
PYFLOW_ENGINE_DIR = os.path.join(settings.BASE_DIR, '..', '..')
PYFLOW_WORKFLOW_DIR = os.path.join(PYFLOW_ENGINE_DIR, 'INSTA', 'WORKFLOW')
PYFLOW_TEMPLATE_DIR = os.path.join(settings.BASE_DIR, 'campaigns', 'pyflow_templates')
PYFLOW_LOG_DIR = os.path.join(PYFLOW_ENGINE_DIR, 'logs')

# Ensure directories exist
os.makedirs(PYFLOW_WORKFLOW_DIR, exist_ok=True)
os.makedirs(PYFLOW_TEMPLATE_DIR, exist_ok=True)
os.makedirs(PYFLOW_LOG_DIR, exist_ok=True)


class PyFlowService:
    """
    Service for integrating with PyFlow workflows.
    Provides methods for creating, running, and managing PyFlow workflows.
    """

    def __init__(self):
        """Initialize the PyFlow service."""
        self.workflow_dir = PYFLOW_WORKFLOW_DIR
        self.template_dir = PYFLOW_TEMPLATE_DIR

    def _load_template(self, template_name):
        """
        Load a PyFlow template from file.

        Args:
            template_name (str): Name of the template file (without .pygraph extension)

        Returns:
            dict: Template data or None if not found
        """
        template_path = os.path.join(self.template_dir, f"{template_name}.pygraph")

        if not os.path.exists(template_path):
            logger.error(f"Template not found: {template_path}")
            return None

        try:
            with open(template_path, 'r') as f:
                import orjson
                return orjson.loads(f.read())
        except Exception as e:
            logger.exception(f"Error loading template {template_name}: {str(e)}")
            return None

    def _save_workflow(self, workflow_data, workflow_name):
        """
        Save a PyFlow workflow to file.

        Args:
            workflow_data (dict): Workflow data
            workflow_name (str): Name of the workflow file (without .pygraph extension)

        Returns:
            str: Path to the saved workflow or None if failed
        """
        workflow_path = os.path.join(self.workflow_dir, f"{workflow_name}.pygraph")

        try:
            with open(workflow_path, 'w') as f:
                import orjson
                f.write(orjson.dumps(workflow_data).decode('utf-8'))
            return workflow_path
        except Exception as e:
            logger.exception(f"Error saving workflow {workflow_name}: {str(e)}")
            return None

    def _run_workflow(self, workflow_path):
        """
        Run a PyFlow workflow.

        Args:
            workflow_path (str): Path to the workflow file

        Returns:
            dict: Result of the workflow execution
        """
        try:
            # Change to PyFlow engine directory
            os.chdir(PYFLOW_ENGINE_DIR)

            # Run PyFlow command
            cmd = f"python -m pyflow -m run -f {workflow_path}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            # Check result
            if result.returncode == 0:
                return {
                    'success': True,
                    'output': result.stdout,
                    'workflow_path': workflow_path
                }
            else:
                logger.error(f"Error running workflow: {result.stderr}")
                return {
                    'success': False,
                    'error': result.stderr,
                    'workflow_path': workflow_path
                }
        except Exception as e:
            logger.exception(f"Error running workflow: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'workflow_path': workflow_path
            }

    def create_account_collection_workflow(self, campaign_id, targets, options=None):
        """
        Create a PyFlow workflow for collecting accounts from targets.

        Args:
            campaign_id (str): Campaign ID
            targets (list): List of targets (locations or usernames)
            options (dict): Optional workflow options

        Returns:
            dict: Result of workflow creation
        """
        options = options or {}

        # Load template
        template = self._load_template('account_collection')
        if not template:
            return {
                'success': False,
                'error': 'Template not found'
            }

        # Generate workflow name
        workflow_name = f"campaign_{campaign_id}_collection_{uuid.uuid4().hex[:8]}"

        # Customize workflow
        workflow_data = template.copy()

        # Set campaign ID
        for node in workflow_data.get('nodes', []):
            if node.get('name') == 'campaignID':
                node['value'] = str(campaign_id)

            # Set targets
            if node.get('name') == 'targets':
                node['value'] = json.dumps(targets)

            # Set options
            if node.get('name') == 'options':
                node['value'] = json.dumps(options)

        # Set active graph
        workflow_data['activeGraph'] = workflow_data.get('graphs', {}).get('root', '')

        # Save workflow
        workflow_path = self._save_workflow(workflow_data, workflow_name)
        if not workflow_path:
            return {
                'success': False,
                'error': 'Failed to save workflow'
            }

        return {
            'success': True,
            'workflow_name': workflow_name,
            'workflow_path': workflow_path
        }

    def create_account_analysis_workflow(self, campaign_id, analysis_settings=None, options=None):
        """
        Create a PyFlow workflow for analyzing accounts.

        Args:
            campaign_id (str): Campaign ID
            analysis_settings (dict): Analysis settings
            options (dict): Optional workflow options

        Returns:
            dict: Result of workflow creation
        """
        options = options or {}
        analysis_settings = analysis_settings or {}

        # Load template
        template = self._load_template('account_analysis')
        if not template:
            return {
                'success': False,
                'error': 'Template not found'
            }

        # Generate workflow name
        workflow_name = f"campaign_{campaign_id}_analysis_{uuid.uuid4().hex[:8]}"

        # Customize workflow
        workflow_data = template.copy()

        # Set campaign ID
        for node in workflow_data.get('nodes', []):
            if node.get('name') == 'campaignID':
                node['value'] = str(campaign_id)

            # Set analysis settings
            if node.get('name') == 'analysisSettings':
                node['value'] = json.dumps(analysis_settings)

            # Set options
            if node.get('name') == 'options':
                node['value'] = json.dumps(options)

        # Set active graph
        workflow_data['activeGraph'] = workflow_data.get('graphs', {}).get('root', '')

        # Save workflow
        workflow_path = self._save_workflow(workflow_data, workflow_name)
        if not workflow_path:
            return {
                'success': False,
                'error': 'Failed to save workflow'
            }

        return {
            'success': True,
            'workflow_name': workflow_name,
            'workflow_path': workflow_path
        }

    def run_workflow(self, workflow_name=None, workflow_path=None, campaign_id=None, workflow_type=None, parameters=None, track_progress=True):
        """
        Run a PyFlow workflow.

        Args:
            workflow_name (str): Name of the workflow file (without .pygraph extension)
            workflow_path (str): Path to the workflow file
            campaign_id (str): Campaign ID
            workflow_type (str): Type of workflow
            parameters (dict): Parameters to pass to the workflow
            track_progress (bool): Whether to track workflow progress

        Returns:
            dict: Result of workflow execution
        """
        if not workflow_path and workflow_name:
            workflow_path = os.path.join(self.workflow_dir, f"{workflow_name}.pygraph")

        if not workflow_path or not os.path.exists(workflow_path):
            return {
                'success': False,
                'error': 'Workflow not found'
            }

        # Extract workflow name from path if not provided
        if not workflow_name:
            workflow_name = os.path.basename(workflow_path).replace('.pygraph', '')

        # Create workflow execution record if tracking progress
        workflow_execution = None
        if track_progress and campaign_id:
            try:
                from campaigns.models.workflow import WorkflowExecution
                from campaigns.models import Campaign

                # Get campaign
                campaign = Campaign.objects.get(id=campaign_id)

                # Determine workflow type if not provided
                if not workflow_type:
                    if 'collection' in workflow_name:
                        workflow_type = 'collection'
                    elif 'analysis' in workflow_name:
                        workflow_type = 'analysis'
                    elif 'tagging' in workflow_name:
                        workflow_type = 'tagging'
                    elif 'follow' in workflow_name:
                        workflow_type = 'follow'
                    elif 'like' in workflow_name:
                        workflow_type = 'like'
                    elif 'comment' in workflow_name:
                        workflow_type = 'comment'
                    elif 'dm' in workflow_name:
                        workflow_type = 'dm'
                    elif 'cep' in workflow_name:
                        workflow_type = 'cep'
                    else:
                        workflow_type = 'collection'  # Default

                # Create workflow execution record
                with transaction.atomic():
                    workflow_execution = WorkflowExecution.objects.create(
                        campaign=campaign,
                        workflow_name=workflow_name,
                        workflow_path=workflow_path,
                        workflow_type=workflow_type,
                        status='pending',
                        parameters=parameters or {},
                        log_file=os.path.join(PYFLOW_LOG_DIR, f"{workflow_name}.log")
                    )

                # Start tracking progress
                from campaigns.services.workflow_progress_service import WorkflowProgressService
                progress_service = WorkflowProgressService()
                progress_service.start_tracking(workflow_execution)

            except Exception as e:
                logger.exception(f"Error creating workflow execution record: {str(e)}")
                # Continue without tracking progress

        # Run workflow
        result = self._run_workflow(workflow_path)

        # Update workflow execution record if tracking progress
        if workflow_execution:
            try:
                if result.get('success'):
                    workflow_execution.complete(result)
                else:
                    workflow_execution.fail(result.get('error'))
            except Exception as e:
                logger.exception(f"Error updating workflow execution record: {str(e)}")

        # Add workflow execution ID to result if available
        if workflow_execution:
            result['workflow_execution_id'] = str(workflow_execution.id)

        return result

    def create_and_run_collection_workflow(self, campaign_id, targets, options=None):
        """
        Create and run a PyFlow workflow for collecting accounts.

        Args:
            campaign_id (str): Campaign ID
            targets (list): List of targets (locations or usernames)
            options (dict): Optional workflow options

        Returns:
            dict: Result of workflow execution
        """
        # Create workflow
        result = self.create_account_collection_workflow(campaign_id, targets, options)
        if not result.get('success'):
            return result

        # Prepare parameters
        parameters = {
            'campaign_id': campaign_id,
            'targets': targets,
            'options': options or {}
        }

        # Run workflow with progress tracking
        return self.run_workflow(
            workflow_name=result.get('workflow_name'),
            workflow_path=result.get('workflow_path'),
            campaign_id=campaign_id,
            workflow_type='collection',
            parameters=parameters,
            track_progress=True
        )

    def create_and_run_analysis_workflow(self, campaign_id, analysis_settings=None, options=None):
        """
        Create and run a PyFlow workflow for analyzing accounts.

        Args:
            campaign_id (str): Campaign ID
            analysis_settings (dict): Analysis settings
            options (dict): Optional workflow options

        Returns:
            dict: Result of workflow execution
        """
        # Create workflow
        result = self.create_account_analysis_workflow(campaign_id, analysis_settings, options)
        if not result.get('success'):
            return result

        # Prepare parameters
        parameters = {
            'campaign_id': campaign_id,
            'analysis_settings': analysis_settings or {},
            'options': options or {}
        }

        # Run workflow with progress tracking
        return self.run_workflow(
            workflow_name=result.get('workflow_name'),
            workflow_path=result.get('workflow_path'),
            campaign_id=campaign_id,
            workflow_type='analysis',
            parameters=parameters,
            track_progress=True
        )

    def get_workflow_status(self, workflow_name):
        """
        Get the status of a PyFlow workflow.

        Args:
            workflow_name (str): Name of the workflow file (without .pygraph extension)

        Returns:
            dict: Status of the workflow
        """
        workflow_path = os.path.join(self.workflow_dir, f"{workflow_name}.pygraph")

        if not os.path.exists(workflow_path):
            return {
                'success': False,
                'error': 'Workflow not found'
            }

        try:
            # Check if workflow is running
            cmd = f"ps aux | grep pyflow | grep {workflow_name} | grep -v grep"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.stdout.strip():
                return {
                    'success': True,
                    'status': 'running',
                    'workflow_path': workflow_path
                }
            else:
                # Check if workflow has completed
                log_path = os.path.join(PYFLOW_ENGINE_DIR, 'logs', f"{workflow_name}.log")
                if os.path.exists(log_path):
                    with open(log_path, 'r') as f:
                        log_content = f.read()
                        if 'Workflow completed successfully' in log_content:
                            return {
                                'success': True,
                                'status': 'completed',
                                'workflow_path': workflow_path
                            }
                        elif 'Workflow failed' in log_content:
                            return {
                                'success': True,
                                'status': 'failed',
                                'error': 'Workflow execution failed',
                                'workflow_path': workflow_path
                            }

                return {
                    'success': True,
                    'status': 'unknown',
                    'workflow_path': workflow_path
                }
        except Exception as e:
            logger.exception(f"Error getting workflow status: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'workflow_path': workflow_path
            }

    def list_workflows(self, campaign_id=None):
        """
        List PyFlow workflows.

        Args:
            campaign_id (str): Optional campaign ID to filter by

        Returns:
            list: List of workflows
        """
        workflows = []

        try:
            for filename in os.listdir(self.workflow_dir):
                if filename.endswith('.pygraph'):
                    if campaign_id and f"campaign_{campaign_id}" not in filename:
                        continue

                    workflow_path = os.path.join(self.workflow_dir, filename)
                    workflow_name = filename[:-8]  # Remove .pygraph extension

                    # Get workflow status
                    status = self.get_workflow_status(workflow_name)

                    # Get workflow creation time
                    created_at = timezone.datetime.fromtimestamp(
                        os.path.getctime(workflow_path),
                        tz=timezone.get_current_timezone()
                    )

                    workflows.append({
                        'name': workflow_name,
                        'path': workflow_path,
                        'status': status.get('status', 'unknown'),
                        'created_at': created_at.isoformat()
                    })

            return workflows
        except Exception as e:
            logger.exception(f"Error listing workflows: {str(e)}")
            return []

    def create_template(self, template_data, template_name):
        """
        Create a PyFlow template.

        Args:
            template_data (dict): Template data
            template_name (str): Name of the template file (without .pygraph extension)

        Returns:
            dict: Result of template creation
        """
        template_path = os.path.join(self.template_dir, f"{template_name}.pygraph")

        try:
            with open(template_path, 'w') as f:
                import orjson
                f.write(orjson.dumps(template_data).decode('utf-8'))

            return {
                'success': True,
                'template_name': template_name,
                'template_path': template_path
            }
        except Exception as e:
            logger.exception(f"Error creating template {template_name}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }

    def list_templates(self):
        """
        List PyFlow templates.

        Returns:
            list: List of templates
        """
        templates = []

        try:
            for filename in os.listdir(self.template_dir):
                if filename.endswith('.pygraph'):
                    template_path = os.path.join(self.template_dir, filename)
                    template_name = filename[:-8]  # Remove .pygraph extension

                    # Get template creation time
                    created_at = timezone.datetime.fromtimestamp(
                        os.path.getctime(template_path),
                        tz=timezone.get_current_timezone()
                    )

                    templates.append({
                        'name': template_name,
                        'path': template_path,
                        'created_at': created_at.isoformat()
                    })

            return templates
        except Exception as e:
            logger.exception(f"Error listing templates: {str(e)}")
            return []
