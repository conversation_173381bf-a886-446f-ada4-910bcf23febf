"""
Services for the campaigns app.
"""
# Import legacy services for backward compatibility
from .legacy import AirflowService, LocationService, CampaignAnalysisService

# Conditionally import new services if dependencies are available
try:
    # Core services
    from .campaign_service import CampaignService
    from .analysis_service import AnalysisService
    from .tag_service import TagService

    # Enhanced analysis services
    from .ml_service import MLService
    from .nlp_service import NLPService
    from .enhanced_analysis_service import EnhancedAnalysisService
    from .dashboard_service import DashboardService
    from .advanced_ml_service import AdvancedMLService

    # User experience services
    from .roi_service import ROIService
    from .tag_builder_service import TagBuilderService

    # Integration and automation services
    from .pyflow_service import PyFlowService
    from .pyflow_factory import PyFlowFactory
    from .workflow_service import WorkflowService
    from .external_api_service import ExternalAPIService
    from .notification_service import NotificationService

    # Performance optimization services
    from .optimization_service import OptimizationService

    __all__ = [
        # Legacy services
        'AirflowService', 'LocationService', 'CampaignAnalysisService',

        # Core services
        'CampaignService', 'AnalysisService', 'TagService',

        # Enhanced analysis services
        'MLService', 'NLPService', 'EnhancedAnalysisService', 'DashboardService', 'AdvancedMLService',

        # User experience services
        'ROIService', 'TagBuilderService',

        # Integration and automation services
        'PyFlowService', 'PyFlowFactory', 'WorkflowService', 'ExternalAPIService', 'NotificationService',

        # Performance optimization services
        'OptimizationService'
    ]
except ImportError:
    # If dependencies are not available, only expose legacy services
    __all__ = ['AirflowService', 'LocationService', 'CampaignAnalysisService']
