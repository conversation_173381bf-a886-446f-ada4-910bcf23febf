"""
Tag Builder Service for interactive tag creation and management.
"""
import logging
import re
import random
from django.db import transaction
from django.utils import timezone

logger = logging.getLogger(__name__)


class TagBuilderService:
    """
    Service for interactive tag creation, testing, and management.
    Provides methods for building tags, testing them on sample data, and organizing them.
    """

    def __init__(self, repository=None, ml_service=None, nlp_service=None):
        """
        Initialize the tag builder service.

        Args:
            repository: Optional repository for data access
            ml_service: Optional machine learning service
            nlp_service: Optional natural language processing service
        """
        self.repository = repository
        self.ml_service = ml_service
        self.nlp_service = nlp_service

        # Import services if not provided
        if not self.ml_service:
            try:
                from campaigns.services.ml_service import MLService
                self.ml_service = MLService()
            except ImportError:
                self.ml_service = None

        if not self.nlp_service:
            try:
                from campaigns.services.nlp_service import NLPService
                self.nlp_service = NLPService()
            except ImportError:
                self.nlp_service = None

    def create_tag_category(self, data):
        """
        Create a new tag category.

        Args:
            data (dict): Category data

        Returns:
            dict: Created category
        """
        from campaigns.models import TagCategory

        try:
            with transaction.atomic():
                # Create category
                category = TagCategory.objects.create(
                    name=data['name'],
                    description=data.get('description', ''),
                    parent_id=data.get('parent_id'),
                    color=data.get('color', '#6c757d'),
                    icon=data.get('icon'),
                    priority=data.get('priority', 0)
                )

                return {
                    'id': str(category.id),
                    'name': category.name,
                    'description': category.description,
                    'parent_id': str(category.parent_id) if category.parent_id else None,
                    'color': category.color,
                    'icon': category.icon,
                    'priority': category.priority,
                    'created_at': category.created_at.isoformat()
                }
        except Exception as e:
            logger.exception(f"Error creating tag category: {str(e)}")
            return {
                'error': str(e)
            }

    def get_tag_categories(self, include_tags=False):
        """
        Get all tag categories.

        Args:
            include_tags (bool): Whether to include tags in each category

        Returns:
            list: List of categories
        """
        from campaigns.models import TagCategory, DynamicTag

        try:
            # Get all categories
            categories = TagCategory.objects.all().order_by('-priority', 'name')

            result = []

            for category in categories:
                category_data = {
                    'id': str(category.id),
                    'name': category.name,
                    'description': category.description,
                    'parent_id': str(category.parent_id) if category.parent_id else None,
                    'color': category.color,
                    'icon': category.icon,
                    'priority': category.priority,
                    'created_at': category.created_at.isoformat()
                }

                # Include tags if requested
                if include_tags:
                    tags = DynamicTag.objects.filter(category=category).order_by('name')
                    category_data['tags'] = [
                        {
                            'id': str(tag.id),
                            'name': tag.name,
                            'tag_type': tag.tag_type,
                            'field': tag.field,
                            'is_global': tag.is_global
                        }
                        for tag in tags
                    ]

                result.append(category_data)

            return result
        except Exception as e:
            logger.exception(f"Error getting tag categories: {str(e)}")
            return {
                'error': str(e)
            }

    def create_tag(self, data):
        """
        Create a new dynamic tag.

        Args:
            data (dict): Tag data

        Returns:
            dict: Created tag
        """
        from campaigns.models import DynamicTag, TagCategory

        try:
            with transaction.atomic():
                # Get category if provided
                category = None
                if data.get('category_id'):
                    try:
                        category = TagCategory.objects.get(id=data['category_id'])
                    except TagCategory.DoesNotExist:
                        pass

                # Create tag
                tag = DynamicTag.objects.create(
                    name=data['name'],
                    description=data.get('description', ''),
                    category=category,
                    tag_type=data.get('tag_type', 'keyword'),
                    pattern=data.get('pattern', ''),
                    field=data.get('field', 'bio'),
                    is_global=data.get('is_global', False),
                    confidence_level=data.get('confidence_level', 'medium'),
                    weight=data.get('weight', 1.0)
                )

                # Add related tags if provided
                if data.get('related_tag_ids'):
                    related_tags = DynamicTag.objects.filter(id__in=data['related_tag_ids'])
                    tag.related_tags.add(*related_tags)

                return {
                    'id': str(tag.id),
                    'name': tag.name,
                    'description': tag.description,
                    'category_id': str(tag.category_id) if tag.category_id else None,
                    'tag_type': tag.tag_type,
                    'pattern': tag.pattern,
                    'field': tag.field,
                    'is_global': tag.is_global,
                    'confidence_level': tag.confidence_level,
                    'weight': float(tag.weight),
                    'created_at': tag.created_at.isoformat()
                }
        except Exception as e:
            logger.exception(f"Error creating tag: {str(e)}")
            return {
                'error': str(e)
            }

    def test_tag(self, tag_id, text=None, account_id=None):
        """
        Test a tag on a text sample or account.

        Args:
            tag_id: UUID of the tag
            text: Optional text to test
            account_id: Optional account ID to test

        Returns:
            dict: Test results
        """
        from campaigns.models import DynamicTag

        try:
            # Get tag
            tag = DynamicTag.objects.get(id=tag_id)

            # Test on account if provided
            if account_id:
                from instagram.models import Accounts
                try:
                    account = Accounts.objects.get(id=account_id)

                    # Get field value
                    field_value = getattr(account, tag.field, None)

                    # Skip if field value is None
                    if field_value is None:
                        return {
                            'matched': False,
                            'confidence': 0.0,
                            'error': f'Field {tag.field} not found on account'
                        }

                    # Convert to string if it's not already
                    if isinstance(field_value, list):
                        # For array fields like interests, locations, etc.
                        field_value = ' '.join(str(item) for item in field_value if item)
                    else:
                        field_value = str(field_value)

                    # Use field value as text
                    text = field_value
                except Accounts.DoesNotExist:
                    return {
                        'error': 'Account not found'
                    }

            # Ensure we have text to test
            if not text:
                return {
                    'error': 'No text or account provided for testing'
                }

            # Test based on tag type
            if tag.tag_type == 'ml' and self.ml_service:
                # Use machine learning
                result = self.ml_service.analyze_text(str(tag.id), text)
                return result

            elif tag.tag_type in ['sentiment', 'category'] and self.nlp_service:
                # Use NLP
                if tag.tag_type == 'sentiment':
                    # Sentiment analysis
                    sentiment = self.nlp_service.analyze_sentiment(text)
                    threshold = float(tag.pattern) if tag.pattern.replace('.', '', 1).isdigit() else 0.0

                    matched = False
                    confidence = 0.0

                    if 'positive' in tag.pattern.lower() and sentiment['sentiment'] == 'positive':
                        matched = True
                        confidence = abs(sentiment['score'])
                    elif 'negative' in tag.pattern.lower() and sentiment['sentiment'] == 'negative':
                        matched = True
                        confidence = abs(sentiment['score'])
                    elif 'neutral' in tag.pattern.lower() and sentiment['sentiment'] == 'neutral':
                        matched = True
                        confidence = 1.0 - abs(sentiment['score'])
                    elif sentiment['score'] >= threshold:
                        matched = True
                        confidence = sentiment['score']

                    return {
                        'matched': matched,
                        'confidence': confidence,
                        'details': sentiment
                    }

                elif tag.tag_type == 'category':
                    # Topic/category analysis
                    topics = self.nlp_service.identify_topics(text)

                    # Check if any of the specified categories match
                    categories = [c.strip().lower() for c in tag.pattern.split(',')]
                    matched = False
                    confidence = 0.0

                    if topics['primary_topic'] and topics['primary_topic'].lower() in categories:
                        matched = True
                        topic_score = topics['topics'].get(topics['primary_topic'], 0)
                        confidence = min(topic_score / 10, 1.0)  # Normalize confidence

                    return {
                        'matched': matched,
                        'confidence': confidence,
                        'details': topics
                    }

            else:
                # Basic analysis (keyword or regex)
                if tag.tag_type == 'keyword':
                    # Split pattern into keywords
                    keywords = [k.strip().lower() for k in tag.pattern.split(',')]
                    matched = any(keyword in text.lower() for keyword in keywords)

                    # Calculate confidence based on number of matching keywords
                    matching_keywords = [k for k in keywords if k in text.lower()]
                    confidence = len(matching_keywords) / len(keywords) if keywords else 0.0

                    return {
                        'matched': matched,
                        'confidence': confidence,
                        'details': {
                            'matching_keywords': matching_keywords,
                            'total_keywords': len(keywords)
                        }
                    }

                elif tag.tag_type == 'regex':
                    try:
                        matches = re.findall(tag.pattern, text, re.IGNORECASE)
                        return {
                            'matched': bool(matches),
                            'confidence': 1.0 if matches else 0.0,
                            'details': {
                                'matches': matches,
                                'count': len(matches)
                            }
                        }
                    except Exception as e:
                        logger.error(f"Error in regex analysis: {str(e)}")
                        return {
                            'matched': False,
                            'confidence': 0.0,
                            'error': f'Invalid regex pattern: {str(e)}'
                        }

                else:
                    # Unsupported tag type
                    return {
                        'matched': False,
                        'confidence': 0.0,
                        'error': f'Unsupported tag type: {tag.tag_type}'
                    }

        except DynamicTag.DoesNotExist:
            return {
                'error': 'Tag not found'
            }
        except Exception as e:
            logger.exception(f"Error testing tag: {str(e)}")
            return {
                'error': str(e)
            }

    def get_sample_data(self, field, count=10):
        """
        Get sample data for testing tags.

        Args:
            field (str): Field to get samples for
            count (int): Number of samples to return

        Returns:
            list: Sample data
        """
        from instagram.models import Accounts

        try:
            # Get random accounts
            accounts = Accounts.objects.filter(**{f"{field}__isnull": False}).exclude(**{field: ''})

            # Get total count
            total = accounts.count()

            if total == 0:
                return []

            # Get random samples
            samples = []

            if total <= count:
                # If we have fewer accounts than requested, return all
                for account in accounts:
                    field_value = getattr(account, field)
                    if field_value:
                        if isinstance(field_value, list):
                            field_value = ' '.join(str(item) for item in field_value if item)
                        samples.append({
                            'account_id': str(account.id),
                            'username': account.username,
                            'value': str(field_value)
                        })
            else:
                # Get random accounts
                random_ids = random.sample(range(total), min(count, total))
                for i in random_ids:
                    try:
                        account = accounts[i]
                        field_value = getattr(account, field)
                        if field_value:
                            if isinstance(field_value, list):
                                field_value = ' '.join(str(item) for item in field_value if item)
                            samples.append({
                                'account_id': str(account.id),
                                'username': account.username,
                                'value': str(field_value)
                            })
                    except IndexError:
                        continue

            return samples
        except Exception as e:
            logger.exception(f"Error getting sample data: {str(e)}")
            return {
                'error': str(e)
            }

    def suggest_tags(self, text, field='bio', use_advanced_ml=True):
        """
        Suggest tags based on text analysis.

        Args:
            text (str): Text to analyze
            field (str): Field the text is from
            use_advanced_ml (bool): Whether to use advanced ML for suggestions

        Returns:
            dict: Suggested tags
        """
        try:
            suggestions = []

            # Try to use advanced ML service if available and requested
            advanced_ml_service = None
            if use_advanced_ml:
                try:
                    from campaigns.services import AdvancedMLService
                    advanced_ml_service = AdvancedMLService()
                except ImportError:
                    advanced_ml_service = None

            # Use advanced ML service if available
            if advanced_ml_service:
                # Analyze sentiment with advanced ML
                sentiment_result = advanced_ml_service.analyze_sentiment(text)
                if sentiment_result and 'sentiment' in sentiment_result:
                    sentiment = sentiment_result['sentiment']
                    score = sentiment_result['score']
                    confidence = sentiment_result['confidence']

                    if sentiment == 'positive' and score > 0.6:
                        suggestions.append({
                            'name': 'Very Positive Sentiment',
                            'tag_type': 'sentiment',
                            'pattern': 'positive',
                            'field': field,
                            'confidence': confidence,
                            'source': 'advanced_sentiment_analysis'
                        })
                    elif sentiment == 'positive' and score > 0.3:
                        suggestions.append({
                            'name': 'Positive Sentiment',
                            'tag_type': 'sentiment',
                            'pattern': 'positive',
                            'field': field,
                            'confidence': confidence,
                            'source': 'advanced_sentiment_analysis'
                        })
                    elif sentiment == 'negative' and score < 0.3:
                        suggestions.append({
                            'name': 'Very Negative Sentiment',
                            'tag_type': 'sentiment',
                            'pattern': 'negative',
                            'field': field,
                            'confidence': confidence,
                            'source': 'advanced_sentiment_analysis'
                        })
                    elif sentiment == 'negative' and score < 0.6:
                        suggestions.append({
                            'name': 'Negative Sentiment',
                            'tag_type': 'sentiment',
                            'pattern': 'negative',
                            'field': field,
                            'confidence': confidence,
                            'source': 'advanced_sentiment_analysis'
                        })

                # Predict content performance
                mock_content = {'caption': text, 'hashtags': [], 'image_type': 'photo'}
                performance = advanced_ml_service.predict_content_performance(mock_content)

                if performance and 'performance_level' in performance:
                    level = performance['performance_level']
                    score = performance['performance_score']

                    if level == 'high':
                        suggestions.append({
                            'name': 'High Performing Content',
                            'tag_type': 'ml',
                            'pattern': 'performance_score > 0.8',
                            'field': field,
                            'confidence': score,
                            'source': 'content_performance_prediction'
                        })
                    elif level == 'low':
                        suggestions.append({
                            'name': 'Low Performing Content',
                            'tag_type': 'ml',
                            'pattern': 'performance_score < 0.5',
                            'field': field,
                            'confidence': 1.0 - score,
                            'source': 'content_performance_prediction'
                        })

                # Fall back to regular NLP if needed
                if not suggestions and self.nlp_service:
                    # Use regular NLP service
                    analysis = self.nlp_service.analyze_text(text)

                    # Add suggestions from NLP analysis
                    if 'topics' in analysis and analysis['topics'].get('primary_topic'):
                        topic = analysis['topics']['primary_topic']
                        topic_score = analysis['topics']['topics'].get(topic, 0)
                        if topic_score > 0:
                            suggestions.append({
                                'name': f'Topic: {topic.title()}',
                                'tag_type': 'category',
                                'pattern': topic,
                                'field': field,
                                'confidence': min(topic_score / 10, 1.0),
                                'source': 'topic_analysis'
                            })

            # Use NLP service if available and no advanced ML or not enough suggestions
            elif self.nlp_service:
                # Analyze text
                analysis = self.nlp_service.analyze_text(text)

                # Suggest sentiment tags
                if 'sentiment' in analysis:
                    sentiment = analysis['sentiment']
                    if sentiment['sentiment'] == 'positive' and sentiment['score'] > 0.2:
                        suggestions.append({
                            'name': 'Positive Sentiment',
                            'tag_type': 'sentiment',
                            'pattern': 'positive',
                            'field': field,
                            'confidence': sentiment['score'],
                            'source': 'sentiment_analysis'
                        })
                    elif sentiment['sentiment'] == 'negative' and sentiment['score'] < -0.2:
                        suggestions.append({
                            'name': 'Negative Sentiment',
                            'tag_type': 'sentiment',
                            'pattern': 'negative',
                            'field': field,
                            'confidence': abs(sentiment['score']),
                            'source': 'sentiment_analysis'
                        })

                # Suggest topic tags
                if 'topics' in analysis and analysis['topics'].get('primary_topic'):
                    topic = analysis['topics']['primary_topic']
                    topic_score = analysis['topics']['topics'].get(topic, 0)
                    if topic_score > 0:
                        suggestions.append({
                            'name': f'Topic: {topic.title()}',
                            'tag_type': 'category',
                            'pattern': topic,
                            'field': field,
                            'confidence': min(topic_score / 10, 1.0),
                            'source': 'topic_analysis'
                        })

                # Suggest entity tags
                if 'entities' in analysis and analysis['entities'].get('entities'):
                    for entity_type, entities in analysis['entities']['entities'].items():
                        if entities and len(entities) > 0:
                            entity_str = ', '.join(entities[:5])  # Limit to 5 entities
                            suggestions.append({
                                'name': f'Has {entity_type.title()}',
                                'tag_type': 'regex',
                                'pattern': '|'.join(re.escape(e) for e in entities[:5]),
                                'field': field,
                                'confidence': 0.8,
                                'source': 'entity_extraction'
                            })

            # Suggest keyword tags based on frequent words
            words = re.findall(r'\b[a-zA-Z]{4,}\b', text.lower())
            word_counts = {}
            for word in words:
                if word not in ['this', 'that', 'with', 'from', 'have', 'about', 'what', 'when', 'where', 'which']:
                    word_counts[word] = word_counts.get(word, 0) + 1

            # Get top words
            top_words = sorted(word_counts.items(), key=lambda x: x[1], reverse=True)[:5]
            if top_words:
                word_str = ', '.join(word for word, count in top_words)
                suggestions.append({
                    'name': 'Keywords',
                    'tag_type': 'keyword',
                    'pattern': word_str,
                    'field': field,
                    'confidence': 0.7,
                    'source': 'keyword_extraction'
                })

            return {
                'suggestions': suggestions,
                'count': len(suggestions)
            }
        except Exception as e:
            logger.exception(f"Error suggesting tags: {str(e)}")
            return {
                'error': str(e)
            }

    def get_tag_effectiveness(self, tag_id):
        """
        Get effectiveness metrics for a tag.

        Args:
            tag_id: UUID of the tag

        Returns:
            dict: Effectiveness metrics
        """
        from campaigns.models import DynamicTag, TagMetrics, TagAnalysisResult

        try:
            # Get tag
            tag = DynamicTag.objects.get(id=tag_id)

            # Get or create metrics
            metrics, created = TagMetrics.objects.get_or_create(tag=tag)

            # Get analysis results
            results = TagAnalysisResult.objects.filter(tag=tag)

            # Calculate metrics
            total_uses = results.count()
            total_matches = results.filter(matched=True).count()

            if total_uses > 0:
                match_rate = total_matches / total_uses
            else:
                match_rate = 0

            # Update metrics
            metrics.usage_count = total_uses
            metrics.match_count = total_matches
            metrics.last_evaluated = timezone.now()
            metrics.save()

            return {
                'tag_id': str(tag.id),
                'tag_name': tag.name,
                'usage_count': metrics.usage_count,
                'match_count': metrics.match_count,
                'match_rate': match_rate,
                'precision': float(metrics.precision),
                'recall': float(metrics.recall),
                'f1_score': float(metrics.f1_score),
                'last_evaluated': metrics.last_evaluated.isoformat() if metrics.last_evaluated else None
            }
        except DynamicTag.DoesNotExist:
            return {
                'error': 'Tag not found'
            }
        except Exception as e:
            logger.exception(f"Error getting tag effectiveness: {str(e)}")
            return {
                'error': str(e)
            }
