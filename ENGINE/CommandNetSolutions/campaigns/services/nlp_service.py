"""
Natural Language Processing service for advanced text analysis.
"""
import logging
import re
import os
import json
from collections import Counter
from django.conf import settings

logger = logging.getLogger(__name__)

# Define NLP resources directory
NLP_RESOURCES_DIR = os.path.join(settings.BASE_DIR, 'campaigns', 'nlp_resources')
os.makedirs(NLP_RESOURCES_DIR, exist_ok=True)

# Load sentiment lexicon
SENTIMENT_LEXICON = {}
lexicon_path = os.path.join(NLP_RESOURCES_DIR, 'sentiment_lexicon.json')
if os.path.exists(lexicon_path):
    try:
        with open(lexicon_path, 'r') as f:
            SENTIMENT_LEXICON = json.load(f)
    except Exception as e:
        logger.error(f"Error loading sentiment lexicon: {str(e)}")
else:
    # Create a basic sentiment lexicon if none exists
    SENTIMENT_LEXICON = {
        'positive': [
            'good', 'great', 'excellent', 'amazing', 'love', 'best', 'happy',
            'awesome', 'fantastic', 'wonderful', 'perfect', 'beautiful', 'enjoy',
            'like', 'nice', 'fun', 'exciting', 'positive', 'recommend', 'favorite'
        ],
        'negative': [
            'bad', 'worst', 'hate', 'terrible', 'awful', 'poor', 'horrible',
            'disappointing', 'dislike', 'negative', 'boring', 'ugly', 'wrong',
            'annoying', 'stupid', 'waste', 'fail', 'sucks', 'avoid', 'problem'
        ]
    }
    # Save the basic lexicon
    try:
        with open(lexicon_path, 'w') as f:
            json.dump(SENTIMENT_LEXICON, f)
    except Exception as e:
        logger.error(f"Error saving sentiment lexicon: {str(e)}")


class NLPService:
    """
    Service for natural language processing and text analysis.
    Provides methods for sentiment analysis, entity recognition, and topic modeling.
    """
    
    def __init__(self):
        """Initialize the NLP service."""
        self.sentiment_lexicon = SENTIMENT_LEXICON
        self.entity_patterns = self._load_entity_patterns()
        self.topic_keywords = self._load_topic_keywords()
    
    def _load_entity_patterns(self):
        """Load entity recognition patterns."""
        patterns_path = os.path.join(NLP_RESOURCES_DIR, 'entity_patterns.json')
        if os.path.exists(patterns_path):
            try:
                with open(patterns_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading entity patterns: {str(e)}")
        
        # Default patterns
        default_patterns = {
            'email': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
            'url': r'https?://(?:[-\w.]|(?:%[\da-fA-F]{2}))+',
            'phone': r'\b(?:\+\d{1,3}[- ]?)?\(?\d{3}\)?[- ]?\d{3}[- ]?\d{4}\b',
            'hashtag': r'#[a-zA-Z0-9_]+',
            'mention': r'@[a-zA-Z0-9_]+',
            'location': r'\b(?:in|at|from)\s+([A-Z][a-z]+(?:\s+[A-Z][a-z]+)*)'
        }
        
        # Save default patterns
        try:
            with open(patterns_path, 'w') as f:
                json.dump(default_patterns, f)
        except Exception as e:
            logger.error(f"Error saving entity patterns: {str(e)}")
        
        return default_patterns
    
    def _load_topic_keywords(self):
        """Load topic modeling keywords."""
        topics_path = os.path.join(NLP_RESOURCES_DIR, 'topic_keywords.json')
        if os.path.exists(topics_path):
            try:
                with open(topics_path, 'r') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Error loading topic keywords: {str(e)}")
        
        # Default topics
        default_topics = {
            'fashion': ['fashion', 'style', 'outfit', 'clothes', 'dress', 'wear', 'model', 'designer'],
            'food': ['food', 'recipe', 'cook', 'meal', 'restaurant', 'eat', 'chef', 'delicious'],
            'travel': ['travel', 'trip', 'vacation', 'adventure', 'explore', 'destination', 'journey', 'tourism'],
            'fitness': ['fitness', 'workout', 'gym', 'exercise', 'training', 'health', 'muscle', 'weight'],
            'technology': ['tech', 'technology', 'digital', 'software', 'app', 'computer', 'device', 'innovation'],
            'beauty': ['beauty', 'makeup', 'skincare', 'cosmetics', 'hair', 'products', 'routine', 'natural'],
            'business': ['business', 'entrepreneur', 'startup', 'company', 'marketing', 'success', 'career', 'professional'],
            'art': ['art', 'artist', 'creative', 'design', 'photography', 'painting', 'drawing', 'illustration'],
            'music': ['music', 'song', 'artist', 'band', 'album', 'concert', 'playlist', 'singer'],
            'education': ['education', 'learn', 'student', 'school', 'teacher', 'course', 'knowledge', 'study']
        }
        
        # Save default topics
        try:
            with open(topics_path, 'w') as f:
                json.dump(default_topics, f)
        except Exception as e:
            logger.error(f"Error saving topic keywords: {str(e)}")
        
        return default_topics
    
    def analyze_sentiment(self, text):
        """
        Analyze sentiment of text.
        
        Args:
            text (str): Text to analyze
            
        Returns:
            dict: Sentiment analysis results
        """
        if not text:
            return {
                'sentiment': 'neutral',
                'score': 0.0,
                'positive_words': [],
                'negative_words': []
            }
        
        # Tokenize text
        words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        
        # Count positive and negative words
        positive_words = [word for word in words if word in self.sentiment_lexicon['positive']]
        negative_words = [word for word in words if word in self.sentiment_lexicon['negative']]
        
        positive_count = len(positive_words)
        negative_count = len(negative_words)
        
        # Calculate sentiment score
        total_words = len(words)
        if total_words > 0:
            score = (positive_count - negative_count) / total_words
        else:
            score = 0.0
        
        # Determine sentiment
        if score > 0.05:
            sentiment = 'positive'
        elif score < -0.05:
            sentiment = 'negative'
        else:
            sentiment = 'neutral'
        
        return {
            'sentiment': sentiment,
            'score': score,
            'positive_words': positive_words,
            'negative_words': negative_words,
            'positive_count': positive_count,
            'negative_count': negative_count,
            'total_words': total_words
        }
    
    def extract_entities(self, text):
        """
        Extract entities from text.
        
        Args:
            text (str): Text to analyze
            
        Returns:
            dict: Extracted entities
        """
        if not text:
            return {
                'entities': {}
            }
        
        entities = {}
        
        # Extract entities using patterns
        for entity_type, pattern in self.entity_patterns.items():
            matches = re.findall(pattern, text)
            if matches:
                entities[entity_type] = matches
        
        return {
            'entities': entities,
            'count': sum(len(matches) for matches in entities.values())
        }
    
    def identify_topics(self, text):
        """
        Identify topics in text.
        
        Args:
            text (str): Text to analyze
            
        Returns:
            dict: Identified topics
        """
        if not text:
            return {
                'topics': {},
                'primary_topic': None
            }
        
        # Tokenize text
        words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        
        # Count topic keywords
        topic_scores = {}
        for topic, keywords in self.topic_keywords.items():
            matches = [word for word in words if word in keywords]
            if matches:
                topic_scores[topic] = len(matches)
        
        # Sort topics by score
        sorted_topics = sorted(topic_scores.items(), key=lambda x: x[1], reverse=True)
        
        # Get primary topic
        primary_topic = sorted_topics[0][0] if sorted_topics else None
        
        return {
            'topics': dict(sorted_topics),
            'primary_topic': primary_topic,
            'topic_count': len(sorted_topics)
        }
    
    def analyze_text(self, text):
        """
        Perform comprehensive text analysis.
        
        Args:
            text (str): Text to analyze
            
        Returns:
            dict: Analysis results
        """
        if not text:
            return {
                'error': 'No text provided'
            }
        
        # Perform all analyses
        sentiment_analysis = self.analyze_sentiment(text)
        entity_analysis = self.extract_entities(text)
        topic_analysis = self.identify_topics(text)
        
        # Calculate text statistics
        word_count = len(re.findall(r'\b[a-zA-Z]+\b', text))
        char_count = len(text)
        sentence_count = len(re.split(r'[.!?]+', text))
        
        # Get most frequent words
        words = re.findall(r'\b[a-zA-Z]+\b', text.lower())
        word_freq = Counter(words).most_common(10)
        
        return {
            'sentiment': sentiment_analysis,
            'entities': entity_analysis,
            'topics': topic_analysis,
            'statistics': {
                'word_count': word_count,
                'char_count': char_count,
                'sentence_count': sentence_count,
                'frequent_words': dict(word_freq)
            }
        }
    
    def analyze_account(self, account):
        """
        Analyze an Instagram account using NLP.
        
        Args:
            account (Accounts): Account to analyze
            
        Returns:
            dict: Analysis results
        """
        results = {}
        
        # Analyze biography
        if hasattr(account, 'bio') and account.bio:
            results['bio_analysis'] = self.analyze_text(account.bio)
        
        # Analyze full name
        if hasattr(account, 'full_name') and account.full_name:
            results['name_analysis'] = self.analyze_text(account.full_name)
        
        # Analyze interests (if available)
        if hasattr(account, 'interests') and account.interests:
            if isinstance(account.interests, list):
                interests_text = ' '.join(account.interests)
            else:
                interests_text = str(account.interests)
            
            results['interests_analysis'] = self.analyze_text(interests_text)
        
        return results
    
    def match_pattern(self, text, pattern):
        """
        Match a pattern in text.
        
        Args:
            text (str): Text to analyze
            pattern (str): Regular expression pattern
            
        Returns:
            dict: Match results
        """
        if not text or not pattern:
            return {
                'matched': False,
                'matches': []
            }
        
        try:
            matches = re.findall(pattern, text)
            return {
                'matched': bool(matches),
                'matches': matches,
                'count': len(matches)
            }
        except Exception as e:
            logger.error(f"Error matching pattern: {str(e)}")
            return {
                'matched': False,
                'matches': [],
                'error': str(e)
            }
    
    def update_sentiment_lexicon(self, positive_words=None, negative_words=None):
        """
        Update the sentiment lexicon.
        
        Args:
            positive_words (list): List of positive words to add
            negative_words (list): List of negative words to add
            
        Returns:
            bool: Success status
        """
        try:
            if positive_words:
                self.sentiment_lexicon['positive'].extend([word.lower() for word in positive_words])
                self.sentiment_lexicon['positive'] = list(set(self.sentiment_lexicon['positive']))
            
            if negative_words:
                self.sentiment_lexicon['negative'].extend([word.lower() for word in negative_words])
                self.sentiment_lexicon['negative'] = list(set(self.sentiment_lexicon['negative']))
            
            # Save updated lexicon
            lexicon_path = os.path.join(NLP_RESOURCES_DIR, 'sentiment_lexicon.json')
            with open(lexicon_path, 'w') as f:
                json.dump(self.sentiment_lexicon, f)
            
            return True
        except Exception as e:
            logger.error(f"Error updating sentiment lexicon: {str(e)}")
            return False
    
    def add_topic(self, topic_name, keywords):
        """
        Add a new topic with keywords.
        
        Args:
            topic_name (str): Name of the topic
            keywords (list): List of keywords for the topic
            
        Returns:
            bool: Success status
        """
        try:
            self.topic_keywords[topic_name] = keywords
            
            # Save updated topics
            topics_path = os.path.join(NLP_RESOURCES_DIR, 'topic_keywords.json')
            with open(topics_path, 'w') as f:
                json.dump(self.topic_keywords, f)
            
            return True
        except Exception as e:
            logger.error(f"Error adding topic: {str(e)}")
            return False
