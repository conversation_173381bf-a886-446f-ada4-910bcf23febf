"""
PyFlow service factory.

This module provides a factory for creating PyFlow service instances.
It decides whether to use the real PyFlow service or the dummy implementation
based on a configuration setting.
"""
import os
import logging
from django.conf import settings

logger = logging.getLogger(__name__)

class PyFlowFactory:
    """
    Factory for creating PyFlow service instances.
    
    This class decides whether to use the real PyFlow service or the dummy implementation
    based on a configuration setting.
    """
    
    @staticmethod
    def create_pyflow_service():
        """
        Create a PyFlow service instance.
        
        Returns:
            PyFlowService or DummyPyFlowService: PyFlow service instance
        """
        # Check if we should use the dummy implementation
        use_dummy = getattr(settings, 'USE_DUMMY_PYFLOW', False)
        
        if use_dummy:
            logger.info("Using dummy PyFlow service")
            from campaigns.services.dummy_pyflow_service import DummyPyFlowService
            return DummyPyFlowService()
        else:
            logger.info("Using real PyFlow service")
            from campaigns.services.pyflow_service import PyFlowService
            return PyFlowService()
