"""
External API Integration Service for connecting with external services.
"""
import os
import json
import logging
import requests
import hashlib
import hmac
import time
from datetime import datetime
from django.conf import settings
from django.utils import timezone
from django.core.cache import cache

logger = logging.getLogger(__name__)

# Define API configuration directory
API_CONFIG_DIR = os.path.join(settings.BASE_DIR, 'campaigns', 'api_configs')
os.makedirs(API_CONFIG_DIR, exist_ok=True)


class ExternalAPIService:
    """
    Service for integrating with external APIs.
    Provides methods for configuring, authenticating, and calling external APIs.
    """
    
    def __init__(self):
        """Initialize the external API service."""
        self.api_configs = {}
        self._load_api_configs()
    
    def _load_api_configs(self):
        """Load API configurations from files."""
        try:
            for filename in os.listdir(API_CONFIG_DIR):
                if filename.endswith('.json'):
                    api_name = filename[:-5]  # Remove .json extension
                    config_path = os.path.join(API_CONFIG_DIR, filename)
                    
                    with open(config_path, 'r') as f:
                        self.api_configs[api_name] = json.load(f)
                        logger.info(f"Loaded API configuration for {api_name}")
        except Exception as e:
            logger.exception(f"Error loading API configurations: {str(e)}")
    
    def _save_api_config(self, api_name, config):
        """
        Save API configuration to file.
        
        Args:
            api_name (str): Name of the API
            config (dict): API configuration
            
        Returns:
            bool: Success status
        """
        try:
            config_path = os.path.join(API_CONFIG_DIR, f"{api_name}.json")
            
            with open(config_path, 'w') as f:
                json.dump(config, f, indent=2)
            
            # Update in-memory config
            self.api_configs[api_name] = config
            
            return True
        except Exception as e:
            logger.exception(f"Error saving API configuration for {api_name}: {str(e)}")
            return False
    
    def _get_auth_headers(self, api_name):
        """
        Get authentication headers for an API.
        
        Args:
            api_name (str): Name of the API
            
        Returns:
            dict: Authentication headers
        """
        if api_name not in self.api_configs:
            logger.error(f"API configuration not found for {api_name}")
            return {}
        
        config = self.api_configs[api_name]
        auth_type = config.get('auth_type', 'none')
        
        if auth_type == 'none':
            return {}
        
        elif auth_type == 'api_key':
            key_name = config.get('api_key_name', 'api_key')
            key_value = config.get('api_key', '')
            key_location = config.get('api_key_location', 'header')
            
            if key_location == 'header':
                return {key_name: key_value}
            else:
                # For query params, return empty headers
                return {}
        
        elif auth_type == 'bearer':
            token = config.get('token', '')
            return {'Authorization': f"Bearer {token}"}
        
        elif auth_type == 'basic':
            username = config.get('username', '')
            password = config.get('password', '')
            import base64
            auth_string = base64.b64encode(f"{username}:{password}".encode()).decode()
            return {'Authorization': f"Basic {auth_string}"}
        
        elif auth_type == 'oauth2':
            # Check if we have a valid token
            token = config.get('access_token', '')
            expires_at = config.get('expires_at', 0)
            
            if not token or expires_at < time.time():
                # Token is expired or missing, refresh it
                token_result = self._refresh_oauth2_token(api_name)
                if token_result.get('success'):
                    token = token_result.get('access_token', '')
                else:
                    logger.error(f"Failed to refresh OAuth2 token for {api_name}")
                    return {}
            
            return {'Authorization': f"Bearer {token}"}
        
        elif auth_type == 'hmac':
            key = config.get('hmac_key', '')
            secret = config.get('hmac_secret', '')
            timestamp = str(int(time.time()))
            
            # Create signature
            message = f"{timestamp}{key}"
            signature = hmac.new(
                secret.encode(),
                message.encode(),
                hashlib.sha256
            ).hexdigest()
            
            return {
                'X-API-Key': key,
                'X-Timestamp': timestamp,
                'X-Signature': signature
            }
        
        else:
            logger.error(f"Unsupported auth type: {auth_type}")
            return {}
    
    def _refresh_oauth2_token(self, api_name):
        """
        Refresh OAuth2 token.
        
        Args:
            api_name (str): Name of the API
            
        Returns:
            dict: Token refresh result
        """
        if api_name not in self.api_configs:
            return {'success': False, 'error': 'API configuration not found'}
        
        config = self.api_configs[api_name]
        
        if config.get('auth_type') != 'oauth2':
            return {'success': False, 'error': 'Not an OAuth2 API'}
        
        token_url = config.get('token_url', '')
        client_id = config.get('client_id', '')
        client_secret = config.get('client_secret', '')
        refresh_token = config.get('refresh_token', '')
        
        if not token_url or not client_id or not client_secret:
            return {'success': False, 'error': 'Missing OAuth2 configuration'}
        
        try:
            # Prepare token request
            if refresh_token:
                # Use refresh token
                data = {
                    'grant_type': 'refresh_token',
                    'refresh_token': refresh_token,
                    'client_id': client_id,
                    'client_secret': client_secret
                }
            else:
                # Use client credentials
                data = {
                    'grant_type': 'client_credentials',
                    'client_id': client_id,
                    'client_secret': client_secret
                }
            
            # Make token request
            response = requests.post(token_url, data=data, timeout=10)
            
            if response.status_code in (200, 201):
                token_data = response.json()
                
                # Update configuration
                config['access_token'] = token_data.get('access_token', '')
                config['refresh_token'] = token_data.get('refresh_token', refresh_token)
                config['expires_at'] = time.time() + token_data.get('expires_in', 3600)
                
                # Save updated configuration
                self._save_api_config(api_name, config)
                
                return {
                    'success': True,
                    'access_token': config['access_token'],
                    'expires_at': config['expires_at']
                }
            else:
                logger.error(f"Failed to refresh OAuth2 token: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f"Failed to refresh token: {response.status_code}"
                }
        except Exception as e:
            logger.exception(f"Error refreshing OAuth2 token: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _build_url(self, api_name, endpoint, params=None):
        """
        Build URL for API request.
        
        Args:
            api_name (str): Name of the API
            endpoint (str): API endpoint
            params (dict): Query parameters
            
        Returns:
            str: Full URL
        """
        if api_name not in self.api_configs:
            logger.error(f"API configuration not found for {api_name}")
            return None
        
        config = self.api_configs[api_name]
        base_url = config.get('base_url', '')
        
        # Remove leading slash from endpoint if present
        if endpoint.startswith('/'):
            endpoint = endpoint[1:]
        
        # Add trailing slash to base_url if not present
        if base_url and not base_url.endswith('/'):
            base_url += '/'
        
        # Build URL
        url = f"{base_url}{endpoint}"
        
        # Add API key to query params if needed
        if config.get('auth_type') == 'api_key' and config.get('api_key_location') == 'query':
            if params is None:
                params = {}
            
            key_name = config.get('api_key_name', 'api_key')
            key_value = config.get('api_key', '')
            params[key_name] = key_value
        
        # Add query parameters
        if params:
            import urllib.parse
            query_string = urllib.parse.urlencode(params)
            url = f"{url}?{query_string}"
        
        return url
    
    def call_api(self, api_name, endpoint, method='GET', params=None, data=None, headers=None, cache_key=None, cache_ttl=300):
        """
        Call an external API.
        
        Args:
            api_name (str): Name of the API
            endpoint (str): API endpoint
            method (str): HTTP method (GET, POST, PUT, DELETE)
            params (dict): Query parameters
            data (dict): Request body
            headers (dict): Additional headers
            cache_key (str): Optional cache key for GET requests
            cache_ttl (int): Cache TTL in seconds
            
        Returns:
            dict: API response
        """
        # Check if result is in cache
        if method == 'GET' and cache_key:
            cached_result = cache.get(f"api_{api_name}_{cache_key}")
            if cached_result:
                return {
                    'success': True,
                    'data': cached_result,
                    'cached': True
                }
        
        # Build URL
        url = self._build_url(api_name, endpoint, params)
        if not url:
            return {
                'success': False,
                'error': f"Failed to build URL for {api_name}"
            }
        
        # Get authentication headers
        auth_headers = self._get_auth_headers(api_name)
        
        # Merge headers
        request_headers = auth_headers.copy()
        if headers:
            request_headers.update(headers)
        
        # Add default content type if not provided
        if method in ('POST', 'PUT', 'PATCH') and 'Content-Type' not in request_headers:
            request_headers['Content-Type'] = 'application/json'
        
        try:
            # Make request
            if method == 'GET':
                response = requests.get(url, headers=request_headers, timeout=30)
            elif method == 'POST':
                if isinstance(data, dict) and request_headers.get('Content-Type') == 'application/json':
                    response = requests.post(url, json=data, headers=request_headers, timeout=30)
                else:
                    response = requests.post(url, data=data, headers=request_headers, timeout=30)
            elif method == 'PUT':
                if isinstance(data, dict) and request_headers.get('Content-Type') == 'application/json':
                    response = requests.put(url, json=data, headers=request_headers, timeout=30)
                else:
                    response = requests.put(url, data=data, headers=request_headers, timeout=30)
            elif method == 'DELETE':
                response = requests.delete(url, headers=request_headers, timeout=30)
            else:
                return {
                    'success': False,
                    'error': f"Unsupported HTTP method: {method}"
                }
            
            # Parse response
            if response.status_code in (200, 201, 202, 204):
                try:
                    if response.status_code == 204 or not response.text:
                        result = {}
                    else:
                        result = response.json()
                    
                    # Cache result if needed
                    if method == 'GET' and cache_key:
                        cache.set(f"api_{api_name}_{cache_key}", result, cache_ttl)
                    
                    return {
                        'success': True,
                        'data': result,
                        'status_code': response.status_code
                    }
                except ValueError:
                    # Not JSON
                    return {
                        'success': True,
                        'data': response.text,
                        'status_code': response.status_code
                    }
            else:
                logger.error(f"API call failed: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f"API call failed: {response.status_code}",
                    'status_code': response.status_code,
                    'response': response.text
                }
        except Exception as e:
            logger.exception(f"Error calling API {api_name}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def configure_api(self, api_name, config):
        """
        Configure an external API.
        
        Args:
            api_name (str): Name of the API
            config (dict): API configuration
            
        Returns:
            dict: Configuration result
        """
        try:
            # Validate configuration
            required_fields = ['base_url', 'auth_type']
            for field in required_fields:
                if field not in config:
                    return {
                        'success': False,
                        'error': f"Missing required field: {field}"
                    }
            
            # Add timestamp
            config['updated_at'] = datetime.now().isoformat()
            
            # Save configuration
            if self._save_api_config(api_name, config):
                return {
                    'success': True,
                    'message': f"API {api_name} configured successfully"
                }
            else:
                return {
                    'success': False,
                    'error': f"Failed to save configuration for {api_name}"
                }
        except Exception as e:
            logger.exception(f"Error configuring API {api_name}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_api_config(self, api_name):
        """
        Get API configuration.
        
        Args:
            api_name (str): Name of the API
            
        Returns:
            dict: API configuration
        """
        if api_name not in self.api_configs:
            return {
                'success': False,
                'error': f"API configuration not found for {api_name}"
            }
        
        # Return a copy to prevent modification
        config = self.api_configs[api_name].copy()
        
        # Remove sensitive information
        if 'password' in config:
            config['password'] = '********'
        if 'api_key' in config:
            config['api_key'] = '********'
        if 'client_secret' in config:
            config['client_secret'] = '********'
        if 'hmac_secret' in config:
            config['hmac_secret'] = '********'
        
        return {
            'success': True,
            'config': config
        }
    
    def list_apis(self):
        """
        List all configured APIs.
        
        Returns:
            list: List of API configurations
        """
        result = []
        
        for api_name, config in self.api_configs.items():
            # Create a sanitized copy
            api_info = {
                'name': api_name,
                'base_url': config.get('base_url', ''),
                'auth_type': config.get('auth_type', 'none'),
                'description': config.get('description', ''),
                'updated_at': config.get('updated_at', '')
            }
            
            result.append(api_info)
        
        return result
    
    def delete_api_config(self, api_name):
        """
        Delete API configuration.
        
        Args:
            api_name (str): Name of the API
            
        Returns:
            dict: Deletion result
        """
        if api_name not in self.api_configs:
            return {
                'success': False,
                'error': f"API configuration not found for {api_name}"
            }
        
        try:
            # Remove configuration file
            config_path = os.path.join(API_CONFIG_DIR, f"{api_name}.json")
            if os.path.exists(config_path):
                os.remove(config_path)
            
            # Remove from in-memory configs
            del self.api_configs[api_name]
            
            return {
                'success': True,
                'message': f"API configuration for {api_name} deleted successfully"
            }
        except Exception as e:
            logger.exception(f"Error deleting API configuration for {api_name}: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def test_api_connection(self, api_name, endpoint='/'):
        """
        Test connection to an API.
        
        Args:
            api_name (str): Name of the API
            endpoint (str): API endpoint to test
            
        Returns:
            dict: Test result
        """
        if api_name not in self.api_configs:
            return {
                'success': False,
                'error': f"API configuration not found for {api_name}"
            }
        
        # Make a test request
        result = self.call_api(api_name, endpoint, method='GET')
        
        # Add latency information
        if 'latency' not in result:
            result['latency'] = 0
        
        return result
