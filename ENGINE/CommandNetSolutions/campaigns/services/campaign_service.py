"""
Service for campaign operations.
"""
from campaigns.interfaces.campaign_service import CampaignServiceInterface
from campaigns.domains.campaign import CampaignDomain


class CampaignService(CampaignServiceInterface):
    """
    Service for campaign operations.
    Coordinates between domain, repository, and external services.
    """
    
    def __init__(self, repository, airflow_service=None):
        """
        Initialize service.
        
        Args:
            repository (CampaignRepository): Campaign repository
            airflow_service (AirflowService): Optional Airflow service
        """
        self.repository = repository
        self.airflow_service = airflow_service
        self.domain = CampaignDomain(repository)
    
    def create_campaign(self, campaign_data, user):
        """
        Create a new campaign.
        
        Args:
            campaign_data (dict): Campaign data
            user: User creating the campaign
            
        Returns:
            Campaign: Created campaign
        """
        # Add user as creator
        campaign_data['creator'] = user
        
        # Create campaign
        return self.domain.create_campaign(campaign_data)
    
    def update_campaign(self, campaign_id, campaign_data):
        """
        Update an existing campaign.
        
        Args:
            campaign_id (uuid): Campaign ID
            campaign_data (dict): Updated campaign data
            
        Returns:
            Campaign: Updated campaign
        """
        return self.domain.update_campaign(campaign_id, campaign_data)
    
    def launch_campaign(self, campaign_id):
        """
        Launch a campaign.
        
        Args:
            campaign_id (uuid): Campaign ID
            
        Returns:
            Campaign: Updated campaign
        """
        return self.domain.launch_campaign(campaign_id, self.airflow_service)
    
    def get_campaign(self, campaign_id):
        """
        Get campaign by ID.
        
        Args:
            campaign_id (uuid): Campaign ID
            
        Returns:
            Campaign: Campaign instance
        """
        return self.repository.get_by_id(campaign_id)
    
    def list_campaigns(self, filters=None, order_by=None):
        """
        List campaigns with optional filtering and ordering.
        
        Args:
            filters (dict): Optional filters
            order_by (str): Optional ordering field
            
        Returns:
            QuerySet: Campaign queryset
        """
        return self.repository.get_all(filters, order_by, with_stats=True)
    
    def add_location_target(self, campaign_id, location_data):
        """
        Add location target to campaign.
        
        Args:
            campaign_id (uuid): Campaign ID
            location_data (dict): Location data
            
        Returns:
            LocationTarget: Created location target
        """
        return self.domain.add_location_target(campaign_id, location_data)
    
    def add_username_target(self, campaign_id, username_data):
        """
        Add username target to campaign.
        
        Args:
            campaign_id (uuid): Campaign ID
            username_data (dict): Username data
            
        Returns:
            UsernameTarget: Created username target
        """
        return self.domain.add_username_target(campaign_id, username_data)
