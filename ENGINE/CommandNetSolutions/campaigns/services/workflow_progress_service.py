"""
Service for tracking workflow progress.
"""
import os
import re
import json
import logging
import threading
import time
from datetime import datetime
from django.utils import timezone
from django.db import transaction

logger = logging.getLogger(__name__)

class WorkflowProgressService:
    """
    Service for tracking workflow progress.
    """
    
    def __init__(self):
        """
        Initialize service.
        """
        self.tracking_threads = {}
    
    def start_tracking(self, workflow_execution):
        """
        Start tracking workflow progress.
        
        Args:
            workflow_execution (WorkflowExecution): Workflow execution to track
        """
        from campaigns.models.workflow import WorkflowExecution, WorkflowProgressUpdate
        
        # Update workflow status
        workflow_execution.status = 'running'
        workflow_execution.save(update_fields=['status'])
        
        # Create initial progress update
        WorkflowProgressUpdate.objects.create(
            workflow_execution=workflow_execution,
            processed_items=0,
            successful_items=0,
            failed_items=0,
            progress=0.0,
            message="Workflow started"
        )
        
        # Start tracking thread
        thread = threading.Thread(
            target=self._track_progress,
            args=(workflow_execution.id,),
            daemon=True
        )
        thread.start()
        
        # Store thread
        self.tracking_threads[str(workflow_execution.id)] = thread
        
        return workflow_execution
    
    def _track_progress(self, workflow_execution_id):
        """
        Track workflow progress.
        
        Args:
            workflow_execution_id (uuid): Workflow execution ID
        """
        from campaigns.models.workflow import WorkflowExecution, WorkflowProgressUpdate
        
        try:
            # Get workflow execution
            workflow_execution = WorkflowExecution.objects.get(id=workflow_execution_id)
            
            # Get log file path
            log_file = workflow_execution.log_file
            if not log_file:
                # Try to find log file
                workflow_name = workflow_execution.workflow_name
                log_file = os.path.join('/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/logs', f"{workflow_name}.log")
                
                # Update log file path
                workflow_execution.log_file = log_file
                workflow_execution.save(update_fields=['log_file'])
            
            # Track progress until workflow completes or fails
            last_position = 0
            last_update_time = time.time()
            update_interval = 5  # Update every 5 seconds
            
            while workflow_execution.status == 'running':
                # Check if log file exists
                if not os.path.exists(log_file):
                    time.sleep(1)
                    continue
                
                # Read log file
                try:
                    with open(log_file, 'r') as f:
                        f.seek(last_position)
                        log_content = f.read()
                        last_position = f.tell()
                except Exception as e:
                    logger.warning(f"Error reading log file: {str(e)}")
                    time.sleep(1)
                    continue
                
                # Parse progress from log content
                progress_data = self._parse_progress(log_content)
                
                # Check for completion or failure
                if 'Workflow completed successfully' in log_content:
                    # Extract results
                    results = self._extract_results(log_content)
                    
                    # Mark workflow as completed
                    with transaction.atomic():
                        workflow_execution.refresh_from_db()
                        workflow_execution.complete(results)
                        
                        # Create final progress update
                        WorkflowProgressUpdate.objects.create(
                            workflow_execution=workflow_execution,
                            processed_items=workflow_execution.total_items,
                            successful_items=workflow_execution.successful_items,
                            failed_items=workflow_execution.failed_items,
                            progress=100.0,
                            message="Workflow completed successfully",
                            details=results
                        )
                    
                    break
                elif 'Workflow failed' in log_content:
                    # Extract error message
                    error_match = re.search(r'Error: (.*?)$', log_content, re.MULTILINE)
                    error_message = error_match.group(1) if error_match else "Unknown error"
                    
                    # Mark workflow as failed
                    with transaction.atomic():
                        workflow_execution.refresh_from_db()
                        workflow_execution.fail(error_message)
                        
                        # Create final progress update
                        WorkflowProgressUpdate.objects.create(
                            workflow_execution=workflow_execution,
                            processed_items=workflow_execution.processed_items,
                            successful_items=workflow_execution.successful_items,
                            failed_items=workflow_execution.failed_items,
                            progress=workflow_execution.progress,
                            message=f"Workflow failed: {error_message}"
                        )
                    
                    break
                
                # Update progress if data is available and enough time has passed
                current_time = time.time()
                if progress_data and (current_time - last_update_time) >= update_interval:
                    with transaction.atomic():
                        # Refresh from database to avoid race conditions
                        workflow_execution.refresh_from_db()
                        
                        # Update workflow progress
                        workflow_execution.update_progress(
                            processed_items=progress_data.get('processed_items', workflow_execution.processed_items),
                            successful_items=progress_data.get('successful_items', workflow_execution.successful_items),
                            failed_items=progress_data.get('failed_items', workflow_execution.failed_items)
                        )
                        
                        # Create progress update
                        WorkflowProgressUpdate.objects.create(
                            workflow_execution=workflow_execution,
                            processed_items=workflow_execution.processed_items,
                            successful_items=workflow_execution.successful_items,
                            failed_items=workflow_execution.failed_items,
                            progress=workflow_execution.progress,
                            message=progress_data.get('message', ''),
                            details=progress_data.get('details', {})
                        )
                    
                    last_update_time = current_time
                
                # Check if workflow is still running
                workflow_execution.refresh_from_db()
                
                # Sleep before next check
                time.sleep(1)
            
            # Remove thread from tracking
            self.tracking_threads.pop(str(workflow_execution_id), None)
            
        except Exception as e:
            logger.exception(f"Error tracking workflow progress: {str(e)}")
            
            # Try to mark workflow as failed
            try:
                workflow_execution = WorkflowExecution.objects.get(id=workflow_execution_id)
                workflow_execution.fail(str(e))
            except Exception:
                pass
            
            # Remove thread from tracking
            self.tracking_threads.pop(str(workflow_execution_id), None)
    
    def _parse_progress(self, log_content):
        """
        Parse progress information from log content.
        
        Args:
            log_content (str): Log content
            
        Returns:
            dict: Progress data
        """
        progress_data = {}
        
        # Parse processed items
        processed_match = re.search(r'Processed (\d+) of (\d+) items', log_content)
        if processed_match:
            processed_items = int(processed_match.group(1))
            total_items = int(processed_match.group(2))
            progress_data['processed_items'] = processed_items
            progress_data['total_items'] = total_items
        
        # Parse successful items
        success_match = re.search(r'Successfully processed (\d+) items', log_content)
        if success_match:
            progress_data['successful_items'] = int(success_match.group(1))
        
        # Parse failed items
        failed_match = re.search(r'Failed to process (\d+) items', log_content)
        if failed_match:
            progress_data['failed_items'] = int(failed_match.group(1))
        
        # Parse progress message
        message_match = re.search(r'Progress: (.*?)$', log_content, re.MULTILINE)
        if message_match:
            progress_data['message'] = message_match.group(1)
        
        # Parse details
        details_match = re.search(r'Details: ({.*?})$', log_content, re.MULTILINE)
        if details_match:
            try:
                details = json.loads(details_match.group(1))
                progress_data['details'] = details
            except json.JSONDecodeError:
                pass
        
        return progress_data
    
    def _extract_results(self, log_content):
        """
        Extract results from log content.
        
        Args:
            log_content (str): Log content
            
        Returns:
            dict: Results data
        """
        results = {}
        
        # Extract results
        results_match = re.search(r'Results: ({.*?})$', log_content, re.MULTILINE | re.DOTALL)
        if results_match:
            try:
                results = json.loads(results_match.group(1))
            except json.JSONDecodeError:
                pass
        
        return results
    
    def get_progress(self, workflow_execution_id):
        """
        Get workflow progress.
        
        Args:
            workflow_execution_id (uuid): Workflow execution ID
            
        Returns:
            dict: Progress data
        """
        from campaigns.models.workflow import WorkflowExecution, WorkflowProgressUpdate
        
        try:
            # Get workflow execution
            workflow_execution = WorkflowExecution.objects.get(id=workflow_execution_id)
            
            # Get latest progress update
            latest_update = WorkflowProgressUpdate.objects.filter(
                workflow_execution=workflow_execution
            ).order_by('-timestamp').first()
            
            # Return progress data
            return {
                'id': str(workflow_execution.id),
                'status': workflow_execution.status,
                'workflow_type': workflow_execution.workflow_type,
                'start_time': workflow_execution.start_time,
                'end_time': workflow_execution.end_time,
                'duration': workflow_execution.duration,
                'progress': workflow_execution.progress,
                'total_items': workflow_execution.total_items,
                'processed_items': workflow_execution.processed_items,
                'successful_items': workflow_execution.successful_items,
                'failed_items': workflow_execution.failed_items,
                'error_message': workflow_execution.error_message,
                'latest_update': {
                    'timestamp': latest_update.timestamp if latest_update else None,
                    'message': latest_update.message if latest_update else None,
                    'details': latest_update.details if latest_update else {}
                } if latest_update else None
            }
        except WorkflowExecution.DoesNotExist:
            return {
                'error': 'Workflow execution not found'
            }
        except Exception as e:
            logger.exception(f"Error getting workflow progress: {str(e)}")
            return {
                'error': str(e)
            }
    
    def cancel_workflow(self, workflow_execution_id):
        """
        Cancel workflow execution.
        
        Args:
            workflow_execution_id (uuid): Workflow execution ID
            
        Returns:
            dict: Result of cancellation
        """
        from campaigns.models.workflow import WorkflowExecution, WorkflowProgressUpdate
        
        try:
            # Get workflow execution
            workflow_execution = WorkflowExecution.objects.get(id=workflow_execution_id)
            
            # Check if workflow is running
            if workflow_execution.status != 'running':
                return {
                    'success': False,
                    'error': f"Workflow is not running (current status: {workflow_execution.status})"
                }
            
            # Try to kill the process
            import subprocess
            workflow_name = workflow_execution.workflow_name
            kill_cmd = f"pkill -f 'pyflow.*{workflow_name}'"
            subprocess.run(kill_cmd, shell=True)
            
            # Mark workflow as cancelled
            workflow_execution.cancel()
            
            # Create progress update
            WorkflowProgressUpdate.objects.create(
                workflow_execution=workflow_execution,
                processed_items=workflow_execution.processed_items,
                successful_items=workflow_execution.successful_items,
                failed_items=workflow_execution.failed_items,
                progress=workflow_execution.progress,
                message="Workflow cancelled by user"
            )
            
            # Remove thread from tracking
            self.tracking_threads.pop(str(workflow_execution_id), None)
            
            return {
                'success': True,
                'message': 'Workflow cancelled successfully'
            }
        except WorkflowExecution.DoesNotExist:
            return {
                'success': False,
                'error': 'Workflow execution not found'
            }
        except Exception as e:
            logger.exception(f"Error cancelling workflow: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def list_active_workflows(self, campaign_id=None):
        """
        List active workflow executions.
        
        Args:
            campaign_id (uuid): Campaign ID to filter by
            
        Returns:
            list: List of active workflow executions
        """
        from campaigns.models.workflow import WorkflowExecution
        
        try:
            # Get active workflow executions
            query = WorkflowExecution.objects.filter(status='running')
            
            # Filter by campaign if provided
            if campaign_id:
                query = query.filter(campaign_id=campaign_id)
            
            # Get workflow executions
            executions = query.order_by('-start_time')
            
            # Return workflow executions
            return [
                {
                    'id': str(execution.id),
                    'campaign_id': str(execution.campaign_id),
                    'campaign_name': execution.campaign.name,
                    'workflow_type': execution.workflow_type,
                    'workflow_name': execution.workflow_name,
                    'start_time': execution.start_time,
                    'progress': execution.progress,
                    'total_items': execution.total_items,
                    'processed_items': execution.processed_items
                }
                for execution in executions
            ]
        except Exception as e:
            logger.exception(f"Error listing active workflows: {str(e)}")
            return []
