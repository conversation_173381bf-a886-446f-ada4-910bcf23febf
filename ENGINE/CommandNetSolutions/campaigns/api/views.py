"""
API views for the campaigns app.
"""
from rest_framework import viewsets, status, mixins
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from drf_spectacular.utils import extend_schema, OpenApiParameter

from campaigns.models import Campaign, DynamicTag, TagCategory
from campaigns.repositories.campaign_repository import CampaignRepository
from campaigns.repositories.tag_repository import TagRepository
from campaigns.services.campaign_service import CampaignService
from campaigns.services.analysis_service import AnalysisService
from campaigns.services.tag_service import TagService
from campaigns.api.serializers import (
    CampaignSerializer, CampaignCreateSerializer, CampaignUpdateSerializer,
    LocationTargetCreateSerializer, UsernameTargetCreateSerializer,
    DynamicTagSerializer, TagCategorySerializer
)

from instagram.tagging_engine import AutoTaggingSystem


class CampaignViewSet(viewsets.ModelViewSet):
    """
    API endpoint for campaigns.
    """
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Get campaign queryset with optional filtering.
        """
        # Initialize repository and service
        repository = CampaignRepository()

        # Get filters from query parameters
        filters = {}

        if 'status' in self.request.query_params:
            filters['status'] = self.request.query_params['status']

        if 'target_type' in self.request.query_params:
            filters['target_type'] = self.request.query_params['target_type']

        if 'search' in self.request.query_params:
            filters['search'] = self.request.query_params['search']

        # Get ordering
        order_by = self.request.query_params.get('order_by', '-created_at')

        # Get campaigns with statistics
        return repository.get_all(filters, order_by, with_stats=True)

    def get_serializer_class(self):
        """
        Get appropriate serializer based on action.
        """
        if self.action == 'create':
            return CampaignCreateSerializer
        elif self.action == 'update' or self.action == 'partial_update':
            return CampaignUpdateSerializer
        elif self.action == 'add_location_target':
            return LocationTargetCreateSerializer
        elif self.action == 'add_username_target':
            return UsernameTargetCreateSerializer
        else:
            return CampaignSerializer

    def perform_create(self, serializer):
        """
        Create campaign with current user as creator.
        """
        # Initialize repository and service
        repository = CampaignRepository()
        service = CampaignService(repository)

        # Create campaign
        campaign_data = serializer.validated_data
        campaign = service.create_campaign(campaign_data, self.request.user)

        # Set serializer instance
        serializer.instance = campaign

    @extend_schema(
        parameters=[
            OpenApiParameter(name='status', description='Filter by status', required=False, type=str),
            OpenApiParameter(name='target_type', description='Filter by target type', required=False, type=str),
            OpenApiParameter(name='search', description='Search in name and description', required=False, type=str),
            OpenApiParameter(name='order_by', description='Order by field (prefix with - for descending)', required=False, type=str),
        ]
    )
    def list(self, request, *args, **kwargs):
        """
        List campaigns with optional filtering and ordering.
        """
        return super().list(request, *args, **kwargs)

    @action(detail=True, methods=['post'])
    def launch(self, request, pk=None):
        """
        Launch a campaign.
        """
        # Get launch method
        launch_method = request.data.get('launch_method', 'airflow')

        try:
            if launch_method == 'pyflow':
                # Use PyFlow service
                from campaigns.services import PyFlowService
                pyflow_service = PyFlowService()

                # Initialize repository and service
                repository = CampaignRepository()
                service = CampaignService(repository)

                # Get campaign
                campaign = service.get_campaign(pk)

                # Get targets
                targets = []

                if campaign.target_type == 'location':
                    for location_target in campaign.location_targets.all():
                        targets.append({
                            'type': 'location',
                            'location_id': location_target.location_id,
                            'location_name': location_target.country + ' - ' + location_target.city
                        })
                else:
                    for username_target in campaign.username_targets.all():
                        targets.append({
                            'type': 'username',
                            'username': username_target.username,
                            'audience_type': username_target.audience_type
                        })

                # Create and run workflow
                result = pyflow_service.create_and_run_collection_workflow(
                    str(campaign.id),
                    targets,
                    {
                        'audience_type': campaign.audience_type
                    }
                )

                if result.get('success'):
                    # Update campaign
                    campaign.status = 'running'
                    campaign.airflow_run_id = result.get('workflow_path', '')
                    campaign.save()

                    # Return updated campaign
                    serializer = self.get_serializer(campaign)
                    return Response(serializer.data)
                else:
                    return Response(
                        {'error': result.get('error', 'Failed to launch workflow')},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            else:
                # Use Airflow service (default)
                repository = CampaignRepository()
                from campaigns.services import AirflowService
                airflow_service = AirflowService()
                service = CampaignService(repository, airflow_service)

                # Launch campaign
                campaign = service.launch_campaign(pk)

                # Return updated campaign
                serializer = self.get_serializer(campaign)
                return Response(serializer.data)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def add_location_target(self, request, pk=None):
        """
        Add location target to campaign.
        """
        # Initialize repository and service
        repository = CampaignRepository()
        service = CampaignService(repository)

        # Validate data
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            # Add location target
            location_target = service.add_location_target(pk, serializer.validated_data)

            # Return updated campaign
            campaign = service.get_campaign(pk)
            campaign_serializer = CampaignSerializer(campaign)
            return Response(campaign_serializer.data)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def add_username_target(self, request, pk=None):
        """
        Add username target to campaign.
        """
        # Initialize repository and service
        repository = CampaignRepository()
        service = CampaignService(repository)

        # Validate data
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        try:
            # Add username target
            username_target = service.add_username_target(pk, serializer.validated_data)

            # Return updated campaign
            campaign = service.get_campaign(pk)
            campaign_serializer = CampaignSerializer(campaign)
            return Response(campaign_serializer.data)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def analyze(self, request, pk=None):
        """
        Analyze campaign accounts.
        """
        # Get analysis method
        analysis_method = request.data.get('analysis_method', 'internal')

        if analysis_method == 'pyflow':
            # Use PyFlow service
            try:
                from campaigns.services import PyFlowService
                pyflow_service = PyFlowService()

                # Initialize repository and service
                repository = CampaignRepository()
                service = CampaignService(repository)

                # Get campaign
                campaign = service.get_campaign(pk)

                # Use default settings
                settings_dict = {
                    'auto_analyze': True,
                    'analysis_frequency': 'immediate',
                    'enable_dynamic_tagging': True
                }

                # Create and run workflow
                result = pyflow_service.create_and_run_analysis_workflow(
                    str(campaign.id),
                    settings_dict,
                    request.data.get('options', {})
                )

                if result.get('success'):
                    return Response({
                        'success': True,
                        'message': 'Analysis workflow started',
                        'workflow_path': result.get('workflow_path', '')
                    })
                else:
                    return Response(
                        {'error': result.get('error', 'Failed to launch analysis workflow')},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            # Use internal analysis services
            repository = CampaignRepository()
            tagging_system = AutoTaggingSystem()

            # Determine analysis mode
            analysis_mode = request.data.get('analysis_mode', 'basic')

            if analysis_mode == 'enhanced':
                # Use enhanced analysis service
                try:
                    from campaigns.services.enhanced_analysis_service import EnhancedAnalysisService
                    from campaigns.services.ml_service import MLService
                    from campaigns.services.nlp_service import NLPService

                    ml_service = MLService()
                    nlp_service = NLPService()
                    service = EnhancedAnalysisService(repository, tagging_system, ml_service, nlp_service)
                except ImportError:
                    # Fall back to basic analysis if enhanced services are not available
                    service = AnalysisService(repository, tagging_system)
            else:
                # Use basic analysis service
                service = AnalysisService(repository, tagging_system)

            # Get analysis options
            options = {
                'async': request.data.get('async', True),
                'batch_size': request.data.get('batch_size', 100)
            }

            try:
                # Analyze campaign
                result = service.analyze_campaign(pk, options)

                return Response(result)
            except Exception as e:
                return Response(
                    {'error': str(e)},
                    status=status.HTTP_400_BAD_REQUEST
                )

    @action(detail=True, methods=['get'])
    def analysis_stats(self, request, pk=None):
        """
        Get analysis statistics for a campaign.
        """
        # Initialize repository and service
        repository = CampaignRepository()

        # Determine analysis mode
        analysis_mode = request.query_params.get('analysis_mode', 'basic')

        if analysis_mode == 'enhanced':
            # Use enhanced analysis service
            try:
                from campaigns.services.enhanced_analysis_service import EnhancedAnalysisService
                service = EnhancedAnalysisService(repository)
                stats = service.get_analysis_stats(pk)
            except ImportError:
                # Fall back to basic analysis if enhanced services are not available
                service = AnalysisService(repository)
                stats = service.get_analysis_stats(pk)
        else:
            # Use basic analysis service
            service = AnalysisService(repository)
            stats = service.get_analysis_stats(pk)

        try:
            return Response(stats)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'])
    def dashboard(self, request, pk=None):
        """
        Get dashboard data for a campaign.
        """
        try:
            # Use dashboard service
            from campaigns.services.dashboard_service import DashboardService
            service = DashboardService()

            # Get dashboard data
            data = service.get_dashboard_data(pk)

            # Add ROI data if available
            try:
                from campaigns.services.roi_service import ROIService
                roi_service = ROIService()
                roi_data = roi_service.get_campaign_roi(pk)
                quality_data = roi_service.calculate_campaign_quality(pk)

                data['roi'] = roi_data
                data['quality'] = quality_data
            except ImportError:
                data['roi'] = {'error': 'ROI service not available'}

            return Response(data)
        except ImportError:
            # Fall back to basic stats if dashboard service is not available
            repository = CampaignRepository()
            service = AnalysisService(repository)
            stats = service.get_analysis_stats(pk)

            return Response({
                'campaign_summary': {
                    'error': 'Dashboard service not available'
                },
                'analysis_stats': stats
            })
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get', 'put'])
    def roi(self, request, pk=None):
        """
        Get or update ROI data for a campaign.
        """
        try:
            from campaigns.services.roi_service import ROIService
            service = ROIService()

            if request.method == 'GET':
                # Get ROI data
                data = service.get_campaign_roi(pk)
                return Response(data)
            else:
                # Update ROI data
                data = service.update_campaign_roi(pk, request.data)
                return Response(data)
        except ImportError:
            return Response(
                {'error': 'ROI service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'])
    def quality(self, request, pk=None):
        """
        Get quality metrics for a campaign.
        """
        try:
            from campaigns.services.roi_service import ROIService
            service = ROIService()

            # Get quality data
            data = service.calculate_campaign_quality(pk)
            return Response(data)
        except ImportError:
            return Response(
                {'error': 'ROI service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'])
    def roi_trends(self, request, pk=None):
        """
        Get ROI trends over time for a campaign.
        """
        try:
            from campaigns.services.roi_service import ROIService
            service = ROIService()

            # Get period parameter
            period = request.query_params.get('period', 'monthly')

            # Get trend data
            data = service.get_roi_trends(pk, period)
            return Response(data)
        except ImportError:
            return Response(
                {'error': 'ROI service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def roi_comparison(self, request):
        """
        Get ROI comparison for multiple campaigns.
        """
        try:
            from campaigns.services.roi_service import ROIService
            service = ROIService()

            # Get campaign IDs from query params
            campaign_ids = request.query_params.getlist('campaign_ids')

            # Get comparison data
            data = service.get_roi_comparison(campaign_ids)
            return Response(data)
        except ImportError:
            return Response(
                {'error': 'ROI service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'])
    def workflows(self, request, pk=None):
        """
        Get PyFlow workflows for a campaign.
        """
        try:
            from campaigns.services import PyFlowService
            service = PyFlowService()

            # Get workflows
            workflows = service.list_workflows(pk)

            return Response(workflows)
        except ImportError:
            return Response(
                {'error': 'PyFlow service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'])
    def workflow_status(self, request, pk=None):
        """
        Get status of a PyFlow workflow.
        """
        try:
            from campaigns.services import PyFlowService
            service = PyFlowService()

            # Get workflow name
            workflow_name = request.query_params.get('workflow_name')

            if not workflow_name:
                return Response(
                    {'error': 'Workflow name is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Get workflow status
            status_data = service.get_workflow_status(workflow_name)

            return Response(status_data)
        except ImportError:
            return Response(
                {'error': 'PyFlow service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )




class DynamicTagViewSet(viewsets.ModelViewSet):
    """
    API endpoint for dynamic tags.
    """
    serializer_class = DynamicTagSerializer
    permission_classes = [IsAuthenticated]

    @action(detail=True, methods=['post'])
    def train_model(self, request, pk=None):
        """
        Train a machine learning model for a tag.
        """
        try:
            # Get tag
            tag = self.get_object()

            # Get training data
            positive_examples = request.data.get('positive_examples', [])
            negative_examples = request.data.get('negative_examples', [])

            if not positive_examples or not negative_examples:
                return Response(
                    {'error': 'Both positive and negative examples are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Train model
            from campaigns.services.ml_service import MLService
            ml_service = MLService()

            result = ml_service.train_model(
                str(tag.id),
                positive_examples,
                negative_examples,
                tag.field
            )

            if result.get('success'):
                # Update tag type to ML
                tag.tag_type = 'ml'
                tag.save()

            return Response(result)
        except ImportError:
            return Response(
                {'error': 'Machine learning service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def test_model(self, request, pk=None):
        """
        Test a machine learning model on a text sample.
        """
        try:
            # Get tag
            tag = self.get_object()

            # Get text to analyze
            text = request.data.get('text')

            if not text:
                return Response(
                    {'error': 'Text is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Analyze text
            from campaigns.services.ml_service import MLService
            ml_service = MLService()

            result = ml_service.analyze_text(str(tag.id), text)

            return Response(result)
        except ImportError:
            return Response(
                {'error': 'Machine learning service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'])
    def model_info(self, request, pk=None):
        """
        Get information about a trained model.
        """
        try:
            # Get tag
            tag = self.get_object()

            # Get model info
            from campaigns.services.ml_service import MLService
            ml_service = MLService()

            result = ml_service.get_model_info(str(tag.id))

            return Response(result)
        except ImportError:
            return Response(
                {'error': 'Machine learning service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'])
    def analyze_text(self, request):
        """
        Analyze text using NLP.
        """
        try:
            # Get text to analyze
            text = request.data.get('text')

            if not text:
                return Response(
                    {'error': 'Text is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Analyze text
            from campaigns.services.nlp_service import NLPService
            nlp_service = NLPService()

            result = nlp_service.analyze_text(text)

            return Response(result)
        except ImportError:
            return Response(
                {'error': 'NLP service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def test(self, request, pk=None):
        """
        Test a tag on a text sample or account.
        """
        try:
            # Get tag
            tag = self.get_object()

            # Get text or account ID
            text = request.data.get('text')
            account_id = request.data.get('account_id')

            if not text and not account_id:
                return Response(
                    {'error': 'Either text or account_id is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Test tag
            from campaigns.services.tag_builder_service import TagBuilderService
            service = TagBuilderService()

            result = service.test_tag(str(tag.id), text, account_id)

            return Response(result)
        except ImportError:
            return Response(
                {'error': 'Tag builder service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['get'])
    def effectiveness(self, request, pk=None):
        """
        Get effectiveness metrics for a tag.
        """
        try:
            # Get tag
            tag = self.get_object()

            # Get effectiveness metrics
            from campaigns.services.tag_builder_service import TagBuilderService
            service = TagBuilderService()

            result = service.get_tag_effectiveness(str(tag.id))

            return Response(result)
        except ImportError:
            return Response(
                {'error': 'Tag builder service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def sample_data(self, request):
        """
        Get sample data for testing tags.
        """
        try:
            # Get field parameter
            field = request.query_params.get('field', 'bio')
            count = int(request.query_params.get('count', 10))

            # Get sample data
            from campaigns.services.tag_builder_service import TagBuilderService
            service = TagBuilderService()

            result = service.get_sample_data(field, count)

            return Response(result)
        except ImportError:
            return Response(
                {'error': 'Tag builder service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['post'])
    def suggest(self, request):
        """
        Suggest tags based on text analysis.
        """
        try:
            # Get text to analyze
            text = request.data.get('text')
            field = request.data.get('field', 'bio')

            if not text:
                return Response(
                    {'error': 'Text is required'},
                    status=status.HTTP_400_BAD_REQUEST
                )

            # Suggest tags
            from campaigns.services.tag_builder_service import TagBuilderService
            service = TagBuilderService()

            result = service.suggest_tags(text, field)

            return Response(result)
        except ImportError:
            return Response(
                {'error': 'Tag builder service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )


class TagCategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for tag categories.
    """
    serializer_class = TagCategorySerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Get tag categories with optional filtering.
        """
        queryset = TagCategory.objects.all()

        # Filter by parent
        parent_id = self.request.query_params.get('parent_id')
        if parent_id:
            if parent_id == 'null':
                queryset = queryset.filter(parent__isnull=True)
            else:
                queryset = queryset.filter(parent_id=parent_id)

        # Order by priority and name
        return queryset.order_by('-priority', 'name')

    @action(detail=True, methods=['get'])
    def tags(self, request, pk=None):
        """
        Get tags in this category.
        """
        try:
            # Get category
            category = self.get_object()

            # Get tags
            tags = DynamicTag.objects.filter(category=category)

            # Serialize tags
            serializer = DynamicTagSerializer(tags, many=True)

            return Response(serializer.data)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def tree(self, request):
        """
        Get tag categories in a tree structure.
        """
        try:
            # Get all categories
            categories = TagCategory.objects.all()

            # Build tree
            tree = {}

            # First, add all root categories
            for category in categories.filter(parent__isnull=True):
                tree[str(category.id)] = {
                    'id': str(category.id),
                    'name': category.name,
                    'description': category.description,
                    'color': category.color,
                    'icon': category.icon,
                    'priority': category.priority,
                    'children': []
                }

            # Then, add all child categories
            for category in categories.filter(parent__isnull=False):
                parent_id = str(category.parent_id)
                if parent_id in tree:
                    tree[parent_id]['children'].append({
                        'id': str(category.id),
                        'name': category.name,
                        'description': category.description,
                        'color': category.color,
                        'icon': category.icon,
                        'priority': category.priority
                    })

            # Convert to list
            result = list(tree.values())

            # Sort by priority
            result.sort(key=lambda x: (-x['priority'], x['name']))
            for category in result:
                category['children'].sort(key=lambda x: (-x['priority'], x['name']))

            return Response(result)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @extend_schema(
        parameters=[
            OpenApiParameter(name='is_global', description='Filter by global status', required=False, type=bool),
            OpenApiParameter(name='tag_type', description='Filter by tag type', required=False, type=str),
            OpenApiParameter(name='field', description='Filter by field', required=False, type=str),
            OpenApiParameter(name='search', description='Search in name, description, and pattern', required=False, type=str),
            OpenApiParameter(name='campaign_id', description='Filter by campaign ID (includes global tags)', required=False, type=str),
            OpenApiParameter(name='order_by', description='Order by field (prefix with - for descending)', required=False, type=str),
        ]
    )
    def list(self, request, *args, **kwargs):
        """
        List tags with optional filtering and ordering.
        """
        return super().list(request, *args, **kwargs)

    @action(detail=True, methods=['post'])
    def assign_to_campaign(self, request, pk=None):
        """
        Assign tag to campaign.
        """
        # Initialize repository and service
        repository = TagRepository()
        service = TagService(repository)

        # Get campaign ID
        campaign_id = request.data.get('campaign_id')

        if not campaign_id:
            return Response(
                {'error': 'Campaign ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Assign tag to campaign
            service.assign_tag_to_campaign(pk, campaign_id)

            return Response({'status': 'success'})
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def remove_from_campaign(self, request, pk=None):
        """
        Remove tag from campaign.
        """
        # Initialize repository and service
        repository = TagRepository()
        service = TagService(repository)

        # Get campaign ID
        campaign_id = request.data.get('campaign_id')

        if not campaign_id:
            return Response(
                {'error': 'Campaign ID is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            # Remove tag from campaign
            service.remove_tag_from_campaign(pk, campaign_id)

            return Response({'status': 'success'})
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
