"""
Resource Manager API endpoints for the campaigns app.

This module provides API endpoints for interacting with the resource manager service.
"""
import logging
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from django.conf import settings

from campaigns.services.resource_manager_service import ResourceManagerService
from campaigns.models.workflow import WorkflowExecution

logger = logging.getLogger(__name__)

# Initialize resource manager service
resource_manager = ResourceManagerService()

class ResourceManagerStatusView(APIView):
    """
    API endpoint for getting resource manager status.
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """
        Get resource manager status.
        
        Returns:
            Response: Resource manager status
        """
        try:
            # Get system resources
            resources = resource_manager.get_system_resources()
            
            # Get running workflows
            running_workflows = []
            with resource_manager.lock:
                for workflow_id, info in resource_manager.running_workflows.items():
                    running_workflows.append({
                        'id': workflow_id,
                        'campaign_id': info.get('campaign_id'),
                        'workflow_type': info.get('workflow_type'),
                        'priority': info.get('priority'),
                        'start_time': info.get('start_time').isoformat() if info.get('start_time') else None,
                        'paused': info.get('paused', False),
                        'resource_usage': info.get('resource_usage', {}),
                        'api_usage': info.get('api_usage', {})
                    })
            
            return Response({
                'resources': resources,
                'running_workflows': running_workflows,
                'timestamp': timezone.now().isoformat()
            })
        except Exception as e:
            logger.exception(f"Error getting resource manager status: {str(e)}")
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class CheckRateLimitView(APIView):
    """
    API endpoint for checking rate limits.
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """
        Check if an API call would exceed rate limits.
        
        Query Parameters:
            workflow_id (str): Workflow execution ID
            api_type (str): Type of API call
            count (int): Number of API calls
            
        Returns:
            Response: Rate limit check result
        """
        try:
            # Get parameters
            workflow_id = request.query_params.get('workflow_id')
            api_type = request.query_params.get('api_type')
            count = int(request.query_params.get('count', 1))
            
            # Validate parameters
            if not workflow_id or not api_type:
                return Response({
                    'error': 'Missing required parameters'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check rate limit
            rate_limit = resource_manager.api_rate_limits.get(api_type, resource_manager.api_rate_limits['default'])
            limit = rate_limit['limit']
            window = rate_limit['window']
            
            # Get current usage from Redis
            key = f"api_rate:{api_type}"
            current_usage = resource_manager.redis_client.get(key)
            current_usage = int(current_usage) if current_usage else 0
            
            # Check if limit would be exceeded
            can_proceed = (current_usage + count) <= limit
            
            # Calculate wait time if rate limited
            wait_time = 0
            if not can_proceed:
                ttl = resource_manager.redis_client.ttl(key)
                wait_time = ttl if ttl > 0 else window
            
            return Response({
                'can_proceed': can_proceed,
                'current_usage': current_usage,
                'limit': limit,
                'wait_time': wait_time,
                'api_type': api_type
            })
        except Exception as e:
            logger.exception(f"Error checking rate limit: {str(e)}")
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class TrackAPIUsageView(APIView):
    """
    API endpoint for tracking API usage.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """
        Track API usage for rate limiting.
        
        Request Body:
            workflow_id (str): Workflow execution ID
            api_type (str): Type of API call
            count (int): Number of API calls
            
        Returns:
            Response: API usage tracking result
        """
        try:
            # Get parameters
            workflow_id = request.data.get('workflow_id')
            api_type = request.data.get('api_type')
            count = int(request.data.get('count', 1))
            
            # Validate parameters
            if not workflow_id or not api_type:
                return Response({
                    'error': 'Missing required parameters'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Track API usage
            within_limits = resource_manager.track_api_usage(workflow_id, api_type, count)
            
            return Response({
                'success': True,
                'within_limits': within_limits,
                'api_type': api_type,
                'count': count
            })
        except Exception as e:
            logger.exception(f"Error tracking API usage: {str(e)}")
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class OptimizeResourcesView(APIView):
    """
    API endpoint for optimizing resource allocation.
    """
    permission_classes = [IsAuthenticated]
    
    def post(self, request):
        """
        Optimize resource allocation across workflows.
        
        Returns:
            Response: Optimization results
        """
        try:
            # Optimize resource allocation
            result = resource_manager.optimize_resource_allocation()
            
            return Response(result)
        except Exception as e:
            logger.exception(f"Error optimizing resources: {str(e)}")
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
