"""
API views for advanced machine learning features.
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from campaigns.services import AdvancedMLService


class AdvancedMLViewSet(viewsets.ViewSet):
    """
    API endpoint for advanced machine learning features.
    """
    permission_classes = [IsAuthenticated]
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        try:
            self.ml_service = AdvancedMLService()
        except ImportError:
            self.ml_service = None
    
    @action(detail=False, methods=['post'])
    def analyze_sentiment(self, request):
        """
        Analyze sentiment of text.
        """
        if not self.ml_service:
            return Response(
                {'error': 'Advanced ML service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        
        text = request.data.get('text')
        if not text:
            return Response(
                {'error': 'Text is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        result = self.ml_service.analyze_sentiment(text)
        return Response(result)
    
    @action(detail=False, methods=['post'])
    def predict_engagement(self, request):
        """
        Predict engagement rate for an account.
        """
        if not self.ml_service:
            return Response(
                {'error': 'Advanced ML service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        
        account_data = request.data.get('account_data')
        if not account_data:
            return Response(
                {'error': 'Account data is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        result = self.ml_service.predict_engagement(account_data)
        return Response(result)
    
    @action(detail=False, methods=['post'])
    def assess_account_quality(self, request):
        """
        Assess overall quality of an account.
        """
        if not self.ml_service:
            return Response(
                {'error': 'Advanced ML service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        
        account_data = request.data.get('account_data')
        if not account_data:
            return Response(
                {'error': 'Account data is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        result = self.ml_service.assess_account_quality(account_data)
        return Response(result)
    
    @action(detail=False, methods=['post'])
    def detect_fake_followers(self, request):
        """
        Detect fake followers in an account.
        """
        if not self.ml_service:
            return Response(
                {'error': 'Advanced ML service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        
        account_data = request.data.get('account_data')
        if not account_data:
            return Response(
                {'error': 'Account data is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        result = self.ml_service.detect_fake_followers(account_data)
        return Response(result)
    
    @action(detail=False, methods=['post'])
    def predict_content_performance(self, request):
        """
        Predict performance of content.
        """
        if not self.ml_service:
            return Response(
                {'error': 'Advanced ML service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        
        content_data = request.data.get('content_data')
        if not content_data:
            return Response(
                {'error': 'Content data is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        result = self.ml_service.predict_content_performance(content_data)
        return Response(result)
    
    @action(detail=False, methods=['post'])
    def analyze_audience(self, request):
        """
        Analyze audience demographics and interests.
        """
        if not self.ml_service:
            return Response(
                {'error': 'Advanced ML service not available'},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )
        
        audience_data = request.data.get('audience_data')
        if not audience_data:
            return Response(
                {'error': 'Audience data is required'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        result = self.ml_service.analyze_audience(audience_data)
        return Response(result)
