"""
URL patterns for the campaigns API.
"""
from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from campaigns.api.views import CampaignViewSet, DynamicTagViewSet, TagCategoryViewSet
from campaigns.api.external_api_views import ExternalAPIViewSet
from campaigns.api.notification_views import NotificationViewSet
from campaigns.api.advanced_ml_views import AdvancedMLViewSet
from campaigns.api.resource_manager_api import (
    ResourceManagerStatusView,
    CheckRateLimitView,
    TrackAPIUsageView,
    OptimizeResourcesView
)

# Create router
router = DefaultRouter()
router.register(r'campaigns', CampaignViewSet, basename='campaign')
router.register(r'tags', DynamicTagViewSet, basename='tag')
router.register(r'tag-categories', TagCategoryViewSet, basename='tag-category')
router.register(r'external-apis', ExternalAPIViewSet, basename='external-api')
router.register(r'notifications', NotificationViewSet, basename='notification')
router.register(r'advanced-ml', AdvancedMLViewSet, basename='advanced-ml')

# URL patterns
urlpatterns = [
    path('', include(router.urls)),

    # Resource manager endpoints
    path('resource-manager/status/', ResourceManagerStatusView.as_view(), name='resource-manager-status'),
    path('resource-manager/check-rate-limit/', CheckRateLimitView.as_view(), name='check-rate-limit'),
    path('resource-manager/track-api-usage/', TrackAPIUsageView.as_view(), name='track-api-usage'),
    path('resource-manager/optimize/', OptimizeResourcesView.as_view(), name='optimize-resources'),
]
