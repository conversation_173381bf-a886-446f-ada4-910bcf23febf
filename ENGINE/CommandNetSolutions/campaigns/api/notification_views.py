"""
API views for notifications.
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.utils import timezone
from campaigns.models import Notification
from campaigns.services import NotificationService


class NotificationViewSet(viewsets.ModelViewSet):
    """
    API endpoint for notifications.
    """
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        """
        Get notifications for the current user.
        """
        return Notification.objects.filter(user=self.request.user).order_by('-created_at')

    def list(self, request):
        """
        List notifications for the current user.
        """
        # Get notifications
        notifications = self.get_queryset()

        # Get unread count
        unread_count = notifications.filter(is_read=False).count()

        # Serialize notifications
        data = []
        for notification in notifications:
            data.append({
                'id': str(notification.id),
                'title': notification.title,
                'message': notification.message,
                'notification_type': notification.notification_type,
                'link': notification.link,
                'data': notification.data,
                'is_read': notification.is_read,
                'created_at': notification.created_at.isoformat(),
                'read_at': notification.read_at.isoformat() if notification.read_at else None
            })

        return Response({
            'notifications': data,
            'unread_count': unread_count,
            'total_count': len(data)
        })

    def retrieve(self, request, pk=None):
        """
        Get a notification.
        """
        try:
            # Get notification
            notification = Notification.objects.get(id=pk, user=request.user)

            # Serialize notification
            data = {
                'id': str(notification.id),
                'title': notification.title,
                'message': notification.message,
                'notification_type': notification.notification_type,
                'link': notification.link,
                'data': notification.data,
                'is_read': notification.is_read,
                'created_at': notification.created_at.isoformat(),
                'read_at': notification.read_at.isoformat() if notification.read_at else None
            }

            return Response(data)
        except Notification.DoesNotExist:
            return Response(
                {'error': 'Notification not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=True, methods=['post'])
    def mark_as_read(self, request, pk=None):
        """
        Mark a notification as read.
        """
        try:
            # Get notification
            notification = Notification.objects.get(id=pk, user=request.user)

            # Mark as read
            notification.is_read = True
            notification.read_at = timezone.now()
            notification.save()

            return Response({
                'success': True,
                'id': str(notification.id),
                'is_read': notification.is_read,
                'read_at': notification.read_at.isoformat()
            })
        except Notification.DoesNotExist:
            return Response(
                {'error': 'Notification not found'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['post'])
    def mark_all_as_read(self, request):
        """
        Mark all notifications as read.
        """
        # Get unread notifications
        notifications = Notification.objects.filter(user=request.user, is_read=False)

        # Mark all as read
        now = timezone.now()
        count = notifications.count()

        notifications.update(is_read=True, read_at=now)

        return Response({
            'success': True,
            'count': count
        })

    @action(detail=False, methods=['get'])
    def unread_count(self, request):
        """
        Get unread notification count.
        """
        # Get unread count
        unread_count = Notification.objects.filter(user=request.user, is_read=False).count()

        return Response({
            'unread_count': unread_count
        })

    @action(detail=False, methods=['post'])
    def send(self, request):
        """
        Send a notification to users.
        """
        # Check admin permission
        if not request.user.is_staff:
            return Response(
                {'error': 'Admin permission required'},
                status=status.HTTP_403_FORBIDDEN
            )

        # Get notification data
        user_ids = request.data.get('user_ids', [])
        title = request.data.get('title')
        message = request.data.get('message')
        notification_type = request.data.get('notification_type', 'info')
        link = request.data.get('link')
        data = request.data.get('data')

        if not user_ids or not title or not message:
            return Response(
                {'error': 'User IDs, title, and message are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Send notification
        service = NotificationService()
        result = service.send_in_app_notification(
            user_ids,
            title,
            message,
            notification_type,
            link,
            data
        )

        return Response(result)
