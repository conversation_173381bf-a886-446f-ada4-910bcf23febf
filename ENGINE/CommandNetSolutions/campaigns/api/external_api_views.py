"""
API views for external API integration.
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from campaigns.services import ExternalAPIService


class ExternalAPIViewSet(viewsets.ViewSet):
    """
    API endpoint for external API integration.
    """
    permission_classes = [IsAuthenticated]
    
    def list(self, request):
        """
        List all configured APIs.
        """
        try:
            service = ExternalAPIService()
            apis = service.list_apis()
            return Response(apis)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def retrieve(self, request, pk=None):
        """
        Get API configuration.
        """
        try:
            service = ExternalAPIService()
            result = service.get_api_config(pk)
            
            if result.get('success'):
                return Response(result.get('config'))
            else:
                return Response(
                    {'error': result.get('error')},
                    status=status.HTTP_404_NOT_FOUND
                )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def create(self, request):
        """
        Configure a new API.
        """
        try:
            # Check admin permission
            if not request.user.is_staff:
                return Response(
                    {'error': 'Admin permission required'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            api_name = request.data.get('name')
            config = request.data.get('config')
            
            if not api_name or not config:
                return Response(
                    {'error': 'API name and configuration are required'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            service = ExternalAPIService()
            result = service.configure_api(api_name, config)
            
            if result.get('success'):
                return Response(result)
            else:
                return Response(
                    {'error': result.get('error')},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    def destroy(self, request, pk=None):
        """
        Delete API configuration.
        """
        try:
            # Check admin permission
            if not request.user.is_staff:
                return Response(
                    {'error': 'Admin permission required'},
                    status=status.HTTP_403_FORBIDDEN
                )
            
            service = ExternalAPIService()
            result = service.delete_api_config(pk)
            
            if result.get('success'):
                return Response(result)
            else:
                return Response(
                    {'error': result.get('error')},
                    status=status.HTTP_404_NOT_FOUND
                )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['post'])
    def call(self, request, pk=None):
        """
        Call an external API.
        """
        try:
            endpoint = request.data.get('endpoint', '/')
            method = request.data.get('method', 'GET')
            params = request.data.get('params')
            data = request.data.get('data')
            headers = request.data.get('headers')
            cache_key = request.data.get('cache_key')
            cache_ttl = request.data.get('cache_ttl', 300)
            
            service = ExternalAPIService()
            result = service.call_api(
                pk, endpoint, method, params, data, headers, cache_key, cache_ttl
            )
            
            if result.get('success'):
                return Response(result)
            else:
                return Response(
                    {'error': result.get('error')},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
    
    @action(detail=True, methods=['get'])
    def test(self, request, pk=None):
        """
        Test connection to an API.
        """
        try:
            endpoint = request.query_params.get('endpoint', '/')
            
            service = ExternalAPIService()
            result = service.test_api_connection(pk, endpoint)
            
            if result.get('success'):
                return Response(result)
            else:
                return Response(
                    {'error': result.get('error')},
                    status=status.HTTP_400_BAD_REQUEST
                )
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )
