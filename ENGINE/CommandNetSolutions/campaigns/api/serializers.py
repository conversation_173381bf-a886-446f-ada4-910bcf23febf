"""
Serializers for the campaigns API.
"""
from rest_framework import serializers
from campaigns.models import (
    Campaign, LocationTarget, UsernameTarget, CampaignResult,
    DynamicTag, TagCategory, CampaignROI, TagMetrics
)


class LocationTargetSerializer(serializers.ModelSerializer):
    """
    Serializer for location targets.
    """
    class Meta:
        model = LocationTarget
        fields = ['id', 'location_id', 'country', 'city']


class UsernameTargetSerializer(serializers.ModelSerializer):
    """
    Serializer for username targets.
    """
    class Meta:
        model = UsernameTarget
        fields = ['id', 'username', 'audience_type']


class CampaignResultSerializer(serializers.ModelSerializer):
    """
    Serializer for campaign results.
    """
    class Meta:
        model = CampaignResult
        fields = ['total_accounts_found', 'total_accounts_processed', 'total_accounts_pending', 'last_processed_at']


class TagMetricsSerializer(serializers.ModelSerializer):
    """
    Serializer for tag metrics.
    """
    class Meta:
        model = TagMetrics
        fields = [
            'usage_count', 'match_count', 'conversion_rate',
            'precision', 'recall', 'f1_score', 'last_evaluated'
        ]


class TagCategorySerializer(serializers.ModelSerializer):
    """
    Serializer for tag categories.
    """
    class Meta:
        model = TagCategory
        fields = [
            'id', 'name', 'description', 'parent', 'color',
            'icon', 'priority', 'created_at', 'updated_at'
        ]


class DynamicTagSerializer(serializers.ModelSerializer):
    """
    Serializer for dynamic tags.
    """
    tag_type_display = serializers.CharField(source='get_tag_type_display', read_only=True)
    confidence_level_display = serializers.CharField(source='get_confidence_level_display', read_only=True)
    metrics = TagMetricsSerializer(read_only=True)
    category_details = TagCategorySerializer(source='category', read_only=True)

    class Meta:
        model = DynamicTag
        fields = [
            'id', 'name', 'description', 'category', 'category_details', 'tag_type',
            'tag_type_display', 'pattern', 'field', 'is_global', 'confidence_level',
            'confidence_level_display', 'weight', 'related_tags', 'metrics',
            'created_at', 'updated_at'
        ]





class CampaignSerializer(serializers.ModelSerializer):
    """
    Serializer for campaigns.
    """
    location_targets = LocationTargetSerializer(many=True, read_only=True)
    username_targets = UsernameTargetSerializer(many=True, read_only=True)
    results = CampaignResultSerializer(read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    target_type_display = serializers.CharField(source='get_target_type_display', read_only=True)
    audience_type_display = serializers.CharField(source='get_audience_type_display', read_only=True)

    # Statistics fields (populated when with_stats=True)
    account_count = serializers.IntegerField(read_only=True, required=False)
    whitelist_count = serializers.IntegerField(read_only=True, required=False)
    avg_followers = serializers.IntegerField(read_only=True, required=False)

    class Meta:
        model = Campaign
        fields = [
            'id', 'name', 'description', 'status', 'status_display',
            'target_type', 'target_type_display', 'audience_type', 'audience_type_display',
            'created_at', 'updated_at',
            'airflow_run_id', 'airflow_dag_id', 'location_targets', 'username_targets',
            'results', 'account_count', 'whitelist_count', 'avg_followers'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'airflow_run_id', 'airflow_dag_id']


class CampaignCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating campaigns.
    """
    class Meta:
        model = Campaign
        fields = ['name', 'description', 'target_type', 'audience_type']


class CampaignUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for updating campaigns.
    """
    class Meta:
        model = Campaign
        fields = ['name', 'description']


class LocationTargetCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating location targets.
    """
    class Meta:
        model = LocationTarget
        fields = ['location_id', 'country', 'city']


class UsernameTargetCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating username targets.
    """
    class Meta:
        model = UsernameTarget
        fields = ['username', 'audience_type']





class CampaignROISerializer(serializers.ModelSerializer):
    """
    Serializer for campaign ROI.
    """
    class Meta:
        model = CampaignROI
        fields = [
            'id', 'cost_type', 'cost_value', 'total_cost', 'estimated_value',
            'actual_value', 'roi_percentage', 'conversion_rate', 'cost_per_account',
            'cost_per_whitelist', 'notes', 'created_at', 'updated_at'
        ]
