#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to trigger comprehensive whitelist testing via Airflow DAG.

This script provides an easy way to trigger the whitelist testing DAG
for a specific campaign with customizable parameters.

Usage:
    python trigger_whitelist_testing.py <campaign_id> [options]

Examples:
    python trigger_whitelist_testing.py 1
    python trigger_whitelist_testing.py 1 --num-accounts 200 --no-clear
    python trigger_whitelist_testing.py 1 --email <EMAIL>
"""

import argparse
import json
import requests
import sys
import os
from datetime import datetime

# Add Django directory to path
django_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
if django_dir not in sys.path:
    sys.path.append(django_dir)

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")
import django
django.setup()

from campaigns.models import Campaign

# Airflow configuration
AIRFLOW_BASE_URL = "http://localhost:8080"
DAG_ID = "comprehensive_whitelist_testing"

def trigger_dag(campaign_id, num_accounts=150, clear_existing=True, notify_email=None):
    """
    Trigger the whitelist testing DAG via Airflow REST API.
    """
    # Prepare the configuration
    config = {
        "campaign_id": campaign_id,
        "num_accounts": num_accounts,
        "clear_existing": clear_existing
    }
    
    if notify_email:
        config["notify_email"] = notify_email

    # Create the DAG run
    dag_run_id = f"whitelist_test_{campaign_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    url = f"{AIRFLOW_BASE_URL}/api/v1/dags/{DAG_ID}/dagRuns"
    
    payload = {
        "dag_run_id": dag_run_id,
        "conf": config
    }
    
    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    print(f"Triggering whitelist testing for campaign {campaign_id}...")
    print(f"Configuration: {json.dumps(config, indent=2)}")
    print(f"DAG Run ID: {dag_run_id}")
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ DAG triggered successfully!")
            print(f"DAG Run ID: {result.get('dag_run_id')}")
            print(f"Execution Date: {result.get('execution_date')}")
            print(f"State: {result.get('state')}")
            print(f"\n🔗 Monitor progress at: {AIRFLOW_BASE_URL}/dags/{DAG_ID}/grid")
            return True
        else:
            print(f"❌ Failed to trigger DAG. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print(f"❌ Could not connect to Airflow at {AIRFLOW_BASE_URL}")
        print("Make sure Airflow is running and accessible.")
        return False
    except requests.exceptions.Timeout:
        print(f"❌ Request timed out. Airflow might be slow to respond.")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {str(e)}")
        return False

def validate_campaign(campaign_id):
    """
    Validate that the campaign exists and is suitable for testing.
    """
    try:
        campaign = Campaign.objects.get(id=campaign_id)
        print(f"📋 Campaign found: {campaign.name}")
        print(f"   Status: {campaign.status}")
        print(f"   Created: {campaign.created_at}")
        
        # Check if campaign has tags
        tag_count = campaign.campaign_tags.count()
        if tag_count > 0:
            print(f"   Tags: {tag_count} campaign tags configured")
        else:
            print(f"   Tags: No tags configured (default tags will be created)")
        
        return campaign
    except Campaign.DoesNotExist:
        print(f"❌ Campaign with ID {campaign_id} does not exist")
        return None

def main():
    parser = argparse.ArgumentParser(
        description="Trigger comprehensive whitelist testing for a campaign",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  %(prog)s 1                                    # Test campaign 1 with default settings
  %(prog)s 1 --num-accounts 200                # Test with 200 accounts
  %(prog)s 1 --no-clear                        # Don't clear existing data
  %(prog)s 1 --email <EMAIL>         # Send notification to specific email
  %(prog)s 1 --num-accounts 300 --no-clear --email <EMAIL>
        """
    )
    
    parser.add_argument(
        'campaign_id',
        type=str,
        help='Campaign ID to test'
    )
    
    parser.add_argument(
        '--num-accounts',
        type=int,
        default=150,
        help='Number of accounts to simulate (default: 150)'
    )
    
    parser.add_argument(
        '--no-clear',
        action='store_true',
        help='Do not clear existing data (default: clear existing data)'
    )
    
    parser.add_argument(
        '--email',
        type=str,
        help='Email address for notifications'
    )
    
    parser.add_argument(
        '--airflow-url',
        type=str,
        default=AIRFLOW_BASE_URL,
        help=f'Airflow base URL (default: {AIRFLOW_BASE_URL})'
    )
    
    args = parser.parse_args()
    
    # Update global Airflow URL if provided
    global AIRFLOW_BASE_URL
    AIRFLOW_BASE_URL = args.airflow_url
    
    print("🧪 Comprehensive Whitelist Testing")
    print("=" * 50)
    
    # Validate campaign
    campaign = validate_campaign(args.campaign_id)
    if not campaign:
        sys.exit(1)
    
    print()
    
    # Trigger the DAG
    clear_existing = not args.no_clear
    success = trigger_dag(
        campaign_id=args.campaign_id,
        num_accounts=args.num_accounts,
        clear_existing=clear_existing,
        notify_email=args.email
    )
    
    if success:
        print(f"\n📊 Expected Results:")
        print(f"   • {args.num_accounts} realistic Instagram accounts will be created")
        print(f"   • Tag analysis will be applied based on campaign tags")
        print(f"   • Whitelist entries will be generated with appropriate privileges")
        print(f"   • Comprehensive analytics will be available in the dashboard")
        print(f"\n🎯 Next Steps:")
        print(f"   1. Monitor the DAG execution in Airflow")
        print(f"   2. Wait for completion notification (if email provided)")
        print(f"   3. View results in Whitelist Analytics Dashboard:")
        print(f"      http://127.0.0.1:8001/campaigns/{args.campaign_id}/simulation/")
        sys.exit(0)
    else:
        print(f"\n🔧 Troubleshooting:")
        print(f"   • Check if Airflow is running: {AIRFLOW_BASE_URL}")
        print(f"   • Verify the DAG is available: {AIRFLOW_BASE_URL}/dags/{DAG_ID}")
        print(f"   • Check Airflow logs for errors")
        sys.exit(1)

if __name__ == "__main__":
    main()
