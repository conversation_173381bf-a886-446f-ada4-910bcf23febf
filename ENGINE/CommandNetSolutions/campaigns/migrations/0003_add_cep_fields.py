"""
Migration to add CEP-related fields to the Campaign model and create the CEPWorkflow model.
"""
import uuid
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('campaigns', '0002_initial'),
    ]

    operations = [
        # Add is_in_cep field to Campaign model
        migrations.AddField(
            model_name='campaign',
            name='is_in_cep',
            field=models.BooleanField(
                default=False,
                help_text='Indicates if this campaign is currently being used in a CEP workflow'
            ),
        ),
        
        # Create CEPWorkflow model
        migrations.CreateModel(
            name='CEPWorkflow',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('subscription_tier', models.CharField(
                    choices=[('bronze', 'Bronze'), ('silver', 'Silver'), ('gold', 'Gold')],
                    default='bronze',
                    help_text='Subscription tier determining available engagement actions',
                    max_length=10
                )),
                ('status', models.CharField(
                    choices=[
                        ('pending', 'Pending'),
                        ('running', 'Running'),
                        ('paused', 'Paused'),
                        ('completed', 'Completed'),
                        ('failed', 'Failed'),
                        ('stopped', 'Stopped')
                    ],
                    default='pending',
                    help_text='Current status of the CEP workflow',
                    max_length=10
                )),
                ('airflow_dag_id', models.CharField(
                    blank=True,
                    help_text='Airflow DAG ID for this CEP workflow',
                    max_length=255,
                    null=True
                )),
                ('airflow_run_id', models.CharField(
                    blank=True,
                    help_text='Airflow run ID for this CEP workflow',
                    max_length=255,
                    null=True
                )),
                ('follow_progress', models.FloatField(
                    default=0.0,
                    help_text='Progress percentage for follow actions (0-100)'
                )),
                ('like_progress', models.FloatField(
                    default=0.0,
                    help_text='Progress percentage for like actions (0-100)'
                )),
                ('comment_progress', models.FloatField(
                    default=0.0,
                    help_text='Progress percentage for comment actions (0-100)'
                )),
                ('dm_progress', models.FloatField(
                    default=0.0,
                    help_text='Progress percentage for direct message actions (0-100)'
                )),
                ('whitelist_count', models.IntegerField(
                    default=0,
                    help_text='Number of accounts in the whitelist'
                )),
                ('started_at', models.DateTimeField(
                    blank=True,
                    help_text='When the CEP workflow was started',
                    null=True
                )),
                ('completed_at', models.DateTimeField(
                    blank=True,
                    help_text='When the CEP workflow was completed',
                    null=True
                )),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('campaign', models.OneToOneField(
                    help_text='Campaign associated with this CEP workflow',
                    on_delete=django.db.models.deletion.CASCADE,
                    related_name='cep_workflow',
                    to='campaigns.campaign'
                )),
            ],
            options={
                'verbose_name': 'CEP Workflow',
                'verbose_name_plural': 'CEP Workflows',
                'db_table': 'campaigns_cep_workflow',
            },
        ),
    ]
