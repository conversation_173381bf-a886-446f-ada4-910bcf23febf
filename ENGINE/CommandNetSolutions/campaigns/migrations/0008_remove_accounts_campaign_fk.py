# Generated manually to remove foreign key constraint from accounts table

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ("campaigns", "0007_fix_creator_constraint"),
    ]

    operations = [
        migrations.RunSQL(
            "ALTER TABLE accounts DROP CONSTRAINT IF EXISTS accounts_campaign_id_c9ddf721_fk_campaigns_campaign_id;",
            reverse_sql="ALTER TABLE accounts ADD CONSTRAINT accounts_campaign_id_c9ddf721_fk_campaigns_campaign_id FOREIGN KEY (campaign_id) REFERENCES campaigns_campaign(id);"
        ),
    ]
