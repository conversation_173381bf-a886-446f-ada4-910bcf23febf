# Generated by Django 4.2.16 on 2025-05-12 20:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("campaigns", "0001_initial"),
    ]

    operations = [
        migrations.CreateModel(
            name="Campaign",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=120)),
                ("description", models.TextField(blank=True, null=True)),
                ("dmp_conf", models.JSONField(default=dict)),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("draft", "Draft"),
                            ("pending", "Pending"),
                            ("running", "Running"),
                            ("paused", "Paused"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("stopped", "Stopped"),
                        ],
                        default="draft",
                        max_length=10,
                    ),
                ),
                (
                    "target_type",
                    models.CharField(
                        choices=[
                            ("location", "Location Based"),
                            ("username", "Username Based"),
                            ("mixed", "Mixed (Both Types)"),
                        ],
                        default="location",
                        max_length=20,
                    ),
                ),
                (
                    "audience_type",
                    models.CharField(
                        choices=[
                            ("profile", "Profile Only"),
                            ("followers", "Followers Only"),
                            ("following", "Following Only"),
                            ("both", "Followers and Following"),
                        ],
                        default="profile",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
                ("airflow_run_id", models.CharField(default="pending", max_length=200)),
                (
                    "is_favorite",
                    models.BooleanField(
                        default=False,
                        help_text="Mark as favorite to easily reuse this campaign's settings",
                    ),
                ),
                (
                    "airflow_dag_id",
                    models.CharField(blank=True, max_length=255, null=True),
                ),
            ],
            options={
                "verbose_name": "Campaign",
                "verbose_name_plural": "Campaigns",
                "db_table": "campaigns_campaign",
                "managed": True,
                "default_permissions": ("add", "change", "delete", "view"),
            },
        ),
        migrations.CreateModel(
            name="CampaignResult",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("total_accounts_found", models.IntegerField(default=0)),
                ("total_accounts_processed", models.IntegerField(default=0)),
                ("total_accounts_pending", models.IntegerField(default=0)),
                (
                    "total_accounts_tagged",
                    models.IntegerField(
                        default=0,
                        help_text="Number of accounts that received at least one tag",
                    ),
                ),
                (
                    "total_accounts_whitelisted",
                    models.IntegerField(
                        default=0, help_text="Number of accounts added to whitelist"
                    ),
                ),
                (
                    "average_confidence_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Average confidence score across all tag matches",
                    ),
                ),
                (
                    "analysis_duration",
                    models.FloatField(
                        default=0.0, help_text="Total analysis duration in seconds"
                    ),
                ),
                (
                    "engagement_rate",
                    models.FloatField(
                        default=0.0,
                        help_text="Average engagement rate of processed accounts",
                    ),
                ),
                (
                    "quality_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Overall quality score of the campaign results",
                    ),
                ),
                ("last_processed_at", models.DateTimeField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Campaign Result",
                "verbose_name_plural": "Campaign Results",
                "db_table": "campaigns_campaignresult",
            },
        ),
        migrations.CreateModel(
            name="CampaignROI",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "cost_type",
                    models.CharField(
                        choices=[
                            ("fixed", "Fixed Cost"),
                            ("hourly", "Hourly Rate"),
                            ("per_account", "Per Account"),
                            ("per_whitelist", "Per Whitelist Entry"),
                        ],
                        default="fixed",
                        max_length=20,
                    ),
                ),
                (
                    "cost_value",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                (
                    "total_cost",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                (
                    "estimated_value",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                (
                    "actual_value",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                (
                    "roi_percentage",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                (
                    "conversion_rate",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=5),
                ),
                (
                    "cost_per_account",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                (
                    "cost_per_whitelist",
                    models.DecimalField(decimal_places=2, default=0.0, max_digits=10),
                ),
                ("notes", models.TextField(blank=True, null=True)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Campaign ROI",
                "verbose_name_plural": "Campaign ROIs",
                "db_table": "campaigns_campaign_roi",
            },
        ),
        migrations.CreateModel(
            name="CampaignTag",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "is_required",
                    models.BooleanField(
                        default=False,
                        help_text="If true, this tag is required for whitelist inclusion",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Campaign Tag",
                "verbose_name_plural": "Campaign Tags",
                "db_table": "campaigns_campaign_tag",
                "ordering": ["tag__name"],
            },
        ),
        migrations.CreateModel(
            name="CampaignTagCondition",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("field", models.CharField(max_length=50)),
                (
                    "field_type",
                    models.CharField(
                        choices=[
                            ("string", "String"),
                            ("number", "Number"),
                            ("boolean", "Boolean"),
                            ("array", "Array"),
                            ("date", "Date"),
                        ],
                        default="string",
                        max_length=10,
                    ),
                ),
                (
                    "operator",
                    models.CharField(
                        choices=[
                            ("contains", "Contains"),
                            ("not_contains", "Does Not Contain"),
                            ("equals", "Equals"),
                            ("not_equals", "Does Not Equal"),
                            ("starts_with", "Starts With"),
                            ("ends_with", "Ends With"),
                            ("matches", "Matches Regex"),
                            ("isnull", "Is Null"),
                            ("notnull", "Is Not Null"),
                            ("isempty", "Is Empty"),
                            ("isnotempty", "Is Not Empty"),
                            ("gt", "Greater Than"),
                            ("gte", "Greater Than or Equal"),
                            ("lt", "Less Than"),
                            ("lte", "Less Than or Equal"),
                            ("between", "Between"),
                            ("is_true", "Is True"),
                            ("is_false", "Is False"),
                            ("contains_any", "Contains Any"),
                            ("contains_all", "Contains All"),
                            ("not_contains_any", "Does Not Contain Any"),
                            ("length", "Has Length"),
                            ("before", "Before"),
                            ("after", "After"),
                            ("between_dates", "Between Dates"),
                            ("older_than", "Older Than"),
                            ("newer_than", "Newer Than"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "value",
                    models.JSONField(
                        help_text="Value to compare against (stored as JSON)"
                    ),
                ),
                (
                    "score",
                    models.IntegerField(
                        default=1, help_text="Score contribution for this condition"
                    ),
                ),
                (
                    "required",
                    models.BooleanField(
                        default=True,
                        help_text="If true, this condition must pass for the rule to match",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Tag Rule Condition",
                "verbose_name_plural": "Tag Rule Conditions",
                "db_table": "campaigns_tag_condition",
            },
        ),
        migrations.CreateModel(
            name="CampaignTagRule",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("tag", models.CharField(max_length=50)),
                ("description", models.TextField(blank=True, null=True)),
                ("active", models.BooleanField(default=True)),
                (
                    "is_global",
                    models.BooleanField(
                        default=False,
                        help_text="If true, this tag is available to all campaigns",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Campaign Tag Rule",
                "verbose_name_plural": "Campaign Tag Rules",
                "db_table": "campaigns_tag_rule",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="DynamicTag",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "tag_type",
                    models.CharField(
                        choices=[
                            ("keyword", "Keyword Match"),
                            ("regex", "Regular Expression"),
                            ("sentiment", "Sentiment Analysis"),
                            ("category", "Category Classification"),
                            ("ml", "Machine Learning"),
                            ("nlp", "Natural Language Processing"),
                        ],
                        default="keyword",
                        max_length=20,
                    ),
                ),
                (
                    "pattern",
                    models.TextField(
                        help_text="Pattern to match (keyword, regex, etc.)"
                    ),
                ),
                (
                    "field",
                    models.CharField(
                        help_text="Account field to analyze (bio, interests, etc.)",
                        max_length=50,
                    ),
                ),
                (
                    "is_global",
                    models.BooleanField(
                        default=False,
                        help_text="If true, this tag is available to all campaigns",
                    ),
                ),
                (
                    "is_system",
                    models.BooleanField(
                        default=False,
                        help_text="If true, this tag is a system tag and cannot be deleted",
                    ),
                ),
                (
                    "weight",
                    models.FloatField(
                        default=1.0, help_text="Weight factor for scoring calculations"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Dynamic Tag",
                "verbose_name_plural": "Dynamic Tags",
                "db_table": "campaigns_dynamic_tag",
                "ordering": ["-is_system", "category__name", "name"],
            },
        ),
        migrations.CreateModel(
            name="LocationTarget",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("country", models.CharField(max_length=100)),
                ("city", models.CharField(max_length=100)),
                ("location_id", models.CharField(max_length=100)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "db_table": "campaigns_locationtarget",
            },
        ),
        migrations.CreateModel(
            name="Notification",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("title", models.CharField(max_length=255)),
                ("message", models.TextField()),
                (
                    "notification_type",
                    models.CharField(
                        choices=[
                            ("info", "Information"),
                            ("success", "Success"),
                            ("warning", "Warning"),
                            ("error", "Error"),
                        ],
                        default="info",
                        max_length=20,
                    ),
                ),
                ("link", models.CharField(blank=True, max_length=255, null=True)),
                ("data", models.JSONField(blank=True, default=dict)),
                ("is_read", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("read_at", models.DateTimeField(blank=True, null=True)),
            ],
            options={
                "verbose_name": "User Notification",
                "verbose_name_plural": "User Notifications",
                "db_table": "campaigns_user_notification",
                "ordering": ["-created_at"],
            },
        ),
        migrations.CreateModel(
            name="TagAnalysisResult",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("account_id", models.CharField(default="unknown", max_length=255)),
                (
                    "matched",
                    models.BooleanField(
                        default=False, help_text="Whether the tag matched the account"
                    ),
                ),
                (
                    "confidence_score",
                    models.FloatField(
                        default=0.0,
                        help_text="Confidence score for the match (0.0-1.0)",
                    ),
                ),
                (
                    "match_details",
                    models.JSONField(
                        blank=True,
                        default=dict,
                        help_text="Detailed information about the match",
                    ),
                ),
                (
                    "analysis_mode",
                    models.CharField(
                        default="manual",
                        help_text="Analysis mode used for this result",
                        max_length=20,
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "verbose_name": "Tag Analysis Result",
                "verbose_name_plural": "Tag Analysis Results",
                "db_table": "campaigns_tag_analysis_result",
            },
        ),
        migrations.CreateModel(
            name="TagCategory",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "color",
                    models.CharField(
                        default="#6c757d",
                        help_text="Color code for visual representation",
                        max_length=20,
                    ),
                ),
                (
                    "icon",
                    models.CharField(
                        blank=True,
                        help_text="Icon name (e.g., 'tag', 'user', etc.)",
                        max_length=50,
                        null=True,
                    ),
                ),
                (
                    "priority",
                    models.IntegerField(
                        default=0, help_text="Higher priority categories appear first"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Tag Category",
                "verbose_name_plural": "Tag Categories",
                "db_table": "campaigns_tag_category",
                "ordering": ["-priority", "name"],
            },
        ),
        migrations.CreateModel(
            name="TagGroup",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("name", models.CharField(max_length=100)),
                ("description", models.TextField(blank=True, null=True)),
                (
                    "color",
                    models.CharField(
                        default="#6c757d",
                        help_text="Color code for visual representation",
                        max_length=20,
                    ),
                ),
                (
                    "is_global",
                    models.BooleanField(
                        default=False,
                        help_text="If true, this tag group is available to all campaigns",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Tag Group",
                "verbose_name_plural": "Tag Groups",
                "db_table": "campaigns_tag_group",
                "ordering": ["name"],
            },
        ),
        migrations.CreateModel(
            name="TagMetrics",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "usage_count",
                    models.IntegerField(
                        default=0, help_text="Number of times this tag has been used"
                    ),
                ),
                (
                    "match_count",
                    models.IntegerField(
                        default=0, help_text="Number of accounts that matched this tag"
                    ),
                ),
                (
                    "conversion_rate",
                    models.FloatField(
                        default=0.0,
                        help_text="Percentage of matches that led to conversions",
                    ),
                ),
                (
                    "precision",
                    models.FloatField(
                        default=0.0,
                        help_text="Precision score (true positives / (true positives + false positives))",
                    ),
                ),
                (
                    "recall",
                    models.FloatField(
                        default=0.0,
                        help_text="Recall score (true positives / (true positives + false negatives))",
                    ),
                ),
                (
                    "f1_score",
                    models.FloatField(
                        default=0.0,
                        help_text="F1 score (2 * precision * recall / (precision + recall))",
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Tag Metrics",
                "verbose_name_plural": "Tag Metrics",
                "db_table": "campaigns_tag_metrics",
            },
        ),
        migrations.CreateModel(
            name="UsernameTarget",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("username", models.CharField(max_length=255)),
                (
                    "audience_type",
                    models.CharField(
                        choices=[
                            ("profile", "Profile Only"),
                            ("followers", "Followers Only"),
                            ("following", "Following Only"),
                            ("both", "Followers and Following"),
                        ],
                        default="profile",
                        max_length=20,
                    ),
                ),
                ("processed", models.BooleanField(default=False)),
                ("created_at", models.DateTimeField(auto_now_add=True)),
            ],
            options={
                "db_table": "campaigns_usernametarget",
            },
        ),
        migrations.CreateModel(
            name="WorkflowExecution",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    "campaign_id",
                    models.CharField(help_text="ID of the campaign", max_length=36),
                ),
                (
                    "workflow_name",
                    models.CharField(
                        help_text="Name of the PyFlow workflow file", max_length=255
                    ),
                ),
                (
                    "workflow_path",
                    models.CharField(
                        help_text="Path to the PyFlow workflow file", max_length=512
                    ),
                ),
                (
                    "workflow_type",
                    models.CharField(
                        choices=[
                            ("collection", "Account Collection"),
                            ("analysis", "Account Analysis"),
                            ("tagging", "Account Tagging"),
                            ("follow", "Follow"),
                            ("like", "Like"),
                            ("comment", "Comment"),
                            ("dm", "Direct Message"),
                            ("cep", "CEP"),
                        ],
                        max_length=20,
                    ),
                ),
                (
                    "status",
                    models.CharField(
                        choices=[
                            ("pending", "Pending"),
                            ("running", "Running"),
                            ("completed", "Completed"),
                            ("failed", "Failed"),
                            ("cancelled", "Cancelled"),
                        ],
                        default="pending",
                        max_length=20,
                    ),
                ),
                ("start_time", models.DateTimeField(auto_now_add=True)),
                ("end_time", models.DateTimeField(blank=True, null=True)),
                (
                    "duration",
                    models.FloatField(default=0.0, help_text="Duration in seconds"),
                ),
                (
                    "progress",
                    models.FloatField(
                        default=0.0, help_text="Progress percentage (0-100)"
                    ),
                ),
                (
                    "total_items",
                    models.IntegerField(
                        default=0, help_text="Total number of items to process"
                    ),
                ),
                (
                    "processed_items",
                    models.IntegerField(
                        default=0, help_text="Number of items processed"
                    ),
                ),
                (
                    "successful_items",
                    models.IntegerField(
                        default=0, help_text="Number of items processed successfully"
                    ),
                ),
                (
                    "failed_items",
                    models.IntegerField(
                        default=0, help_text="Number of items that failed processing"
                    ),
                ),
                (
                    "error_message",
                    models.TextField(
                        blank=True,
                        help_text="Error message if workflow failed",
                        null=True,
                    ),
                ),
                (
                    "log_file",
                    models.CharField(
                        blank=True,
                        help_text="Path to the workflow log file",
                        max_length=512,
                        null=True,
                    ),
                ),
                (
                    "parameters",
                    models.JSONField(
                        default=dict, help_text="Parameters passed to the workflow"
                    ),
                ),
                (
                    "results",
                    models.JSONField(
                        default=dict, help_text="Results of the workflow execution"
                    ),
                ),
                ("created_at", models.DateTimeField(auto_now_add=True)),
                ("updated_at", models.DateTimeField(auto_now=True)),
            ],
            options={
                "verbose_name": "Workflow Execution",
                "verbose_name_plural": "Workflow Executions",
                "db_table": "campaigns_workflow_execution",
                "ordering": ["-start_time"],
            },
        ),
        migrations.CreateModel(
            name="WorkflowProgressUpdate",
            fields=[
                (
                    "id",
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                ("timestamp", models.DateTimeField(auto_now_add=True)),
                ("processed_items", models.IntegerField(default=0)),
                ("successful_items", models.IntegerField(default=0)),
                ("failed_items", models.IntegerField(default=0)),
                ("progress", models.FloatField(default=0.0)),
                ("message", models.TextField(blank=True, null=True)),
                ("details", models.JSONField(default=dict)),
                (
                    "workflow_execution",
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name="progress_updates",
                        to="campaigns.workflowexecution",
                    ),
                ),
            ],
            options={
                "verbose_name": "Workflow Progress Update",
                "verbose_name_plural": "Workflow Progress Updates",
                "db_table": "campaigns_workflow_progress_update",
                "ordering": ["-timestamp"],
            },
        ),
        migrations.AddIndex(
            model_name="workflowexecution",
            index=models.Index(
                fields=["campaign_id", "workflow_type"],
                name="campaigns_w_campaig_9e6090_idx",
            ),
        ),
        migrations.AddIndex(
            model_name="workflowexecution",
            index=models.Index(fields=["status"], name="campaigns_w_status_9b5550_idx"),
        ),
        migrations.AddIndex(
            model_name="workflowexecution",
            index=models.Index(
                fields=["start_time"], name="campaigns_w_start_t_53fe83_idx"
            ),
        ),
        migrations.AddField(
            model_name="usernametarget",
            name="campaign",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="username_targets",
                to="campaigns.campaign",
            ),
        ),
        migrations.AddField(
            model_name="tagmetrics",
            name="tag",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="metrics",
                to="campaigns.dynamictag",
            ),
        ),
        migrations.AddField(
            model_name="tagcategory",
            name="parent",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name="children",
                to="campaigns.tagcategory",
            ),
        ),
        migrations.AddField(
            model_name="taganalysisresult",
            name="campaign",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="tag_analyses",
                to="campaigns.campaign",
            ),
        ),
        migrations.AddField(
            model_name="taganalysisresult",
            name="tag",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="analyses",
                to="campaigns.dynamictag",
            ),
        ),
        migrations.AddField(
            model_name="notification",
            name="user",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="notifications",
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name="locationtarget",
            name="campaign",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="location_targets",
                to="campaigns.campaign",
            ),
        ),
        migrations.AddField(
            model_name="dynamictag",
            name="category",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="tags",
                to="campaigns.tagcategory",
            ),
        ),
        migrations.AddField(
            model_name="dynamictag",
            name="related_tags",
            field=models.ManyToManyField(
                blank=True,
                help_text="Tags that are related to this tag",
                related_name="related_to",
                to="campaigns.dynamictag",
            ),
        ),
        migrations.AddField(
            model_name="dynamictag",
            name="tag_group",
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name="tags",
                to="campaigns.taggroup",
            ),
        ),
        migrations.AddField(
            model_name="campaigntagcondition",
            name="rule",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="conditions",
                to="campaigns.campaigntagrule",
            ),
        ),
        migrations.AddField(
            model_name="campaigntag",
            name="campaign",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="campaign_tags",
                to="campaigns.campaign",
            ),
        ),
        migrations.AddField(
            model_name="campaigntag",
            name="tag",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="campaign_assignments",
                to="campaigns.dynamictag",
            ),
        ),
        migrations.AddField(
            model_name="campaignroi",
            name="campaign",
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="roi",
                to="campaigns.campaign",
            ),
        ),
        migrations.AddField(
            model_name="campaignresult",
            name="campaign",
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name="results",
                to="campaigns.campaign",
            ),
        ),
        migrations.AddField(
            model_name="campaign",
            name="creator",
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.DO_NOTHING,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.CreateModel(
            name="TagRuleCondition",
            fields=[],
            options={
                "verbose_name": "Tag Rule Condition",
                "verbose_name_plural": "Tag Rule Conditions",
                "proxy": True,
                "indexes": [],
                "constraints": [],
            },
            bases=("campaigns.campaigntagcondition",),
        ),
        migrations.AddIndex(
            model_name="taganalysisresult",
            index=models.Index(
                fields=["account_id", "campaign"], name="campaigns_t_account_f008a3_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="taganalysisresult",
            index=models.Index(
                fields=["tag", "matched"], name="campaigns_t_tag_id_e8eaae_idx"
            ),
        ),
        migrations.AddIndex(
            model_name="taganalysisresult",
            index=models.Index(
                fields=["created_at"], name="campaigns_t_created_c043f7_idx"
            ),
        ),
        migrations.AlterUniqueTogether(
            name="taganalysisresult",
            unique_together={("account_id", "campaign", "tag")},
        ),
        migrations.AlterUniqueTogether(
            name="campaigntag",
            unique_together={("campaign", "tag")},
        ),
    ]
