# Generated by Django 4.2.16 on 2025-05-17 14:50

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ("campaigns", "0003_add_cep_fields"),
    ]

    operations = [
        migrations.AlterModelOptions(
            name="taggroup",
            options={
                "ordering": ["-priority", "name"],
                "verbose_name": "Tag Group",
                "verbose_name_plural": "Tag Groups",
            },
        ),
        migrations.AddField(
            model_name="campaigntagrule",
            name="logic",
            field=models.CharField(
                choices=[
                    ("all", "All conditions must match"),
                    ("any", "Any condition can match"),
                ],
                default="all",
                help_text="Determines how conditions are combined",
                max_length=10,
            ),
        ),
        migrations.AlterField(
            model_name="campaigntagrule",
            name="name",
            field=models.Char<PERSON>ield(blank=True, max_length=100, null=True),
        ),
        migrations.AlterField(
            model_name="campaigntagrule",
            name="tag",
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
    ]
