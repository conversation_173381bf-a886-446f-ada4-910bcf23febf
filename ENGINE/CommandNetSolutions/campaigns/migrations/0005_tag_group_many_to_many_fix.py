# Generated by Django 4.2.16 on 2025-05-25 22:30

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("campaigns", "0004_alter_taggroup_options_campaigntagrule_logic_and_more"),
    ]

    operations = [
        migrations.RemoveField(
            model_name="dynamictag",
            name="tag_group",
        ),
        migrations.AddField(
            model_name="dynamictag",
            name="tag_groups",
            field=models.ManyToManyField(
                blank=True,
                help_text="Tag groups this tag belongs to",
                related_name="tags",
                to="campaigns.taggroup",
            ),
        ),
    ]
