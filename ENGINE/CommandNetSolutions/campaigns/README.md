# Campaigns App

The Campaigns app is a Django application for managing data collection campaigns in the CommandNetSolutions system. It integrates with Airflow to automate the execution of data collection workflows.

## Features

- Create and manage data collection campaigns
- Target Instagram accounts by location or username
- Configure audience targeting (profile, followers, following, or both)
- Schedule campaigns for future execution
- Track campaign progress and results
- Integration with Airflow for workflow automation

## Setup

1. Make sure the app is included in `INSTALLED_APPS` in your Django settings:

```python
INSTALLED_APPS = [
    # ...
    'campaigns',
    # ...
]
```

2. Add the app's URLs to your project's `urls.py`:

```python
urlpatterns += [
    path('campaigns/', include('campaigns.urls', namespace='campaigns')),
]
```

3. Run migrations to create the necessary database tables:

```bash
python manage.py makemigrations campaigns
python manage.py migrate
```

4. Import location data (if using location-based targeting):

```bash
python manage.py import_locations /path/to/locations.zip
```

## Usage

### Creating a Campaign

1. Navigate to the Campaigns dashboard at `/campaigns/`
2. Click "Create New Campaign"
3. Fill in the campaign details:
   - Name and description
   - Target type (location or username)
   - Audience type (profile, followers, following, or both)
   - Optional scheduling information

### Adding Targets

After creating a campaign, you can add targets:

- For location-based campaigns, search for and select locations
- For username-based campaigns, enter usernames directly

### Launching a Campaign

Once targets are added, you can launch the campaign:

1. Go to the campaign detail page
2. Click "Launch Campaign"
3. The system will trigger the appropriate Airflow DAG with the campaign configuration

## Integration with Airflow

The app integrates with Airflow through the `AirflowService` class, which:

1. Triggers DAGs with campaign configuration
2. Monitors DAG execution status
3. Updates campaign results based on DAG output

The default DAG for campaign data collection is `campaign_data_collection`.

## Models

- `Campaign`: Stores campaign information and configuration
- `LocationTarget`: Stores location-based targets for a campaign
- `UsernameTarget`: Stores username-based targets for a campaign
- `CampaignResult`: Stores campaign execution results and statistics
