"""
Celery tasks for campaign operations.
"""
from celery import shared_task
from celery.utils.log import get_task_logger
from django.utils import timezone

from campaigns.domains.analysis import AnalysisDomain
from campaigns.repositories.campaign_repository import CampaignRepository
from instagram.tagging_engine import AutoTaggingSystem

logger = get_task_logger(__name__)

@shared_task(bind=True)
def analyze_campaign_accounts_task(self, campaign_id, batch_size=100):
    """
    Celery task to analyze campaign accounts.
    Supports progress tracking and error handling.
    
    Args:
        campaign_id (uuid): Campaign ID
        batch_size (int): Number of accounts to process in each batch
        
    Returns:
        dict: Analysis results
    """
    logger.info(f"Starting analysis for campaign {campaign_id}")
    
    # Initialize repository and domain
    repository = CampaignRepository()
    tagging_system = AutoTaggingSystem()
    domain = AnalysisDomain(repository, tagging_system)
    
    # Get total accounts to process
    from instagram.models import Accounts
    from campaigns.models import Campaign
    
    try:
        campaign = Campaign.objects.get(id=campaign_id)
        total_accounts = Accounts.objects.filter(campaign=campaign).count()
        
        if total_accounts == 0:
            logger.warning(f"No accounts found for campaign {campaign_id}")
            return {"status": "completed", "processed": 0, "total": 0}
        
        # Update task state with progress information
        self.update_state(
            state='PROGRESS',
            meta={'current': 0, 'total': total_accounts}
        )
        
        # Process accounts with progress tracking
        processed = 0
        white_listed = 0
        
        # Get accounts to analyze
        accounts = Accounts.objects.filter(campaign=campaign)
        
        # Get analysis settings
        try:
            analysis_settings = campaign.analysis_settings
            
            # Apply filters from analysis settings
            if analysis_settings.min_followers is not None:
                accounts = accounts.filter(followers__gte=analysis_settings.min_followers)
                
            if analysis_settings.max_followers is not None:
                accounts = accounts.filter(followers__lte=analysis_settings.max_followers)
        except:
            # If no analysis settings, use all accounts
            pass
        
        # Update total after filtering
        total_accounts = accounts.count()
        
        # Process in batches
        for i in range(0, total_accounts, batch_size):
            batch = accounts[i:i+batch_size]
            
            for account in batch:
                # Process account
                result = tagging_system.process_account(account)
                
                # Apply dynamic tags if enabled
                try:
                    if analysis_settings.enable_dynamic_tagging:
                        from campaigns.domains.analysis import AnalysisDomain
                        dynamic_tags = AnalysisDomain._process_dynamic_tags(
                            None, account, analysis_settings.dynamic_tags.all()
                        )
                        result['tags'].extend(dynamic_tags)
                except Exception as e:
                    logger.warning(f"Error processing dynamic tags: {str(e)}")
                
                # Update whitelist
                from instagram.models import WhiteListEntry
                qualifies = bool(result.get('privileges'))
                
                if qualifies:
                    WhiteListEntry.objects.update_or_create(
                        account=account,
                        defaults={
                            'tags': result['tags'],
                            'is_auto': True,
                            **result['privileges']
                        }
                    )
                    white_listed += 1
                else:
                    # Remove from whitelist if exists
                    WhiteListEntry.objects.filter(account=account).delete()
                
                processed += 1
                
                # Update progress every 10 accounts
                if processed % 10 == 0:
                    self.update_state(
                        state='PROGRESS',
                        meta={'current': processed, 'total': total_accounts}
                    )
        
        # Update campaign result
        from campaigns.models import CampaignResult
        result, created = CampaignResult.objects.get_or_create(campaign=campaign)
        result.total_accounts_processed = processed
        result.last_processed_at = timezone.now()
        result.save()
        
        logger.info(f"Completed analysis for campaign {campaign_id}")
        return {
            "status": "completed",
            "processed": processed,
            "white_listed": white_listed,
            "total": total_accounts,
            "percentage": (white_listed / total_accounts * 100) if total_accounts > 0 else 0
        }
        
    except Exception as e:
        logger.exception(f"Error analyzing campaign {campaign_id}: {str(e)}")
        return {
            "status": "error",
            "error": str(e)
        }

@shared_task
def schedule_campaign_analysis():
    """
    Scheduled task to analyze campaigns based on their analysis frequency.
    """
    logger.info("Running scheduled campaign analysis")
    
    from campaigns.models import Campaign, CampaignAnalysisSettings
    from django.db.models import Q
    
    # Get campaigns with auto-analyze enabled
    settings = CampaignAnalysisSettings.objects.filter(
        auto_analyze=True,
        campaign__status='completed'
    ).select_related('campaign')
    
    # Group by frequency
    for setting in settings:
        frequency = setting.analysis_frequency
        campaign = setting.campaign
        
        # Skip manual analysis
        if frequency == 'manual':
            continue
            
        # Get last analysis time
        last_analyzed = None
        if hasattr(campaign, 'results') and campaign.results.last_processed_at:
            last_analyzed = campaign.results.last_processed_at
        
        # Determine if analysis is due
        should_analyze = False
        
        if not last_analyzed:
            # Never analyzed
            should_analyze = True
        elif frequency == 'immediate':
            # Already analyzed after collection
            continue
        elif frequency == 'hourly':
            # Check if more than an hour has passed
            from django.utils import timezone
            should_analyze = (timezone.now() - last_analyzed).total_seconds() > 3600
        elif frequency == 'daily':
            # Check if more than a day has passed
            from django.utils import timezone
            should_analyze = (timezone.now() - last_analyzed).total_seconds() > 86400
        
        # Schedule analysis if due
        if should_analyze:
            logger.info(f"Scheduling analysis for campaign {campaign.id}")
            analyze_campaign_accounts_task.delay(str(campaign.id))
