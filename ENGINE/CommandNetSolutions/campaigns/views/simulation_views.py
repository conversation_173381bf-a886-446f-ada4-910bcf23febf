"""
Views for campaign simulation.

This module contains views for the campaign simulation dashboard and related functionality.
"""
import json
import csv
import logging
import random
from django.shortcuts import render, get_object_or_404, redirect
from django.http import HttpResponse, JsonResponse
# Removed login_required to disable authentication requirements
# from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils import timezone
from django.db.models import Count, Avg, Max, Min, F, Q
from django.core.paginator import Paginator

from campaigns.models import (
    Campaign, CampaignTag, TagAnalysisResult, WorkflowExecution, WorkflowProgressUpdate
)
from instagram.models import Accounts, WhiteListEntry

# Set up logging
logger = logging.getLogger(__name__)

# Removed login_required decorator to disable authentication requirements
# @login_required
def simulation_dashboard(request, campaign_id):
    """
    Display the whitelist analytics dashboard for a campaign.
    """
    campaign = get_object_or_404(Campaign, id=campaign_id)

    # Get workflow executions
    workflows = WorkflowExecution.objects.filter(campaign_id=str(campaign.id)).order_by('-start_time')

    # Get all accounts for this campaign
    accounts = Accounts.objects.filter(campaign_id=str(campaign.id))
    accounts_count = accounts.count()

    # Get tag analysis results
    tag_results = TagAnalysisResult.objects.filter(campaign=campaign, matched=True)
    tagged_accounts = set(tag_results.values_list('account_id', flat=True))
    tagged_accounts_count = len(tagged_accounts)

    # Get whitelist entries
    whitelist_entries = WhiteListEntry.objects.filter(account__campaign_id=str(campaign.id))
    whitelist_count = whitelist_entries.count()

    # Calculate whitelist-specific metrics
    conversion_rate = (whitelist_count / tagged_accounts_count * 100) if tagged_accounts_count > 0 else 0

    # Calculate privilege distribution
    dm_count = whitelist_entries.filter(dm=True).count()
    follow_count = whitelist_entries.filter(follow=True).count()
    comment_count = whitelist_entries.filter(comment=True).count()
    like_count = whitelist_entries.filter(post_like=True).count()
    discover_count = whitelist_entries.filter(discover=True).count()
    favorite_count = whitelist_entries.filter(favorite=True).count()

    # Calculate average privileges per account
    total_privileges = dm_count + follow_count + comment_count + like_count + discover_count + favorite_count
    avg_privileges = (total_privileges / whitelist_count) if whitelist_count > 0 else 0

    # Count high-value accounts (accounts with all privileges)
    high_value_accounts = whitelist_entries.filter(
        dm=True, follow=True, comment=True, post_like=True, discover=True, favorite=True
    ).count()

    # Calculate quality metrics
    verified_whitelist_count = whitelist_entries.filter(account__is_verified=True).count()
    verified_percentage = (verified_whitelist_count / whitelist_count * 100) if whitelist_count > 0 else 0

    # Premium accounts (accounts with 4+ privileges)
    premium_accounts = 0
    for entry in whitelist_entries:
        privilege_count = sum([entry.dm, entry.follow, entry.comment, entry.post_like, entry.discover, entry.favorite])
        if privilege_count >= 4:
            premium_accounts += 1

    premium_percentage = (premium_accounts / whitelist_count * 100) if whitelist_count > 0 else 0

    # High engagement accounts (accounts with >50K followers)
    high_engagement_count = whitelist_entries.filter(account__followers__gt=50000).count()
    engagement_percentage = (high_engagement_count / whitelist_count * 100) if whitelist_count > 0 else 0

    # Get campaign tags
    campaign_tags = CampaignTag.objects.filter(campaign=campaign).select_related('tag')

    # Get tag match counts for chart
    tag_match_counts = []
    tag_names = []
    tag_effectiveness = []  # Conversion rate (% of matched accounts that got whitelisted)

    for campaign_tag in campaign_tags:
        # Count accounts that matched this tag
        matched_accounts = TagAnalysisResult.objects.filter(
            campaign=campaign,
            tag=campaign_tag.tag,
            matched=True
        )
        matched_count = matched_accounts.count()

        # Count how many of those matched accounts were whitelisted
        matched_account_ids = matched_accounts.values_list('account_id', flat=True)
        whitelisted_count = WhiteListEntry.objects.filter(
            account__username__in=matched_account_ids,
            account__campaign_id=str(campaign.id)
        ).count()

        # Calculate effectiveness (conversion rate)
        effectiveness = round((whitelisted_count / matched_count * 100) if matched_count > 0 else 0)

        tag_match_counts.append(matched_count)
        tag_names.append(campaign_tag.tag.name)
        tag_effectiveness.append(effectiveness)

    # Get follower distribution
    follower_ranges = ['0-1K', '1K-5K', '5K-10K', '10K-50K', '50K-100K', '100K+']
    follower_distribution = [0, 0, 0, 0, 0, 0]

    for account in accounts:
        if account.followers is None:
            continue

        if account.followers < 1000:
            follower_distribution[0] += 1
        elif account.followers < 5000:
            follower_distribution[1] += 1
        elif account.followers < 10000:
            follower_distribution[2] += 1
        elif account.followers < 50000:
            follower_distribution[3] += 1
        elif account.followers < 100000:
            follower_distribution[4] += 1
        else:
            follower_distribution[5] += 1

    # Get account type distribution
    account_types = accounts.values('account_type').annotate(count=Count('account_type'))
    account_types_labels = []
    account_types_data = []

    for account_type in account_types:
        if account_type['account_type']:
            account_types_labels.append(account_type['account_type'].capitalize())
            account_types_data.append(account_type['count'])

    # Calculate engagement rates
    engagement_ranges = ['0-1%', '1-3%', '3-5%', '5-10%', '10%+']
    engagement_distribution = [0, 0, 0, 0, 0]

    # For engagement vs. followers scatter plot
    engagement_followers_data = []

    for account in accounts:
        if account.followers is None or account.followers == 0:
            continue

        # Simulate engagement rate based on followers and posts
        # This is a simplified calculation for demonstration purposes
        likes_per_post = int(account.followers * random.uniform(0.01, 0.2))
        comments_per_post = int(likes_per_post * random.uniform(0.01, 0.1))
        engagement_rate = ((likes_per_post + comments_per_post) / account.followers) * 100

        # Add to engagement distribution
        if engagement_rate < 1:
            engagement_distribution[0] += 1
        elif engagement_rate < 3:
            engagement_distribution[1] += 1
        elif engagement_rate < 5:
            engagement_distribution[2] += 1
        elif engagement_rate < 10:
            engagement_distribution[3] += 1
        else:
            engagement_distribution[4] += 1

        # Add to engagement vs. followers scatter data
        engagement_followers_data.append({
            'x': account.followers,
            'y': engagement_rate,
            'username': account.username,
            'posts': account.number_of_posts,
            'account_type': account.account_type
        })

    # Generate collection timeline data
    # Group accounts by collection date (hour)
    collection_timeline_labels = []
    collection_timeline_data = []

    # If we have collection dates, use them to create a timeline
    if accounts.filter(collection_date__isnull=False).exists():
        # Get the earliest and latest collection dates
        earliest_date = accounts.filter(collection_date__isnull=False).order_by('collection_date').first().collection_date
        latest_date = accounts.filter(collection_date__isnull=False).order_by('-collection_date').first().collection_date

        # Create hourly intervals
        current_date = earliest_date
        cumulative_count = 0

        while current_date <= latest_date:
            # Format the date for display
            date_label = current_date.strftime('%Y-%m-%d %H:%M')

            # Count accounts collected up to this point
            count_up_to_now = accounts.filter(collection_date__lte=current_date).count()

            collection_timeline_labels.append(date_label)
            collection_timeline_data.append(count_up_to_now)

            # Move to the next hour
            current_date = current_date + timezone.timedelta(hours=1)
    else:
        # If no collection dates, create a simulated timeline
        # Simulate collection over 24 hours
        start_time = timezone.now() - timezone.timedelta(days=1)

        for i in range(25):  # 0 to 24 hours
            current_time = start_time + timezone.timedelta(hours=i)
            date_label = current_time.strftime('%Y-%m-%d %H:%M')

            # Simulate a realistic collection curve (slower at start, faster in middle, slower at end)
            if i < 6:  # First quarter - slow start
                progress = i / 24 * 0.5  # 0% to 12.5%
            elif i < 18:  # Middle half - faster collection
                progress = 0.125 + ((i - 6) / 12) * 0.75  # 12.5% to 87.5%
            else:  # Last quarter - slow finish
                progress = 0.875 + ((i - 18) / 6) * 0.125  # 87.5% to 100%

            count = int(accounts.count() * progress)

            collection_timeline_labels.append(date_label)
            collection_timeline_data.append(count)

    # Generate geographic distribution data
    geo_distribution_labels = []
    geo_distribution_data = []

    # Count accounts by location
    location_counts = {}

    for account in accounts:
        if not account.locations:
            continue

        for location in account.locations:
            if location in location_counts:
                location_counts[location] += 1
            else:
                location_counts[location] = 1

    # Sort locations by count (descending) and take top 10
    sorted_locations = sorted(location_counts.items(), key=lambda x: x[1], reverse=True)[:10]

    for location, count in sorted_locations:
        geo_distribution_labels.append(location)
        geo_distribution_data.append(count)

    # Get sample accounts
    sample_accounts = accounts.order_by('-followers')[:10]

    # Get sample whitelist entries
    sample_whitelist = whitelist_entries.order_by('-account__followers')[:10]

    context = {
        'campaign': campaign,
        'workflows': workflows,
        'accounts_count': accounts_count,
        'tagged_accounts_count': tagged_accounts_count,
        'whitelist_count': whitelist_count,
        'campaign_tags': campaign_tags,
        'tag_matches': json.dumps(tag_match_counts),
        'tag_names': json.dumps(tag_names),
        'tag_effectiveness': json.dumps(tag_effectiveness),
        'follower_ranges': json.dumps(follower_ranges),
        'follower_distribution': json.dumps(follower_distribution),
        'account_types_labels': json.dumps(account_types_labels),
        'account_types_data': json.dumps(account_types_data),
        'engagement_ranges': json.dumps(engagement_ranges),
        'engagement_distribution': json.dumps(engagement_distribution),
        'engagement_followers_data': json.dumps(engagement_followers_data),
        'collection_timeline_labels': json.dumps(collection_timeline_labels),
        'collection_timeline_data': json.dumps(collection_timeline_data),
        'geo_distribution_labels': json.dumps(geo_distribution_labels),
        'geo_distribution_data': json.dumps(geo_distribution_data),
        'sample_accounts': sample_accounts,
        'sample_whitelist': sample_whitelist,

        # Whitelist-specific analytics
        'conversion_rate': conversion_rate,
        'avg_privileges': avg_privileges,
        'high_value_accounts': high_value_accounts,
        'dm_count': dm_count,
        'follow_count': follow_count,
        'comment_count': comment_count,
        'like_count': like_count,
        'discover_count': discover_count,
        'favorite_count': favorite_count,
        'verified_whitelist_count': verified_whitelist_count,
        'verified_percentage': verified_percentage,
        'premium_accounts': premium_accounts,
        'premium_percentage': premium_percentage,
        'high_engagement_count': high_engagement_count,
        'engagement_percentage': engagement_percentage,
    }

    return render(request, 'campaigns/simulation_dashboard.html', context)

# Removed login_required decorator to disable authentication requirements
# @login_required
def simulation_export(request, campaign_id):
    """
    Export simulation results for a campaign.
    """
    campaign = get_object_or_404(Campaign, id=campaign_id)

    # Determine export type and format
    export_type = request.GET.get('type', 'accounts')
    export_format = request.GET.get('format', 'csv')

    # Get the data based on export type
    if export_type == 'accounts':
        # Get accounts data
        accounts = Accounts.objects.filter(campaign_id=str(campaign.id))

        if export_format == 'csv':
            # Export as CSV
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="campaign_{campaign.id}_accounts.csv"'

            writer = csv.writer(response)
            writer.writerow([
                'Username', 'Full Name', 'Bio', 'Followers', 'Following', 'Posts',
                'Account Type', 'Verified', 'Collection Date', 'Interests', 'Locations'
            ])

            for account in accounts:
                writer.writerow([
                    account.username,
                    account.full_name,
                    account.bio,
                    account.followers,
                    account.following,
                    account.number_of_posts,
                    account.account_type,
                    account.is_verified,
                    account.collection_date.strftime('%Y-%m-%d %H:%M:%S') if account.collection_date else '',
                    ', '.join(account.interests) if account.interests else '',
                    ', '.join(account.locations) if account.locations else ''
                ])

        elif export_format == 'excel':
            # Export as Excel
            try:
                import xlsxwriter
                from io import BytesIO

                # Create a workbook and add a worksheet
                output = BytesIO()
                workbook = xlsxwriter.Workbook(output)
                worksheet = workbook.add_worksheet('Accounts')

                # Add a bold format to use to highlight cells
                bold = workbook.add_format({'bold': True})
                date_format = workbook.add_format({'num_format': 'yyyy-mm-dd hh:mm:ss'})

                # Write headers
                headers = [
                    'Username', 'Full Name', 'Bio', 'Followers', 'Following', 'Posts',
                    'Account Type', 'Verified', 'Collection Date', 'Interests', 'Locations'
                ]
                for col, header in enumerate(headers):
                    worksheet.write(0, col, header, bold)

                # Write data rows
                for row, account in enumerate(accounts, 1):
                    worksheet.write(row, 0, account.username)
                    worksheet.write(row, 1, account.full_name or '')
                    worksheet.write(row, 2, account.bio or '')
                    worksheet.write(row, 3, account.followers or 0)
                    worksheet.write(row, 4, account.following or 0)
                    worksheet.write(row, 5, account.number_of_posts or 0)
                    worksheet.write(row, 6, account.account_type or '')
                    worksheet.write(row, 7, 'Yes' if account.is_verified else 'No')
                    if account.collection_date:
                        worksheet.write_datetime(row, 8, account.collection_date, date_format)
                    else:
                        worksheet.write(row, 8, '')
                    worksheet.write(row, 9, ', '.join(account.interests) if account.interests else '')
                    worksheet.write(row, 10, ', '.join(account.locations) if account.locations else '')

                # Adjust column widths
                worksheet.set_column(0, 0, 20)  # Username
                worksheet.set_column(1, 1, 20)  # Full Name
                worksheet.set_column(2, 2, 40)  # Bio
                worksheet.set_column(3, 5, 10)  # Followers, Following, Posts
                worksheet.set_column(6, 7, 15)  # Account Type, Verified
                worksheet.set_column(8, 8, 20)  # Collection Date
                worksheet.set_column(9, 10, 30)  # Interests, Locations

                # Close the workbook
                workbook.close()

                # Create the response
                output.seek(0)
                response = HttpResponse(
                    output.read(),
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                response['Content-Disposition'] = f'attachment; filename="campaign_{campaign.id}_accounts.xlsx"'

            except ImportError:
                messages.error(request, "Excel export requires xlsxwriter package. Using CSV instead.")
                return redirect(f"{request.path}?type={export_type}&format=csv")

        elif export_format == 'pdf':
            # Export as PDF
            try:
                from reportlab.lib import colors
                from reportlab.lib.pagesizes import letter, landscape
                from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib.units import inch
                from reportlab.graphics.shapes import Drawing
                from reportlab.graphics.charts.barcharts import VerticalBarChart
                from reportlab.graphics.charts.piecharts import Pie
                from io import BytesIO
                import matplotlib.pyplot as plt
                import numpy as np

                # Create a file-like buffer to receive PDF data
                buffer = BytesIO()

                # Create the PDF object, using the buffer as its "file"
                doc = SimpleDocTemplate(buffer, pagesize=landscape(letter),
                                        title=f"Campaign {campaign.name} - Accounts Report",
                                        author="Campaign Simulation System")

                # Get the default style sheet and add custom styles
                styles = getSampleStyleSheet()
                styles.add(ParagraphStyle(name='Heading2',
                                         parent=styles['Heading2'],
                                         fontSize=14,
                                         spaceAfter=12))
                styles.add(ParagraphStyle(name='Normal_Center',
                                         parent=styles['Normal'],
                                         alignment=1))  # 1 is centered

                # Create elements list for the PDF
                elements = []

                # Add title and campaign info
                elements.append(Paragraph(f"Campaign: {campaign.name}", styles['Title']))
                elements.append(Spacer(1, 0.25*inch))

                # Add campaign summary
                summary_data = [
                    ['Total Accounts', 'Tagged Accounts', 'Whitelist Entries'],
                    [str(accounts_count), str(tagged_accounts_count), str(whitelist_count)]
                ]
                summary_table = Table(summary_data, colWidths=[2.5*inch, 2.5*inch, 2.5*inch])
                summary_table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, 1), colors.lightblue),
                    ('TEXTCOLOR', (0, 1), (-1, 1), colors.black),
                    ('ALIGN', (0, 1), (-1, 1), 'CENTER'),
                    ('FONTNAME', (0, 1), (-1, 1), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 1), (-1, 1), 14),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                elements.append(summary_table)
                elements.append(Spacer(1, 0.5*inch))

                # Create charts using matplotlib and add to PDF

                # 1. Follower Distribution Chart
                plt.figure(figsize=(8, 4))
                plt.bar(follower_ranges, follower_distribution, color='skyblue')
                plt.title('Follower Distribution')
                plt.xlabel('Follower Range')
                plt.ylabel('Number of Accounts')
                plt.xticks(rotation=45)
                plt.tight_layout()

                # Save the chart to a BytesIO object
                chart_buffer = BytesIO()
                plt.savefig(chart_buffer, format='png')
                chart_buffer.seek(0)

                # Add the chart to the PDF
                elements.append(Paragraph("Follower Distribution", styles['Heading2']))
                img = Image(chart_buffer, width=7*inch, height=3.5*inch)
                elements.append(img)
                elements.append(Spacer(1, 0.25*inch))

                # 2. Account Type Distribution Chart (Pie Chart)
                plt.figure(figsize=(6, 6))
                plt.clf()
                plt.pie(account_types_data, labels=account_types_labels, autopct='%1.1f%%',
                        shadow=True, startangle=90, colors=['lightblue', 'lightgreen', 'coral'])
                plt.axis('equal')
                plt.title('Account Types')
                plt.tight_layout()

                # Save the chart to a BytesIO object
                chart_buffer2 = BytesIO()
                plt.savefig(chart_buffer2, format='png')
                chart_buffer2.seek(0)

                # Add the chart to the PDF
                elements.append(Paragraph("Account Types", styles['Heading2']))
                img2 = Image(chart_buffer2, width=5*inch, height=5*inch)
                elements.append(img2)
                elements.append(Spacer(1, 0.25*inch))

                # Add accounts table
                elements.append(Paragraph("Account Details", styles['Heading2']))

                # Create the table data
                data = [['Username', 'Full Name', 'Followers', 'Following', 'Posts', 'Account Type', 'Verified']]

                # Add account data (limit to 50 accounts to keep PDF manageable)
                for account in accounts.order_by('-followers')[:50]:
                    data.append([
                        account.username,
                        account.full_name or '',
                        f"{account.followers:,}" if account.followers else '0',
                        f"{account.following:,}" if account.following else '0',
                        str(account.number_of_posts or 0),
                        account.account_type or '',
                        'Yes' if account.is_verified else 'No'
                    ])

                # Create the table with specific column widths
                col_widths = [1.5*inch, 1.5*inch, 1*inch, 1*inch, 0.8*inch, 1.2*inch, 0.8*inch]
                table = Table(data, colWidths=col_widths)

                # Style the table
                style = TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                    ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                    ('ALIGN', (0, 1), (-1, -1), 'LEFT'),
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 10),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black),
                    # Alternate row colors
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightgrey),
                ])

                # Add alternating row colors
                for i in range(2, len(data), 2):
                    style.add('BACKGROUND', (0, i), (-1, i), colors.whitesmoke)

                table.setStyle(style)
                elements.append(table)

                # Add footer with timestamp
                elements.append(Spacer(1, 0.5*inch))
                elements.append(Paragraph(f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}",
                                         styles['Normal_Center']))

                # Build the PDF
                doc.build(elements)

                # Get the value of the BytesIO buffer
                pdf = buffer.getvalue()
                buffer.close()

                # Create the HTTP response
                response = HttpResponse(content_type='application/pdf')
                response['Content-Disposition'] = f'attachment; filename="campaign_{campaign.id}_accounts.pdf"'
                response.write(pdf)

            except ImportError:
                messages.error(request, "PDF export requires reportlab package. Using CSV instead.")
                return redirect(f"{request.path}?type={export_type}&format=csv")

        else:
            # Invalid format
            messages.error(request, f"Invalid export format: {export_format}")
            return redirect('campaigns:simulation_dashboard', campaign_id=campaign.id)

    elif export_type == 'tags':
        # Get tag analysis results
        tag_results = TagAnalysisResult.objects.filter(campaign=campaign)

        if export_format == 'csv':
            # Export as CSV
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="campaign_{campaign.id}_tag_analysis.csv"'

            writer = csv.writer(response)
            writer.writerow([
                'Account', 'Tag', 'Matched', 'Confidence Score', 'Match Details'
            ])

            for result in tag_results:
                writer.writerow([
                    result.account_id,
                    result.tag.name,
                    result.matched,
                    result.confidence_score,
                    json.dumps(result.match_details) if result.match_details else ''
                ])

        elif export_format == 'excel':
            # Export as Excel
            try:
                import xlsxwriter
                from io import BytesIO

                # Create a workbook and add a worksheet
                output = BytesIO()
                workbook = xlsxwriter.Workbook(output)
                worksheet = workbook.add_worksheet('Tag Analysis')

                # Add a bold format to use to highlight cells
                bold = workbook.add_format({'bold': True})

                # Write headers
                headers = ['Account', 'Tag', 'Matched', 'Confidence Score', 'Match Details']
                for col, header in enumerate(headers):
                    worksheet.write(0, col, header, bold)

                # Write data rows
                for row, result in enumerate(tag_results, 1):
                    worksheet.write(row, 0, result.account_id)
                    worksheet.write(row, 1, result.tag.name)
                    worksheet.write(row, 2, 'Yes' if result.matched else 'No')
                    worksheet.write(row, 3, result.confidence_score)
                    worksheet.write(row, 4, json.dumps(result.match_details) if result.match_details else '')

                # Adjust column widths
                worksheet.set_column(0, 0, 20)  # Account
                worksheet.set_column(1, 1, 20)  # Tag
                worksheet.set_column(2, 2, 10)  # Matched
                worksheet.set_column(3, 3, 15)  # Confidence Score
                worksheet.set_column(4, 4, 50)  # Match Details

                # Close the workbook
                workbook.close()

                # Create the response
                output.seek(0)
                response = HttpResponse(
                    output.read(),
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                response['Content-Disposition'] = f'attachment; filename="campaign_{campaign.id}_tag_analysis.xlsx"'

            except ImportError:
                messages.error(request, "Excel export requires xlsxwriter package. Using CSV instead.")
                return redirect(f"{request.path}?type={export_type}&format=csv")

        else:
            # Invalid format
            messages.error(request, f"Invalid export format: {export_format}")
            return redirect('campaigns:simulation_dashboard', campaign_id=campaign.id)

    elif export_type == 'whitelist':
        # Get whitelist entries
        whitelist_entries = WhiteListEntry.objects.filter(account__campaign_id=str(campaign.id))

        if export_format == 'csv':
            # Export as CSV
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="campaign_{campaign.id}_whitelist.csv"'

            writer = csv.writer(response)
            writer.writerow([
                'Account', 'Tags', 'DM', 'Discover', 'Comment', 'Post Like', 'Favorite', 'Follow', 'Auto'
            ])

            for entry in whitelist_entries:
                writer.writerow([
                    entry.account.username,
                    ','.join(entry.tags) if entry.tags else '',
                    entry.dm,
                    entry.discover,
                    entry.comment,
                    entry.post_like,
                    entry.favorite,
                    entry.follow,
                    entry.is_auto
                ])

        elif export_format == 'excel':
            # Export as Excel
            try:
                import xlsxwriter
                from io import BytesIO

                # Create a workbook and add a worksheet
                output = BytesIO()
                workbook = xlsxwriter.Workbook(output)
                worksheet = workbook.add_worksheet('Whitelist')

                # Add a bold format to use to highlight cells
                bold = workbook.add_format({'bold': True})

                # Write headers
                headers = ['Account', 'Tags', 'DM', 'Discover', 'Comment', 'Post Like', 'Favorite', 'Follow', 'Auto']
                for col, header in enumerate(headers):
                    worksheet.write(0, col, header, bold)

                # Write data rows
                for row, entry in enumerate(whitelist_entries, 1):
                    worksheet.write(row, 0, entry.account.username)
                    worksheet.write(row, 1, ','.join(entry.tags) if entry.tags else '')
                    worksheet.write(row, 2, 'Yes' if entry.dm else 'No')
                    worksheet.write(row, 3, 'Yes' if entry.discover else 'No')
                    worksheet.write(row, 4, 'Yes' if entry.comment else 'No')
                    worksheet.write(row, 5, 'Yes' if entry.post_like else 'No')
                    worksheet.write(row, 6, 'Yes' if entry.favorite else 'No')
                    worksheet.write(row, 7, 'Yes' if entry.follow else 'No')
                    worksheet.write(row, 8, 'Yes' if entry.is_auto else 'No')

                # Adjust column widths
                worksheet.set_column(0, 0, 20)  # Account
                worksheet.set_column(1, 1, 30)  # Tags
                worksheet.set_column(2, 8, 10)  # Privileges

                # Close the workbook
                workbook.close()

                # Create the response
                output.seek(0)
                response = HttpResponse(
                    output.read(),
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                response['Content-Disposition'] = f'attachment; filename="campaign_{campaign.id}_whitelist.xlsx"'

            except ImportError:
                messages.error(request, "Excel export requires xlsxwriter package. Using CSV instead.")
                return redirect(f"{request.path}?type={export_type}&format=csv")

        else:
            # Invalid format
            messages.error(request, f"Invalid export format: {export_format}")
            return redirect('campaigns:simulation_dashboard', campaign_id=campaign.id)

    elif export_type == 'combined':
        # Combined export of all data types
        if export_format == 'excel':
            try:
                import xlsxwriter
                from io import BytesIO
                import matplotlib.pyplot as plt

                # Get all data
                accounts = Accounts.objects.filter(campaign_id=str(campaign.id))
                tag_results = TagAnalysisResult.objects.filter(campaign=campaign)
                whitelist_entries = WhiteListEntry.objects.filter(account__campaign_id=str(campaign.id))

                # Create a workbook and add worksheets
                output = BytesIO()
                workbook = xlsxwriter.Workbook(output)

                # Add a bold format to use to highlight cells
                bold = workbook.add_format({'bold': True})
                header_format = workbook.add_format({
                    'bold': True,
                    'bg_color': '#4472C4',
                    'font_color': 'white',
                    'border': 1
                })

                # Create formats for different data types
                date_format = workbook.add_format({'num_format': 'yyyy-mm-dd hh:mm:ss'})
                number_format = workbook.add_format({'num_format': '#,##0'})
                percent_format = workbook.add_format({'num_format': '0.0%'})

                # 1. Summary worksheet
                summary_ws = workbook.add_worksheet('Summary')

                # Add campaign info
                summary_ws.write(0, 0, 'Campaign Summary', bold)
                summary_ws.write(1, 0, 'Campaign Name:', bold)
                summary_ws.write(1, 1, campaign.name)
                summary_ws.write(2, 0, 'Campaign ID:', bold)
                summary_ws.write(2, 1, str(campaign.id))
                summary_ws.write(3, 0, 'Status:', bold)
                summary_ws.write(3, 1, campaign.status)
                summary_ws.write(4, 0, 'Created At:', bold)
                summary_ws.write_datetime(4, 1, campaign.created_at, date_format)

                # Add metrics
                summary_ws.write(6, 0, 'Metrics', bold)
                summary_ws.write(7, 0, 'Total Accounts:')
                summary_ws.write(7, 1, accounts.count(), number_format)
                summary_ws.write(8, 0, 'Tagged Accounts:')
                summary_ws.write(8, 1, len(set(tag_results.filter(matched=True).values_list('account_id', flat=True))), number_format)
                summary_ws.write(9, 0, 'Whitelisted Accounts:')
                summary_ws.write(9, 1, whitelist_entries.count(), number_format)

                # Add follower distribution
                summary_ws.write(11, 0, 'Follower Distribution', bold)
                follower_ranges = ['0-1K', '1K-5K', '5K-10K', '10K-50K', '50K-100K', '100K+']
                follower_distribution = [0, 0, 0, 0, 0, 0]

                for account in accounts:
                    if account.followers is None:
                        continue

                    if account.followers < 1000:
                        follower_distribution[0] += 1
                    elif account.followers < 5000:
                        follower_distribution[1] += 1
                    elif account.followers < 10000:
                        follower_distribution[2] += 1
                    elif account.followers < 50000:
                        follower_distribution[3] += 1
                    elif account.followers < 100000:
                        follower_distribution[4] += 1
                    else:
                        follower_distribution[5] += 1

                for i, range_label in enumerate(follower_ranges):
                    summary_ws.write(12 + i, 0, range_label)
                    summary_ws.write(12 + i, 1, follower_distribution[i], number_format)

                # Add chart
                chart = workbook.add_chart({'type': 'column'})
                chart.add_series({
                    'name': 'Follower Distribution',
                    'categories': ['Summary', 12, 0, 12 + len(follower_ranges) - 1, 0],
                    'values': ['Summary', 12, 1, 12 + len(follower_ranges) - 1, 1],
                    'fill': {'color': '#5B9BD5'}
                })
                chart.set_title({'name': 'Follower Distribution'})
                chart.set_x_axis({'name': 'Follower Range'})
                chart.set_y_axis({'name': 'Number of Accounts'})
                summary_ws.insert_chart('D6', chart, {'x_offset': 10, 'y_offset': 10, 'x_scale': 1.5, 'y_scale': 1.5})

                # Set column widths
                summary_ws.set_column(0, 0, 20)
                summary_ws.set_column(1, 1, 15)

                # 2. Accounts worksheet
                accounts_ws = workbook.add_worksheet('Accounts')

                # Write headers
                account_headers = [
                    'Username', 'Full Name', 'Bio', 'Followers', 'Following', 'Posts',
                    'Account Type', 'Verified', 'Collection Date', 'Interests', 'Locations'
                ]
                for col, header in enumerate(account_headers):
                    accounts_ws.write(0, col, header, header_format)

                # Write data rows
                for row, account in enumerate(accounts, 1):
                    accounts_ws.write(row, 0, account.username)
                    accounts_ws.write(row, 1, account.full_name or '')
                    accounts_ws.write(row, 2, account.bio or '')
                    accounts_ws.write(row, 3, account.followers or 0, number_format)
                    accounts_ws.write(row, 4, account.following or 0, number_format)
                    accounts_ws.write(row, 5, account.number_of_posts or 0, number_format)
                    accounts_ws.write(row, 6, account.account_type or '')
                    accounts_ws.write(row, 7, 'Yes' if account.is_verified else 'No')
                    if account.collection_date:
                        accounts_ws.write_datetime(row, 8, account.collection_date, date_format)
                    else:
                        accounts_ws.write(row, 8, '')
                    accounts_ws.write(row, 9, ', '.join(account.interests) if account.interests else '')
                    accounts_ws.write(row, 10, ', '.join(account.locations) if account.locations else '')

                # Adjust column widths
                accounts_ws.set_column(0, 0, 20)  # Username
                accounts_ws.set_column(1, 1, 20)  # Full Name
                accounts_ws.set_column(2, 2, 40)  # Bio
                accounts_ws.set_column(3, 5, 10)  # Followers, Following, Posts
                accounts_ws.set_column(6, 7, 15)  # Account Type, Verified
                accounts_ws.set_column(8, 8, 20)  # Collection Date
                accounts_ws.set_column(9, 10, 30)  # Interests, Locations

                # 3. Tag Analysis worksheet
                tags_ws = workbook.add_worksheet('Tag Analysis')

                # Write headers
                tag_headers = ['Account', 'Tag', 'Matched', 'Confidence Score', 'Match Details']
                for col, header in enumerate(tag_headers):
                    tags_ws.write(0, col, header, header_format)

                # Write data rows
                for row, result in enumerate(tag_results, 1):
                    tags_ws.write(row, 0, result.account_id)
                    tags_ws.write(row, 1, result.tag.name)
                    tags_ws.write(row, 2, 'Yes' if result.matched else 'No')
                    tags_ws.write(row, 3, result.confidence_score)
                    tags_ws.write(row, 4, json.dumps(result.match_details) if result.match_details else '')

                # Adjust column widths
                tags_ws.set_column(0, 0, 20)  # Account
                tags_ws.set_column(1, 1, 20)  # Tag
                tags_ws.set_column(2, 2, 10)  # Matched
                tags_ws.set_column(3, 3, 15)  # Confidence Score
                tags_ws.set_column(4, 4, 50)  # Match Details

                # 4. Whitelist worksheet
                whitelist_ws = workbook.add_worksheet('Whitelist')

                # Write headers
                whitelist_headers = ['Account', 'Tags', 'DM', 'Discover', 'Comment', 'Post Like', 'Favorite', 'Follow', 'Auto']
                for col, header in enumerate(whitelist_headers):
                    whitelist_ws.write(0, col, header, header_format)

                # Write data rows
                for row, entry in enumerate(whitelist_entries, 1):
                    whitelist_ws.write(row, 0, entry.account.username)
                    whitelist_ws.write(row, 1, ','.join(entry.tags) if entry.tags else '')
                    whitelist_ws.write(row, 2, 'Yes' if entry.dm else 'No')
                    whitelist_ws.write(row, 3, 'Yes' if entry.discover else 'No')
                    whitelist_ws.write(row, 4, 'Yes' if entry.comment else 'No')
                    whitelist_ws.write(row, 5, 'Yes' if entry.post_like else 'No')
                    whitelist_ws.write(row, 6, 'Yes' if entry.favorite else 'No')
                    whitelist_ws.write(row, 7, 'Yes' if entry.follow else 'No')
                    whitelist_ws.write(row, 8, 'Yes' if entry.is_auto else 'No')

                # Adjust column widths
                whitelist_ws.set_column(0, 0, 20)  # Account
                whitelist_ws.set_column(1, 1, 30)  # Tags
                whitelist_ws.set_column(2, 8, 10)  # Privileges

                # 5. Tag Effectiveness worksheet
                effectiveness_ws = workbook.add_worksheet('Tag Effectiveness')

                # Get campaign tags
                campaign_tags = CampaignTag.objects.filter(campaign=campaign).select_related('tag')

                # Write headers
                effectiveness_ws.write(0, 0, 'Tag', header_format)
                effectiveness_ws.write(0, 1, 'Matched Accounts', header_format)
                effectiveness_ws.write(0, 2, 'Whitelisted Accounts', header_format)
                effectiveness_ws.write(0, 3, 'Conversion Rate', header_format)

                # Write data rows
                for row, campaign_tag in enumerate(campaign_tags, 1):
                    # Count accounts that matched this tag
                    matched_accounts = TagAnalysisResult.objects.filter(
                        campaign=campaign,
                        tag=campaign_tag.tag,
                        matched=True
                    )
                    matched_count = matched_accounts.count()

                    # Count how many of those matched accounts were whitelisted
                    matched_account_ids = matched_accounts.values_list('account_id', flat=True)
                    whitelisted_count = WhiteListEntry.objects.filter(
                        account__username__in=matched_account_ids,
                        account__campaign_id=str(campaign.id)
                    ).count()

                    # Calculate effectiveness (conversion rate)
                    conversion_rate = (whitelisted_count / matched_count) if matched_count > 0 else 0

                    effectiveness_ws.write(row, 0, campaign_tag.tag.name)
                    effectiveness_ws.write(row, 1, matched_count, number_format)
                    effectiveness_ws.write(row, 2, whitelisted_count, number_format)
                    effectiveness_ws.write(row, 3, conversion_rate, percent_format)

                # Add chart
                chart2 = workbook.add_chart({'type': 'column'})
                chart2.add_series({
                    'name': 'Conversion Rate',
                    'categories': ['Tag Effectiveness', 1, 0, row, 0],
                    'values': ['Tag Effectiveness', 1, 3, row, 3],
                    'fill': {'color': '#ED7D31'}
                })
                chart2.set_title({'name': 'Tag Conversion Rates'})
                chart2.set_x_axis({'name': 'Tag'})
                chart2.set_y_axis({'name': 'Conversion Rate', 'num_format': '0%'})
                effectiveness_ws.insert_chart('E2', chart2, {'x_offset': 10, 'y_offset': 10, 'x_scale': 1.5, 'y_scale': 1.5})

                # Adjust column widths
                effectiveness_ws.set_column(0, 0, 25)  # Tag
                effectiveness_ws.set_column(1, 3, 20)  # Metrics

                # Close the workbook
                workbook.close()

                # Create the response
                output.seek(0)
                response = HttpResponse(
                    output.read(),
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                response['Content-Disposition'] = f'attachment; filename="campaign_{campaign.id}_all_data.xlsx"'

            except ImportError:
                messages.error(request, "Excel export requires xlsxwriter package. Using CSV instead.")
                return redirect(f"{request.path}?type=accounts&format=csv")
        else:
            # Only Excel format is supported for combined export
            messages.error(request, f"Combined export only supports Excel format.")
            return redirect('campaigns:simulation_dashboard', campaign_id=campaign.id)

    else:
        # Invalid export type
        messages.error(request, f"Invalid export type: {export_type}")
        return redirect('campaigns:simulation_dashboard', campaign_id=campaign.id)

    return response

# Removed login_required decorator to disable authentication requirements
# @login_required
def run_simulation(request, campaign_id):
    """
    Run a simulation for a campaign.
    """
    campaign = get_object_or_404(Campaign, id=campaign_id)

    if request.method == 'POST':
        # Get simulation parameters
        num_accounts = int(request.POST.get('num_accounts', 100))
        use_airflow = request.POST.get('use_airflow') == 'on'

        try:
            # Run the simulation
            from django.core.management import call_command

            if use_airflow:
                # Use Airflow for simulation
                from campaigns.services.airflow_service import AirflowService
                airflow_service = AirflowService()

                # Trigger the Airflow DAG
                response = airflow_service.trigger_dag(
                    dag_id='simulate_campaign_workflow',
                    conf={
                        'campaign_id': str(campaign.id),
                        'num_accounts': num_accounts,
                        'notify_email': request.user.email
                    }
                )

                if response and 'dag_run_id' in response:
                    messages.success(
                        request,
                        f"Simulation started with Airflow (DAG Run ID: {response['dag_run_id']}). "
                        f"You will receive an email when it completes."
                    )
                else:
                    messages.error(request, f"Failed to start Airflow simulation: {response}")
            else:
                # Use direct simulation
                call_command(
                    'simulate_full_campaign',
                    name=campaign.name,
                    accounts=num_accounts,
                    delay=0.1
                )
                messages.success(request, f"Simulation completed for {num_accounts} accounts.")

            return redirect('campaigns:simulation_dashboard', campaign_id=campaign.id)

        except Exception as e:
            logger.exception(f"Error running simulation: {str(e)}")
            messages.error(request, f"Error running simulation: {str(e)}")

    context = {
        'campaign': campaign
    }

    return render(request, 'campaigns/run_simulation.html', context)
