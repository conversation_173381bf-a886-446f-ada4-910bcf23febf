"""
Dashboard views for the campaigns app.
"""
import logging
from django.views.generic import TemplateView
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.db.models import Q

from campaigns.models import Campaign

logger = logging.getLogger(__name__)


class CampaignDashboardView(TemplateView):
    """
    Dashboard view showing campaign statistics.
    """
    template_name = 'campaigns/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get filter status from query parameters
        status_filter = self.request.GET.get('status', None)

        # Get campaign statistics
        total_campaigns = Campaign.objects.count()
        context['total_campaigns'] = total_campaigns

        # Get counts for each status
        context['draft_campaigns'] = Campaign.objects.filter(status='draft').count()
        context['pending_campaigns'] = Campaign.objects.filter(status='pending').count()
        context['running_campaigns'] = Campaign.objects.filter(status='running').count()
        context['paused_campaigns'] = Campaign.objects.filter(status='paused').count()
        context['completed_campaigns'] = Campaign.objects.filter(status='completed').count()
        context['failed_campaigns'] = Campaign.objects.filter(status='failed').count()
        context['stopped_campaigns'] = Campaign.objects.filter(status='stopped').count()

        # Active campaigns are those with pending, running, or paused status
        context['active_campaigns'] = context['pending_campaigns'] + context['running_campaigns'] + context['paused_campaigns']

        # Get campaigns created this month
        now = timezone.now()
        first_day_of_month = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        context['this_month_campaigns'] = Campaign.objects.filter(created_at__gte=first_day_of_month).count()

        # Get recent campaigns with optional filtering
        campaigns_query = Campaign.objects.order_by('-created_at')

        # Apply status filter if provided
        if status_filter:
            campaigns_query = campaigns_query.filter(status=status_filter)

        # Store the current filter in context
        context['current_filter'] = status_filter

        # Get the filtered campaigns - always limit to 7 records
        context['recent_campaigns'] = campaigns_query[:7]  # Always limit to 7 records

        # Calculate campaign type percentages based on database values
        # Get counts for each target type
        location_only = Campaign.objects.filter(target_type='location').count()
        username_only = Campaign.objects.filter(target_type='username').count()
        mixed = Campaign.objects.filter(target_type='mixed').count()

        # Set default values for when there are no campaigns
        context['location_only_percentage'] = 0
        context['username_only_percentage'] = 0
        context['mixed_percentage'] = 0
        context['has_campaigns'] = total_campaigns > 0

        # Store the actual counts for reference
        context['location_only_count'] = location_only
        context['username_only_count'] = username_only
        context['mixed_count'] = mixed

        # Only calculate percentages if there are campaigns
        if total_campaigns > 0:
            # Calculate percentages based on all campaigns
            context['location_only_percentage'] = round((location_only / total_campaigns) * 100)
            context['username_only_percentage'] = round((username_only / total_campaigns) * 100)
            context['mixed_percentage'] = round((mixed / total_campaigns) * 100)

            # Ensure percentages add up to exactly 100%
            total_percentage = context['location_only_percentage'] + context['username_only_percentage'] + context['mixed_percentage']
            
            # Adjust if needed to make sure total is 100%
            if total_percentage != 100 and total_percentage > 0:
                # Find the largest percentage and adjust it
                if context['location_only_percentage'] >= context['username_only_percentage'] and context['location_only_percentage'] >= context['mixed_percentage']:
                    context['location_only_percentage'] += (100 - total_percentage)
                elif context['username_only_percentage'] >= context['location_only_percentage'] and context['username_only_percentage'] >= context['mixed_percentage']:
                    context['username_only_percentage'] += (100 - total_percentage)
                else:
                    context['mixed_percentage'] += (100 - total_percentage)

        return context
