"""
Workflow details view for the Resource Manager dashboard.
"""
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.permissions import IsAuthenticated

from campaigns.services.mock_resource_manager_service import mock_resource_manager as resource_manager


@method_decorator(csrf_exempt, name='dispatch')
class WorkflowDetailsView(APIView):
    """
    API view for getting workflow details.
    """
    # Temporarily removed authentication for testing
    # permission_classes = [IsAuthenticated]
    
    def get(self, request, workflow_id):
        """
        Get workflow details.
        
        Args:
            workflow_id (str): Workflow ID
            
        Returns:
            Response: Workflow details
        """
        try:
            # Get workflow details
            workflow = resource_manager.get_workflow_details(workflow_id)
            
            if workflow:
                return Response({
                    'success': True,
                    **workflow
                })
            else:
                return Response({
                    'success': False,
                    'error': f"Workflow {workflow_id} not found"
                }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'success': False,
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
