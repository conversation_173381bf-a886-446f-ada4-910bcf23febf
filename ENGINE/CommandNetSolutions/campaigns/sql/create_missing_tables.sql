-- Create LocationTarget table
CREATE TABLE IF NOT EXISTS campaigns_locationtarget (
    id UUID PRIMARY KEY,
    country VARCHAR(100) NOT NULL,
    city VARCHAR(100) NOT NULL,
    location_id VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    campaign_id UUID NOT NULL REFERENCES campaigns_campaign(id) ON DELETE CASCADE
);

-- Create UsernameTarget table
CREATE TABLE IF NOT EXISTS campaigns_usernametarget (
    id UUID PRIMARY KEY,
    username VARCHAR(255) NOT NULL,
    processed BOOLEAN NOT NULL DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    campaign_id UUID NOT NULL REFERENCES campaigns_campaign(id) ON DELETE CASCADE
);

-- Create CampaignResult table
CREATE TABLE IF NOT EXISTS campaigns_campaignresult (
    id UUID PRIMARY KEY,
    total_accounts_found INTEGER NOT NULL DEFAULT 0,
    total_accounts_processed INTEGER NOT NULL DEFAULT 0,
    total_accounts_pending INTEGER NOT NULL DEFAULT 0,
    last_processed_at TIMESTAMP WITH TIME ZONE NULL,
    created_at TIMESTAMP WITH TIME ZONE NOT NULL,
    campaign_id UUID NOT NULL REFERENCES campaigns_campaign(id) ON DELETE CASCADE
);

-- Create indexes
CREATE INDEX IF NOT EXISTS campaigns_locationtarget_campaign_id ON campaigns_locationtarget(campaign_id);
CREATE INDEX IF NOT EXISTS campaigns_usernametarget_campaign_id ON campaigns_usernametarget(campaign_id);
CREATE INDEX IF NOT EXISTS campaigns_campaignresult_campaign_id ON campaigns_campaignresult(campaign_id);
