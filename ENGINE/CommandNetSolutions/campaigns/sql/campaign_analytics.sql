-- Campaign Analytics SQL Queries
-- This file contains SQL queries for campaign analytics

-- Get campaign statistics
-- noqa: disable=L034
SELECT
    c.id AS campaign_id,
    c.name AS campaign_name,
    c.status,
    c.created_at,
    COUNT(lt.id) AS location_target_count,
    COUNT(ut.id) AS username_target_count,
    COUNT(tar.id) AS analysis_result_count
FROM
    campaigns_campaign c
    LEFT JOIN campaigns_locationtarget lt ON lt.campaign_id = c.id
    LEFT JOIN campaigns_usernametarget ut ON ut.campaign_id = c.id
    LEFT JOIN campaigns_taganalysisresult tar ON tar.campaign_id = c.id
GROUP BY
    c.id, c.name, c.status, c.created_at
ORDER BY
    c.created_at DESC;

-- Get tag distribution by campaign
SELECT
    c.id AS campaign_id,
    c.name AS campaign_name,
    dt.name AS tag_name,
    tg.name AS tag_group_name,
    COUNT(tar.id) AS tag_count
FROM
    campaigns_campaign c
    JOIN campaigns_taganalysisresult tar ON tar.campaign_id = c.id
    JOIN campaigns_dynamictag dt ON tar.tag_id = dt.id
    LEFT JOIN campaigns_taggroup tg ON dt.group_id = tg.id
GROUP BY
    c.id, c.name, dt.name, tg.name
ORDER BY
    c.name, tag_count DESC;

-- Get whitelist accounts by campaign
SELECT
    c.id AS campaign_id,
    c.name AS campaign_name,
    tar.account_username,
    tar.account_full_name,
    tar.follower_count,
    tar.following_count,
    tar.post_count,
    tar.is_whitelisted
FROM
    campaigns_campaign c
    JOIN campaigns_taganalysisresult tar ON tar.campaign_id = c.id
WHERE
    tar.is_whitelisted = TRUE
ORDER BY
    c.name, tar.follower_count DESC;

-- Get campaign performance over time
WITH daily_stats AS (
    SELECT
        c.id AS campaign_id,
        c.name AS campaign_name,
        DATE_TRUNC('day', tar.created_at) AS day,
        COUNT(DISTINCT tar.id) AS accounts_analyzed,
        COUNT(DISTINCT CASE WHEN tar.is_whitelisted THEN tar.id END) AS accounts_whitelisted
    FROM
        campaigns_campaign c
        JOIN campaigns_taganalysisresult tar ON tar.campaign_id = c.id
    GROUP BY
        c.id, c.name, DATE_TRUNC('day', tar.created_at)
)
SELECT
    campaign_id,
    campaign_name,
    day,
    accounts_analyzed,
    accounts_whitelisted,
    CASE
        WHEN accounts_analyzed > 0 THEN
            ROUND((accounts_whitelisted::NUMERIC / accounts_analyzed) * 100, 2)
        ELSE 0
    END AS whitelist_percentage
FROM
    daily_stats
ORDER BY
    campaign_name, day;
