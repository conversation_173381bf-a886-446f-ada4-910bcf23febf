"""
Unit tests for PyFlow Service.
"""
import os
import json
import uuid
import tempfile
from unittest import mock
from django.test import TestCase
from django.conf import settings
from campaigns.services import PyFlowService


class PyFlowServiceTestCase(TestCase):
    """Test cases for PyFlow Service."""

    def setUp(self):
        """Set up test environment."""
        # Create temporary directories for testing
        self.temp_dir = tempfile.TemporaryDirectory()
        self.workflow_dir = os.path.join(self.temp_dir.name, 'workflows')
        self.template_dir = os.path.join(self.temp_dir.name, 'templates')
        
        os.makedirs(self.workflow_dir, exist_ok=True)
        os.makedirs(self.template_dir, exist_ok=True)
        
        # Create test template
        self.template_data = {
            "activeGraph": "root",
            "graphs": {"root": "root"},
            "nodes": [
                {
                    "name": "campaignID",
                    "value": ""
                },
                {
                    "name": "targets",
                    "value": "[]"
                },
                {
                    "name": "options",
                    "value": "{}"
                }
            ]
        }
        
        self.template_path = os.path.join(self.template_dir, 'test_template.pygraph')
        with open(self.template_path, 'w') as f:
            json.dump(self.template_data, f)
        
        # Create service with mocked directories
        self.service = PyFlowService()
        self.service.workflow_dir = self.workflow_dir
        self.service.template_dir = self.template_dir

    def tearDown(self):
        """Clean up test environment."""
        self.temp_dir.cleanup()

    def test_load_template(self):
        """Test loading a template."""
        # Test loading existing template
        template = self.service._load_template('test_template')
        self.assertIsNotNone(template)
        self.assertEqual(template['activeGraph'], 'root')
        
        # Test loading non-existent template
        template = self.service._load_template('non_existent')
        self.assertIsNone(template)

    def test_save_workflow(self):
        """Test saving a workflow."""
        workflow_name = f"test_workflow_{uuid.uuid4().hex[:8]}"
        workflow_data = self.template_data.copy()
        
        # Test saving workflow
        workflow_path = self.service._save_workflow(workflow_data, workflow_name)
        self.assertIsNotNone(workflow_path)
        self.assertTrue(os.path.exists(workflow_path))
        
        # Verify saved data
        with open(workflow_path, 'r') as f:
            saved_data = json.load(f)
        self.assertEqual(saved_data['activeGraph'], workflow_data['activeGraph'])

    @mock.patch('campaigns.services.pyflow_service.subprocess.run')
    def test_run_workflow(self, mock_subprocess):
        """Test running a workflow."""
        # Create test workflow
        workflow_name = f"test_workflow_{uuid.uuid4().hex[:8]}"
        workflow_path = os.path.join(self.workflow_dir, f"{workflow_name}.pygraph")
        with open(workflow_path, 'w') as f:
            json.dump(self.template_data, f)
        
        # Mock subprocess result
        mock_result = mock.Mock()
        mock_result.returncode = 0
        mock_result.stdout = "Workflow completed successfully"
        mock_subprocess.return_value = mock_result
        
        # Test running workflow
        result = self.service._run_workflow(workflow_path)
        self.assertTrue(result['success'])
        self.assertEqual(result['workflow_path'], workflow_path)
        
        # Test failure case
        mock_result.returncode = 1
        mock_result.stderr = "Error running workflow"
        result = self.service._run_workflow(workflow_path)
        self.assertFalse(result['success'])

    def test_create_account_collection_workflow(self):
        """Test creating an account collection workflow."""
        # Create test template
        template_path = os.path.join(self.template_dir, 'account_collection.pygraph')
        with open(template_path, 'w') as f:
            json.dump(self.template_data, f)
        
        # Test creating workflow
        campaign_id = str(uuid.uuid4())
        targets = [{"type": "location", "location_id": "123", "location_name": "Test Location"}]
        options = {"audience_type": "followers"}
        
        result = self.service.create_account_collection_workflow(campaign_id, targets, options)
        self.assertTrue(result['success'])
        self.assertTrue(os.path.exists(result['workflow_path']))
        
        # Verify workflow data
        with open(result['workflow_path'], 'r') as f:
            workflow_data = json.load(f)
        
        # Find nodes and check values
        for node in workflow_data['nodes']:
            if node['name'] == 'campaignID':
                self.assertEqual(node['value'], campaign_id)
            elif node['name'] == 'targets':
                self.assertEqual(json.loads(node['value']), targets)
            elif node['name'] == 'options':
                self.assertEqual(json.loads(node['value']), options)

    @mock.patch('campaigns.services.pyflow_service.subprocess.run')
    def test_get_workflow_status(self, mock_subprocess):
        """Test getting workflow status."""
        # Create test workflow
        workflow_name = f"test_workflow_{uuid.uuid4().hex[:8]}"
        workflow_path = os.path.join(self.workflow_dir, f"{workflow_name}.pygraph")
        with open(workflow_path, 'w') as f:
            json.dump(self.template_data, f)
        
        # Mock subprocess result for running workflow
        mock_result = mock.Mock()
        mock_result.stdout = "process info"
        mock_subprocess.return_value = mock_result
        
        # Test running workflow status
        result = self.service.get_workflow_status(workflow_name)
        self.assertTrue(result['success'])
        self.assertEqual(result['status'], 'running')
        
        # Test completed workflow
        mock_result.stdout = ""
        log_dir = os.path.join(os.path.dirname(self.workflow_dir), 'logs')
        os.makedirs(log_dir, exist_ok=True)
        log_path = os.path.join(log_dir, f"{workflow_name}.log")
        with open(log_path, 'w') as f:
            f.write("Workflow completed successfully")
        
        result = self.service.get_workflow_status(workflow_name)
        self.assertTrue(result['success'])
        self.assertEqual(result['status'], 'completed')

    def test_list_workflows(self):
        """Test listing workflows."""
        # Create test workflows
        campaign_id = str(uuid.uuid4())
        for i in range(3):
            workflow_name = f"campaign_{campaign_id}_workflow_{i}"
            workflow_path = os.path.join(self.workflow_dir, f"{workflow_name}.pygraph")
            with open(workflow_path, 'w') as f:
                json.dump(self.template_data, f)
        
        # Test listing all workflows
        workflows = self.service.list_workflows()
        self.assertEqual(len(workflows), 3)
        
        # Test filtering by campaign ID
        workflows = self.service.list_workflows(campaign_id)
        self.assertEqual(len(workflows), 3)
        
        # Test filtering by non-existent campaign ID
        workflows = self.service.list_workflows("non_existent")
        self.assertEqual(len(workflows), 0)
