"""
Unit tests for External API Service.
"""
import os
import json
import tempfile
import time
from unittest import mock
from django.test import TestCase
from django.core.cache import cache
from campaigns.services import ExternalAPIService


class ExternalAPIServiceTestCase(TestCase):
    """Test cases for External API Service."""

    def setUp(self):
        """Set up test environment."""
        # Create temporary directory for API configs
        self.temp_dir = tempfile.TemporaryDirectory()
        self.api_config_dir = self.temp_dir.name
        
        # Create service with mocked config directory
        self.service = ExternalAPIService()
        self.service.api_configs = {}
        
        # Create test API configs
        self.api_configs = {
            'test_api': {
                'base_url': 'https://api.example.com',
                'auth_type': 'api_key',
                'api_key_name': 'X-API-Key',
                'api_key': 'test_api_key',
                'api_key_location': 'header',
                'description': 'Test API'
            },
            'oauth_api': {
                'base_url': 'https://oauth.example.com',
                'auth_type': 'oauth2',
                'client_id': 'test_client_id',
                'client_secret': 'test_client_secret',
                'token_url': 'https://oauth.example.com/token',
                'access_token': 'test_access_token',
                'expires_at': time.time() + 3600,
                'description': 'OAuth API'
            },
            'hmac_api': {
                'base_url': 'https://hmac.example.com',
                'auth_type': 'hmac',
                'hmac_key': 'test_hmac_key',
                'hmac_secret': 'test_hmac_secret',
                'description': 'HMAC API'
            }
        }
        
        # Add configs to service
        self.service.api_configs = self.api_configs.copy()
        
        # Clear cache
        cache.clear()

    def tearDown(self):
        """Clean up test environment."""
        self.temp_dir.cleanup()
        cache.clear()

    def test_get_auth_headers(self):
        """Test getting authentication headers."""
        # Test API key auth
        headers = self.service._get_auth_headers('test_api')
        self.assertEqual(headers, {'X-API-Key': 'test_api_key'})
        
        # Test OAuth2 auth
        headers = self.service._get_auth_headers('oauth_api')
        self.assertEqual(headers, {'Authorization': 'Bearer test_access_token'})
        
        # Test HMAC auth
        headers = self.service._get_auth_headers('hmac_api')
        self.assertIn('X-API-Key', headers)
        self.assertIn('X-Timestamp', headers)
        self.assertIn('X-Signature', headers)
        
        # Test non-existent API
        headers = self.service._get_auth_headers('non_existent')
        self.assertEqual(headers, {})

    @mock.patch('campaigns.services.external_api_service.requests.post')
    def test_refresh_oauth2_token(self, mock_post):
        """Test refreshing OAuth2 token."""
        # Mock response
        mock_response = mock.Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            'access_token': 'new_access_token',
            'refresh_token': 'new_refresh_token',
            'expires_in': 3600
        }
        mock_post.return_value = mock_response
        
        # Test refreshing token
        result = self.service._refresh_oauth2_token('oauth_api')
        
        # Check result
        self.assertTrue(result['success'])
        self.assertEqual(result['access_token'], 'new_access_token')
        
        # Check if token was updated in config
        self.assertEqual(self.service.api_configs['oauth_api']['access_token'], 'new_access_token')
        self.assertEqual(self.service.api_configs['oauth_api']['refresh_token'], 'new_refresh_token')
        
        # Test with non-existent API
        result = self.service._refresh_oauth2_token('non_existent')
        self.assertFalse(result['success'])
        
        # Test with non-OAuth API
        result = self.service._refresh_oauth2_token('test_api')
        self.assertFalse(result['success'])

    def test_build_url(self):
        """Test building URL for API request."""
        # Test basic URL
        url = self.service._build_url('test_api', 'endpoint')
        self.assertEqual(url, 'https://api.example.com/endpoint')
        
        # Test URL with query parameters
        url = self.service._build_url('test_api', 'endpoint', {'param1': 'value1', 'param2': 'value2'})
        self.assertEqual(url, 'https://api.example.com/endpoint?param1=value1&param2=value2')
        
        # Test URL with leading slash in endpoint
        url = self.service._build_url('test_api', '/endpoint')
        self.assertEqual(url, 'https://api.example.com/endpoint')
        
        # Test URL with API key in query params
        self.service.api_configs['test_api']['api_key_location'] = 'query'
        url = self.service._build_url('test_api', 'endpoint')
        self.assertEqual(url, 'https://api.example.com/endpoint?X-API-Key=test_api_key')
        
        # Test with non-existent API
        url = self.service._build_url('non_existent', 'endpoint')
        self.assertIsNone(url)

    @mock.patch('campaigns.services.external_api_service.requests.get')
    def test_call_api(self, mock_get):
        """Test calling an API."""
        # Mock response
        mock_response = mock.Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'key': 'value'}
        mock_response.text = '{"key": "value"}'
        mock_get.return_value = mock_response
        
        # Test calling API
        result = self.service.call_api('test_api', 'endpoint')
        
        # Check result
        self.assertTrue(result['success'])
        self.assertEqual(result['data'], {'key': 'value'})
        self.assertEqual(result['status_code'], 200)
        
        # Check if requests.get was called
        mock_get.assert_called_once()
        
        # Test with cache
        cache.clear()
        result = self.service.call_api('test_api', 'endpoint', cache_key='test_cache')
        self.assertTrue(result['success'])
        
        # Call again to test cache
        mock_get.reset_mock()
        result = self.service.call_api('test_api', 'endpoint', cache_key='test_cache')
        self.assertTrue(result['success'])
        self.assertTrue(result.get('cached', False))
        mock_get.assert_not_called()
        
        # Test with non-existent API
        result = self.service.call_api('non_existent', 'endpoint')
        self.assertFalse(result['success'])

    def test_configure_api(self):
        """Test configuring an API."""
        # Test configuring new API
        config = {
            'base_url': 'https://new.example.com',
            'auth_type': 'none',
            'description': 'New API'
        }
        
        result = self.service.configure_api('new_api', config)
        
        # Check result
        self.assertTrue(result['success'])
        
        # Check if API was added to configs
        self.assertIn('new_api', self.service.api_configs)
        self.assertEqual(self.service.api_configs['new_api']['base_url'], 'https://new.example.com')
        
        # Test with missing required field
        config = {
            'auth_type': 'none',
            'description': 'Invalid API'
        }
        
        result = self.service.configure_api('invalid_api', config)
        self.assertFalse(result['success'])

    def test_get_api_config(self):
        """Test getting API configuration."""
        # Test getting existing API
        result = self.service.get_api_config('test_api')
        
        # Check result
        self.assertTrue(result['success'])
        self.assertEqual(result['config']['base_url'], 'https://api.example.com')
        
        # Check if sensitive information is masked
        self.assertEqual(result['config']['api_key'], '********')
        
        # Test with non-existent API
        result = self.service.get_api_config('non_existent')
        self.assertFalse(result['success'])

    def test_list_apis(self):
        """Test listing APIs."""
        # Test listing APIs
        apis = self.service.list_apis()
        
        # Check result
        self.assertEqual(len(apis), 3)
        
        # Check if API info is correct
        api_names = [api['name'] for api in apis]
        self.assertIn('test_api', api_names)
        self.assertIn('oauth_api', api_names)
        self.assertIn('hmac_api', api_names)

    def test_delete_api_config(self):
        """Test deleting API configuration."""
        # Test deleting existing API
        result = self.service.delete_api_config('test_api')
        
        # Check result
        self.assertTrue(result['success'])
        
        # Check if API was removed from configs
        self.assertNotIn('test_api', self.service.api_configs)
        
        # Test with non-existent API
        result = self.service.delete_api_config('non_existent')
        self.assertFalse(result['success'])
