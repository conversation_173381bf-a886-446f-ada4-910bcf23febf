"""
Tests for tag creation functionality.
"""
import json
import uuid
from django.test import TestCase, Client, override_settings
from django.urls import reverse
from django.views.decorators.csrf import csrf_exempt
from campaigns.models import DynamicTag, CampaignTagRule, CampaignTagCondition


@override_settings(CSRF_COOKIE_SECURE=False, CSRF_COOKIE_HTTPONLY=False, CSRF_USE_SESSIONS=False)
class TagCreationTestCase(TestCase):
    """
    Test case for tag creation functionality.
    """
    def setUp(self):
        """
        Set up test data.
        """
        self.client = Client()
        self.tag_create_url = reverse('campaigns:dynamic_tag_create')
        self.tag_list_url = reverse('campaigns:dynamic_tag_list')

        # Create a test user
        from django.contrib.auth.models import User
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )

        # Log in the test user
        self.client.login(username='testuser', password='testpassword')

    def test_tag_creation_with_single_condition(self):
        """
        Test creating a tag with a single condition.
        """
        # Create a tag with a single condition
        tag_data = {
            'name': 'Test Tag',
            'description': 'Test tag description',
            'category': '',
            'tag_group': '',
            'is_global': 'on',
            'field': 'bio',
            'pattern': 'test',
            'tag_type': 'icontains',
            'conditions_json': json.dumps([{
                'id': 0,
                'field_category': 'text',
                'field': 'bio',
                'field_type': 'string',
                'operator': 'icontains',
                'value': 'test',
                'required': True
            }]),
            'condition_logic': 'all'
        }

        # Submit the form with CSRF check disabled for testing
        response = self.client.post(self.tag_create_url, tag_data, follow=True)
        self.assertEqual(response.status_code, 200)

        # Print the response content for debugging
        print("Response content:", response.content.decode('utf-8')[:500])

        # Print all tags in the database
        print("All tags in database:", list(DynamicTag.objects.all().values_list('name', flat=True)))

        # Check that the tag was created
        self.assertTrue(DynamicTag.objects.filter(name='Test Tag').exists())
        tag = DynamicTag.objects.get(name='Test Tag')

        # Check that the tag has the correct values
        self.assertEqual(tag.description, 'Test tag description')
        self.assertEqual(tag.field, 'bio')
        self.assertTrue(tag.is_global)

        # Check that the tag rule was created
        pattern_data = json.loads(tag.pattern)
        self.assertIn('rule_id', pattern_data)
        rule_id = pattern_data['rule_id']
        self.assertTrue(CampaignTagRule.objects.filter(id=rule_id).exists())
        rule = CampaignTagRule.objects.get(id=rule_id)

        # Check that the rule has the correct values
        self.assertEqual(rule.name, 'Test Tag')
        self.assertEqual(rule.tag, 'Test Tag')
        self.assertEqual(rule.logic, 'all')

        # Check that the condition was created
        self.assertEqual(rule.conditions.count(), 1)
        condition = rule.conditions.first()
        self.assertEqual(condition.field, 'bio')
        self.assertEqual(condition.field_type, 'string')
        self.assertEqual(condition.operator, 'icontains')
        self.assertEqual(condition.value, '"test"')
        self.assertTrue(condition.required)

    def test_tag_creation_with_multiple_conditions(self):
        """
        Test creating a tag with multiple conditions.
        """
        # Create a tag with multiple conditions
        tag_data = {
            'name': 'Test Tag Multiple',
            'description': 'Test tag with multiple conditions',
            'category': '',
            'tag_group': '',
            'is_global': 'on',
            'field': 'bio',
            'pattern': 'test',
            'tag_type': 'icontains',
            'conditions_json': json.dumps([
                {
                    'id': 0,
                    'field_category': 'text',
                    'field': 'bio',
                    'field_type': 'string',
                    'operator': 'icontains',
                    'value': 'test',
                    'required': True
                },
                {
                    'id': 1,
                    'field_category': 'boolean',
                    'field': 'is_verified',
                    'field_type': 'boolean',
                    'operator': 'is_true',
                    'value': 'true',
                    'required': True
                }
            ]),
            'condition_logic': 'all'
        }

        # Submit the form with CSRF check disabled for testing
        response = self.client.post(self.tag_create_url, tag_data, follow=True)
        self.assertEqual(response.status_code, 200)

        # Print the response content for debugging
        print("Response content:", response.content.decode('utf-8')[:500])

        # Print all tags in the database
        print("All tags in database:", list(DynamicTag.objects.all().values_list('name', flat=True)))

        # Check that the tag was created
        self.assertTrue(DynamicTag.objects.filter(name='Test Tag Multiple').exists())
        tag = DynamicTag.objects.get(name='Test Tag Multiple')

        # Check that the tag rule was created
        pattern_data = json.loads(tag.pattern)
        self.assertIn('rule_id', pattern_data)
        rule_id = pattern_data['rule_id']
        self.assertTrue(CampaignTagRule.objects.filter(id=rule_id).exists())
        rule = CampaignTagRule.objects.get(id=rule_id)

        # Check that the conditions were created
        self.assertEqual(rule.conditions.count(), 2)

        # Check the first condition
        bio_condition = rule.conditions.filter(field='bio').first()
        self.assertIsNotNone(bio_condition)
        self.assertEqual(bio_condition.field_type, 'string')
        self.assertEqual(bio_condition.operator, 'icontains')
        self.assertEqual(bio_condition.value, '"test"')

        # Check the second condition
        verified_condition = rule.conditions.filter(field='is_verified').first()
        self.assertIsNotNone(verified_condition)
        self.assertEqual(verified_condition.field_type, 'boolean')
        self.assertEqual(verified_condition.operator, 'is_true')
        self.assertEqual(verified_condition.value, '"true"')

    def test_tag_update(self):
        """
        Test updating a tag.
        """
        # First create a tag
        tag = DynamicTag.objects.create(
            id=uuid.uuid4(),
            name='Tag to Update',
            description='Original description',
            field='bio',
            pattern='{}',
            tag_type='icontains',
            is_global=True
        )

        # Create a rule for the tag
        rule = CampaignTagRule.objects.create(
            id=uuid.uuid4(),
            name=tag.name,
            tag=tag.name,
            description=tag.description,
            active=True,
            is_global=tag.is_global,
            logic='all'
        )

        # Create a condition for the rule
        condition = CampaignTagCondition.objects.create(
            id=uuid.uuid4(),
            rule=rule,
            field='bio',
            field_type='string',
            operator='icontains',
            value='"original"',
            score=1,
            required=True
        )

        # Update the tag's pattern to reference the rule
        tag.pattern = json.dumps({'rule_id': str(rule.id), 'logic': 'all'})
        tag.save()

        # Now update the tag
        tag_update_url = reverse('campaigns:dynamic_tag_update', kwargs={'pk': tag.id})
        tag_data = {
            'name': 'Tag to Update',
            'description': 'Updated description',
            'category': '',
            'tag_group': '',
            'is_global': 'on',
            'field': 'bio',
            'pattern': 'updated',
            'tag_type': 'icontains',
            'conditions_json': json.dumps([
                {
                    'id': 0,
                    'field_category': 'text',
                    'field': 'bio',
                    'field_type': 'string',
                    'operator': 'icontains',
                    'value': 'updated',
                    'required': True
                }
            ]),
            'condition_logic': 'all'
        }

        # Submit the form with CSRF check disabled for testing
        response = self.client.post(tag_update_url, tag_data, follow=True)
        self.assertEqual(response.status_code, 200)

        # Print the response content for debugging
        print("Response content:", response.content.decode('utf-8')[:500])

        # Print all tags in the database
        print("All tags in database:", list(DynamicTag.objects.all().values_list('name', flat=True)))

        # Refresh the tag from the database
        tag.refresh_from_db()

        # Check that the tag was updated
        self.assertEqual(tag.description, 'Updated description')

        # Check that the rule was updated
        rule.refresh_from_db()
        self.assertEqual(rule.description, 'Updated description')

        # Check that the condition was updated
        self.assertEqual(rule.conditions.count(), 1)
        condition = rule.conditions.first()
        self.assertEqual(condition.value, '"updated"')
