"""
Integration tests for the campaign workflow.

This module tests the full campaign workflow, including:
1. Campaign creation
2. Tag assignment
3. Account collection
4. Tag analysis
5. Whitelist generation
"""
import uuid
from unittest.mock import patch
from django.test import TestCase
from django.utils import timezone
from django.contrib.auth.models import User
from django.core.management import call_command

from campaigns.models import (
    Campaign, DynamicTag, TagCategory, TagGroup, CampaignTag, TagAnalysisResult,
    WorkflowExecution, WorkflowProgressUpdate
)
from instagram.models import Accounts, WhiteListEntry


class CampaignWorkflowTestCase(TestCase):
    """Test case for the full campaign workflow."""

    def setUp(self):
        """Set up test data."""
        # Create a user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )

    def test_full_campaign_workflow(self):
        """Test the full campaign workflow."""
        # First, populate the backend with predefined data
        with patch('uuid.uuid4', return_value=uuid.UUID('********-1234-5678-1234-************')):
            call_command('populate_backend')

        # Check that tag categories, groups, and tags were created
        self.assertTrue(TagCategory.objects.exists(), "Tag categories should be created")
        self.assertTrue(TagGroup.objects.exists(), "Tag groups should be created")
        self.assertTrue(DynamicTag.objects.exists(), "Tags should be created")

        # Now simulate a full campaign
        with patch('uuid.uuid4', return_value=uuid.UUID('*************-4321-8765-************')):
            call_command('simulate_full_campaign', name='Test Campaign', accounts=20, delay=0.1)

        # Check that a campaign was created
        campaign = Campaign.objects.get(name='Test Campaign')
        self.assertIsNotNone(campaign, "Campaign should be created")
        self.assertEqual(campaign.status, 'completed', "Campaign status should be 'completed'")

        # Check that campaign tags were assigned
        campaign_tags = CampaignTag.objects.filter(campaign=campaign)
        self.assertTrue(campaign_tags.exists(), "Campaign tags should be assigned")

        # Check that accounts were collected
        accounts = Accounts.objects.filter(campaign_id=str(campaign.id))
        self.assertEqual(accounts.count(), 20, "20 accounts should be collected")

        # Check that tag analysis results were created
        tag_analysis_results = TagAnalysisResult.objects.filter(campaign=campaign)
        self.assertTrue(tag_analysis_results.exists(), "Tag analysis results should be created")

        # Check that whitelist entries were created
        whitelist_entries = WhiteListEntry.objects.filter(account__campaign_id=str(campaign.id))
        self.assertTrue(whitelist_entries.exists(), "Whitelist entries should be created")

        # Check that workflow executions were created
        workflow_executions = WorkflowExecution.objects.filter(campaign_id=str(campaign.id))
        self.assertEqual(workflow_executions.count(), 2, "Two workflow executions should be created")

        # Check that workflow progress updates were created
        workflow_progress_updates = WorkflowProgressUpdate.objects.filter(
            workflow_execution__campaign_id=str(campaign.id)
        )
        self.assertTrue(workflow_progress_updates.exists(), "Workflow progress updates should be created")

    def test_campaign_with_airflow(self):
        """Test the campaign workflow using Airflow."""
        # First, populate the backend with predefined data
        with patch('uuid.uuid4', return_value=uuid.UUID('********-1234-5678-1234-************')):
            call_command('populate_backend')

        # Mock the AirflowService to avoid actual Airflow API calls
        with patch('campaigns.services.airflow_service.AirflowService.trigger_campaign_data_collection') as mock_trigger_collection, \
             patch('campaigns.services.airflow_service.AirflowService.trigger_campaign_tagging') as mock_trigger_tagging, \
             patch('uuid.uuid4', return_value=uuid.UUID('*************-4321-8765-************')):
            
            # Set up mock return values
            mock_trigger_collection.return_value = {'dag_run_id': 'test_collection_run'}
            mock_trigger_tagging.return_value = {'dag_run_id': 'test_tagging_run'}
            
            # Simulate a campaign with Airflow
            call_command('simulate_full_campaign', name='Airflow Test Campaign', accounts=10, delay=0.1, use_airflow=True)
            
            # Check that the Airflow service was called
            self.assertTrue(mock_trigger_collection.called, "Airflow data collection should be triggered")
            self.assertTrue(mock_trigger_tagging.called, "Airflow tagging should be triggered")
            
            # Check that a campaign was created
            campaign = Campaign.objects.get(name='Airflow Test Campaign')
            self.assertIsNotNone(campaign, "Campaign should be created")

    def test_empty_and_populate_backend(self):
        """Test emptying and populating the backend."""
        # First, populate the backend with predefined data
        call_command('populate_backend')
        
        # Check that data was created
        self.assertTrue(TagCategory.objects.exists(), "Tag categories should be created")
        self.assertTrue(TagGroup.objects.exists(), "Tag groups should be created")
        self.assertTrue(DynamicTag.objects.exists(), "Tags should be created")
        
        # Now empty the backend
        call_command('empty_backend', confirm=True, keep_users=True)
        
        # Check that data was deleted
        self.assertFalse(TagCategory.objects.exists(), "Tag categories should be deleted")
        self.assertFalse(TagGroup.objects.exists(), "Tag groups should be deleted")
        self.assertFalse(DynamicTag.objects.exists(), "Tags should be deleted")
        
        # Check that users were kept
        self.assertTrue(User.objects.exists(), "Users should be kept")
        
        # Populate the backend again
        call_command('populate_backend')
        
        # Check that data was created again
        self.assertTrue(TagCategory.objects.exists(), "Tag categories should be created again")
        self.assertTrue(TagGroup.objects.exists(), "Tag groups should be created again")
        self.assertTrue(DynamicTag.objects.exists(), "Tags should be created again")

    def test_run_campaign_simulation_script(self):
        """Test the run_campaign_simulation.py script."""
        # Mock the subprocess.run function to avoid actually running the script
        with patch('subprocess.run') as mock_run:
            # Import the script
            import sys
            import os
            script_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'run_campaign_simulation.py')
            
            # Add the script directory to sys.path
            sys.path.append(os.path.dirname(script_path))
            
            # Import the script
            from run_campaign_simulation import main
            
            # Run the script with arguments
            sys.argv = ['run_campaign_simulation.py', '--accounts', '30', '--skip-empty', '--skip-populate']
            main()
            
            # Check that subprocess.run was called with the correct commands
            self.assertTrue(mock_run.called, "subprocess.run should be called")
            
            # Check that the simulate_full_campaign command was called
            simulate_command_calls = [
                call for call in mock_run.call_args_list
                if 'simulate_full_campaign' in call[0][0]
            ]
            self.assertTrue(simulate_command_calls, "simulate_full_campaign should be called")
