"""
Tests for the dummy workflow implementation.
"""
import json
import uuid
from django.test import TestCase, override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model

from campaigns.models import Campaign
from campaigns.models.workflow import WorkflowExecution
from campaigns.services.dummy_workflow import DummyWorkflowRunner
from campaigns.services.dummy_pyflow_service import DummyPyFlowService
from campaigns.services.workflow_service import WorkflowService

User = get_user_model()

@override_settings(USE_DUMMY_PYFLOW=True)
class DummyWorkflowTests(TestCase):
    """Tests for the dummy workflow implementation."""
    
    def setUp(self):
        """Set up test data."""
        # Create a test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create a test campaign
        self.campaign = Campaign.objects.create(
            id=uuid.uuid4(),
            name='Test Campaign',
            description='Test campaign description',
            status='draft',
            target_type='mixed',
            airflow_run_id='pending',
            creator=self.user
        )
        
        # Initialize the dummy workflow runner
        self.workflow_runner = DummyWorkflowRunner()
        
        # Initialize the dummy PyFlow service
        self.pyflow_service = DummyPyFlowService()
        
        # Initialize the workflow service
        self.workflow_service = WorkflowService()
    
    def test_dummy_workflow_runner(self):
        """Test the dummy workflow runner."""
        # Create a workflow file
        result = self.workflow_runner.create_workflow_file(
            workflow_name='test_workflow',
            workflow_type='collection',
            parameters={'campaign_id': str(self.campaign.id)}
        )
        
        # Check that the workflow file was created successfully
        self.assertTrue(result['success'])
        self.assertEqual(result['workflow_name'], 'test_workflow')
        self.assertEqual(result['workflow_type'], 'collection')
        
        # Run the workflow
        result = self.workflow_runner.run_workflow(
            workflow_name='test_workflow',
            campaign_id=str(self.campaign.id),
            workflow_type='collection',
            parameters={'campaign_id': str(self.campaign.id)},
            track_progress=True
        )
        
        # Check that the workflow was started successfully
        self.assertTrue(result['success'])
        self.assertEqual(result['status'], 'running')
        
        # Get the workflow execution ID
        workflow_execution_id = result['workflow_execution_id']
        
        # Get the workflow status
        result = self.workflow_runner.get_workflow_status(workflow_execution_id)
        
        # Check that the workflow status was retrieved successfully
        self.assertTrue(result['success'])
    
    def test_dummy_pyflow_service(self):
        """Test the dummy PyFlow service."""
        # Create and run a collection workflow
        result = self.pyflow_service.create_and_run_collection_workflow(
            campaign_id=str(self.campaign.id),
            targets=[
                {'type': 'location', 'location_id': '123456789', 'location_name': 'Test Location'},
                {'type': 'username', 'username': 'testuser', 'audience_type': 'followers'}
            ]
        )
        
        # Check that the workflow was started successfully
        self.assertTrue(result['success'])
        self.assertEqual(result['status'], 'running')
        
        # Get the workflow execution ID
        workflow_execution_id = result['workflow_execution_id']
        
        # Get the workflow status
        result = self.pyflow_service.get_workflow_status(workflow_execution_id)
        
        # Check that the workflow status was retrieved successfully
        self.assertTrue(result['success'])
    
    def test_workflow_service(self):
        """Test the workflow service."""
        # Run a collection workflow
        result = self.workflow_service.run_collection_workflow(
            campaign_id=str(self.campaign.id),
            targets=[
                {'type': 'location', 'location_id': '123456789', 'location_name': 'Test Location'},
                {'type': 'username', 'username': 'testuser', 'audience_type': 'followers'}
            ]
        )
        
        # Check that the workflow was started successfully
        self.assertTrue(result['success'])
        self.assertEqual(result['status'], 'running')
        
        # Get the workflow execution ID
        workflow_execution_id = result['workflow_execution_id']
        
        # Get the workflow status
        result = self.workflow_service.get_workflow_status(workflow_execution_id)
        
        # Check that the workflow status was retrieved successfully
        self.assertTrue(result['success'])
        
        # Check that the campaign status was updated
        self.campaign.refresh_from_db()
        self.assertEqual(self.campaign.status, 'running')
