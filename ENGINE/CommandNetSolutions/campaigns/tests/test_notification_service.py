"""
Unit tests for Notification Service.
"""
import uuid
from unittest import mock
from django.test import TestCase
from django.contrib.auth.models import User
from campaigns.models import Notification, Campaign
from campaigns.services import NotificationService


class NotificationServiceTestCase(TestCase):
    """Test cases for Notification Service."""

    def setUp(self):
        """Set up test environment."""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create test campaign
        self.campaign = Campaign.objects.create(
            name='Test Campaign',
            description='Test campaign description',
            creator=self.user
        )
        
        # Create service
        self.service = NotificationService()
        
        # Enable notifications for testing
        self.service.email_enabled = True
        self.service.webhook_enabled = True
        self.service.slack_enabled = True

    @mock.patch('campaigns.services.notification_service.send_mail')
    def test_send_email(self, mock_send_mail):
        """Test sending an email notification."""
        # Test sending email
        result = self.service.send_email(
            recipients=['<EMAIL>'],
            subject='Test Subject',
            template='test_template',
            context={'key': 'value'}
        )
        
        # Check result
        self.assertTrue(result['success'])
        self.assertEqual(result['recipients'], ['<EMAIL>'])
        self.assertEqual(result['subject'], 'Test Subject')
        
        # Check if send_mail was called
        mock_send_mail.assert_called_once()
        
        # Test with no recipients
        result = self.service.send_email(
            recipients=[],
            subject='Test Subject',
            template='test_template'
        )
        self.assertFalse(result['success'])
        
        # Test with disabled email
        self.service.email_enabled = False
        result = self.service.send_email(
            recipients=['<EMAIL>'],
            subject='Test Subject',
            template='test_template'
        )
        self.assertFalse(result['success'])

    def test_send_in_app_notification(self):
        """Test sending an in-app notification."""
        # Test sending notification
        result = self.service.send_in_app_notification(
            user_ids=[self.user.id],
            title='Test Notification',
            message='This is a test notification',
            notification_type='info',
            link='/test/link/',
            data={'key': 'value'}
        )
        
        # Check result
        self.assertTrue(result['success'])
        self.assertEqual(result['count'], 1)
        
        # Check if notification was created
        notification = Notification.objects.get(user=self.user)
        self.assertEqual(notification.title, 'Test Notification')
        self.assertEqual(notification.message, 'This is a test notification')
        self.assertEqual(notification.notification_type, 'info')
        self.assertEqual(notification.link, '/test/link/')
        self.assertEqual(notification.data, {'key': 'value'})
        
        # Test with non-existent user
        result = self.service.send_in_app_notification(
            user_ids=[999],
            title='Test Notification',
            message='This is a test notification'
        )
        self.assertTrue(result['success'])  # Should still succeed but with 0 notifications
        self.assertEqual(result['count'], 0)
        
        # Test with no users
        result = self.service.send_in_app_notification(
            user_ids=[],
            title='Test Notification',
            message='This is a test notification'
        )
        self.assertFalse(result['success'])

    @mock.patch('campaigns.services.notification_service.requests.post')
    def test_send_webhook(self, mock_post):
        """Test sending a webhook notification."""
        # Mock response
        mock_response = mock.Mock()
        mock_response.status_code = 200
        mock_post.return_value = mock_response
        
        # Test sending webhook
        result = self.service.send_webhook(
            webhook_url='https://example.com/webhook',
            event_type='test_event',
            payload={'key': 'value'}
        )
        
        # Check result
        self.assertTrue(result['success'])
        
        # Check if requests.post was called
        mock_post.assert_called_once()
        
        # Test with no webhook URL
        result = self.service.send_webhook(
            webhook_url='',
            event_type='test_event',
            payload={'key': 'value'}
        )
        self.assertFalse(result['success'])
        
        # Test with disabled webhook
        self.service.webhook_enabled = False
        result = self.service.send_webhook(
            webhook_url='https://example.com/webhook',
            event_type='test_event',
            payload={'key': 'value'}
        )
        self.assertFalse(result['success'])

    @mock.patch('campaigns.services.notification_service.WebClient')
    def test_send_slack_notification(self, mock_webclient):
        """Test sending a Slack notification."""
        # Mock client
        mock_client = mock.Mock()
        mock_client.chat_postMessage.return_value = {'ts': '1234567890.123456'}
        mock_webclient.return_value = mock_client
        
        # Test sending Slack notification
        result = self.service.send_slack_notification(
            channel='#test-channel',
            message='Test message',
            attachments=[{'text': 'Attachment text'}]
        )
        
        # Check result
        self.assertTrue(result['success'])
        self.assertEqual(result['channel'], '#test-channel')
        self.assertEqual(result['ts'], '1234567890.123456')
        
        # Check if WebClient was called
        mock_webclient.assert_called_once()
        mock_client.chat_postMessage.assert_called_once_with(
            channel='#test-channel',
            text='Test message',
            attachments=[{'text': 'Attachment text'}]
        )
        
        # Test with disabled Slack
        self.service.slack_enabled = False
        result = self.service.send_slack_notification(
            channel='#test-channel',
            message='Test message'
        )
        self.assertFalse(result['success'])

    @mock.patch.object(NotificationService, 'send_in_app_notification')
    @mock.patch.object(NotificationService, 'send_email')
    def test_notify_campaign_status(self, mock_send_email, mock_send_in_app):
        """Test notifying about campaign status."""
        # Mock notification results
        mock_send_in_app.return_value = {'success': True, 'count': 1}
        mock_send_email.return_value = {'success': True, 'recipients': ['<EMAIL>']}
        
        # Test notifying about campaign status
        result = self.service.notify_campaign_status(
            campaign_id=str(self.campaign.id),
            status='running',
            details={'message': 'Campaign is now running'}
        )
        
        # Check result
        self.assertTrue(result['success'])
        
        # Check if notification methods were called
        mock_send_in_app.assert_called_once()
        mock_send_email.assert_called_once()
        
        # Test with non-existent campaign
        result = self.service.notify_campaign_status(
            campaign_id=str(uuid.uuid4()),
            status='running'
        )
        self.assertFalse(result['success'])

    @mock.patch.object(NotificationService, 'send_in_app_notification')
    @mock.patch.object(NotificationService, 'send_email')
    def test_notify_analysis_complete(self, mock_send_email, mock_send_in_app):
        """Test notifying about completed analysis."""
        # Mock notification results
        mock_send_in_app.return_value = {'success': True, 'count': 1}
        mock_send_email.return_value = {'success': True, 'recipients': ['<EMAIL>']}
        
        # Test notifying about analysis completion
        result = self.service.notify_analysis_complete(
            campaign_id=str(self.campaign.id),
            results={
                'total': 100,
                'white_listed': 50,
                'percentage': 50.0
            }
        )
        
        # Check result
        self.assertTrue(result['success'])
        
        # Check if notification methods were called
        mock_send_in_app.assert_called_once()
        mock_send_email.assert_called_once()
        
        # Test with non-existent campaign
        result = self.service.notify_analysis_complete(
            campaign_id=str(uuid.uuid4()),
            results={}
        )
        self.assertFalse(result['success'])
