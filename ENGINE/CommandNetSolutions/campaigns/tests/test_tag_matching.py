"""
Unit tests for tag matching logic.

This module tests the tag matching logic used in the campaign simulation.
"""
import uuid
import unittest
from unittest.mock import patch, MagicMock
from django.test import TestCase
from django.utils import timezone
from django.contrib.auth.models import User

from campaigns.models import (
    Campaign, DynamicTag, TagCategory, TagGroup, CampaignTag, TagAnalysisResult
)
from instagram.models import Accounts


class TagMatchingTestCase(TestCase):
    """Test case for tag matching logic."""

    def setUp(self):
        """Set up test data."""
        # Create a user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )

        # Create a tag category
        self.category = TagCategory.objects.create(
            id=uuid.uuid4(),
            name='Test Category',
            description='Test category description',
            color='#3498db',
            priority=10,
            icon='chart-line'
        )

        # Create a tag group
        self.tag_group = TagGroup.objects.create(
            id=uuid.uuid4(),
            name='Test Group',
            description='Test group description',
            color='#2ecc71',
            is_global=True,
            priority=10
        )

        # Create a campaign
        self.campaign = Campaign.objects.create(
            id=uuid.uuid4(),
            name='Test Campaign',
            description='Test campaign description',
            creator=self.user,
            status='draft',
            target_type='mixed',
            audience_type='profile'
        )

        # Create tags for different field types
        self.bio_tag = DynamicTag.objects.create(
            id=uuid.uuid4(),
            name='Bio Tag',
            description='Tag for bio matching',
            category=self.category,
            tag_group=self.tag_group,
            tag_type='keyword',
            pattern='travel|photography',
            field='bio',
            is_global=True,
            weight=1.0
        )

        self.posts_tag = DynamicTag.objects.create(
            id=uuid.uuid4(),
            name='Posts Tag',
            description='Tag for posts count matching',
            category=self.category,
            tag_group=self.tag_group,
            tag_type='keyword',
            pattern='posts > 100',
            field='number_of_posts',
            is_global=True,
            weight=1.0
        )

        self.followers_tag = DynamicTag.objects.create(
            id=uuid.uuid4(),
            name='Followers Tag',
            description='Tag for followers count matching',
            category=self.category,
            tag_group=self.tag_group,
            tag_type='keyword',
            pattern='followers > 1000',
            field='followers',
            is_global=True,
            weight=1.0
        )

        self.engagement_tag = DynamicTag.objects.create(
            id=uuid.uuid4(),
            name='Engagement Tag',
            description='Tag for engagement rate matching',
            category=self.category,
            tag_group=self.tag_group,
            tag_type='keyword',
            pattern='engagement > 5%',
            field='engagement_rate',
            is_global=True,
            weight=1.0
        )

        # Assign tags to campaign
        for tag in [self.bio_tag, self.posts_tag, self.followers_tag, self.engagement_tag]:
            CampaignTag.objects.create(
                id=uuid.uuid4(),
                campaign=self.campaign,
                tag=tag,
                is_required=False
            )

        # Create test accounts
        self.account1 = Accounts.objects.create(
            username='test_user_1',
            full_name='Test User 1',
            bio='I love travel and photography',
            followers=5000,
            following=500,
            number_of_posts=200,
            account_type='personal',
            is_verified=True,
            campaign_id=str(self.campaign.id),
            collection_date=timezone.now(),
            interests=['travel', 'photography'],
            locations=['NYC']
        )

        self.account2 = Accounts.objects.create(
            username='test_user_2',
            full_name='Test User 2',
            bio='Just a regular user',
            followers=500,
            following=200,
            number_of_posts=50,
            account_type='personal',
            is_verified=False,
            campaign_id=str(self.campaign.id),
            collection_date=timezone.now(),
            interests=['food', 'music'],
            locations=['LA']
        )

        self.account3 = Accounts.objects.create(
            username='test_user_3',
            full_name='Test User 3',
            bio='Influencer and content creator',
            followers=50000,
            following=1000,
            number_of_posts=500,
            account_type='creator',
            is_verified=True,
            campaign_id=str(self.campaign.id),
            collection_date=timezone.now(),
            interests=['fashion', 'beauty'],
            locations=['London']
        )

    def test_bio_tag_matching(self):
        """Test bio tag matching logic."""
        # Import the tag matching function from the simulation command
        from campaigns.management.commands.simulate_full_campaign import Command

        # Create a command instance
        command = Command()

        # Mock the tag analysis result creation
        with patch('campaigns.models.TagAnalysisResult.objects.create') as mock_create:
            # Call the simulate_analysis_workflow method
            command.simulate_analysis_workflow(self.campaign, 0)

            # Check that TagAnalysisResult.objects.create was called for account1 with bio_tag
            bio_tag_calls = [
                call for call in mock_create.call_args_list
                if call[1]['account_id'] == 'test_user_1' and call[1]['tag'] == self.bio_tag
            ]
            self.assertTrue(bio_tag_calls, "Bio tag should match account1")

            # Check that TagAnalysisResult.objects.create was not called for account2 with bio_tag
            bio_tag_calls_account2 = [
                call for call in mock_create.call_args_list
                if call[1]['account_id'] == 'test_user_2' and call[1]['tag'] == self.bio_tag
            ]
            self.assertFalse(bio_tag_calls_account2, "Bio tag should not match account2")

    def test_posts_tag_matching(self):
        """Test posts tag matching logic."""
        # Import the tag matching function from the simulation command
        from campaigns.management.commands.simulate_full_campaign import Command

        # Create a command instance
        command = Command()

        # Mock the tag analysis result creation
        with patch('campaigns.models.TagAnalysisResult.objects.create') as mock_create:
            # Call the simulate_analysis_workflow method
            command.simulate_analysis_workflow(self.campaign, 0)

            # Check that TagAnalysisResult.objects.create was called for account1 and account3 with posts_tag
            posts_tag_calls_account1 = [
                call for call in mock_create.call_args_list
                if call[1]['account_id'] == 'test_user_1' and call[1]['tag'] == self.posts_tag
            ]
            self.assertTrue(posts_tag_calls_account1, "Posts tag should match account1")

            posts_tag_calls_account3 = [
                call for call in mock_create.call_args_list
                if call[1]['account_id'] == 'test_user_3' and call[1]['tag'] == self.posts_tag
            ]
            self.assertTrue(posts_tag_calls_account3, "Posts tag should match account3")

            # Check that TagAnalysisResult.objects.create was not called for account2 with posts_tag
            posts_tag_calls_account2 = [
                call for call in mock_create.call_args_list
                if call[1]['account_id'] == 'test_user_2' and call[1]['tag'] == self.posts_tag
            ]
            self.assertFalse(posts_tag_calls_account2, "Posts tag should not match account2")

    def test_followers_tag_matching(self):
        """Test followers tag matching logic."""
        # Import the tag matching function from the simulation command
        from campaigns.management.commands.simulate_full_campaign import Command

        # Create a command instance
        command = Command()

        # Mock the tag analysis result creation
        with patch('campaigns.models.TagAnalysisResult.objects.create') as mock_create:
            # Call the simulate_analysis_workflow method
            command.simulate_analysis_workflow(self.campaign, 0)

            # Check that TagAnalysisResult.objects.create was called for account1 and account3 with followers_tag
            followers_tag_calls_account1 = [
                call for call in mock_create.call_args_list
                if call[1]['account_id'] == 'test_user_1' and call[1]['tag'] == self.followers_tag
            ]
            self.assertTrue(followers_tag_calls_account1, "Followers tag should match account1")

            followers_tag_calls_account3 = [
                call for call in mock_create.call_args_list
                if call[1]['account_id'] == 'test_user_3' and call[1]['tag'] == self.followers_tag
            ]
            self.assertTrue(followers_tag_calls_account3, "Followers tag should match account3")

            # Check that TagAnalysisResult.objects.create was not called for account2 with followers_tag
            followers_tag_calls_account2 = [
                call for call in mock_create.call_args_list
                if call[1]['account_id'] == 'test_user_2' and call[1]['tag'] == self.followers_tag
            ]
            self.assertFalse(followers_tag_calls_account2, "Followers tag should not match account2")

    def test_engagement_tag_matching(self):
        """Test engagement tag matching logic."""
        # Import the tag matching function from the simulation command
        from campaigns.management.commands.simulate_full_campaign import Command

        # Create a command instance
        command = Command()

        # Mock the tag analysis result creation and random functions
        with patch('campaigns.models.TagAnalysisResult.objects.create') as mock_create, \
             patch('random.randint') as mock_randint, \
             patch('random.uniform') as mock_uniform:

            # Set up mock return values for engagement rate calculation
            # For account1: 5000 followers, set up for ~6% engagement
            mock_randint.side_effect = lambda min_val, max_val: {
                # For account1: likes = 250 (5% of followers), comments = 50 (20% of likes)
                (50, 1000): 250,  # likes for account1
                (2, 25): 50,      # comments for account1
                # For account2: likes = 10 (2% of followers), comments = 1 (10% of likes)
                (5, 100): 10,     # likes for account2
                (0, 1): 1,        # comments for account2
                # For account3: likes = 5000 (10% of followers), comments = 500 (10% of likes)
                (500, 10000): 5000,  # likes for account3
                (50, 500): 500,      # comments for account3
            }.get((min_val, max_val), 0)

            # Set up mock return values for confidence calculation
            mock_uniform.return_value = 0.8

            # Call the simulate_analysis_workflow method
            command.simulate_analysis_workflow(self.campaign, 0)

            # Check that TagAnalysisResult.objects.create was called for account1 and account3 with engagement_tag
            engagement_tag_calls_account1 = [
                call for call in mock_create.call_args_list
                if call[1]['account_id'] == 'test_user_1' and call[1]['tag'] == self.engagement_tag
            ]
            self.assertTrue(engagement_tag_calls_account1, "Engagement tag should match account1")

            engagement_tag_calls_account3 = [
                call for call in mock_create.call_args_list
                if call[1]['account_id'] == 'test_user_3' and call[1]['tag'] == self.engagement_tag
            ]
            self.assertTrue(engagement_tag_calls_account3, "Engagement tag should match account3")

            # Check that TagAnalysisResult.objects.create was not called for account2 with engagement_tag
            engagement_tag_calls_account2 = [
                call for call in mock_create.call_args_list
                if call[1]['account_id'] == 'test_user_2' and call[1]['tag'] == self.engagement_tag
            ]
            self.assertFalse(engagement_tag_calls_account2, "Engagement tag should not match account2")

    def test_whitelist_generation(self):
        """Test whitelist generation logic."""
        # Import the tag matching function from the simulation command
        from campaigns.management.commands.simulate_full_campaign import Command
        from instagram.models import WhiteListEntry

        # Create a command instance
        command = Command()

        # Make one of the tags required
        campaign_tag = CampaignTag.objects.get(campaign=self.campaign, tag=self.bio_tag)
        campaign_tag.is_required = True
        campaign_tag.save()

        # Call the simulate_analysis_workflow method
        command.simulate_analysis_workflow(self.campaign, 0)

        # Check that WhiteListEntry was created for account1 (matches bio_tag which is required)
        whitelist_entry_account1 = WhiteListEntry.objects.filter(account=self.account1).exists()
        self.assertTrue(whitelist_entry_account1, "Account1 should be whitelisted")

        # Check that WhiteListEntry was not created for account2 (doesn't match any required tag)
        whitelist_entry_account2 = WhiteListEntry.objects.filter(account=self.account2).exists()
        self.assertFalse(whitelist_entry_account2, "Account2 should not be whitelisted")

        # Account3 might be whitelisted based on random chance, so we don't test it

    def test_edge_cases(self):
        """Test edge cases in tag matching logic."""
        # Import the tag matching function from the simulation command
        from campaigns.management.commands.simulate_full_campaign import Command

        # Create a command instance
        command = Command()

        # Create an account with empty bio
        account_empty_bio = Accounts.objects.create(
            username='test_user_empty_bio',
            full_name='Test User Empty Bio',
            bio='',
            followers=1000,
            following=500,
            number_of_posts=100,
            account_type='personal',
            is_verified=False,
            campaign_id=str(self.campaign.id),
            collection_date=timezone.now(),
            interests=[],
            locations=[]
        )

        # Create an account with very high follower count
        account_high_followers = Accounts.objects.create(
            username='test_user_high_followers',
            full_name='Test User High Followers',
            bio='Just a user with many followers',
            followers=1000000,
            following=1000,
            number_of_posts=100,
            account_type='creator',
            is_verified=True,
            campaign_id=str(self.campaign.id),
            collection_date=timezone.now(),
            interests=['fashion'],
            locations=['NYC']
        )

        # Mock the tag analysis result creation
        with patch('campaigns.models.TagAnalysisResult.objects.create') as mock_create:
            # Call the simulate_analysis_workflow method
            command.simulate_analysis_workflow(self.campaign, 0)

            # Check that bio tag doesn't match account with empty bio
            bio_tag_calls_empty_bio = [
                call for call in mock_create.call_args_list
                if call[1]['account_id'] == 'test_user_empty_bio' and call[1]['tag'] == self.bio_tag
            ]
            self.assertFalse(bio_tag_calls_empty_bio, "Bio tag should not match account with empty bio")

            # Check that followers tag matches account with high followers
            followers_tag_calls_high_followers = [
                call for call in mock_create.call_args_list
                if call[1]['account_id'] == 'test_user_high_followers' and call[1]['tag'] == self.followers_tag
            ]
            self.assertTrue(followers_tag_calls_high_followers, "Followers tag should match account with high followers")
