"""
Integration tests for Campaign Analysis System.
"""
import uuid
from unittest import mock
from django.test import TestCase, Client
from django.urls import reverse
from django.contrib.auth.models import User
from rest_framework import status
from campaigns.models import Campaign, Notification
from campaigns.services import PyFlowService, NotificationService


class CampaignWorkflowIntegrationTestCase(TestCase):
    """Integration tests for campaign workflow."""

    def setUp(self):
        """Set up test environment."""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword',
            is_staff=True
        )
        
        # Create test client
        self.client = Client()
        self.client.login(username='testuser', password='testpassword')
        
        # Create test campaign
        self.campaign = Campaign.objects.create(
            name='Test Campaign',
            description='Test campaign description',
            creator=self.user,
            target_type='location'
        )
        
        # Mock PyFlow service
        self.pyflow_service_patcher = mock.patch('campaigns.services.PyFlowService')
        self.mock_pyflow_service = self.pyflow_service_patcher.start()
        self.mock_pyflow_service.return_value.create_and_run_collection_workflow.return_value = {
            'success': True,
            'workflow_path': '/path/to/workflow.pygraph'
        }
        self.mock_pyflow_service.return_value.create_and_run_analysis_workflow.return_value = {
            'success': True,
            'workflow_path': '/path/to/analysis_workflow.pygraph'
        }
        
        # Mock notification service
        self.notification_service_patcher = mock.patch('campaigns.services.NotificationService')
        self.mock_notification_service = self.notification_service_patcher.start()
        self.mock_notification_service.return_value.notify_campaign_status.return_value = {
            'success': True
        }
        self.mock_notification_service.return_value.notify_analysis_complete.return_value = {
            'success': True
        }
    
    def tearDown(self):
        """Clean up test environment."""
        self.pyflow_service_patcher.stop()
        self.notification_service_patcher.stop()
    
    def test_campaign_launch_workflow(self):
        """Test launching a campaign workflow."""
        # Add location target to campaign
        url = reverse('campaign-add-location-target', args=[str(self.campaign.id)])
        data = {
            'country': 'United States',
            'city': 'New York',
            'location_id': '123456789'
        }
        response = self.client.post(url, data, content_type='application/json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Launch campaign with PyFlow
        url = reverse('campaign-launch', args=[str(self.campaign.id)])
        data = {
            'launch_method': 'pyflow'
        }
        response = self.client.post(url, data, content_type='application/json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check if PyFlow service was called
        self.mock_pyflow_service.return_value.create_and_run_collection_workflow.assert_called_once()
        
        # Check if campaign status was updated
        self.campaign.refresh_from_db()
        self.assertEqual(self.campaign.status, 'running')
        
        # Check if notification service was called
        self.mock_notification_service.return_value.notify_campaign_status.assert_called_once()
    
    def test_campaign_analysis_workflow(self):
        """Test analyzing a campaign workflow."""
        # Analyze campaign with PyFlow
        url = reverse('campaign-analyze', args=[str(self.campaign.id)])
        data = {
            'analysis_method': 'pyflow'
        }
        response = self.client.post(url, data, content_type='application/json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check if PyFlow service was called
        self.mock_pyflow_service.return_value.create_and_run_analysis_workflow.assert_called_once()
        
        # Check if notification service was called
        # Note: In this test, notification is not called directly since we're mocking the workflow
        # In a real scenario, the notification would be called after the workflow completes


class NotificationIntegrationTestCase(TestCase):
    """Integration tests for notification system."""

    def setUp(self):
        """Set up test environment."""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword'
        )
        
        # Create test client
        self.client = Client()
        self.client.login(username='testuser', password='testpassword')
        
        # Create test notifications
        self.notifications = []
        for i in range(3):
            notification = Notification.objects.create(
                user=self.user,
                title=f'Test Notification {i}',
                message=f'This is test notification {i}',
                notification_type='info'
            )
            self.notifications.append(notification)
    
    def test_list_notifications(self):
        """Test listing notifications."""
        url = reverse('notification-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check response data
        data = response.json()
        self.assertEqual(data['total_count'], 3)
        self.assertEqual(data['unread_count'], 3)
    
    def test_mark_notification_as_read(self):
        """Test marking a notification as read."""
        url = reverse('notification-mark-as-read', args=[str(self.notifications[0].id)])
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check if notification was marked as read
        self.notifications[0].refresh_from_db()
        self.assertTrue(self.notifications[0].is_read)
        
        # Check unread count
        url = reverse('notification-unread-count')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['unread_count'], 2)
    
    def test_mark_all_notifications_as_read(self):
        """Test marking all notifications as read."""
        url = reverse('notification-mark-all-as-read')
        response = self.client.post(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check if all notifications were marked as read
        for notification in self.notifications:
            notification.refresh_from_db()
            self.assertTrue(notification.is_read)
        
        # Check unread count
        url = reverse('notification-unread-count')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.json()['unread_count'], 0)


class ExternalAPIIntegrationTestCase(TestCase):
    """Integration tests for external API integration."""

    def setUp(self):
        """Set up test environment."""
        # Create test user
        self.user = User.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpassword',
            is_staff=True
        )
        
        # Create test client
        self.client = Client()
        self.client.login(username='testuser', password='testpassword')
        
        # Mock external API service
        self.external_api_service_patcher = mock.patch('campaigns.services.ExternalAPIService')
        self.mock_external_api_service = self.external_api_service_patcher.start()
        self.mock_external_api_service.return_value.list_apis.return_value = [
            {
                'name': 'test_api',
                'base_url': 'https://api.example.com',
                'auth_type': 'api_key',
                'description': 'Test API'
            }
        ]
        self.mock_external_api_service.return_value.get_api_config.return_value = {
            'success': True,
            'config': {
                'base_url': 'https://api.example.com',
                'auth_type': 'api_key',
                'api_key_name': 'X-API-Key',
                'api_key': '********',
                'description': 'Test API'
            }
        }
        self.mock_external_api_service.return_value.configure_api.return_value = {
            'success': True,
            'message': 'API configured successfully'
        }
        self.mock_external_api_service.return_value.call_api.return_value = {
            'success': True,
            'data': {'key': 'value'},
            'status_code': 200
        }
    
    def tearDown(self):
        """Clean up test environment."""
        self.external_api_service_patcher.stop()
    
    def test_list_apis(self):
        """Test listing APIs."""
        url = reverse('external-api-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check response data
        data = response.json()
        self.assertEqual(len(data), 1)
        self.assertEqual(data[0]['name'], 'test_api')
    
    def test_get_api_config(self):
        """Test getting API configuration."""
        url = reverse('external-api-detail', args=['test_api'])
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check response data
        data = response.json()
        self.assertEqual(data['base_url'], 'https://api.example.com')
        self.assertEqual(data['auth_type'], 'api_key')
    
    def test_configure_api(self):
        """Test configuring an API."""
        url = reverse('external-api-list')
        data = {
            'name': 'new_api',
            'config': {
                'base_url': 'https://new.example.com',
                'auth_type': 'none',
                'description': 'New API'
            }
        }
        response = self.client.post(url, data, content_type='application/json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check if service was called
        self.mock_external_api_service.return_value.configure_api.assert_called_once()
    
    def test_call_api(self):
        """Test calling an API."""
        url = reverse('external-api-call', args=['test_api'])
        data = {
            'endpoint': 'test_endpoint',
            'method': 'GET',
            'params': {'param1': 'value1'}
        }
        response = self.client.post(url, data, content_type='application/json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check if service was called
        self.mock_external_api_service.return_value.call_api.assert_called_once()
