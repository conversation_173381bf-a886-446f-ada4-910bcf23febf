"""
Forms for CEP (Customer Engagement Process) workflow management.

This module provides forms for creating and managing CEP workflows.
"""
from django import forms
from campaigns.models import Campaign
from campaigns.models.cep import CEPWorkflow

class CEPWorkflowForm(forms.Form):
    """
    Form for creating a new CEP workflow.
    """
    campaign = forms.UUIDField(
        label="Campaign",
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    subscription_tier = forms.ChoiceField(
        label="Subscription Tier",
        choices=CEPWorkflow.SUBSCRIPTION_TIER_CHOICES,
        initial='bronze',
        widget=forms.Select(attrs={'class': 'form-control'})
    )
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Get available campaigns
        available_campaigns = Campaign.objects.filter(
            is_in_cep=False,
            status__in=['completed', 'stopped']
        ).order_by('-created_at')
        
        # Create choices for campaign field
        campaign_choices = [(str(campaign.id), campaign.name) for campaign in available_campaigns]
        
        # Update campaign field
        self.fields['campaign'].widget.choices = campaign_choices
        
        # Add empty choice if no campaigns available
        if not campaign_choices:
            self.fields['campaign'].widget.choices = [('', '-- No campaigns available --')]
            self.fields['campaign'].disabled = True
    
    def clean_campaign(self):
        """
        Validate that the campaign exists and is available.
        """
        campaign_id = self.cleaned_data['campaign']
        
        try:
            campaign = Campaign.objects.get(id=campaign_id)
            
            # Check if campaign is already in CEP
            if campaign.is_in_cep:
                raise forms.ValidationError("This campaign is already in a CEP workflow.")
            
            # Check if campaign has a whitelist
            from instagram.models import WhiteListEntry
            whitelist_count = WhiteListEntry.objects.filter(
                account__campaign_id=str(campaign.id)
            ).count()
            
            if whitelist_count == 0:
                raise forms.ValidationError("This campaign has no accounts in the whitelist.")
            
            return str(campaign_id)
        except Campaign.DoesNotExist:
            raise forms.ValidationError("Invalid campaign selected.")
        except Exception as e:
            raise forms.ValidationError(f"Error validating campaign: {str(e)}")
    
    def clean_subscription_tier(self):
        """
        Validate the subscription tier.
        """
        subscription_tier = self.cleaned_data['subscription_tier']
        
        if subscription_tier not in dict(CEPWorkflow.SUBSCRIPTION_TIER_CHOICES):
            raise forms.ValidationError("Invalid subscription tier selected.")
        
        return subscription_tier
