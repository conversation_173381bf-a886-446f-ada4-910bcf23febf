"""
Forms for tag-related functionality in the campaigns app.
"""
from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
import json
import uuid
import logging

from campaigns.models import DynamicTag, CampaignTagRule, CampaignTagCondition, TagGroup

logger = logging.getLogger(__name__)


class DynamicTagForm(forms.ModelForm):
    """
    Form for creating and editing dynamic tags.
    """
    # Add a hidden field for conditions JSON
    conditions_json = forms.CharField(
        required=False,
        widget=forms.HiddenInput(attrs={'id': 'conditions-json'})
    )

    # Add a field for condition logic
    condition_logic = forms.ChoiceField(
        choices=[('all', 'All conditions must match'), ('any', 'Any condition can match')],
        initial='all',
        required=False,
        widget=forms.RadioSelect
    )

    class Meta:
        model = DynamicTag
        fields = [
            'name', 'description', 'category', 'tag_group', 'tag_type',
            'pattern', 'field', 'is_global', 'is_system'
        ]
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'category': forms.Select(attrs={'class': 'form-select'}),
            'tag_group': forms.Select(attrs={'class': 'form-select'}),
            'tag_type': forms.HiddenInput(),
            'pattern': forms.HiddenInput(),
            'field': forms.HiddenInput(),
            'is_global': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_system': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

        # Make tag_group optional
        self.fields['tag_group'].required = False

        # Set initial values for existing tag
        if self.instance and self.instance.pk:
            # Try to get the condition logic from the pattern
            try:
                pattern_data = json.loads(self.instance.pattern)
                if 'logic' in pattern_data:
                    self.fields['condition_logic'].initial = pattern_data.get('logic', 'all')
            except (json.JSONDecodeError, TypeError):
                pass

    def clean(self):
        """
        Validate the form data.
        """
        cleaned_data = super().clean()

        # Get the conditions from the conditions_json field
        conditions_json = cleaned_data.get('conditions_json', '[]')
        try:
            conditions = json.loads(conditions_json)
            cleaned_data['conditions'] = conditions
        except json.JSONDecodeError:
            conditions = []
            cleaned_data['conditions'] = []
            self.add_error('conditions_json', _("Invalid JSON format for conditions."))

        # Validate conditions
        for i, condition in enumerate(conditions):
            if not isinstance(condition, dict):
                self.add_error('conditions_json', _("Condition {} is not a valid object.").format(i + 1))
                continue

            # Check required fields
            required_fields = ['field', 'operator', 'value']
            for field in required_fields:
                if field not in condition:
                    self.add_error('conditions_json', _("Condition {} is missing the '{}' field.").format(i + 1, field))

        return cleaned_data

    def save_conditions(self, tag):
        """
        Save the conditions for a tag.
        This method can be called separately after saving the tag.
        """
        from campaigns.models import CampaignTagRule, CampaignTagCondition
        import logging
        logger = logging.getLogger(__name__)

        # Get the conditions and logic from cleaned_data
        conditions = self.cleaned_data.get('conditions', [])
        condition_logic = self.cleaned_data.get('condition_logic', 'all')

        # Get conditions from conditions_json if not already parsed
        if not conditions and 'conditions_json' in self.cleaned_data:
            try:
                conditions_json = self.cleaned_data.get('conditions_json', '[]')
                logger.info(f"Parsing conditions from conditions_json: {conditions_json}")
                conditions = json.loads(conditions_json)
                self.cleaned_data['conditions'] = conditions
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing conditions_json: {str(e)}")
                conditions = []

        # Ensure we have the conditions_json field even if it wasn't in the form
        if 'conditions_json' not in self.cleaned_data and hasattr(self, 'data') and 'conditions_json' in self.data:
            try:
                conditions_json = self.data.get('conditions_json', '[]')
                logger.info(f"Getting conditions_json from form data: {conditions_json}")
                conditions = json.loads(conditions_json)
                self.cleaned_data['conditions'] = conditions
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing conditions_json from form data: {str(e)}")

        # If we still don't have conditions, check if there's a POST parameter
        if not conditions and hasattr(self, 'data') and 'conditions_json' in self.data:
            try:
                conditions_json = self.data.get('conditions_json', '[]')
                logger.info(f"Getting conditions_json from POST data: {conditions_json}")
                conditions = json.loads(conditions_json)
                self.cleaned_data['conditions'] = conditions
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing conditions_json from POST data: {str(e)}")

        # Log the conditions
        logger.info(f"Conditions to save: {conditions}")
        logger.info(f"Condition logic: {condition_logic}")
        logger.info(f"Tag ID: {tag.id}, Tag name: {tag.name}")

        # Check if we have a rule ID in the pattern
        rule_id = None
        try:
            pattern_data = json.loads(tag.pattern) if tag.pattern else {}
            rule_id = pattern_data.get('rule_id')
            logger.info(f"Found rule_id in pattern: {rule_id}")
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"Error parsing pattern data: {str(e)}")
            pattern_data = {}

        # Get or create the rule
        if rule_id:
            try:
                rule = CampaignTagRule.objects.get(id=rule_id)
                logger.info(f"Found existing rule with ID {rule_id}")
            except CampaignTagRule.DoesNotExist:
                logger.warning(f"Rule with ID {rule_id} not found, creating new rule")
                rule = CampaignTagRule.objects.create(
                    id=uuid.uuid4(),
                    name=f"Rule for {tag.name}",
                    tag=tag.name,
                    logic=condition_logic,
                    active=True,
                    is_global=tag.is_global
                )
        else:
            # Create a new rule
            new_rule_id = uuid.uuid4()
            logger.info(f"Creating new rule with ID {new_rule_id}")
            rule = CampaignTagRule.objects.create(
                id=new_rule_id,
                name=f"Rule for {tag.name}",
                tag=tag.name,
                logic=condition_logic,
                active=True,
                is_global=tag.is_global
            )
            logger.info(f"Created new rule with ID {rule.id}")

        # Update the rule logic
        rule.logic = condition_logic
        rule.save()
        logger.info(f"Updated rule logic to {condition_logic}")

        # Update the tag pattern to include the rule ID
        pattern_data = {
            'rule_id': str(rule.id),
            'logic': condition_logic
        }
        tag.pattern = json.dumps(pattern_data)
        tag.save()
        logger.info(f"Updated tag pattern with rule ID: {pattern_data}")

        # Delete existing conditions
        existing_conditions = rule.conditions.all()
        logger.info(f"Deleting {existing_conditions.count()} existing conditions")
        existing_conditions.delete()

        # Create new conditions
        created_conditions = []
        for i, condition in enumerate(conditions):
            # Skip invalid conditions
            if not isinstance(condition, dict):
                logger.warning(f"Skipping invalid condition {i}: not a dictionary")
                continue

            # Get condition fields
            field = condition.get('field', '')
            operator = condition.get('operator', '')
            value = condition.get('value', '')
            required = condition.get('required', False)
            field_type = condition.get('field_type', 'text')

            if not field or not operator:
                logger.warning(f"Skipping condition {i} with missing field or operator: {condition}")
                continue

            # Create the condition
            try:
                new_condition = CampaignTagCondition.objects.create(
                    id=uuid.uuid4(),
                    rule=rule,
                    field=field,
                    field_type=field_type,
                    operator=operator,
                    value=value,
                    required=required
                )
                created_conditions.append(new_condition)
                logger.info(f"Created condition {i}: field={field}, operator={operator}, value={value}")
            except Exception as e:
                logger.error(f"Error creating condition {i}: {str(e)}")

        logger.info(f"Created {len(created_conditions)} new conditions")

        # Verify the conditions were saved
        saved_conditions = rule.conditions.all()
        logger.info(f"Verification: Found {saved_conditions.count()} conditions for rule {rule.id}")

        return True
