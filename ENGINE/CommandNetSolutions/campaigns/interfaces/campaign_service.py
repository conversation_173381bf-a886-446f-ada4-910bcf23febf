"""
Interface for campaign service.
"""
from abc import ABC, abstractmethod


class CampaignServiceInterface(ABC):
    """
    Interface for campaign service.
    Defines contract for campaign operations.
    """
    
    @abstractmethod
    def create_campaign(self, campaign_data, user):
        """
        Create a new campaign.
        
        Args:
            campaign_data (dict): Campaign data
            user: User creating the campaign
            
        Returns:
            Campaign: Created campaign
        """
        pass
    
    @abstractmethod
    def update_campaign(self, campaign_id, campaign_data):
        """
        Update an existing campaign.
        
        Args:
            campaign_id (uuid): Campaign ID
            campaign_data (dict): Updated campaign data
            
        Returns:
            Campaign: Updated campaign
        """
        pass
    
    @abstractmethod
    def launch_campaign(self, campaign_id):
        """
        Launch a campaign.
        
        Args:
            campaign_id (uuid): Campaign ID
            
        Returns:
            Campaign: Updated campaign
        """
        pass
    
    @abstractmethod
    def get_campaign(self, campaign_id):
        """
        Get campaign by ID.
        
        Args:
            campaign_id (uuid): Campaign ID
            
        Returns:
            Campaign: Campaign instance
        """
        pass
    
    @abstractmethod
    def list_campaigns(self, filters=None, order_by=None):
        """
        List campaigns with optional filtering and ordering.
        
        Args:
            filters (dict): Optional filters
            order_by (str): Optional ordering field
            
        Returns:
            list: List of campaigns
        """
        pass
