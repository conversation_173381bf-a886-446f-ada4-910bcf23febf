"""
Interface for analysis service.
"""
from abc import ABC, abstractmethod


class AnalysisServiceInterface(ABC):
    """
    Interface for analysis service.
    Defines contract for analysis operations.
    """
    
    @abstractmethod
    def analyze_campaign(self, campaign_id, options=None):
        """
        Analyze campaign accounts.
        
        Args:
            campaign_id (uuid): Campaign ID
            options (dict): Optional analysis options
            
        Returns:
            dict: Analysis results
        """
        pass
    
    @abstractmethod
    def get_analysis_stats(self, campaign_id):
        """
        Get analysis statistics for a campaign.
        
        Args:
            campaign_id (uuid): Campaign ID
            
        Returns:
            dict: Analysis statistics
        """
        pass
    
    @abstractmethod
    def apply_tags(self, account_id, tags):
        """
        Apply tags to account.
        
        Args:
            account_id (str): Account ID
            tags (list): List of tags to apply
            
        Returns:
            bool: Success status
        """
        pass
