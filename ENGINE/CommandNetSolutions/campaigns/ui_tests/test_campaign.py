"""
Campaign UI tests.
"""
import uuid
from campaigns.ui_tests.base import UITestCase
from campaigns.ui_tests.pages.dashboard_page import DashboardPage
from campaigns.ui_tests.pages.campaign_page import CampaignFormPage, CampaignDetailPage
from campaigns.models import Campaign


class CampaignUITestCase(UITestCase):
    """Test case for campaign UI."""
    
    def setUp(self):
        """Set up the test case."""
        super().setUp()
        self.dashboard_page = DashboardPage(self)
        self.campaign_form_page = CampaignFormPage(self)
        self.campaign_detail_page = CampaignDetailPage(self)
        
        # Login
        self.login()
    
    def test_create_campaign(self):
        """Test creating a campaign."""
        # Generate unique campaign name
        campaign_name = f"Test Campaign {uuid.uuid4().hex[:8]}"
        
        # Go to dashboard
        self.dashboard_page.navigate()
        
        # Get initial campaign count
        initial_count = self.dashboard_page.get_campaign_count()
        
        # Click new campaign button
        self.dashboard_page.click_new_campaign()
        
        # Create campaign
        self.campaign_form_page.create_campaign(
            name=campaign_name,
            description="Test campaign description",
            target_type="location",
            audience_type="followers"
        )
        
        # Verify redirect to campaign detail
        self.campaign_detail_page.wait_for_page_load()
        self.assertEqual(self.campaign_detail_page.get_campaign_name(), campaign_name)
        
        # Go back to dashboard
        self.dashboard_page.navigate()
        
        # Verify campaign count increased
        self.assertEqual(self.dashboard_page.get_campaign_count(), initial_count + 1)
        
        # Take screenshot
        self.take_screenshot('create_campaign')
    
    def test_create_campaign_validation(self):
        """Test campaign form validation."""
        # Go to campaign form
        self.campaign_form_page.navigate()
        
        # Submit empty form
        self.campaign_form_page.wait_for_element(self.campaign_form_page.submit_button).click()
        
        # Verify error message
        self.assertTrue(self.campaign_form_page.is_error_displayed())
        
        # Take screenshot
        self.take_screenshot('campaign_validation')
    
    def test_view_campaign_details(self):
        """Test viewing campaign details."""
        # Create test campaign
        campaign = Campaign.objects.create(
            name=f"Test Campaign {uuid.uuid4().hex[:8]}",
            description="Test campaign description",
            target_type="location",
            audience_type="followers",
            creator=self.user
        )
        
        # Set campaign ID in detail page
        self.campaign_detail_page.set_campaign_id(campaign.id)
        
        # Navigate to campaign detail
        self.campaign_detail_page.navigate()
        
        # Verify campaign details
        self.assertEqual(self.campaign_detail_page.get_campaign_name(), campaign.name)
        self.assertEqual(self.campaign_detail_page.get_campaign_status(), "draft")
        
        # Take screenshot
        self.take_screenshot('view_campaign')
    
    def test_delete_campaign(self):
        """Test deleting a campaign."""
        # Create test campaign
        campaign = Campaign.objects.create(
            name=f"Test Campaign {uuid.uuid4().hex[:8]}",
            description="Test campaign description",
            target_type="location",
            audience_type="followers",
            creator=self.user
        )
        
        # Set campaign ID in detail page
        self.campaign_detail_page.set_campaign_id(campaign.id)
        
        # Navigate to campaign detail
        self.campaign_detail_page.navigate()
        
        # Delete campaign
        self.campaign_detail_page.click_delete()
        
        # Take screenshot of confirmation dialog
        self.take_screenshot('delete_confirmation')
        
        # Confirm deletion
        self.campaign_detail_page.confirm_delete()
        
        # Verify redirect to dashboard
        self.dashboard_page.wait_for_page_load()
        
        # Verify campaign is deleted
        with self.assertRaises(Campaign.DoesNotExist):
            Campaign.objects.get(id=campaign.id)
    
    def test_campaign_tabs(self):
        """Test campaign tabs."""
        # Create test campaign
        campaign = Campaign.objects.create(
            name=f"Test Campaign {uuid.uuid4().hex[:8]}",
            description="Test campaign description",
            target_type="location",
            audience_type="followers",
            creator=self.user
        )
        
        # Set campaign ID in detail page
        self.campaign_detail_page.set_campaign_id(campaign.id)
        
        # Navigate to campaign detail
        self.campaign_detail_page.navigate()
        
        # Go to targets tab
        self.campaign_detail_page.go_to_targets_tab()
        self.assert_element_exists(self.campaign_detail_page.add_location_target_button)
        self.take_screenshot('targets_tab')
        
        # Go to results tab
        self.campaign_detail_page.go_to_results_tab()
        self.take_screenshot('results_tab')
        
        # Go to settings tab
        self.campaign_detail_page.go_to_settings_tab()
        self.take_screenshot('settings_tab')
        
        # Go to ROI tab
        self.campaign_detail_page.go_to_roi_tab()
        self.take_screenshot('roi_tab')
