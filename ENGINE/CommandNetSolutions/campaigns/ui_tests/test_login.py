"""
Login UI tests.
"""
from campaigns.ui_tests.base import UITestCase
from campaigns.ui_tests.pages.login_page import LoginPage
from campaigns.ui_tests.pages.dashboard_page import DashboardPage


class LoginUITestCase(UITestCase):
    """Test case for login UI."""
    
    def setUp(self):
        """Set up the test case."""
        super().setUp()
        self.login_page = LoginPage(self)
        self.dashboard_page = DashboardPage(self)
    
    def test_successful_login(self):
        """Test successful login."""
        # Login with valid credentials
        self.login_page.login(self.username, self.password)
        
        # Verify redirect to dashboard
        self.assertTrue(self.dashboard_page.is_loaded())
        
        # Take screenshot
        self.take_screenshot('successful_login')
    
    def test_failed_login(self):
        """Test failed login."""
        # Login with invalid credentials
        self.login_page.login(self.username, 'wrongpassword')
        
        # Verify error message
        self.assertTrue(self.login_page.is_error_displayed())
        
        # Take screenshot
        self.take_screenshot('failed_login')
    
    def test_empty_credentials(self):
        """Test login with empty credentials."""
        # Login with empty credentials
        self.login_page.login('', '')
        
        # Verify error message
        self.assertTrue(self.login_page.is_error_displayed())
        
        # Take screenshot
        self.take_screenshot('empty_credentials')
    
    def test_logout(self):
        """Test logout."""
        # Login
        self.login()
        
        # Verify dashboard is loaded
        self.assertTrue(self.dashboard_page.is_loaded())
        
        # Logout
        self.dashboard_page.logout()
        
        # Verify redirect to login page
        self.login_page.wait_for_page_load()
        self.assert_element_exists(self.login_page.username_input)
        
        # Take screenshot
        self.take_screenshot('logout')
