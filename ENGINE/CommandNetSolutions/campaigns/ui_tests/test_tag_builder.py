"""
Tag Builder UI tests.
"""
import uuid
from campaigns.ui_tests.base import UITestCase
from campaigns.ui_tests.pages.tag_page import TagBuilderPage, TagListPage
from campaigns.models import DynamicTag, TagCategory


class TagBuilderUITestCase(UITestCase):
    """Test case for tag builder UI."""
    
    def setUp(self):
        """Set up the test case."""
        super().setUp()
        self.tag_builder_page = TagBuilderPage(self)
        self.tag_list_page = TagListPage(self)
        
        # Create test category
        self.category = TagCategory.objects.create(
            name="Test Category",
            description="Test category description"
        )
        
        # Login
        self.login()
    
    def test_analyze_text(self):
        """Test analyzing text."""
        # Navigate to tag builder
        self.tag_builder_page.navigate()
        
        # Analyze text
        self.tag_builder_page.analyze_text(
            text="I love Instagram and social media marketing. It's a great platform for engagement.",
            field="bio"
        )
        
        # Verify suggestions
        suggestion_count = self.tag_builder_page.get_suggestion_count()
        self.assertGreater(suggestion_count, 0)
        
        # Take screenshot
        self.take_screenshot('analyze_text')
    
    def test_create_tag_from_suggestion(self):
        """Test creating a tag from a suggestion."""
        # Navigate to tag builder
        self.tag_builder_page.navigate()
        
        # Analyze text
        self.tag_builder_page.analyze_text(
            text="I love Instagram and social media marketing. It's a great platform for engagement.",
            field="bio"
        )
        
        # Verify suggestions
        suggestion_count = self.tag_builder_page.get_suggestion_count()
        self.assertGreater(suggestion_count, 0)
        
        # Select first suggestion
        self.tag_builder_page.select_suggestion(0)
        
        # Take screenshot
        self.take_screenshot('select_suggestion')
        
        # Generate unique tag name
        tag_name = f"Test Tag {uuid.uuid4().hex[:8]}"
        
        # Create tag
        self.tag_builder_page.wait_for_element(self.tag_builder_page.tag_name_input).clear()
        self.tag_builder_page.wait_for_element(self.tag_builder_page.tag_name_input).send_keys(tag_name)
        
        # Save tag
        self.tag_builder_page.wait_for_element_clickable(self.tag_builder_page.save_button).click()
        
        # Verify tag was created
        tag = DynamicTag.objects.filter(name=tag_name).first()
        self.assertIsNotNone(tag)
    
    def test_create_custom_tag(self):
        """Test creating a custom tag."""
        # Navigate to tag builder
        self.tag_builder_page.navigate()
        
        # Generate unique tag name
        tag_name = f"Custom Tag {uuid.uuid4().hex[:8]}"
        
        # Create tag
        self.tag_builder_page.create_tag(
            name=tag_name,
            tag_type="keyword",
            pattern="instagram",
            category=str(self.category.id)
        )
        
        # Verify tag was created
        tag = DynamicTag.objects.filter(name=tag_name).first()
        self.assertIsNotNone(tag)
        self.assertEqual(tag.tag_type, "keyword")
        self.assertEqual(tag.pattern, "instagram")
        self.assertEqual(tag.category, self.category)
        
        # Take screenshot
        self.take_screenshot('create_custom_tag')
    
    def test_test_tag(self):
        """Test testing a tag."""
        # Navigate to tag builder
        self.tag_builder_page.navigate()
        
        # Analyze text
        self.tag_builder_page.analyze_text(
            text="I love Instagram and social media marketing. It's a great platform for engagement.",
            field="bio"
        )
        
        # Create tag
        self.tag_builder_page.create_tag(
            name=f"Test Tag {uuid.uuid4().hex[:8]}",
            tag_type="keyword",
            pattern="instagram",
            category=str(self.category.id)
        )
        
        # Test tag
        self.tag_builder_page.test_tag()
        
        # Take screenshot
        self.take_screenshot('test_tag')
    
    def test_get_sample_data(self):
        """Test getting sample data."""
        # Navigate to tag builder
        self.tag_builder_page.navigate()
        
        # Get sample data
        self.tag_builder_page.get_sample_data()
        
        # Verify sample data
        sample_count = self.tag_builder_page.get_sample_data_count()
        self.assertGreaterEqual(sample_count, 0)
        
        # Take screenshot
        self.take_screenshot('sample_data')
        
        # Close sample data
        self.tag_builder_page.close_sample_data()
