"""
Base classes for UI testing.
"""
import os
import time
import unittest
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from django.contrib.staticfiles.testing import StaticLiveServerTestCase
from django.contrib.auth.models import User


class UITestCase(StaticLiveServerTestCase):
    """Base class for UI tests."""
    
    @classmethod
    def setUpClass(cls):
        """Set up the test class."""
        super().setUpClass()
        
        # Set up Chrome options
        chrome_options = Options()
        
        # Run headless by default, but allow override via environment variable
        if os.environ.get('UI_TEST_HEADLESS', 'true').lower() == 'true':
            chrome_options.add_argument('--headless')
        
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--window-size=1920,1080')
        
        # Create Chrome driver
        cls.selenium = webdriver.Chrome(options=chrome_options)
        cls.selenium.implicitly_wait(10)
    
    @classmethod
    def tearDownClass(cls):
        """Tear down the test class."""
        cls.selenium.quit()
        super().tearDownClass()
    
    def setUp(self):
        """Set up the test case."""
        # Create test user
        self.username = 'testuser'
        self.password = 'testpassword'
        self.user = User.objects.create_user(
            username=self.username,
            email='<EMAIL>',
            password=self.password
        )
    
    def login(self):
        """Log in to the application."""
        # Navigate to login page
        self.selenium.get(f'{self.live_server_url}/accounts/login/')
        
        # Fill in login form
        username_input = self.selenium.find_element(By.NAME, 'username')
        password_input = self.selenium.find_element(By.NAME, 'password')
        
        username_input.send_keys(self.username)
        password_input.send_keys(self.password)
        
        # Submit form
        self.selenium.find_element(By.CSS_SELECTOR, 'button[type="submit"]').click()
        
        # Wait for redirect
        WebDriverWait(self.selenium, 10).until(
            EC.url_contains('/dashboard/')
        )
    
    def wait_for_element(self, selector, by=By.CSS_SELECTOR, timeout=10):
        """
        Wait for an element to be present.
        
        Args:
            selector: Element selector
            by: Selector type
            timeout: Timeout in seconds
            
        Returns:
            WebElement: Found element
        """
        try:
            element = WebDriverWait(self.selenium, timeout).until(
                EC.presence_of_element_located((by, selector))
            )
            return element
        except TimeoutException:
            self.fail(f"Element not found: {selector}")
    
    def wait_for_element_visible(self, selector, by=By.CSS_SELECTOR, timeout=10):
        """
        Wait for an element to be visible.
        
        Args:
            selector: Element selector
            by: Selector type
            timeout: Timeout in seconds
            
        Returns:
            WebElement: Found element
        """
        try:
            element = WebDriverWait(self.selenium, timeout).until(
                EC.visibility_of_element_located((by, selector))
            )
            return element
        except TimeoutException:
            self.fail(f"Element not visible: {selector}")
    
    def wait_for_element_clickable(self, selector, by=By.CSS_SELECTOR, timeout=10):
        """
        Wait for an element to be clickable.
        
        Args:
            selector: Element selector
            by: Selector type
            timeout: Timeout in seconds
            
        Returns:
            WebElement: Found element
        """
        try:
            element = WebDriverWait(self.selenium, timeout).until(
                EC.element_to_be_clickable((by, selector))
            )
            return element
        except TimeoutException:
            self.fail(f"Element not clickable: {selector}")
    
    def wait_for_text(self, selector, text, by=By.CSS_SELECTOR, timeout=10):
        """
        Wait for an element to contain text.
        
        Args:
            selector: Element selector
            text: Text to wait for
            by: Selector type
            timeout: Timeout in seconds
            
        Returns:
            WebElement: Found element
        """
        try:
            element = WebDriverWait(self.selenium, timeout).until(
                EC.text_to_be_present_in_element((by, selector), text)
            )
            return element
        except TimeoutException:
            self.fail(f"Text not found: {text} in {selector}")
    
    def assert_element_exists(self, selector, by=By.CSS_SELECTOR):
        """
        Assert that an element exists.
        
        Args:
            selector: Element selector
            by: Selector type
        """
        try:
            self.selenium.find_element(by, selector)
        except NoSuchElementException:
            self.fail(f"Element does not exist: {selector}")
    
    def assert_element_not_exists(self, selector, by=By.CSS_SELECTOR):
        """
        Assert that an element does not exist.
        
        Args:
            selector: Element selector
            by: Selector type
        """
        try:
            self.selenium.find_element(by, selector)
            self.fail(f"Element exists but should not: {selector}")
        except NoSuchElementException:
            pass
    
    def assert_text_in_element(self, selector, text, by=By.CSS_SELECTOR):
        """
        Assert that text is in an element.
        
        Args:
            selector: Element selector
            text: Text to check for
            by: Selector type
        """
        element = self.selenium.find_element(by, selector)
        self.assertIn(text, element.text)
    
    def assert_text_not_in_element(self, selector, text, by=By.CSS_SELECTOR):
        """
        Assert that text is not in an element.
        
        Args:
            selector: Element selector
            text: Text to check for
            by: Selector type
        """
        element = self.selenium.find_element(by, selector)
        self.assertNotIn(text, element.text)
    
    def take_screenshot(self, name):
        """
        Take a screenshot.
        
        Args:
            name: Screenshot name
        """
        screenshot_dir = os.path.join(os.path.dirname(__file__), 'screenshots')
        os.makedirs(screenshot_dir, exist_ok=True)
        
        screenshot_path = os.path.join(screenshot_dir, f"{name}.png")
        self.selenium.save_screenshot(screenshot_path)
        
        print(f"Screenshot saved to {screenshot_path}")


class PageObject:
    """Base class for page objects."""
    
    def __init__(self, test_case, url=None):
        """
        Initialize the page object.
        
        Args:
            test_case: Test case instance
            url: Page URL
        """
        self.test_case = test_case
        self.selenium = test_case.selenium
        self.live_server_url = test_case.live_server_url
        self.url = url
    
    def navigate(self):
        """Navigate to the page."""
        if self.url:
            self.selenium.get(f"{self.live_server_url}{self.url}")
        else:
            raise ValueError("URL not set")
    
    def wait_for_page_load(self):
        """Wait for the page to load."""
        WebDriverWait(self.selenium, 10).until(
            lambda driver: driver.execute_script("return document.readyState") == "complete"
        )
    
    def wait_for_element(self, selector, by=By.CSS_SELECTOR, timeout=10):
        """
        Wait for an element to be present.
        
        Args:
            selector: Element selector
            by: Selector type
            timeout: Timeout in seconds
            
        Returns:
            WebElement: Found element
        """
        return self.test_case.wait_for_element(selector, by, timeout)
    
    def wait_for_element_visible(self, selector, by=By.CSS_SELECTOR, timeout=10):
        """
        Wait for an element to be visible.
        
        Args:
            selector: Element selector
            by: Selector type
            timeout: Timeout in seconds
            
        Returns:
            WebElement: Found element
        """
        return self.test_case.wait_for_element_visible(selector, by, timeout)
    
    def wait_for_element_clickable(self, selector, by=By.CSS_SELECTOR, timeout=10):
        """
        Wait for an element to be clickable.
        
        Args:
            selector: Element selector
            by: Selector type
            timeout: Timeout in seconds
            
        Returns:
            WebElement: Found element
        """
        return self.test_case.wait_for_element_clickable(selector, by, timeout)
    
    def wait_for_text(self, selector, text, by=By.CSS_SELECTOR, timeout=10):
        """
        Wait for an element to contain text.
        
        Args:
            selector: Element selector
            text: Text to wait for
            by: Selector type
            timeout: Timeout in seconds
            
        Returns:
            WebElement: Found element
        """
        return self.test_case.wait_for_text(selector, text, by, timeout)
