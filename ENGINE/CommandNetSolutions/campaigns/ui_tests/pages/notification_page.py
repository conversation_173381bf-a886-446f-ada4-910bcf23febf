"""
Notification page object.
"""
from selenium.webdriver.common.by import By
from campaigns.ui_tests.base import PageObject


class NotificationPage(PageObject):
    """Notification page object."""
    
    def __init__(self, test_case):
        """Initialize the page object."""
        super().__init__(test_case, '/notifications/')
        
        # Define element selectors
        self.page_title = 'h1.page-title'
        self.notification_items = '.notification-item'
        self.unread_notifications = '.notification-item.unread'
        self.read_notifications = '.notification-item.read'
        self.mark_all_read_button = '#mark-all-read-btn'
        self.notification_filter = 'select[name="filter"]'
        self.notification_type_filter = 'select[name="type"]'
        self.notification_title = '.notification-title'
        self.notification_message = '.notification-message'
        self.notification_time = '.notification-time'
        self.notification_link = '.notification-link'
        self.mark_read_buttons = '.mark-read-btn'
    
    def get_notification_count(self):
        """
        Get the number of notifications.
        
        Returns:
            int: Number of notifications
        """
        return len(self.selenium.find_elements(By.CSS_SELECTOR, self.notification_items))
    
    def get_unread_count(self):
        """
        Get the number of unread notifications.
        
        Returns:
            int: Number of unread notifications
        """
        return len(self.selenium.find_elements(By.CSS_SELECTOR, self.unread_notifications))
    
    def get_read_count(self):
        """
        Get the number of read notifications.
        
        Returns:
            int: Number of read notifications
        """
        return len(self.selenium.find_elements(By.CSS_SELECTOR, self.read_notifications))
    
    def mark_all_as_read(self):
        """Mark all notifications as read."""
        self.wait_for_element_clickable(self.mark_all_read_button).click()
    
    def mark_notification_as_read(self, index):
        """
        Mark a notification as read.
        
        Args:
            index: Notification index (0-based)
        """
        buttons = self.selenium.find_elements(By.CSS_SELECTOR, self.mark_read_buttons)
        if index < len(buttons):
            buttons[index].click()
    
    def click_notification_link(self, index):
        """
        Click a notification link.
        
        Args:
            index: Notification index (0-based)
        """
        links = self.selenium.find_elements(By.CSS_SELECTOR, self.notification_link)
        if index < len(links):
            links[index].click()
    
    def get_notification_title(self, index):
        """
        Get a notification title.
        
        Args:
            index: Notification index (0-based)
            
        Returns:
            str: Notification title
        """
        titles = self.selenium.find_elements(By.CSS_SELECTOR, self.notification_title)
        if index < len(titles):
            return titles[index].text
        return None
    
    def get_notification_message(self, index):
        """
        Get a notification message.
        
        Args:
            index: Notification index (0-based)
            
        Returns:
            str: Notification message
        """
        messages = self.selenium.find_elements(By.CSS_SELECTOR, self.notification_message)
        if index < len(messages):
            return messages[index].text
        return None
    
    def is_loaded(self):
        """
        Check if the page is loaded.
        
        Returns:
            bool: True if the page is loaded, False otherwise
        """
        try:
            self.wait_for_element_visible(self.page_title)
            return True
        except:
            return False
