"""
Dashboard page object.
"""
from selenium.webdriver.common.by import By
from campaigns.ui_tests.base import PageObject


class DashboardPage(PageObject):
    """Dashboard page object."""
    
    def __init__(self, test_case):
        """Initialize the page object."""
        super().__init__(test_case, '/dashboard/')
        
        # Define element selectors
        self.page_title = 'h1.page-title'
        self.campaign_cards = '.campaign-card'
        self.new_campaign_button = '#new-campaign-btn'
        self.notification_icon = '#notification-icon'
        self.notification_badge = '#notification-badge'
        self.notification_dropdown = '#notification-dropdown'
        self.user_menu = '#user-menu'
        self.logout_link = '#logout-link'
    
    def get_campaign_count(self):
        """
        Get the number of campaign cards.
        
        Returns:
            int: Number of campaign cards
        """
        return len(self.selenium.find_elements(By.CSS_SELECTOR, self.campaign_cards))
    
    def click_new_campaign(self):
        """Click the new campaign button."""
        self.wait_for_element_clickable(self.new_campaign_button).click()
    
    def get_notification_count(self):
        """
        Get the notification count.
        
        Returns:
            int: Notification count
        """
        try:
            badge = self.wait_for_element_visible(self.notification_badge, timeout=2)
            return int(badge.text)
        except:
            return 0
    
    def open_notifications(self):
        """Open the notification dropdown."""
        self.wait_for_element_clickable(self.notification_icon).click()
        self.wait_for_element_visible(self.notification_dropdown)
    
    def logout(self):
        """Log out."""
        self.wait_for_element_clickable(self.user_menu).click()
        self.wait_for_element_clickable(self.logout_link).click()
    
    def is_loaded(self):
        """
        Check if the page is loaded.
        
        Returns:
            bool: True if the page is loaded, False otherwise
        """
        try:
            self.wait_for_element_visible(self.page_title)
            return True
        except:
            return False
