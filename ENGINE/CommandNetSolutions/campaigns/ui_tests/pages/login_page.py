"""
Login page object.
"""
from selenium.webdriver.common.by import By
from campaigns.ui_tests.base import PageObject


class LoginPage(PageObject):
    """Login page object."""
    
    def __init__(self, test_case):
        """Initialize the page object."""
        super().__init__(test_case, '/accounts/login/')
        
        # Define element selectors
        self.username_input = 'input[name="username"]'
        self.password_input = 'input[name="password"]'
        self.submit_button = 'button[type="submit"]'
        self.error_message = '.alert-danger'
    
    def login(self, username, password):
        """
        Log in with the given credentials.
        
        Args:
            username: Username
            password: Password
        """
        # Navigate to login page
        self.navigate()
        
        # Fill in login form
        self.wait_for_element(self.username_input).send_keys(username)
        self.wait_for_element(self.password_input).send_keys(password)
        
        # Submit form
        self.wait_for_element(self.submit_button).click()
    
    def get_error_message(self):
        """
        Get the error message.
        
        Returns:
            str: Error message
        """
        try:
            return self.wait_for_element_visible(self.error_message).text
        except:
            return None
    
    def is_error_displayed(self):
        """
        Check if an error is displayed.
        
        Returns:
            bool: True if an error is displayed, False otherwise
        """
        try:
            self.wait_for_element_visible(self.error_message, timeout=2)
            return True
        except:
            return False
