"""
Tag page objects.
"""
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from campaigns.ui_tests.base import PageObject


class TagListPage(PageObject):
    """Tag list page object."""
    
    def __init__(self, test_case):
        """Initialize the page object."""
        super().__init__(test_case, '/tags/')
        
        # Define element selectors
        self.page_title = 'h1.page-title'
        self.tag_cards = '.tag-card'
        self.new_tag_button = '#new-tag-btn'
        self.category_filter = 'select[name="category"]'
        self.search_input = 'input[name="search"]'
        self.search_button = 'button[type="submit"]'
        self.tag_builder_link = '#tag-builder-link'
    
    def get_tag_count(self):
        """
        Get the number of tag cards.
        
        Returns:
            int: Number of tag cards
        """
        return len(self.selenium.find_elements(By.CSS_SELECTOR, self.tag_cards))
    
    def click_new_tag(self):
        """Click the new tag button."""
        self.wait_for_element_clickable(self.new_tag_button).click()
    
    def filter_by_category(self, category):
        """
        Filter tags by category.
        
        Args:
            category: Category value
        """
        category_select = Select(self.wait_for_element(self.category_filter))
        category_select.select_by_value(category)
    
    def search(self, query):
        """
        Search for tags.
        
        Args:
            query: Search query
        """
        self.wait_for_element(self.search_input).clear()
        self.wait_for_element(self.search_input).send_keys(query)
        self.wait_for_element(self.search_button).click()
    
    def go_to_tag_builder(self):
        """Go to the tag builder."""
        self.wait_for_element_clickable(self.tag_builder_link).click()
    
    def is_loaded(self):
        """
        Check if the page is loaded.
        
        Returns:
            bool: True if the page is loaded, False otherwise
        """
        try:
            self.wait_for_element_visible(self.page_title)
            return True
        except:
            return False


class TagBuilderPage(PageObject):
    """Tag builder page object."""
    
    def __init__(self, test_case):
        """Initialize the page object."""
        super().__init__(test_case, '/tags/builder/')
        
        # Define element selectors
        self.page_title = 'h1.page-title'
        self.sample_text_input = 'textarea[name="sample_text"]'
        self.field_select = 'select[name="field"]'
        self.analyze_button = '#analyze-btn'
        self.tag_name_input = 'input[name="tag_name"]'
        self.tag_type_select = 'select[name="tag_type"]'
        self.pattern_input = 'input[name="pattern"]'
        self.category_select = 'select[name="category"]'
        self.test_button = '#test-tag-btn'
        self.save_button = '#save-tag-btn'
        self.result_panel = '#result-panel'
        self.suggestion_cards = '.suggestion-card'
        self.sample_data_button = '#sample-data-btn'
        self.sample_data_modal = '#sample-data-modal'
        self.sample_data_items = '.sample-data-item'
        self.close_modal_button = '.modal-dialog .btn-close'
    
    def analyze_text(self, text, field):
        """
        Analyze text.
        
        Args:
            text: Text to analyze
            field: Field to analyze
        """
        # Fill in form
        self.wait_for_element(self.sample_text_input).clear()
        self.wait_for_element(self.sample_text_input).send_keys(text)
        
        # Select field
        field_select = Select(self.wait_for_element(self.field_select))
        field_select.select_by_value(field)
        
        # Click analyze button
        self.wait_for_element_clickable(self.analyze_button).click()
        
        # Wait for results
        self.wait_for_element_visible(self.result_panel)
    
    def get_suggestion_count(self):
        """
        Get the number of suggestion cards.
        
        Returns:
            int: Number of suggestion cards
        """
        return len(self.selenium.find_elements(By.CSS_SELECTOR, self.suggestion_cards))
    
    def select_suggestion(self, index):
        """
        Select a suggestion.
        
        Args:
            index: Suggestion index (0-based)
        """
        suggestions = self.selenium.find_elements(By.CSS_SELECTOR, self.suggestion_cards)
        if index < len(suggestions):
            suggestions[index].click()
    
    def create_tag(self, name, tag_type, pattern, category=None):
        """
        Create a tag.
        
        Args:
            name: Tag name
            tag_type: Tag type
            pattern: Tag pattern
            category: Tag category
        """
        # Fill in form
        self.wait_for_element(self.tag_name_input).clear()
        self.wait_for_element(self.tag_name_input).send_keys(name)
        
        # Select tag type
        tag_type_select = Select(self.wait_for_element(self.tag_type_select))
        tag_type_select.select_by_value(tag_type)
        
        # Fill in pattern
        self.wait_for_element(self.pattern_input).clear()
        self.wait_for_element(self.pattern_input).send_keys(pattern)
        
        # Select category if provided
        if category:
            category_select = Select(self.wait_for_element(self.category_select))
            category_select.select_by_value(category)
        
        # Save tag
        self.wait_for_element_clickable(self.save_button).click()
    
    def test_tag(self):
        """Test the current tag."""
        self.wait_for_element_clickable(self.test_button).click()
        self.wait_for_element_visible(self.result_panel)
    
    def get_sample_data(self):
        """Get sample data."""
        self.wait_for_element_clickable(self.sample_data_button).click()
        self.wait_for_element_visible(self.sample_data_modal)
    
    def get_sample_data_count(self):
        """
        Get the number of sample data items.
        
        Returns:
            int: Number of sample data items
        """
        return len(self.selenium.find_elements(By.CSS_SELECTOR, self.sample_data_items))
    
    def close_sample_data(self):
        """Close the sample data modal."""
        self.wait_for_element_clickable(self.close_modal_button).click()
    
    def is_loaded(self):
        """
        Check if the page is loaded.
        
        Returns:
            bool: True if the page is loaded, False otherwise
        """
        try:
            self.wait_for_element_visible(self.page_title)
            return True
        except:
            return False
