"""
Campaign page object.
"""
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import Select
from campaigns.ui_tests.base import PageObject


class CampaignFormPage(PageObject):
    """Campaign form page object."""
    
    def __init__(self, test_case):
        """Initialize the page object."""
        super().__init__(test_case, '/campaigns/new/')
        
        # Define element selectors
        self.page_title = 'h1.page-title'
        self.name_input = 'input[name="name"]'
        self.description_input = 'textarea[name="description"]'
        self.target_type_select = 'select[name="target_type"]'
        self.audience_type_select = 'select[name="audience_type"]'
        self.submit_button = 'button[type="submit"]'
        self.cancel_button = '.btn-secondary'
        self.error_message = '.alert-danger'
    
    def create_campaign(self, name, description, target_type, audience_type):
        """
        Create a campaign.
        
        Args:
            name: Campaign name
            description: Campaign description
            target_type: Target type
            audience_type: Audience type
        """
        # Navigate to campaign form
        self.navigate()
        
        # Fill in form
        self.wait_for_element(self.name_input).send_keys(name)
        self.wait_for_element(self.description_input).send_keys(description)
        
        # Select target type
        target_select = Select(self.wait_for_element(self.target_type_select))
        target_select.select_by_value(target_type)
        
        # Select audience type
        audience_select = Select(self.wait_for_element(self.audience_type_select))
        audience_select.select_by_value(audience_type)
        
        # Submit form
        self.wait_for_element(self.submit_button).click()
    
    def get_error_message(self):
        """
        Get the error message.
        
        Returns:
            str: Error message
        """
        try:
            return self.wait_for_element_visible(self.error_message).text
        except:
            return None
    
    def is_error_displayed(self):
        """
        Check if an error is displayed.
        
        Returns:
            bool: True if an error is displayed, False otherwise
        """
        try:
            self.wait_for_element_visible(self.error_message, timeout=2)
            return True
        except:
            return False


class CampaignDetailPage(PageObject):
    """Campaign detail page object."""
    
    def __init__(self, test_case, campaign_id=None):
        """
        Initialize the page object.
        
        Args:
            test_case: Test case instance
            campaign_id: Campaign ID
        """
        url = f'/campaigns/{campaign_id}/' if campaign_id else None
        super().__init__(test_case, url)
        
        # Define element selectors
        self.page_title = 'h1.page-title'
        self.campaign_name = '.campaign-name'
        self.campaign_description = '.campaign-description'
        self.campaign_status = '.campaign-status'
        self.edit_button = '#edit-campaign-btn'
        self.delete_button = '#delete-campaign-btn'
        self.launch_button = '#launch-campaign-btn'
        self.analyze_button = '#analyze-campaign-btn'
        self.targets_tab = '#targets-tab'
        self.results_tab = '#results-tab'
        self.settings_tab = '#settings-tab'
        self.roi_tab = '#roi-tab'
        self.add_location_target_button = '#add-location-target-btn'
        self.add_username_target_button = '#add-username-target-btn'
        self.target_list = '.target-list'
        self.target_items = '.target-item'
        self.confirm_dialog = '.modal-dialog'
        self.confirm_button = '.modal-dialog .btn-primary'
        self.cancel_button = '.modal-dialog .btn-secondary'
    
    def set_campaign_id(self, campaign_id):
        """
        Set the campaign ID.
        
        Args:
            campaign_id: Campaign ID
        """
        self.url = f'/campaigns/{campaign_id}/'
    
    def get_campaign_name(self):
        """
        Get the campaign name.
        
        Returns:
            str: Campaign name
        """
        return self.wait_for_element(self.campaign_name).text
    
    def get_campaign_status(self):
        """
        Get the campaign status.
        
        Returns:
            str: Campaign status
        """
        return self.wait_for_element(self.campaign_status).text
    
    def click_edit(self):
        """Click the edit button."""
        self.wait_for_element_clickable(self.edit_button).click()
    
    def click_delete(self):
        """Click the delete button."""
        self.wait_for_element_clickable(self.delete_button).click()
        self.wait_for_element_visible(self.confirm_dialog)
    
    def confirm_delete(self):
        """Confirm deletion."""
        self.wait_for_element_clickable(self.confirm_button).click()
    
    def cancel_delete(self):
        """Cancel deletion."""
        self.wait_for_element_clickable(self.cancel_button).click()
    
    def click_launch(self):
        """Click the launch button."""
        self.wait_for_element_clickable(self.launch_button).click()
    
    def click_analyze(self):
        """Click the analyze button."""
        self.wait_for_element_clickable(self.analyze_button).click()
    
    def go_to_targets_tab(self):
        """Go to the targets tab."""
        self.wait_for_element_clickable(self.targets_tab).click()
    
    def go_to_results_tab(self):
        """Go to the results tab."""
        self.wait_for_element_clickable(self.results_tab).click()
    
    def go_to_settings_tab(self):
        """Go to the settings tab."""
        self.wait_for_element_clickable(self.settings_tab).click()
    
    def go_to_roi_tab(self):
        """Go to the ROI tab."""
        self.wait_for_element_clickable(self.roi_tab).click()
    
    def click_add_location_target(self):
        """Click the add location target button."""
        self.wait_for_element_clickable(self.add_location_target_button).click()
    
    def click_add_username_target(self):
        """Click the add username target button."""
        self.wait_for_element_clickable(self.add_username_target_button).click()
    
    def get_target_count(self):
        """
        Get the number of targets.
        
        Returns:
            int: Number of targets
        """
        return len(self.selenium.find_elements(By.CSS_SELECTOR, self.target_items))
    
    def is_loaded(self):
        """
        Check if the page is loaded.
        
        Returns:
            bool: True if the page is loaded, False otherwise
        """
        try:
            self.wait_for_element_visible(self.page_title)
            return True
        except:
            return False
