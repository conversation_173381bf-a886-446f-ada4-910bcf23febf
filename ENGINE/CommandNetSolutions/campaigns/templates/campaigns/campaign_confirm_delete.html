{% extends "campaigns/base.html" %}

{% block title %}Delete Campaign{% endblock %}

{% block campaign_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title">Delete Campaign</h1>
        <p class="page-subtitle">Confirm deletion of "{{ object.name }}"</p>
    </div>
    <div>
        <a href="{% url 'campaigns:campaign_detail' object.id %}" class="btn btn-outline-primary btn-icon">
            <i class="fas fa-arrow-left me-1"></i> Back
        </a>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-6">
        <div class="campaign-card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Warning</h5>
            </div>
            <div class="card-body text-center">
                <div class="empty-state">
                    <div class="empty-state-icon text-danger">
                        <i class="fas fa-trash-alt"></i>
                    </div>
                    <div class="empty-state-text">Are you sure you want to delete this campaign?</div>
                    <p class="text-danger mb-4">This action cannot be undone and all associated data will be permanently deleted.</p>

                    <div class="alert alert-warning">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Campaign Details:</strong>
                        <ul class="mb-0 mt-2 text-start">
                            <li><strong>Name:</strong> {{ object.name }}</li>
                            <li><strong>Type:</strong> {{ object.get_target_type_display }}</li>
                            <li><strong>Status:</strong> {{ object.get_status_display }}</li>
                            <li><strong>Created:</strong> {{ object.created_at|date:"F j, Y" }}</li>
                        </ul>
                    </div>

                    <form method="post">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger btn-icon">
                            <i class="fas fa-trash-alt me-1"></i> Yes, Delete Campaign
                        </button>
                        <a href="{% url 'campaigns:campaign_detail' object.id %}" class="btn btn-outline-secondary">Cancel</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
