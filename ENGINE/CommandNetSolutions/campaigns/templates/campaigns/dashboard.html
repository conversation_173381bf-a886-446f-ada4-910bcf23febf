{% extends "campaigns/base.html" %}
{% load static %}

{% block title %}Campaign Dashboard{% endblock %}

{% block campaign_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title">Campaign Dashboard</h1>
        <p class="page-subtitle">Manage your data collection campaigns</p>
    </div>
    <div>
        <a href="{% url 'campaigns:campaign_create' %}" class="btn btn-primary btn-icon">
            <i class="fas fa-plus-circle"></i> Create Campaign
        </a>
    </div>
</div>

<div class="row g-4 mb-4">
    <div class="col-md-3">
        <div class="stats-card primary">
            <div class="stats-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stats-title">Total Campaigns</div>
            <div class="stats-value">{{ total_campaigns }}</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card secondary">
            <div class="stats-icon">
                <i class="fas fa-play-circle"></i>
            </div>
            <div class="stats-title">Active Campaigns</div>
            <div class="stats-value">{{ active_campaigns }}</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card warning">
            <div class="stats-icon">
                <i class="fas fa-check-circle"></i>
            </div>
            <div class="stats-title">Completed Campaigns</div>
            <div class="stats-value">{{ completed_campaigns }}</div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="stats-card danger">
            <div class="stats-icon">
                <i class="fas fa-calendar-alt"></i>
            </div>
            <div class="stats-title">This Month</div>
            <div class="stats-value">{{ this_month_campaigns }}</div>
        </div>
    </div>
</div>

<div class="row g-4 mb-4">
    <!-- Campaign Statistics -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie me-2"></i>Campaign Statistics</h5>
            </div>
            <div class="card-body">
                <div style="height: 250px; position: relative;" class="d-flex justify-content-center align-items-center">
                    {% if has_campaigns %}
                        <div class="text-center" style="width: 100%; max-width: 250px;">
                            <canvas id="campaignStatusChart"></canvas>
                        </div>
                    {% else %}
                        <div class="d-flex justify-content-center align-items-center h-100">
                            <div class="text-center text-muted">
                                <i class="fas fa-chart-pie fa-3x mb-3"></i>
                                <p>No campaign data to display</p>
                            </div>
                        </div>
                    {% endif %}
                </div>

                <div class="mt-4">
                    <div class="detail-section-title">Campaign Types</div>
                    {% if has_campaigns %}
                        <div class="progress mb-3">
                            <div class="progress-bar bg-primary" role="progressbar" style="width: {{ location_only_percentage }}%; transition: width 1s ease-in-out;" aria-valuenow="{{ location_only_percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <div>
                                <i class="fas fa-map-marker-alt text-primary me-1"></i> Location Only
                            </div>
                            <div class="fw-bold">{{ location_only_percentage }}% ({{ location_only_count }})</div>
                        </div>

                        <div class="progress mb-3">
                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ username_only_percentage }}%; transition: width 1s ease-in-out;" aria-valuenow="{{ username_only_percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <div class="d-flex justify-content-between mb-3">
                            <div>
                                <i class="fas fa-user text-success me-1"></i> Username Only
                            </div>
                            <div class="fw-bold">{{ username_only_percentage }}% ({{ username_only_count }})</div>
                        </div>

                        <div class="progress mb-3">
                            <div class="progress-bar bg-info" role="progressbar" style="width: {{ mixed_percentage }}%; transition: width 1s ease-in-out;" aria-valuenow="{{ mixed_percentage }}" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <div>
                                <i class="fas fa-exchange-alt text-info me-1"></i> Mixed (Both Types)
                            </div>
                            <div class="fw-bold">{{ mixed_percentage }}% ({{ mixed_count }})</div>
                        </div>
                    {% else %}
                        <div class="alert alert-light text-center py-3">
                            <i class="fas fa-info-circle me-2"></i>
                            No campaign data available yet
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Tips -->
    <div class="col-lg-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Quick Tips</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Location-based targeting</strong> allows you to target Instagram accounts by geographic location.
                </div>
                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Username-based targeting</strong> allows you to target specific Instagram accounts by username.
                </div>
                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Mixed campaigns</strong> can include both location and username targets for broader reach.
                </div>
                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    Use the <strong>audience type</strong> setting to specify whether to target the profile, followers, or following.
                </div>
                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Analysis settings</strong> help you filter accounts based on follower count and engagement metrics.
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Use <strong>tag groups</strong> to organize related tags and apply them consistently across campaigns.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Campaigns -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>
                    {% if current_filter == 'active' %}
                        Active Campaigns
                    {% elif current_filter == 'running' %}
                        Running Campaigns
                    {% elif current_filter == 'paused' %}
                        Paused Campaigns
                    {% elif current_filter == 'completed' %}
                        Completed Campaigns
                    {% elif current_filter == 'stopped' %}
                        Stopped Campaigns
                    {% elif current_filter == 'draft' %}
                        Draft Campaigns
                    {% else %}
                        Recent Campaigns
                    {% endif %}
                </h5>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                        <li><a class="dropdown-item" href="{% url 'campaigns:dashboard' %}">All Campaigns</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:dashboard' %}?status=active">Active Campaigns</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:dashboard' %}?status=running">Running Campaigns</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:dashboard' %}?status=paused">Paused Campaigns</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:dashboard' %}?status=completed">Completed Campaigns</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:dashboard' %}?status=stopped">Stopped Campaigns</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:dashboard' %}?status=draft">Draft Campaigns</a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                {% if recent_campaigns %}
                <div class="table-responsive">
                    <table class="table table-campaigns">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for campaign in recent_campaigns %}
                            <tr>
                                <td>
                                    <div>
                                        <div class="fw-bold">{{ campaign.name }}</div>
                                        <div class="small text-muted">{{ campaign.description|truncatechars:50 }}</div>
                                    </div>
                                </td>

                                <td>
                                    <span class="campaign-status status-{{ campaign.status }}">
                                        {{ campaign.get_status_display }}
                                    </span>
                                </td>
                                <td>{{ campaign.created_at|date:"M d, Y" }}</td>
                                <td>
                                    <div class="d-flex justify-content-end">
                                        <div class="btn-group action-buttons">
                                            {% if campaign.status == 'running' %}
                                            <a href="#" class="btn btn-sm btn-warning campaign-action-btn" data-action="pause" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}" data-bs-toggle="tooltip" title="Pause Campaign">
                                                <i class="fas fa-pause"></i>
                                            </a>
                                            <a href="#" class="btn btn-sm btn-danger campaign-action-btn" data-action="stop" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}" data-bs-toggle="tooltip" title="Stop Campaign">
                                                <i class="fas fa-stop"></i>
                                            </a>
                                            {% endif %}

                                            {% if campaign.status == 'paused' %}
                                            <a href="#" class="btn btn-sm btn-success campaign-action-btn" data-action="resume" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}" data-bs-toggle="tooltip" title="Resume Campaign">
                                                <i class="fas fa-play"></i>
                                            </a>
                                            <a href="#" class="btn btn-sm btn-danger campaign-action-btn" data-action="stop" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}" data-bs-toggle="tooltip" title="Stop Campaign">
                                                <i class="fas fa-stop"></i>
                                            </a>
                                            {% endif %}

                                            {% if campaign.status == 'pending' %}
                                            <a href="#" class="btn btn-sm btn-danger campaign-action-btn" data-action="stop" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}" data-bs-toggle="tooltip" title="Stop Campaign">
                                                <i class="fas fa-stop"></i>
                                            </a>
                                            {% endif %}
                                            {% if campaign.status == 'draft' %}
                                            <a href="{% url 'campaigns:campaign_update' campaign.id %}" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="Edit Campaign">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'campaigns:launch_campaign' campaign.id %}" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Launch Campaign">
                                                <i class="fas fa-rocket"></i>
                                            </a>
                                            {% endif %}
                                            <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <div class="empty-state-text">No campaigns created yet.</div>
                    <a href="{% url 'campaigns:campaign_create' %}" class="btn btn-primary btn-icon">
                        <i class="fas fa-plus-circle"></i> Create Your First Campaign
                    </a>
                </div>
                {% endif %}
            </div>
            <div class="card-footer text-center">
                <a href="{% url 'campaigns:campaign_list' %}" class="btn btn-outline-primary btn-icon">
                    <i class="fas fa-list me-1"></i> View All
                </a>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Campaign action buttons (pause, resume, stop)
    const actionButtons = document.querySelectorAll('.campaign-action-btn');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const action = this.dataset.action;
            const campaignId = this.dataset.campaignId;
            const campaignName = this.dataset.campaignName;

            let actionText = '';
            let actionUrl = '';

            switch(action) {
                case 'pause':
                    actionText = 'pause';
                    actionUrl = `/campaigns/${campaignId}/pause/`;
                    break;
                case 'resume':
                    actionText = 'resume';
                    actionUrl = `/campaigns/${campaignId}/resume/`;
                    break;
                case 'stop':
                    actionText = 'stop';
                    actionUrl = `/campaigns/${campaignId}/stop/`;
                    break;
                default:
                    return;
            }

            if (confirm(`Are you sure you want to ${actionText} the campaign "${campaignName}"?`)) {
                window.location.href = actionUrl;
            }
        });
    });

    {% if has_campaigns %}
    // Campaign Status Chart
    var ctx = document.getElementById('campaignStatusChart').getContext('2d');
    var campaignStatusChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Draft', 'Pending', 'Running', 'Paused', 'Completed', 'Failed', 'Stopped'],
            datasets: [{
                data: [
                    {{ draft_campaigns|default:0 }}, // Draft
                    {{ pending_campaigns|default:0 }}, // Pending
                    {{ running_campaigns|default:0 }}, // Running
                    {{ paused_campaigns|default:0 }}, // Paused
                    {{ completed_campaigns|default:0 }}, // Completed
                    {{ failed_campaigns|default:0 }}, // Failed
                    {{ stopped_campaigns|default:0 }} // Stopped
                ],
                backgroundColor: [
                    '#e0e0e0',
                    '#fff3cd',
                    '#d1ecf1',
                    '#ffe8cc',
                    '#d4edda',
                    '#f8d7da',
                    '#e2e3e5'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        boxWidth: 12
                    }
                },
                tooltip: {
                    boxPadding: 8
                }
            },
            layout: {
                padding: {
                    top: 20,
                    bottom: 10,
                    left: 0,
                    right: 0
                }
            },
            cutout: '65%'
        }
    });
    {% endif %}
});
</script>
{% endblock %}
