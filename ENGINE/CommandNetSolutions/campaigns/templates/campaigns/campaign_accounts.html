{% extends 'campaigns/base.html' %}
{% load static %}

{% block title %}{{ campaign.name }} - Accounts{% endblock %}

{% block campaign_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title">{{ campaign.name }} - Accounts</h1>
        <p class="page-subtitle">Manage accounts collected by this campaign</p>
    </div>
    <div>
        <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-outline-primary btn-icon back-button">
            <i class="fas fa-arrow-left me-1"></i> <span>Back</span>
        </a>
    </div>
</div>



    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-primary">
                    <h5 class="mb-0 text-white">Account Statistics</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="stat-card">
                                <h2 class="stat-value">{{ total_accounts }}</h2>
                                <p class="stat-label">Total Accounts</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="stat-card">
                                <h2 class="stat-value">{{ whitelisted_accounts }}</h2>
                                <p class="stat-label">White Listed</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-success">
                    <h5 class="mb-0 text-white">Conversion Rate</h5>
                </div>
                <div class="card-body">
                    <div class="conversion-rate-display">
                        <h1 class="display-4 text-center">{{ conversion_rate|floatformat:1 }}%</h1>
                        <p class="text-center text-muted">Accounts that met whitelist criteria</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <h5>Collected Accounts</h5>
        </div>
        <div class="btn-action-group">
            <a href="{% url 'campaigns:analyze_campaign' campaign.id %}" class="btn btn-primary">
                <i class="fas fa-search me-2"></i> Analyze Accounts
            </a>
            <a href="{% url 'campaigns:campaign_whitelist' campaign.id %}" class="btn btn-primary">
                <i class="fas fa-star me-2"></i> View White List
            </a>
        </div>
        <div class="card-body">
            <!-- Filter Form -->
            <div class="filter-section mb-4">
                <button class="btn btn-outline-secondary mb-3" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" aria-expanded="false" aria-controls="filterCollapse">
                    <i class="fas fa-filter me-1"></i> Show Filters
                </button>

                <div class="collapse" id="filterCollapse">
                    <div class="card card-body bg-light">
                        <form method="get" id="account-filter-form">
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ filter_form.search.id_for_label }}" class="form-label">Search</label>
                                    {{ filter_form.search }}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ filter_form.account_type.id_for_label }}" class="form-label">Account Type</label>
                                    {{ filter_form.account_type }}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ filter_form.whitelist_status.id_for_label }}" class="form-label">Whitelist Status</label>
                                    {{ filter_form.whitelist_status }}
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">Followers</label>
                                    <div class="input-group">
                                        {{ filter_form.min_followers }}
                                        <span class="input-group-text">to</span>
                                        {{ filter_form.max_followers }}
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">Following</label>
                                    <div class="input-group">
                                        {{ filter_form.min_following }}
                                        <span class="input-group-text">to</span>
                                        {{ filter_form.max_following }}
                                    </div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label class="form-label">Posts</label>
                                    <div class="input-group">
                                        {{ filter_form.min_posts }}
                                        <span class="input-group-text">to</span>
                                        {{ filter_form.max_posts }}
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-8 mb-3">
                                    <label for="{{ filter_form.tags.id_for_label }}" class="form-label">Tags</label>
                                    {{ filter_form.tags }}
                                    <div class="form-text">Hold Ctrl (or Cmd on Mac) to select multiple tags</div>
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ filter_form.sort_by.id_for_label }}" class="form-label">Sort By</label>
                                    {{ filter_form.sort_by }}
                                </div>
                            </div>

                            <div class="d-flex justify-content-end">
                                <a href="{% url 'campaigns:campaign_accounts' campaign.id %}" class="btn btn-outline-secondary me-2">
                                    <i class="fas fa-times me-1"></i> Clear Filters
                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-filter me-1"></i> Apply Filters
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            {% if accounts %}
            <!-- Quick search for client-side filtering -->
            <div class="mb-3">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" id="quick-search" class="form-control" placeholder="Quick search in results...">
                </div>
                <div class="form-text">This search filters the current page results only. Use the filters above for more advanced searching.</div>
            </div>

            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Full Name</th>
                            <th>Followers</th>
                            <th>Following</th>
                            <th>Posts</th>
                            <th>Account Type</th>
                            <th>Tags</th>
                            <th>White Listed</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for account in accounts %}
                        <tr>
                            <td>
                                <a href="https://www.instagram.com/{{ account.username }}/" target="_blank" class="text-decoration-none">
                                    {{ account.username }}
                                    {% if account.is_verified %}
                                    <i class="fas fa-check-circle text-primary ms-1" title="Verified"></i>
                                    {% endif %}
                                </a>
                            </td>
                            <td>{{ account.full_name|default:"-" }}</td>
                            <td>{{ account.followers|default:"0" }}</td>
                            <td>{{ account.following|default:"0" }}</td>
                            <td>{{ account.number_of_posts|default:"0" }}</td>
                            <td>{{ account.account_type|default:"-" }}</td>
                            <td>
                                {% if account.tags_list %}
                                {% for tag_name in account.tags_list %}
                                <span class="badge bg-secondary">{{ tag_name }}</span>
                                {% endfor %}
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if account.whitelistentry_set.exists %}
                                <span class="badge bg-success">Yes</span>
                                {% else %}
                                <span class="badge bg-secondary">No</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% include 'includes/pagination.html' with page_obj=page_obj %}
            {% else %}
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-users"></i>
                </div>
                <h4>No Accounts Found</h4>
                <p>This campaign hasn't collected any accounts yet.</p>
                <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-primary">
                    <i class="fas fa-arrow-left"></i> Back
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Show filter collapse if there are active filters
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.toString() && urlParams.get('search') !== null) {
            $('#filterCollapse').addClass('show');
        }

        // Add sorting functionality to table headers
        $('.table th').each(function() {
            const headerText = $(this).text().trim().toLowerCase();
            if (['username', 'full name', 'followers', 'following', 'posts', 'account type'].includes(headerText)) {
                $(this).addClass('sortable');
                $(this).append('<span class="sort-icon ms-1"><i class="fas fa-sort"></i></span>');

                $(this).on('click', function(e) {
                    e.preventDefault();

                    const fieldMap = {
                        'username': 'username',
                        'full name': 'full_name',
                        'followers': 'followers',
                        'following': 'following',
                        'posts': 'number_of_posts',
                        'account type': 'account_type'
                    };

                    const field = fieldMap[headerText];
                    let sortDirection = '';

                    // Check current sort direction
                    if (urlParams.get('sort_by') === field) {
                        sortDirection = '-' + field;
                    } else if (urlParams.get('sort_by') === '-' + field) {
                        sortDirection = field;
                    } else {
                        sortDirection = field;
                    }

                    // Update sort_by parameter and submit form
                    $('select[name="sort_by"]').val(sortDirection);
                    $('#account-filter-form').submit();
                });
            }
        });

        // Highlight active sort column
        const currentSort = urlParams.get('sort_by');
        if (currentSort) {
            const fieldMap = {
                'username': 'Username',
                '-username': 'Username',
                'full_name': 'Full Name',
                '-full_name': 'Full Name',
                'followers': 'Followers',
                '-followers': 'Followers',
                'following': 'Following',
                '-following': 'Following',
                'number_of_posts': 'Posts',
                '-number_of_posts': 'Posts',
                'account_type': 'Account Type',
                '-account_type': 'Account Type'
            };

            const headerText = fieldMap[currentSort];
            if (headerText) {
                $('.table th').each(function() {
                    if ($(this).text().trim() === headerText) {
                        $(this).addClass('active-sort');

                        // Update sort icon
                        const icon = currentSort.startsWith('-') ?
                            '<i class="fas fa-sort-down"></i>' :
                            '<i class="fas fa-sort-up"></i>';
                        $(this).find('.sort-icon').html(icon);
                    }
                });
            }
        }

        // Add quick search functionality
        $('#quick-search').on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();
            $('.table tbody tr').each(function() {
                const rowText = $(this).text().toLowerCase();
                $(this).toggle(rowText.indexOf(searchTerm) > -1);
            });
        });
    });
</script>
<style>
    .stat-card {
        text-align: center;
        padding: 15px;
    }
    .stat-value {
        font-size: 2.5rem;
        font-weight: 600;
        margin-bottom: 5px;
    }
    .stat-label {
        color: #6c757d;
        margin-bottom: 0;
    }
    .conversion-rate-display {
        padding: 15px;
    }
    .sortable {
        cursor: pointer;
    }
    .sortable:hover {
        background-color: #f8f9fa;
    }
    .active-sort {
        background-color: #e9ecef;
    }
    .sort-icon {
        display: inline-block;
        width: 16px;
    }
</style>
{% endblock extra_js %}
