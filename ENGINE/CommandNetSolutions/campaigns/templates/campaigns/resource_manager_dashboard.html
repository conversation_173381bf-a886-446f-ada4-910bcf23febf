{% extends "campaigns/base.html" %}
{% load static %}

{% block title %}Resource Manager Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'campaigns/css/dashboard.css' %}">
{% endblock %}

{% block campaign_content %}
<div class="container-fluid dashboard-container">
    <div class="dashboard-header d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="dashboard-title">Resource Manager Dashboard</h1>
            <p id="last-updated" class="mt-2 mb-0">
                <i class="fas fa-clock me-2"></i> Last updated: <span id="update-time">Never</span>
            </p>
        </div>
        <button id="refresh-btn" class="btn btn-primary refresh-btn shadow-sm" data-bs-toggle="tooltip" title="Refresh dashboard data">
            <i class="fas fa-sync-alt btn-icon"></i> Refresh
        </button>
    </div>

    <!-- Redis Connection Error Alert (hidden by default) -->
    <div id="redis-connection-error" class="alert alert-warning mb-4 shadow-sm" style="display: none;">
        <div class="d-flex align-items-center">
            <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
            <div class="flex-grow-1">
                <h5 class="alert-heading">Redis Connection Error</h5>
                <p class="mb-1">Unable to connect to Redis server. Some features may be unavailable.</p>
                <div class="mt-2">
                    <button id="retry-connection-btn" class="btn btn-sm btn-warning btn-action">
                        <i class="fas fa-redo me-1"></i> Retry Connection
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- System Resources -->
        <div class="col-lg-4 col-md-6">
            <div class="card resource-card shadow h-100">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-robot me-2"></i>
                    <h5 class="m-0 text-white">Bot Worker Status</h5>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        <div class="resource-value">
                            <div class="d-flex align-items-center justify-content-center">
                                <span class="status-indicator available" id="bot-status-indicator"></span>
                                <span id="bot-status-badge" class="badge bg-success">Available</span>
                            </div>
                        </div>
                        <p class="resource-subtitle" id="bot-status-text">Bot worker is ready for workflows</p>
                    </div>

                    <div id="active-workflow-container" class="d-none">
                        <h6 class="mt-4 mb-3 text-primary fw-bold">Currently Running Workflow</h6>
                        <div class="card border-start border-primary border-4 shadow h-100 py-2">
                            <div class="card-body py-2 px-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <h6 class="card-title mb-0 fw-bold text-primary" id="active-workflow-type">Collection</h6>
                                    <span class="badge bg-primary rounded-pill">Priority: <span id="active-workflow-priority">-</span></span>
                                </div>
                                <p class="card-text mb-1 small">
                                    <strong>Campaign:</strong> <span id="active-workflow-campaign">-</span>
                                </p>
                                <p class="card-text mb-1 small">
                                    <strong>Started:</strong> <span id="active-workflow-start">-</span>
                                </p>
                                <div class="d-grid gap-2 mt-3">
                                    <button id="pause-active-workflow-btn" class="btn btn-sm btn-warning btn-action shadow-sm">
                                        <i class="fas fa-pause me-1"></i> Pause Workflow
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API Rate Limits -->
        <div class="col-lg-4 col-md-6">
            <div class="card resource-card shadow h-100">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    <h5 class="m-0 text-white">API Rate Limits</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm table-hover api-usage-table">
                            <thead>
                                <tr>
                                    <th>API Type</th>
                                    <th>Usage</th>
                                    <th>Reset In</th>
                                </tr>
                            </thead>
                            <tbody id="api-usage-table-body">
                                <!-- Will be populated by JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions Card -->
        <div class="col-lg-4 col-md-12">
            <div class="card resource-card shadow h-100">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-tools me-2"></i>
                    <h5 class="m-0 text-white">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-3">
                        <button id="optimize-btn" class="btn btn-success btn-lg btn-action shadow-sm" data-bs-toggle="tooltip" title="Optimize system resources and API usage">
                            <i class="fas fa-magic me-2"></i> Optimize Resources
                        </button>
                        <button id="resume-all-btn" class="btn btn-warning btn-lg btn-action shadow-sm" data-bs-toggle="tooltip" title="Resume all paused workflows">
                            <i class="fas fa-play me-2"></i> Resume All Paused Workflows
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mt-4">
        <!-- Workflow Queue -->
        <div class="col-12">
            <div class="card resource-card shadow">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-list-alt me-2"></i>
                        <h5 class="m-0 text-white">Workflow Queue</h5>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-primary rounded-pill px-3 py-2">
                            <span id="queue-count">0</span> / <span id="max-queue-size">0</span>
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="progress" style="height: 15px;">
                            <div id="queue-usage-bar" class="progress-bar bg-info" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                        <div class="d-flex justify-content-between mt-2">
                            <small class="text-muted">0%</small>
                            <small class="text-muted">50%</small>
                            <small class="text-muted">100%</small>
                        </div>
                    </div>

                    <div id="workflow-queue-container" class="mt-4">
                        <!-- Will be populated by JavaScript -->
                        <div class="text-center py-5" id="no-queued-workflows-message">
                            <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No workflows currently in queue</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Workflow Details Modal -->
<div class="modal fade" id="workflowDetailsModal" tabindex="-1" aria-labelledby="workflowDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="workflowDetailsModalLabel">
                    <i class="fas fa-info-circle me-2"></i> Workflow Details
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div id="workflow-details-loading" class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3">Loading workflow details...</p>
                </div>
                <div id="workflow-details-content" class="d-none">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h6 class="fw-bold text-primary mb-3">Workflow Information</h6>
                            <table class="table table-sm table-bordered">
                                <tr>
                                    <th class="bg-light">ID:</th>
                                    <td id="workflow-detail-id"></td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Type:</th>
                                    <td id="workflow-detail-type"></td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Status:</th>
                                    <td id="workflow-detail-status"></td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Campaign:</th>
                                    <td id="workflow-detail-campaign"></td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6 class="fw-bold text-primary mb-3">Timing Information</h6>
                            <table class="table table-sm table-bordered">
                                <tr>
                                    <th class="bg-light">Start Time:</th>
                                    <td id="workflow-detail-start-time"></td>
                                </tr>
                                <tr>
                                    <th class="bg-light">End Time:</th>
                                    <td id="workflow-detail-end-time"></td>
                                </tr>
                                <tr>
                                    <th class="bg-light">Duration:</th>
                                    <td id="workflow-detail-duration"></td>
                                </tr>
                            </table>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12">
                            <h6 class="fw-bold text-primary mb-3">Progress</h6>
                            <div class="progress mb-3" style="height: 20px;">
                                <div id="workflow-detail-progress-bar" class="progress-bar bg-success" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span id="workflow-detail-progress-text" class="badge bg-primary">0%</span>
                                <span id="workflow-detail-items-text" class="text-muted">0/0 items processed</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="workflow-details-error" class="alert alert-danger d-none">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span id="workflow-details-error-message">Error loading workflow details</span>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary btn-action" data-bs-dismiss="modal">Close</button>
                <button type="button" id="workflow-detail-action-btn" class="btn btn-primary btn-action d-none">
                    <i class="fas fa-play me-2"></i> <span id="workflow-detail-action-text">Action</span>
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="{% static 'campaigns/js/CommandNetSolutions.js' %}"></script>
<script>
    $(document).ready(function() {
        // Initial load
        loadResourceData();

        // Refresh button
        $('#refresh-btn').click(function() {
            loadResourceData();
        });

        // Retry connection button
        $(document).on('click', '#retry-connection-btn', function() {
            $('#redis-connection-error').find('div').html('<strong>Retrying connection...</strong>');
            loadResourceData();
        });

        // Auto refresh every 30 seconds
        setInterval(loadResourceData, 30000);

        // Optimize button
        $('#optimize-btn').click(function() {
            const $btn = $(this);
            const originalText = $btn.html();

            $btn.html('<i class="fas fa-spinner fa-spin btn-icon"></i> Optimizing...').prop('disabled', true);

            $.ajax({
                url: '{% url "campaigns:optimize-resources" %}',
                type: 'POST',
                success: function(data) {
                    showToast('Resources optimized successfully', 'success');
                    loadResourceData();
                },
                error: function(xhr) {
                    showToast('Error optimizing resources', 'error');
                },
                complete: function() {
                    setTimeout(function() {
                        $btn.html(originalText).prop('disabled', false);
                    }, 1000);
                }
            });
        });

        // Resume all button
        $('#resume-all-btn').click(function() {
            const $btn = $(this);
            const originalText = $btn.html();

            $btn.html('<i class="fas fa-spinner fa-spin btn-icon"></i> Resuming...').prop('disabled', true);

            $.ajax({
                url: '{% url "campaigns:resume-all-workflows" %}',
                type: 'POST',
                success: function(data) {
                    showToast('All workflows resumed successfully', 'success');
                    loadResourceData();
                },
                error: function(xhr) {
                    showToast('Error resuming workflows', 'error');
                },
                complete: function() {
                    setTimeout(function() {
                        $btn.html(originalText).prop('disabled', false);
                    }, 1000);
                }
            });
        });

        // Pause active workflow button
        $(document).on('click', '#pause-active-workflow-btn', function() {
            const workflowId = $(this).data('workflow-id');
            const $btn = $(this);
            const originalText = $btn.html();

            $btn.html('<i class="fas fa-spinner fa-spin btn-icon"></i> Pausing...').prop('disabled', true);

            pauseWorkflow(workflowId, function() {
                setTimeout(function() {
                    $btn.html(originalText).prop('disabled', false);
                }, 1000);
            });
        });
    });

    function loadResourceData() {
        $.ajax({
            url: '{% url "campaigns:resource-manager-status" %}',
            type: 'GET',
            success: function(data) {
                updateResourceDisplay(data);
                updateWorkflowDisplay(data.running_workflows);
                updateLastUpdated(data.timestamp);
            },
            error: function(xhr) {
                showToast('Error loading resource data', 'error');
            }
        });
    }

    function updateResourceDisplay(data) {
        // Check for Redis connection errors
        const hasRedisError = checkForRedisErrors(data);

        // Update bot worker status
        const botStatus = data.resources.bot_worker.status;
        const activeWorkflow = data.resources.bot_worker.active_workflow;

        if (botStatus === 'busy') {
            $('#bot-status-badge').removeClass('bg-success').addClass('bg-warning').text('Busy');
            $('#bot-status-text').text('Bot worker is currently running a workflow');
            $('#bot-status-indicator').removeClass('available error').addClass('busy');

            // Show active workflow details
            if (activeWorkflow) {
                $('#active-workflow-container').removeClass('d-none');
                $('#active-workflow-type').text(activeWorkflow.workflow_type.toUpperCase());
                $('#active-workflow-campaign').text(activeWorkflow.campaign_id);
                $('#active-workflow-priority').text(activeWorkflow.priority);
                $('#active-workflow-start').text(formatDateTime(activeWorkflow.start_time));

                // Set pause button action
                $('#pause-active-workflow-btn').data('workflow-id', activeWorkflow.id);
            } else {
                $('#active-workflow-container').addClass('d-none');
            }
        } else {
            $('#bot-status-badge').removeClass('bg-warning').addClass('bg-success').text('Available');
            $('#bot-status-text').text('Bot worker is ready for workflows');
            $('#bot-status-indicator').removeClass('busy error').addClass('available');
            $('#active-workflow-container').addClass('d-none');
        }

        // Update queue information
        const queueTotal = data.resources.queue.total;
        const queueMaxSize = data.resources.queue.max_size;
        const queuePercentage = (queueTotal / queueMaxSize) * 100;

        $('#queue-count').text(queueTotal);
        $('#max-queue-size').text(queueMaxSize);
        $('#queue-usage-bar').css('width', `${queuePercentage}%`);

        // Change progress bar color based on usage
        if (queuePercentage > 80) {
            $('#queue-usage-bar').removeClass('bg-info bg-warning').addClass('bg-danger');
        } else if (queuePercentage > 50) {
            $('#queue-usage-bar').removeClass('bg-info bg-danger').addClass('bg-warning');
        } else {
            $('#queue-usage-bar').removeClass('bg-warning bg-danger').addClass('bg-info');
        }

        // Update API usage table
        updateApiUsageTable(data.resources.api_usage);
    }

    function checkForRedisErrors(data) {
        let hasConnectionError = false;
        let errorMessage = "";

        // Check Redis status from the API response
        if (data.resources && data.resources.redis_status) {
            hasConnectionError = !data.resources.redis_status.available;
            errorMessage = data.resources.redis_status.error || "Unable to connect to Redis server";
        } else if (!data.resources.api_usage) {
            hasConnectionError = true;
            errorMessage = "Redis data not available";
        } else {
            // Check if any API type has a connection error
            for (const [apiType, usage] of Object.entries(data.resources.api_usage)) {
                if (usage.connection_error || usage.error) {
                    hasConnectionError = true;
                    errorMessage = "Redis connection error detected in API usage data";
                    break;
                }
            }
        }

        // Show/hide Redis connection error alert
        if (hasConnectionError) {
            const $errorAlert = $('#redis-connection-error');
            $errorAlert.show();
            $errorAlert.find('div strong').text('Redis Connection Error:');
            $errorAlert.find('div').contents().filter(function() {
                return this.nodeType === 3; // Text nodes
            }).replaceWith(' ' + errorMessage + ' Some features may be unavailable.');

            $('#bot-status-indicator').removeClass('available busy').addClass('error');
        } else {
            $('#redis-connection-error').hide();
        }

        return hasConnectionError;
    }

    function updateApiUsageTable(apiUsage) {
        const tableBody = $('#api-usage-table-body');
        tableBody.empty();

        if (!apiUsage || Object.keys(apiUsage).length === 0) {
            tableBody.append(`
                <tr>
                    <td colspan="3" class="text-center">No API usage data available</td>
                </tr>
            `);
            return;
        }

        for (const [apiType, usage] of Object.entries(apiUsage)) {
            const usagePercent = (usage.current / usage.limit) * 100;
            const resetTime = formatTimeRemaining(usage.reset_in);

            // Determine progress bar class based on usage percentage
            let progressClass = 'bg-success';
            if (usagePercent > 80) {
                progressClass = 'bg-danger';
            } else if (usagePercent > 50) {
                progressClass = 'bg-warning';
            }

            // Check if there was a connection error for this API type
            const hasError = usage.connection_error || usage.error;
            const errorIndicator = hasError ?
                '<i class="fas fa-exclamation-triangle text-warning ms-2" title="Redis connection error"></i>' : '';

            tableBody.append(`
                <tr>
                    <td>
                        <span class="text-capitalize">${apiType}</span>
                        ${errorIndicator}
                    </td>
                    <td>
                        <div class="progress mb-1">
                            <div class="progress-bar ${progressClass}"
                                 role="progressbar"
                                 style="width: ${usagePercent}%"></div>
                        </div>
                        <small>${usage.current} / ${usage.limit}</small>
                    </td>
                    <td>${resetTime}</td>
                </tr>
            `);
        }
    }

    function updateWorkflowDisplay(workflows) {
        const container = $('#workflow-queue-container');
        const noQueuedWorkflowsMessage = $('#no-queued-workflows-message');

        container.empty();

        // Filter to only show queued workflows
        const queuedWorkflows = workflows.filter(workflow => workflow.status === 'queued');

        if (!queuedWorkflows || queuedWorkflows.length === 0) {
            container.append(noQueuedWorkflowsMessage);
            return;
        }

        // Sort by priority (highest first) and then by submission time
        queuedWorkflows.sort((a, b) => {
            if (a.priority !== b.priority) {
                return b.priority - a.priority; // Higher priority first
            }
            return new Date(a.submission_time) - new Date(b.submission_time); // Earlier submission first
        });

        // Create a list group for the queue
        const listGroup = $('<div class="list-group shadow-sm"></div>');
        container.append(listGroup);

        // Add each workflow to the queue display
        queuedWorkflows.forEach((workflow, index) => {
            const isHighPriority = workflow.priority >= 8;
            const priorityClass = isHighPriority ? 'border-danger' : '';
            const priorityBadgeClass = isHighPriority ? 'bg-danger' : 'bg-primary';

            listGroup.append(`
                <div class="list-group-item list-group-item-action ${priorityClass} p-3">
                    <div class="d-flex w-100 justify-content-between align-items-center">
                        <h6 class="mb-1">
                            <span class="badge bg-secondary me-2 rounded-pill">#${index + 1}</span>
                            ${workflow.workflow_type.toUpperCase()}
                        </h6>
                        <span class="badge ${priorityBadgeClass} rounded-pill">Priority: ${workflow.priority}</span>
                    </div>
                    <p class="mb-1 small">
                        <i class="fas fa-project-diagram me-1 text-muted"></i> Campaign: ${workflow.campaign_id}
                    </p>
                    <p class="mb-1 small">
                        <i class="fas fa-clock me-1 text-muted"></i> Queued: ${formatDateTime(workflow.submission_time)}
                    </p>

                    <div class="d-flex justify-content-end mt-3">
                        <button class="btn btn-sm btn-outline-primary me-2 view-workflow-btn btn-action shadow-sm"
                                data-workflow-id="${workflow.id}">
                            <i class="fas fa-eye me-1"></i> View
                        </button>
                        <button class="btn btn-sm btn-danger remove-from-queue-btn btn-action shadow-sm"
                                data-workflow-id="${workflow.id}">
                            <i class="fas fa-times me-1"></i> Remove
                        </button>
                    </div>
                </div>
            `);
        });

        // Attach event handlers
        $('.view-workflow-btn').click(function() {
            const workflowId = $(this).data('workflow-id');
            // Show workflow details in a modal instead of navigating to a non-existent page
            showWorkflowDetailsModal(workflowId);
        });

        $('.remove-from-queue-btn').click(function() {
            const workflowId = $(this).data('workflow-id');
            removeFromQueue(workflowId);
        });
    }

    function removeFromQueue(workflowId) {
        $.ajax({
            url: '{% url "campaigns:remove-from-queue" %}',
            type: 'POST',
            data: { workflow_id: workflowId },
            success: function(data) {
                showToast('Workflow removed from queue', 'success');
                loadResourceData();
            },
            error: function(xhr) {
                showToast('Error removing workflow from queue', 'error');
            }
        });
    }

    function pauseWorkflow(workflowId, callback) {
        $.ajax({
            url: '{% url "campaigns:pause-workflow" %}',
            type: 'POST',
            data: { workflow_id: workflowId },
            success: function(data) {
                showToast('Workflow paused successfully', 'success');
                loadResourceData();
            },
            error: function(xhr) {
                showToast('Error pausing workflow', 'error');
            },
            complete: function() {
                if (typeof callback === 'function') {
                    callback();
                }
            }
        });
    }

    function resumeWorkflow(workflowId, callback) {
        $.ajax({
            url: '{% url "campaigns:resume-workflow" %}',
            type: 'POST',
            data: { workflow_id: workflowId },
            success: function(data) {
                showToast('Workflow resumed successfully', 'success');
                loadResourceData();
            },
            error: function(xhr) {
                showToast('Error resuming workflow', 'error');
            },
            complete: function() {
                if (typeof callback === 'function') {
                    callback();
                }
            }
        });
    }

    function updateLastUpdated(timestamp) {
        $('#update-time').text(formatDateTime(timestamp));
    }

    function showWorkflowDetailsModal(workflowId) {
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('workflowDetailsModal'));
        modal.show();

        // Reset modal state
        $('#workflow-details-loading').removeClass('d-none');
        $('#workflow-details-content').addClass('d-none');
        $('#workflow-details-error').addClass('d-none');
        $('#workflow-detail-action-btn').addClass('d-none');

        // Fetch workflow details
        $.ajax({
            url: '{% url "campaigns:workflow-details" workflow_id="placeholder" %}'.replace('placeholder', workflowId),
            type: 'GET',
            success: function(data) {
                if (data.success) {
                    // Update modal content with workflow details
                    updateWorkflowDetailsModal(data);
                } else {
                    // Show error
                    $('#workflow-details-loading').addClass('d-none');
                    $('#workflow-details-error').removeClass('d-none');
                    $('#workflow-details-error-message').text(data.error || 'Error loading workflow details');
                }
            },
            error: function(xhr) {
                // Show error
                $('#workflow-details-loading').addClass('d-none');
                $('#workflow-details-error').removeClass('d-none');
                $('#workflow-details-error-message').text('Error loading workflow details: ' + xhr.statusText);
            }
        });
    }

    function updateWorkflowDetailsModal(data) {
        // Hide loading, show content
        $('#workflow-details-loading').addClass('d-none');
        $('#workflow-details-content').removeClass('d-none');

        // Update workflow information
        $('#workflow-detail-id').text(data.id || '-');
        $('#workflow-detail-type').text(data.workflow_type ? data.workflow_type.toUpperCase() : '-');

        // Set status with appropriate badge
        let statusBadgeClass = 'bg-secondary';
        if (data.status === 'running') statusBadgeClass = 'bg-success';
        if (data.status === 'paused') statusBadgeClass = 'bg-warning';
        if (data.status === 'failed') statusBadgeClass = 'bg-danger';
        if (data.status === 'completed') statusBadgeClass = 'bg-info';

        $('#workflow-detail-status').html(`
            <span class="badge ${statusBadgeClass}">${data.status ? data.status.toUpperCase() : '-'}</span>
        `);

        $('#workflow-detail-campaign').text(data.campaign_id || '-');

        // Update timing information
        $('#workflow-detail-start-time').text(data.start_time ? formatDateTime(data.start_time) : '-');
        $('#workflow-detail-end-time').text(data.end_time ? formatDateTime(data.end_time) : '-');
        $('#workflow-detail-duration').text(data.duration ? `${data.duration.toFixed(2)} seconds` : '-');

        // Update progress
        const progress = data.progress || 0;
        $('#workflow-detail-progress-bar').css('width', `${progress}%`);
        $('#workflow-detail-progress-text').text(`${progress.toFixed(1)}%`);

        const processedItems = data.processed_items || 0;
        const totalItems = data.total_items || 0;
        $('#workflow-detail-items-text').text(`${processedItems}/${totalItems} items processed`);

        // Show action button based on status
        if (data.status === 'running') {
            $('#workflow-detail-action-btn').removeClass('d-none')
                .removeClass('btn-success').addClass('btn-warning')
                .data('workflow-id', data.id)
                .data('action', 'pause');
            $('#workflow-detail-action-btn i').removeClass('fa-play').addClass('fa-pause');
            $('#workflow-detail-action-text').text('Pause Workflow');
        } else if (data.status === 'paused') {
            $('#workflow-detail-action-btn').removeClass('d-none')
                .removeClass('btn-warning').addClass('btn-success')
                .data('workflow-id', data.id)
                .data('action', 'resume');
            $('#workflow-detail-action-btn i').removeClass('fa-pause').addClass('fa-play');
            $('#workflow-detail-action-text').text('Resume Workflow');
        } else {
            $('#workflow-detail-action-btn').addClass('d-none');
        }

        // Attach event handler to action button
        $('#workflow-detail-action-btn').off('click').on('click', function() {
            const workflowId = $(this).data('workflow-id');
            const action = $(this).data('action');

            if (action === 'pause') {
                pauseWorkflow(workflowId, function() {
                    // Refresh modal data
                    showWorkflowDetailsModal(workflowId);
                });
            } else if (action === 'resume') {
                resumeWorkflow(workflowId, function() {
                    // Refresh modal data
                    showWorkflowDetailsModal(workflowId);
                });
            }
        });
    }
</script>
{% endblock %}
