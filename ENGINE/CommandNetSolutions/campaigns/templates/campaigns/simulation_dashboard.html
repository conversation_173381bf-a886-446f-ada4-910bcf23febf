{% extends "campaigns/base.html" %}
{% load static %}

{% block title %}Whitelist Analytics Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'campaigns/css/simulation.css' %}">
{% endblock %}

{% block campaign_content %}
<div class="simulation-dashboard">
    <div class="simulation-header">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="simulation-title">Whitelist Analytics Dashboard</h1>
                        <p class="simulation-subtitle">Comprehensive whitelist analysis and insights for {{ campaign.name }}</p>
                    </div>
                    <div>
                        <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-outline-primary me-2">
                            <i class="fas fa-arrow-left"></i> Back to Campaign
                        </a>
                        <div class="btn-group">
                            <button type="button" class="btn btn-success dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-file-export"></i> Export Whitelist Data
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li class="dropdown-header">Whitelist Entries</li>
                                <li><a class="dropdown-item" href="{% url 'campaigns:simulation_export' campaign.id %}?type=whitelist&format=csv">
                                    <i class="fas fa-file-csv me-2"></i>CSV Format
                                </a></li>
                                <li><a class="dropdown-item" href="{% url 'campaigns:simulation_export' campaign.id %}?type=whitelist&format=excel">
                                    <i class="fas fa-file-excel me-2"></i>Excel Format
                                </a></li>
                                <li><a class="dropdown-item" href="{% url 'campaigns:simulation_export' campaign.id %}?type=whitelist&format=pdf">
                                    <i class="fas fa-file-pdf me-2"></i>PDF Report
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li class="dropdown-header">Tag Analysis Results</li>
                                <li><a class="dropdown-item" href="{% url 'campaigns:simulation_export' campaign.id %}?type=tags&format=csv">
                                    <i class="fas fa-tags me-2"></i>Tag Matches (CSV)
                                </a></li>
                                <li><a class="dropdown-item" href="{% url 'campaigns:simulation_export' campaign.id %}?type=tags&format=excel">
                                    <i class="fas fa-tags me-2"></i>Tag Analysis (Excel)
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li class="dropdown-header">Privilege Analysis</li>
                                <li><a class="dropdown-item" href="{% url 'campaigns:simulation_export' campaign.id %}?type=privileges&format=csv">
                                    <i class="fas fa-key me-2"></i>Privilege Distribution (CSV)
                                </a></li>
                                <li><a class="dropdown-item" href="{% url 'campaigns:simulation_export' campaign.id %}?type=combined&format=excel">
                                    <i class="fas fa-database me-2"></i>Complete Analytics (Excel)
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Campaign Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card simulation-card fade-in-up">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-info-circle me-2"></i> Campaign Information
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h3>{{ campaign.name }}</h3>
                            <p class="text-muted">{{ campaign.description }}</p>
                            <div class="mt-4">
                                <p><i class="fas fa-user me-2"></i> <strong>Created by:</strong> {{ campaign.creator.username }}</p>
                                <p><i class="fas fa-calendar-alt me-2"></i> <strong>Created at:</strong> {{ campaign.created_at|date:"F j, Y, g:i a" }}</p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-end mb-3">
                                <span class="status-badge badge {% if campaign.status == 'completed' %}bg-success{% elif campaign.status == 'running' %}bg-primary{% elif campaign.status == 'failed' %}bg-danger{% else %}bg-secondary{% endif %}">
                                    <i class="fas {% if campaign.status == 'completed' %}fa-check-circle{% elif campaign.status == 'running' %}fa-play-circle{% elif campaign.status == 'failed' %}fa-exclamation-circle{% else %}fa-circle{% endif %} me-1"></i>
                                    {{ campaign.status|title }}
                                </span>
                            </div>
                            <div class="metric-row">
                                <div class="metric-item">
                                    <div class="metric-value">{{ accounts_count }}</div>
                                    <div class="metric-label">Accounts Collected</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">{{ tagged_accounts_count }}</div>
                                    <div class="metric-label">Accounts Tagged</div>
                                </div>
                                <div class="metric-item">
                                    <div class="metric-value">{{ whitelist_count }}</div>
                                    <div class="metric-label">Accounts Whitelisted</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Whitelist Overview -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card simulation-card fade-in-up" style="animation-delay: 0.05s;">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-star me-2"></i> Whitelist Overview
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="metric-card text-center">
                                <div class="metric-value text-success">{{ whitelist_count }}</div>
                                <div class="metric-label">Total Whitelisted</div>
                                <div class="metric-description">Accounts approved for engagement</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card text-center">
                                <div class="metric-value text-info">{{ conversion_rate|floatformat:1 }}%</div>
                                <div class="metric-label">Conversion Rate</div>
                                <div class="metric-description">Tagged accounts that made whitelist</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card text-center">
                                <div class="metric-value text-warning">{{ avg_privileges|floatformat:1 }}</div>
                                <div class="metric-label">Avg Privileges</div>
                                <div class="metric-description">Average privileges per account</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card text-center">
                                <div class="metric-value text-primary">{{ high_value_accounts }}</div>
                                <div class="metric-label">High-Value Accounts</div>
                                <div class="metric-description">Accounts with all privileges</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Privilege Distribution -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card simulation-card fade-in-up" style="animation-delay: 0.1s;">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-key me-2"></i> Privilege Distribution
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="privilegeDistributionChart"></canvas>
                    </div>
                    <div class="mt-3">
                        <div class="privilege-stats">
                            <div class="row">
                                <div class="col-6">
                                    <div class="privilege-stat">
                                        <i class="fas fa-envelope text-primary"></i>
                                        <span class="privilege-name">DM Permission</span>
                                        <span class="privilege-count badge bg-primary">{{ dm_count }}</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="privilege-stat">
                                        <i class="fas fa-user-plus text-success"></i>
                                        <span class="privilege-name">Follow Permission</span>
                                        <span class="privilege-count badge bg-success">{{ follow_count }}</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="privilege-stat">
                                        <i class="fas fa-comment text-info"></i>
                                        <span class="privilege-name">Comment Permission</span>
                                        <span class="privilege-count badge bg-info">{{ comment_count }}</span>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="privilege-stat">
                                        <i class="fas fa-heart text-danger"></i>
                                        <span class="privilege-name">Like Permission</span>
                                        <span class="privilege-count badge bg-danger">{{ like_count }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card simulation-card fade-in-up" style="animation-delay: 0.15s;">
                <div class="card-header bg-warning text-dark">
                    <i class="fas fa-chart-pie me-2"></i> Whitelist Quality Score
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="qualityScoreChart"></canvas>
                    </div>
                    <div class="mt-3">
                        <div class="quality-breakdown">
                            <div class="quality-item">
                                <span class="quality-label">Premium Accounts</span>
                                <span class="quality-value">{{ premium_accounts }}</span>
                                <div class="quality-bar">
                                    <div class="quality-progress" style="width: {{ premium_percentage }}%"></div>
                                </div>
                            </div>
                            <div class="quality-item">
                                <span class="quality-label">Verified Accounts</span>
                                <span class="quality-value">{{ verified_whitelist_count }}</span>
                                <div class="quality-bar">
                                    <div class="quality-progress" style="width: {{ verified_percentage }}%"></div>
                                </div>
                            </div>
                            <div class="quality-item">
                                <span class="quality-label">High Engagement</span>
                                <span class="quality-value">{{ high_engagement_count }}</span>
                                <div class="quality-bar">
                                    <div class="quality-progress" style="width: {{ engagement_percentage }}%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Workflow Progress -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card simulation-card fade-in-up" style="animation-delay: 0.1s;">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-tasks me-2"></i> Workflow Progress
                </div>
                <div class="card-body">
                    {% for workflow in workflows %}
                    <div class="workflow-item">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h5 class="workflow-title">
                                <i class="fas {% if workflow.status == 'completed' %}fa-check-circle{% elif workflow.status == 'running' %}fa-play-circle{% elif workflow.status == 'failed' %}fa-exclamation-circle{% else %}fa-circle{% endif %} me-2"></i>
                                {{ workflow.workflow_name }}
                            </h5>
                            <span class="badge {% if workflow.status == 'completed' %}bg-success{% elif workflow.status == 'running' %}bg-primary{% elif workflow.status == 'failed' %}bg-danger{% else %}bg-secondary{% endif %}">
                                {{ workflow.status|title }}
                            </span>
                        </div>
                        <div class="workflow-progress">
                            <div class="progress-bar {% if workflow.status == 'completed' %}bg-success{% elif workflow.status == 'running' %}bg-primary{% elif workflow.status == 'failed' %}bg-danger{% else %}bg-secondary{% endif %}"
                                 role="progressbar"
                                 style="width: {{ workflow.progress }}%;"
                                 aria-valuenow="{{ workflow.progress }}"
                                 aria-valuemin="0"
                                 aria-valuemax="100">
                                {{ workflow.progress }}%
                            </div>
                        </div>
                        <div class="workflow-details">
                            <div>
                                <i class="fas fa-clock me-1"></i> Started: {{ workflow.start_time|date:"F j, Y, g:i a" }}
                                {% if workflow.end_time %}
                                <span class="ms-3"><i class="fas fa-flag-checkered me-1"></i> Completed: {{ workflow.end_time|date:"F j, Y, g:i a" }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="workflow-details mt-2">
                            <div>
                                <i class="fas fa-list-ol me-1"></i> Processed: {{ workflow.processed_items }} / {{ workflow.total_items }}
                            </div>
                            {% if workflow.duration %}
                            <div>
                                <i class="fas fa-stopwatch me-1"></i> Duration: {{ workflow.duration|floatformat:1 }} seconds
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% empty %}
                    <div class="empty-state">
                        <div class="empty-state-icon">
                            <i class="fas fa-tasks"></i>
                        </div>
                        <div class="empty-state-text">No workflows have been executed yet.</div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <!-- Tag Analysis Results -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card simulation-card fade-in-up" style="animation-delay: 0.2s;">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-tags me-2"></i> Tag Analysis Results
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="tagMatchChart"></canvas>
                    </div>
                    <div class="mt-4">
                        <h5><i class="fas fa-tag me-2"></i>Campaign Tags</h5>
                        <div class="mt-3">
                            {% for tag in campaign_tags %}
                            <span class="tag-badge badge {% if tag.is_required %}bg-danger{% else %}bg-info{% endif %}">
                                {{ tag.tag.name }}
                                {% if tag.is_required %}<i class="fas fa-exclamation-circle ms-1"></i>{% endif %}
                            </span>
                            {% empty %}
                            <div class="empty-state" style="padding: 20px;">
                                <p class="mb-0">No tags assigned to this campaign.</p>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card simulation-card fade-in-up" style="animation-delay: 0.3s;">
                <div class="card-header bg-warning text-dark">
                    <i class="fas fa-chart-pie me-2"></i> Account Distribution
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="accountDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Analytics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card simulation-card fade-in-up" style="animation-delay: 0.4s;">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-users me-2"></i> Follower Distribution
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="followerDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card simulation-card fade-in-up" style="animation-delay: 0.5s;">
                <div class="card-header bg-secondary text-white">
                    <i class="fas fa-user-tag me-2"></i> Account Types
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="accountTypesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Engagement Analytics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card simulation-card fade-in-up" style="animation-delay: 0.6s;">
                <div class="card-header bg-danger text-white">
                    <i class="fas fa-chart-line me-2"></i> Engagement Rate Distribution
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="engagementRateChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card simulation-card fade-in-up" style="animation-delay: 0.7s;">
                <div class="card-header bg-dark text-white">
                    <i class="fas fa-bullseye me-2"></i> Tag Effectiveness
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="tagEffectivenessChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Analytics -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card simulation-card fade-in-up" style="animation-delay: 0.8s;">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-chart-scatter me-2"></i> Engagement vs. Followers
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="engagementFollowersChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card simulation-card fade-in-up" style="animation-delay: 0.9s;">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-history me-2"></i> Account Collection Timeline
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="collectionTimelineChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Geographic Distribution -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card simulation-card fade-in-up" style="animation-delay: 1s;">
                <div class="card-header bg-info text-white">
                    <i class="fas fa-globe-americas me-2"></i> Geographic Distribution
                </div>
                <div class="card-body">
                    <div class="chart-container">
                        <canvas id="geoDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Account Samples -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card simulation-card fade-in-up" style="animation-delay: 1.1s;">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-user-friends me-2"></i> Sample Collected Accounts
                </div>
                <div class="card-body">
                    <div class="account-list">
                        {% for account in sample_accounts %}
                        <div class="account-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="account-username">
                                    <i class="fas fa-user me-2"></i>{{ account.username }}
                                </h6>
                                <span class="badge {% if account.is_verified %}bg-info{% else %}bg-secondary{% endif %}">
                                    <i class="fas {% if account.is_verified %}fa-check-circle{% else %}fa-user{% endif %} me-1"></i>
                                    {% if account.is_verified %}Verified{% else %}Not Verified{% endif %}
                                </span>
                            </div>
                            <p class="account-bio">{{ account.bio|truncatechars:100 }}</p>
                            <div class="account-stats">
                                <div><i class="fas fa-users me-1"></i> Followers: {{ account.followers }}</div>
                                <div><i class="fas fa-image me-1"></i> Posts: {{ account.number_of_posts }}</div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="empty-state-text">No accounts have been collected yet.</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card simulation-card fade-in-up" style="animation-delay: 1.2s;">
                <div class="card-header bg-success text-white">
                    <i class="fas fa-check-circle me-2"></i> Sample Whitelisted Accounts
                </div>
                <div class="card-body">
                    <div class="account-list">
                        {% for entry in sample_whitelist %}
                        <div class="account-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="account-username">
                                    <i class="fas fa-user me-2"></i>{{ entry.account.username }}
                                </h6>
                                <span class="badge bg-secondary">
                                    {{ entry.account.account_type|title }}
                                </span>
                            </div>
                            <div class="mt-2 mb-2">
                                <span class="text-muted"><i class="fas fa-tags me-1"></i> Tags:</span>
                                {% for tag in entry.tags %}
                                <span class="tag-badge badge bg-info">{{ tag }}</span>
                                {% empty %}
                                <span class="text-muted">None</span>
                                {% endfor %}
                            </div>
                            <div class="account-stats">
                                <div><i class="fas fa-users me-1"></i> Followers: {{ entry.account.followers }}</div>
                                <div><i class="fas fa-image me-1"></i> Posts: {{ entry.account.number_of_posts }}</div>
                            </div>
                        </div>
                        {% empty %}
                        <div class="empty-state">
                            <div class="empty-state-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="empty-state-text">No accounts have been whitelisted yet.</div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Privilege Distribution Chart
        var privilegeDistCtx = document.getElementById('privilegeDistributionChart').getContext('2d');
        var privilegeDistChart = new Chart(privilegeDistCtx, {
            type: 'bar',
            data: {
                labels: ['DM', 'Follow', 'Comment', 'Like', 'Discover', 'Favorite'],
                datasets: [{
                    label: 'Accounts with Permission',
                    data: [
                        {{ dm_count|default:0 }},
                        {{ follow_count|default:0 }},
                        {{ comment_count|default:0 }},
                        {{ like_count|default:0 }},
                        {{ discover_count|default:0 }},
                        {{ favorite_count|default:0 }}
                    ],
                    backgroundColor: [
                        'rgba(0, 123, 255, 0.7)',
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(23, 162, 184, 0.7)',
                        'rgba(220, 53, 69, 0.7)',
                        'rgba(255, 193, 7, 0.7)',
                        'rgba(108, 117, 125, 0.7)'
                    ],
                    borderColor: [
                        'rgba(0, 123, 255, 1)',
                        'rgba(40, 167, 69, 1)',
                        'rgba(23, 162, 184, 1)',
                        'rgba(220, 53, 69, 1)',
                        'rgba(255, 193, 7, 1)',
                        'rgba(108, 117, 125, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Accounts'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Privilege Type'
                        }
                    }
                }
            }
        });

        // Quality Score Chart
        var qualityScoreCtx = document.getElementById('qualityScoreChart').getContext('2d');
        var qualityScoreChart = new Chart(qualityScoreCtx, {
            type: 'doughnut',
            data: {
                labels: ['Premium Accounts', 'Verified Accounts', 'High Engagement', 'Standard Accounts'],
                datasets: [{
                    data: [
                        {{ premium_accounts|default:0 }},
                        {{ verified_whitelist_count|default:0 }},
                        {{ high_engagement_count|default:0 }},
                        {{ whitelist_count|default:0 }} - {{ premium_accounts|default:0 }} - {{ verified_whitelist_count|default:0 }} - {{ high_engagement_count|default:0 }}
                    ],
                    backgroundColor: [
                        'rgba(255, 193, 7, 0.8)',
                        'rgba(0, 123, 255, 0.8)',
                        'rgba(40, 167, 69, 0.8)',
                        'rgba(108, 117, 125, 0.8)'
                    ],
                    borderColor: [
                        'rgba(255, 193, 7, 1)',
                        'rgba(0, 123, 255, 1)',
                        'rgba(40, 167, 69, 1)',
                        'rgba(108, 117, 125, 1)'
                    ],
                    borderWidth: 2
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var label = context.label || '';
                                var value = context.parsed;
                                var total = context.dataset.data.reduce((a, b) => a + b, 0);
                                var percentage = ((value / total) * 100).toFixed(1);
                                return label + ': ' + value + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            }
        });

        // Tag Match Chart
        var tagMatchCtx = document.getElementById('tagMatchChart').getContext('2d');
        var tagMatchChart = new Chart(tagMatchCtx, {
            type: 'bar',
            data: {
                labels: {{ tag_names|safe }},
                datasets: [{
                    label: 'Matched Accounts',
                    data: {{ tag_matches|safe }},
                    backgroundColor: 'rgba(40, 167, 69, 0.7)',
                    borderColor: 'rgba(40, 167, 69, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Accounts'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Tags'
                        }
                    }
                }
            }
        });

        // Account Distribution Chart
        var accountDistCtx = document.getElementById('accountDistributionChart').getContext('2d');
        var accountDistChart = new Chart(accountDistCtx, {
            type: 'pie',
            data: {
                labels: ['Tagged & Whitelisted', 'Tagged (Not Whitelisted)', 'Not Tagged'],
                datasets: [{
                    data: [
                        {{ whitelist_count }},
                        {{ tagged_accounts_count }} - {{ whitelist_count }},
                        {{ accounts_count }} - {{ tagged_accounts_count }}
                    ],
                    backgroundColor: [
                        'rgba(40, 167, 69, 0.7)',
                        'rgba(23, 162, 184, 0.7)',
                        'rgba(108, 117, 125, 0.7)'
                    ],
                    borderColor: [
                        'rgba(40, 167, 69, 1)',
                        'rgba(23, 162, 184, 1)',
                        'rgba(108, 117, 125, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Follower Distribution Chart
        var followerDistCtx = document.getElementById('followerDistributionChart').getContext('2d');
        var followerDistChart = new Chart(followerDistCtx, {
            type: 'bar',
            data: {
                labels: {{ follower_ranges|default:"['0-1K', '1K-5K', '5K-10K', '10K-50K', '50K-100K', '100K+']"|safe }},
                datasets: [{
                    label: 'Number of Accounts',
                    data: {{ follower_distribution|default:"[0, 0, 0, 0, 0, 0]"|safe }},
                    backgroundColor: 'rgba(23, 162, 184, 0.7)',
                    borderColor: 'rgba(23, 162, 184, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Accounts'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Follower Range'
                        }
                    }
                }
            }
        });

        // Account Types Chart
        var accountTypesCtx = document.getElementById('accountTypesChart').getContext('2d');
        var accountTypesChart = new Chart(accountTypesCtx, {
            type: 'doughnut',
            data: {
                labels: {{ account_types_labels|default:"['Personal', 'Business', 'Creator']"|safe }},
                datasets: [{
                    data: {{ account_types_data|default:"[0, 0, 0]"|safe }},
                    backgroundColor: [
                        'rgba(108, 117, 125, 0.7)',
                        'rgba(0, 123, 255, 0.7)',
                        'rgba(255, 193, 7, 0.7)'
                    ],
                    borderColor: [
                        'rgba(108, 117, 125, 1)',
                        'rgba(0, 123, 255, 1)',
                        'rgba(255, 193, 7, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        // Engagement Rate Chart
        var engagementRateCtx = document.getElementById('engagementRateChart').getContext('2d');
        var engagementRateChart = new Chart(engagementRateCtx, {
            type: 'bar',
            data: {
                labels: {{ engagement_ranges|default:"['0-1%', '1-3%', '3-5%', '5-10%', '10%+']"|safe }},
                datasets: [{
                    label: 'Number of Accounts',
                    data: {{ engagement_distribution|default:"[0, 0, 0, 0, 0]"|safe }},
                    backgroundColor: 'rgba(220, 53, 69, 0.7)',
                    borderColor: 'rgba(220, 53, 69, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Accounts'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Engagement Rate'
                        }
                    }
                }
            }
        });

        // Tag Effectiveness Chart
        var tagEffectivenessCtx = document.getElementById('tagEffectivenessChart').getContext('2d');
        var tagEffectivenessChart = new Chart(tagEffectivenessCtx, {
            type: 'radar',
            data: {
                labels: {{ tag_names|default:"[]"|safe }},
                datasets: [{
                    label: 'Conversion Rate',
                    data: {{ tag_effectiveness|default:"[]"|safe }},
                    backgroundColor: 'rgba(52, 58, 64, 0.3)',
                    borderColor: 'rgba(52, 58, 64, 1)',
                    borderWidth: 2,
                    pointBackgroundColor: 'rgba(52, 58, 64, 1)',
                    pointRadius: 4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    r: {
                        beginAtZero: true,
                        max: 100,
                        ticks: {
                            stepSize: 20
                        }
                    }
                }
            }
        });

        // Engagement vs. Followers Scatter Chart
        var engagementFollowersCtx = document.getElementById('engagementFollowersChart').getContext('2d');
        var engagementFollowersChart = new Chart(engagementFollowersCtx, {
            type: 'scatter',
            data: {
                datasets: [{
                    label: 'Accounts',
                    data: {{ engagement_followers_data|default:"[]"|safe }},
                    backgroundColor: 'rgba(0, 123, 255, 0.7)',
                    borderColor: 'rgba(0, 123, 255, 1)',
                    borderWidth: 1,
                    pointRadius: 5,
                    pointHoverRadius: 8
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'logarithmic',
                        position: 'bottom',
                        title: {
                            display: true,
                            text: 'Followers (log scale)'
                        },
                        ticks: {
                            callback: function(value) {
                                if (value === 100 || value === 1000 || value === 10000 || value === 100000 || value === 1000000) {
                                    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
                                }
                                return '';
                            }
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Engagement Rate (%)'
                        },
                        min: 0,
                        max: 20
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                var point = context.raw;
                                return `Username: ${point.username}, Followers: ${point.x.toLocaleString()}, Engagement: ${point.y.toFixed(2)}%`;
                            }
                        }
                    }
                }
            }
        });

        // Account Collection Timeline Chart
        var collectionTimelineCtx = document.getElementById('collectionTimelineChart').getContext('2d');
        var collectionTimelineChart = new Chart(collectionTimelineCtx, {
            type: 'line',
            data: {
                labels: {{ collection_timeline_labels|default:"[]"|safe }},
                datasets: [{
                    label: 'Accounts Collected',
                    data: {{ collection_timeline_data|default:"[]"|safe }},
                    backgroundColor: 'rgba(40, 167, 69, 0.2)',
                    borderColor: 'rgba(40, 167, 69, 1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Time'
                        }
                    },
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Cumulative Accounts'
                        }
                    }
                }
            }
        });

        // Geographic Distribution Chart
        var geoDistributionCtx = document.getElementById('geoDistributionChart').getContext('2d');
        var geoDistributionChart = new Chart(geoDistributionCtx, {
            type: 'bar',
            data: {
                labels: {{ geo_distribution_labels|default:"[]"|safe }},
                datasets: [{
                    label: 'Accounts by Location',
                    data: {{ geo_distribution_data|default:"[]"|safe }},
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(199, 199, 199, 0.7)',
                        'rgba(83, 102, 255, 0.7)',
                        'rgba(40, 159, 64, 0.7)',
                        'rgba(210, 199, 199, 0.7)',
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(199, 199, 199, 1)',
                        'rgba(83, 102, 255, 1)',
                        'rgba(40, 159, 64, 1)',
                        'rgba(210, 199, 199, 1)',
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Accounts'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Location'
                        }
                    }
                }
            }
        });
    });
</script>
{% endblock %}
