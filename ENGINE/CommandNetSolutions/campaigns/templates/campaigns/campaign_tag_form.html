{% extends "campaigns/base.html" %}

{% block title %}{% if form.instance.id %}Edit{% else %}Assign{% endif %} Campaign Tag{% endblock %}

{% block campaign_content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">{% if form.instance.id %}Edit{% else %}Assign{% endif %} Campaign Tag</h1>
            <p class="page-subtitle">{% if form.instance.id %}Update tag settings{% else %}Assign a new tag{% endif %} for campaign: {{ campaign.name }}</p>
        </div>
        <div>
            <a href="{% url 'campaigns:campaign_tags' campaign.id %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Tags
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-tag me-2"></i>{% if form.instance.id %}Edit{% else %}Assign{% endif %} Tag</h5>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}

                        {% if form.instance.id %}
                        <div class="mb-3">
                            <label class="form-label">Tag</label>
                            <div class="form-control-plaintext">
                                <span class="badge bg-primary">{{ form.instance.tag.name }}</span>
                            </div>
                        </div>
                        {% else %}
                        <div class="mb-3">
                            <label for="{{ form.tag.id_for_label }}" class="form-label">Tag</label>
                            {{ form.tag.errors }}
                            {{ form.tag }}
                            <div class="form-text">Select a tag to assign to this campaign.</div>
                        </div>
                        {% endif %}

                        <div class="mb-3 form-check">
                            {{ form.is_required }}
                            <label for="{{ form.is_required.id_for_label }}" class="form-check-label">Required for Whitelist</label>
                            <div class="form-text">If checked, accounts must match this tag to be included in the whitelist.</div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="{% url 'campaigns:campaign_tags' campaign.id %}" class="btn btn-outline-secondary me-2">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> {% if form.instance.id %}Update{% else %}Assign{% endif %} Tag
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
