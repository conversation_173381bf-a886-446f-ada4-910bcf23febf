{% extends 'campaigns/base.html' %}
{% load static %}

{% block title %}Dynamic Tags{% endblock %}

{% block campaign_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title">Dynamic Tags</h1>
        <p class="page-subtitle">Manage dynamic tags for campaign analysis</p>
    </div>
    <div>
        <a href="{% url 'campaigns:dynamic_tag_create' %}" class="btn btn-primary">
            Create Tag <i class="fas fa-plus-circle"></i>
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-tags me-2"></i>Dynamic Tags</h5>
    </div>
    <div class="card-body">
        <div class="d-flex justify-content-end mb-3">
            <form method="get" class="d-flex">
                <select name="category" class="form-select me-2" style="width: 150px;">
                    <option value="">All Categories</option>
                    {% for category in categories %}
                    <option value="{{ category.id }}" {% if request.GET.category == category.id|stringformat:"s" %}selected{% endif %}>{{ category.name }}</option>
                    {% endfor %}
                </select>
                <div class="input-group" style="width: 250px;">
                    <input type="text" class="form-control" id="searchTags" name="search" placeholder="Search tags..." value="{{ request.GET.search|default:'' }}">
                    <button class="btn btn-outline-secondary" type="submit">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
        {% if request.GET.search or request.GET.category %}
        <div class="d-flex justify-content-end mb-3">
            <a href="{% url 'campaigns:dynamic_tag_list' %}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-times me-1"></i> Clear Filters
            </a>
        </div>
        {% endif %}
        {% if dynamic_tags %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Tag Description</th>
                        <th>Tag Category</th>
                        <th>Global</th>
                        <th>System</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for tag in dynamic_tags %}
                    <tr>
                        <td>{{ tag.name }}</td>
                        <td>{{ tag.description|default:"" }}</td>
                        <td>{{ tag.category.name|default:"Uncategorized" }}</td>
                        <td>
                            {% if tag.is_global %}
                            <span class="badge bg-success">Yes</span>
                            {% else %}
                            <span class="badge bg-secondary">No</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if tag.is_system %}
                            <span class="badge bg-primary" data-bs-toggle="tooltip" title="System tags cannot be deleted">System</span>
                            {% else %}
                            <span class="badge bg-light text-dark">User</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="d-flex">
                                <a href="{% url 'campaigns:dynamic_tag_update' tag.id %}" class="btn btn-sm btn-outline-primary me-2" style="width: 80px;">
                                    <i class="fas fa-edit"></i>
                                </a>
                                {% if tag.is_system %}
                                <button class="btn btn-sm btn-outline-secondary" style="width: 80px;" disabled data-bs-toggle="tooltip" title="System tags cannot be deleted">
                                    <i class="fas fa-lock"></i>
                                </button>
                                {% else %}
                                <a href="{% url 'campaigns:dynamic_tag_delete' tag.id %}" class="btn btn-sm btn-outline-danger" style="width: 80px;">
                                    <i class="fas fa-trash"></i>
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        {% include 'includes/pagination.html' with page_obj=page_obj %}
        {% else %}
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-tags"></i>
            </div>
            <h4>No Dynamic Tags Found</h4>
            <p>Create dynamic tags to enhance your campaign analysis.</p>
            <a href="{% url 'campaigns:dynamic_tag_create' %}" class="btn btn-primary">
                Create Tag <i class="fas fa-plus-circle"></i>
            </a>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Client-side filtering for quick results
        $('#searchTags').on('keyup', function() {
            const searchTerm = $(this).val().toLowerCase();

            // Only apply client-side filtering if not submitting the form
            if (searchTerm.length > 0 && searchTerm.length < 3) {
                $('tbody tr').each(function() {
                    const text = $(this).text().toLowerCase();
                    $(this).toggle(text.indexOf(searchTerm) > -1);
                });
            } else if (searchTerm.length === 0) {
                // Show all rows when search is cleared
                $('tbody tr').show();
            }
        });

        // Submit search form when pressing Enter
        $('#searchTags').on('keypress', function(e) {
            if (e.which === 13 && $(this).val().length >= 3) {
                $(this).closest('form').submit();
            }
        });

        // Initialize tooltips
        $('[data-bs-toggle="tooltip"]').tooltip();
    });
</script>
{% endblock extra_js %}
