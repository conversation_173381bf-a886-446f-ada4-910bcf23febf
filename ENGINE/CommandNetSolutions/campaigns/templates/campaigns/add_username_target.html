{% extends "campaigns/base.html" %}

{% block title %}Add Username Targets{% endblock %}

{% block campaign_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title">Add Username Targets</h1>
        <p class="page-subtitle">Campaign: {{ campaign.name }}</p>
    </div>
    <div>
        <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-outline-primary btn-icon">
            <i class="fas fa-arrow-left me-1"></i> Back
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="campaign-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>Add Usernames</h5>
            </div>
            <div class="card-body">
                <form method="post">
                    {% csrf_token %}

                    {% if form.non_field_errors %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        {% for error in form.non_field_errors %}
                        {{ error }}
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="form-group mb-4">
                        <label class="form-label" for="{{ form.usernames.id_for_label }}">Usernames</label>
                        {{ form.usernames.errors }}
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-users"></i></span>
                            <textarea class="form-control {% if form.usernames.errors %}is-invalid{% endif %}"
                                    id="{{ form.usernames.id_for_label }}" name="{{ form.usernames.html_name }}"
                                    rows="10" placeholder="Enter one username per line">{{ form.usernames.value|default:'' }}</textarea>
                        </div>
                        {% if form.usernames.help_text %}
                        <small class="form-text text-muted">{{ form.usernames.help_text }}</small>
                        {% endif %}
                    </div>

                    <div class="form-group mb-4">
                        <label class="form-label">Audience Type</label>
                        <div class="audience-type-selector">
                            <div class="btn-group w-100" role="group">
                                <input type="radio" class="btn-check" name="audience_type" id="audience_profile" value="profile" checked>
                                <label class="btn btn-outline-primary" for="audience_profile">
                                    <i class="fas fa-user me-1"></i> Profile Only
                                </label>

                                <input type="radio" class="btn-check" name="audience_type" id="audience_followers" value="followers">
                                <label class="btn btn-outline-primary" for="audience_followers">
                                    <i class="fas fa-users me-1"></i> Followers
                                </label>

                                <input type="radio" class="btn-check" name="audience_type" id="audience_following" value="following">
                                <label class="btn btn-outline-primary" for="audience_following">
                                    <i class="fas fa-user-friends me-1"></i> Following
                                </label>

                                <input type="radio" class="btn-check" name="audience_type" id="audience_both" value="both">
                                <label class="btn btn-outline-primary" for="audience_both">
                                    <i class="fas fa-arrows-alt-h me-1"></i> Both
                                </label>
                            </div>
                        </div>
                        <small class="form-text text-muted mt-2">
                            Select the type of audience data to collect for these usernames.
                        </small>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-icon">
                            <i class="fas fa-plus-circle me-1"></i> Add Usernames
                        </button>
                        <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </form>
            </div>
            <div class="card-footer">
                <div class="alert alert-info mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Audience Type Explanation:</strong>
                    <p class="mb-0 mt-2">
                        <strong>Profile Only:</strong> The system will collect data only for the specified usernames.<br>
                        <strong>Followers:</strong> The system will collect data for the followers of the specified usernames.<br>
                        <strong>Following:</strong> The system will collect data for the accounts that the specified usernames are following.<br>
                        <strong>Both:</strong> The system will collect data for both followers and following of the specified usernames.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-4">
        <div class="campaign-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-lightbulb me-2"></i>Tips & Information</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Format:</strong> Enter one username per line without the @ symbol.
                </div>

                <div class="alert alert-info mb-3">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Example:</strong>
                    <pre class="mt-2 mb-0">instagram
natgeo
nike</pre>
                </div>

                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Make sure the usernames are valid Instagram accounts. Invalid usernames will be skipped during processing.
                </div>
            </div>
        </div>

        <div class="campaign-card mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Bulk Import</h5>
            </div>
            <div class="card-body">
                <p>You can copy and paste usernames from a spreadsheet or text file.</p>

                <div class="alert alert-info">
                    <i class="fas fa-file-csv me-2"></i>
                    <strong>CSV Import:</strong> Copy a column of usernames from a CSV file and paste them into the textarea.
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
