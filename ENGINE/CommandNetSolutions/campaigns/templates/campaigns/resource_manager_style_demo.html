<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Resource Manager Dashboard - Style Demo</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        /* General Layout */
        .dashboard-container {
            padding: 1.5rem;
        }

        .dashboard-header {
            margin-bottom: 2rem;
        }

        .dashboard-title {
            font-size: 32px;
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
        }

        .dashboard-subtitle {
            color: #7f8c8d;
            font-size: 16px;
        }

        /* Card Styling */
        .resource-card {
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
            margin-bottom: 30px;
            transition: all 0.3s ease;
            overflow: hidden;
            border: none;
            height: 100%;
        }

        .resource-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
        }

        .resource-card .card-header {
            background-color: #3498db;
            color: white;
            font-weight: 600;
            padding: 18px 20px;
            border-bottom: none;
            display: flex;
            align-items: center;
        }

        .resource-card .card-header i {
            margin-right: 12px;
            font-size: 1.2rem;
        }

        .resource-card .card-header h5 {
            margin: 0;
            font-size: 18px;
            font-weight: 600;
            color: white;
        }

        .resource-card .card-body {
            padding: 25px;
        }

        /* Resource Values */
        .resource-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
            color: #3498db;
        }

        .resource-value {
            font-size: 36px;
            font-weight: 700;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .resource-subtitle {
            font-size: 14px;
            color: #7f8c8d;
            margin-bottom: 15px;
        }

        /* Status Indicators */
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.available {
            background-color: #2ecc71;
        }

        .status-indicator.busy {
            background-color: #f39c12;
        }

        .status-indicator.error {
            background-color: #e74c3c;
        }

        /* Progress Bars */
        .progress {
            height: 12px;
            margin-top: 12px;
            margin-bottom: 8px;
            border-radius: 6px;
            background-color: #f5f5f5;
            overflow: hidden;
        }

        .progress-bar {
            border-radius: 6px;
            transition: width 0.3s ease;
        }

        /* Buttons */
        .btn-action {
            border-radius: 6px;
            padding: 8px 16px;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            transition: all 0.2s ease;
        }

        .btn-action:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }

        /* Last Updated Text */
        #last-updated {
            font-style: italic;
            color: #7f8c8d;
            margin-bottom: 20px;
            font-size: 14px;
        }

        /* List Group Styling */
        .list-group-item {
            padding: 15px;
            border-left: 4px solid transparent;
            transition: all 0.2s ease;
            margin-bottom: 8px;
            border-radius: 8px !important;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .list-group-item:hover {
            background-color: #f8f9fa;
            transform: translateX(3px);
        }

        .list-group-item.border-danger {
            border-left-color: #e74c3c;
        }

        /* Responsive Adjustments */
        @media (max-width: 768px) {
            .resource-card {
                margin-bottom: 15px;
            }

            .resource-value {
                font-size: 28px;
            }
            
            .dashboard-title {
                font-size: 24px;
            }
            
            .card-body {
                padding: 15px;
            }
            
            .btn-lg {
                padding: 8px 16px;
                font-size: 16px;
            }
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">CommandNetSolutions</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="#">Dashboard</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Campaigns</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">Tags</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#">CEP Workflows</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="#">Resource Manager</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid dashboard-container">
        <div class="dashboard-header d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="dashboard-title">Resource Manager Dashboard</h1>
                <p id="last-updated" class="mt-2 mb-0">
                    <i class="fas fa-clock me-2"></i> Last updated: <span id="update-time">Just now</span>
                </p>
            </div>
            <button id="refresh-btn" class="btn btn-primary btn-action shadow-sm">
                <i class="fas fa-sync-alt me-2"></i> Refresh
            </button>
        </div>

        <div class="row g-4">
            <!-- System Resources -->
            <div class="col-lg-4 col-md-6">
                <div class="card resource-card shadow h-100">
                    <div class="card-header d-flex align-items-center">
                        <i class="fas fa-robot me-2"></i>
                        <h5 class="m-0 text-white">Bot Worker Status</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-4">
                            <div class="resource-value">
                                <div class="d-flex align-items-center justify-content-center">
                                    <span class="status-indicator available"></span>
                                    <span class="badge bg-success">Available</span>
                                </div>
                            </div>
                            <p class="resource-subtitle">Bot worker is ready for workflows</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- API Rate Limits -->
            <div class="col-lg-4 col-md-6">
                <div class="card resource-card shadow h-100">
                    <div class="card-header d-flex align-items-center">
                        <i class="fas fa-tachometer-alt me-2"></i>
                        <h5 class="m-0 text-white">API Rate Limits</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead>
                                    <tr>
                                        <th>API Type</th>
                                        <th>Usage</th>
                                        <th>Reset In</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td><span class="text-capitalize">Instagram</span></td>
                                        <td>
                                            <div class="progress mb-1">
                                                <div class="progress-bar bg-success" role="progressbar" style="width: 25%"></div>
                                            </div>
                                            <small>25 / 100</small>
                                        </td>
                                        <td>45m</td>
                                    </tr>
                                    <tr>
                                        <td><span class="text-capitalize">Graph</span></td>
                                        <td>
                                            <div class="progress mb-1">
                                                <div class="progress-bar bg-warning" role="progressbar" style="width: 65%"></div>
                                            </div>
                                            <small>65 / 100</small>
                                        </td>
                                        <td>30m</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions Card -->
            <div class="col-lg-4 col-md-12">
                <div class="card resource-card shadow h-100">
                    <div class="card-header d-flex align-items-center">
                        <i class="fas fa-tools me-2"></i>
                        <h5 class="m-0 text-white">Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-3">
                            <button class="btn btn-success btn-lg btn-action shadow-sm">
                                <i class="fas fa-magic me-2"></i> Optimize Resources
                            </button>
                            <button class="btn btn-warning btn-lg btn-action shadow-sm">
                                <i class="fas fa-play me-2"></i> Resume All Paused Workflows
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <!-- Workflow Queue -->
            <div class="col-12">
                <div class="card resource-card shadow">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-list-alt me-2"></i>
                            <h5 class="m-0 text-white">Workflow Queue</h5>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="badge bg-primary rounded-pill px-3 py-2">
                                <span>2</span> / <span>10</span>
                            </span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="progress" style="height: 15px;">
                                <div class="progress-bar bg-info" role="progressbar" style="width: 20%" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                            <div class="d-flex justify-content-between mt-2">
                                <small class="text-muted">0%</small>
                                <small class="text-muted">50%</small>
                                <small class="text-muted">100%</small>
                            </div>
                        </div>

                        <div class="mt-4">
                            <div class="list-group shadow-sm">
                                <div class="list-group-item list-group-item-action p-3">
                                    <div class="d-flex w-100 justify-content-between align-items-center">
                                        <h6 class="mb-1">
                                            <span class="badge bg-secondary me-2 rounded-pill">#1</span>
                                            COLLECTION
                                        </h6>
                                        <span class="badge bg-primary rounded-pill">Priority: 5</span>
                                    </div>
                                    <p class="mb-1 small">
                                        <i class="fas fa-project-diagram me-1 text-muted"></i> Campaign: Summer2025
                                    </p>
                                    <p class="mb-1 small">
                                        <i class="fas fa-clock me-1 text-muted"></i> Queued: 2 hours ago
                                    </p>

                                    <div class="d-flex justify-content-end mt-3">
                                        <button class="btn btn-sm btn-outline-primary me-2 btn-action shadow-sm">
                                            <i class="fas fa-eye me-1"></i> View
                                        </button>
                                        <button class="btn btn-sm btn-danger btn-action shadow-sm">
                                            <i class="fas fa-times me-1"></i> Remove
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
