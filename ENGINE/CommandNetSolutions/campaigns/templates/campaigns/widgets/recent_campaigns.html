{% load static %}

<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><i class="fas fa-list me-2"></i>Recent Campaigns</h5>
        <div class="dropdown">
            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="dropdownMenuButton" data-bs-toggle="dropdown" aria-expanded="false">
                <i class="fas fa-filter me-1"></i> Filter
            </button>
            <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                <li><a class="dropdown-item" href="#">All Campaigns</a></li>
                <li><a class="dropdown-item" href="#">Active Campaigns</a></li>
                <li><a class="dropdown-item" href="#">Completed Campaigns</a></li>
                <li><a class="dropdown-item" href="#">Draft Campaigns</a></li>
            </ul>
        </div>
    </div>
    <div class="card-body">
        {% if recent_campaigns %}
        <div class="table-responsive">
            <table class="table table-campaigns">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Type</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for campaign in recent_campaigns %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    {% with has_location=campaign.location_targets.exists has_username=campaign.username_targets.exists %}
                                    {% if has_location and has_username %}
                                        <i class="fas fa-exchange-alt text-purple"></i>
                                    {% elif has_location %}
                                        <i class="fas fa-map-marker-alt text-danger"></i>
                                    {% elif has_username %}
                                        <i class="fas fa-user text-primary"></i>
                                    {% else %}
                                        <i class="fas {% if campaign.target_type == 'location' %}fa-map-marker-alt text-danger{% else %}fa-user text-primary{% endif %}"></i>
                                    {% endif %}
                                    {% endwith %}
                                </div>
                                <div>
                                    <div class="fw-bold">{{ campaign.name }}</div>
                                    <div class="small text-muted">{{ campaign.description|truncatechars:50 }}</div>
                                </div>
                            </div>
                        </td>
                        <td>
                            {% with has_location=campaign.location_targets.exists has_username=campaign.username_targets.exists %}
                            {% if has_location and has_username %}
                                <span class="badge bg-light text-dark">
                                    Mixed (Both Types)
                                </span>
                            {% elif has_location %}
                                <span class="badge bg-light text-dark">
                                    Location Based
                                </span>
                            {% elif has_username %}
                                <span class="badge bg-light text-dark">
                                    Username Based
                                </span>
                            {% else %}
                                <span class="badge bg-light text-dark">
                                    {{ campaign.get_target_type_display }}
                                </span>
                            {% endif %}
                            {% endwith %}
                        </td>
                        <td>
                            <span class="campaign-status status-{{ campaign.status }}">
                                {{ campaign.get_status_display }}
                            </span>
                        </td>
                        <td>{{ campaign.created_at|date:"M d, Y" }}</td>
                        <td>
                            <div class="d-flex justify-content-end">
                                <div class="btn-group action-buttons">
                                    <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'campaigns:campaign_update' campaign.id %}" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="Edit Campaign">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    {% if campaign.status == 'draft' %}
                                    <a href="{% url 'campaigns:launch_campaign' campaign.id %}" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Launch Campaign">
                                        <i class="fas fa-rocket"></i>
                                    </a>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="empty-state">
            <div class="empty-state-icon">
                <i class="fas fa-clipboard-list"></i>
            </div>
            <div class="empty-state-text">No campaigns created yet.</div>
            <a href="{% url 'campaigns:campaign_create' %}" class="btn btn-primary btn-icon">
                <i class="fas fa-plus-circle"></i> Create Your First Campaign
            </a>
        </div>
        {% endif %}
    </div>
    <div class="card-footer text-center">
        <a href="{% url 'campaigns:campaign_list' %}" class="btn btn-outline-primary btn-icon">
            <i class="fas fa-list me-1"></i> View All
        </a>
    </div>
</div>
