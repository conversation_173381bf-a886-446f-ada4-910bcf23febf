{% extends "campaigns/base.html" %}

{% block title %}Tag Groups{% endblock %}

{% block campaign_content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">Tag Groups</h1>
            <p class="page-subtitle">Manage your tag groups for easier tag organization</p>
        </div>
        <div>
            <a href="{% url 'campaigns:tag_group_create' %}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> Create Tag Group
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-tags me-2"></i>All Tag Groups</h5>
            <div class="d-flex">
                <div class="search-box me-3">
                    <i class="fas fa-search"></i>
                    <input type="text" style="margin-bottom: 0rem;" class="form-control" id="searchTagGroups" placeholder="Search tag groups...">
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if tag_groups %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Tags</th>
                            <th>Global</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for tag_group in tag_groups %}
                        <tr>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        <i class="fas fa-tags text-primary"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ tag_group.name }}</div>
                                    </div>
                                </div>
                            </td>
                            <td>{{ tag_group.description|truncatechars:50|default:"No description" }}</td>
                            <td>{{ tag_group.tags.count }}</td>
                            <td>
                                {% if tag_group.is_global %}
                                <span class="badge bg-success">Yes</span>
                                {% else %}
                                <span class="badge bg-secondary">No</span>
                                {% endif %}
                            </td>
                            <td>{{ tag_group.created_at|date:"M d, Y" }}</td>
                            <td>
                                <div class="btn-group action-buttons">
                                    <a href="{% url 'campaigns:tag_group_detail' tag_group.id %}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'campaigns:tag_group_update' tag_group.id %}" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="Edit Tag Group">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'campaigns:tag_group_delete' tag_group.id %}" class="btn btn-sm btn-danger delete-tag-group-btn" data-bs-toggle="tooltip" title="Delete Tag Group">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1" aria-label="First">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-tags"></i>
                </div>
                <h4>No Tag Groups Found</h4>
                <p>You haven't created any tag groups yet.</p>
                <a href="{% url 'campaigns:tag_group_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-1"></i> Create Your First Tag Group
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchTagGroups');
    if (searchInput) {
        searchInput.addEventListener('keyup', function() {
            const searchTerm = this.value.toLowerCase();
            const rows = document.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        });
    }
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
});
</script>
{% endblock %}
