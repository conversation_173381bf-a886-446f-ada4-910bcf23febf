{% extends "campaigns/base.html" %}
{% load static %}

{% block title %}Run Campaign Simulation{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'campaigns/css/simulation.css' %}">
{% endblock %}

{% block campaign_content %}
<div class="simulation-dashboard">
    <div class="simulation-header">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="simulation-title">Run Campaign Simulation</h1>
                        <p class="simulation-subtitle">Configure and run a simulation for {{ campaign.name }}</p>
                    </div>
                    <div>
                        <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-outline-primary">
                            <i class="fas fa-arrow-left me-2"></i> Back to Campaign
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card simulation-card fade-in-up">
                <div class="card-header bg-primary text-white">
                    <i class="fas fa-cogs me-2"></i> Simulation Parameters
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'campaigns:run_simulation' campaign.id %}" class="simulation-form">
                        {% csrf_token %}

                        <div class="form-group">
                            <label for="campaign_name" class="form-label">Campaign</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-bullhorn"></i></span>
                                <input type="text" class="form-control" id="campaign_name" value="{{ campaign.name }}" readonly>
                            </div>
                            <div class="help-text">The simulation will be run for this campaign.</div>
                        </div>

                        <div class="form-group">
                            <label for="num_accounts" class="form-label">Number of Accounts</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-users"></i></span>
                                <input type="number" class="form-control" id="num_accounts" name="num_accounts" value="100" min="10" max="1000">
                            </div>
                            <div class="help-text">The number of accounts to simulate collecting. More accounts will take longer to process.</div>
                        </div>

                        <div class="form-group">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="use_airflow" name="use_airflow">
                                <label class="form-check-label" for="use_airflow">Use Airflow</label>
                            </div>
                            <div class="help-text">If checked, the simulation will be run using Airflow DAGs. This allows for better monitoring and error handling, but requires Airflow to be set up.</div>
                        </div>

                        <div class="form-group" id="airflow_options" style="display: none;">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-sliders-h me-2"></i>Airflow Options</h5>

                                    <div class="form-group">
                                        <label for="min_followers" class="form-label">Minimum Followers</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user-friends"></i></span>
                                            <input type="number" class="form-control" id="min_followers" name="min_followers" value="0" min="0">
                                        </div>
                                        <div class="help-text">Minimum number of followers for tag analysis. Use 0 for no minimum.</div>
                                    </div>

                                    <div class="form-group">
                                        <label for="max_followers" class="form-label">Maximum Followers</label>
                                        <div class="input-group">
                                            <span class="input-group-text"><i class="fas fa-user-friends"></i></span>
                                            <input type="number" class="form-control" id="max_followers" name="max_followers" value="0" min="0">
                                        </div>
                                        <div class="help-text">Maximum number of followers for tag analysis. Use 0 for no maximum.</div>
                                    </div>

                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="enable_tagging" name="enable_tagging" checked>
                                        <label class="form-check-label" for="enable_tagging">Enable Tagging</label>
                                        <div class="help-text">If checked, accounts will be tagged based on the campaign tags.</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info mt-4">
                            <i class="fas fa-info-circle me-2"></i>
                            <span>This will simulate a full campaign lifecycle, including account collection, tag analysis, and whitelist generation.
                            The simulation will create dummy Instagram accounts and associate them with this campaign.</span>
                        </div>

                        <div class="form-group text-center mt-4">
                            <button type="submit" class="btn btn-primary btn-lg btn-submit">
                                <i class="fas fa-play me-2"></i> Run Simulation
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const useAirflowCheckbox = document.getElementById('use_airflow');
        const airflowOptions = document.getElementById('airflow_options');

        // Show/hide Airflow options based on checkbox
        useAirflowCheckbox.addEventListener('change', function() {
            if (this.checked) {
                airflowOptions.style.display = 'block';
            } else {
                airflowOptions.style.display = 'none';
            }
        });
    });
</script>
{% endblock %}
