{% extends "campaigns/base.html" %}

{% block title %}Add Location Target{% endblock %}

{% block campaign_content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="page-title">Add Location Target</h1>
        <p class="page-subtitle">Campaign: {{ campaign.name }}</p>
    </div>
    <div>
        <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-outline-primary btn-icon">
            <i class="fas fa-arrow-left me-1"></i> Back
        </a>
    </div>
</div>

<div class="row">
    <div class="col-lg-4">
        <div class="campaign-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-search me-2"></i>Search Locations</h5>
            </div>
            <div class="card-body">
                <form method="get">
                    <div class="form-group mb-4">
                        <label class="form-label" for="{{ form.search_term.id_for_label }}">Search Term</label>
                        {{ form.search_term.errors }}
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-globe"></i></span>
                            <input type="text" class="form-control {% if form.search_term.errors %}is-invalid{% endif %}"
                                   id="{{ form.search_term.id_for_label }}" name="{{ form.search_term.html_name }}"
                                   value="{{ search_term|default:'' }}" placeholder="Enter country or city name">
                        </div>
                        {% if form.search_term.help_text %}
                        <small class="form-text text-muted">{{ form.search_term.help_text }}</small>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary btn-icon">
                            <i class="fas fa-search me-1"></i> Search
                        </button>
                        <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-outline-secondary">Cancel</a>
                    </div>
                </form>

                <div class="alert alert-info mt-4">
                    <i class="fas fa-info-circle me-2"></i>
                    Search for locations by country or city name. You can add multiple locations to your campaign.
                </div>

                <div class="alert alert-info">
                    <i class="fas fa-lightbulb me-2"></i>
                    <strong>Tip:</strong> Try searching for major cities or countries to find the most relevant locations.
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-8">
        <div class="campaign-card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-map-marker-alt me-2"></i>Search Results</h5>
                {% if locations %}
                <span class="badge bg-primary">{{ locations|length }} locations found</span>
                {% endif %}
            </div>
            <div class="card-body">
                {% if locations %}
                <div class="search-box mb-3">
                    <i class="fas fa-filter"></i>
                    <input type="text" class="form-control" id="filterLocations" placeholder="Filter results...">
                </div>

                <div class="target-list">
                    {% for location in locations %}
                    <div class="location-item">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-map-marker-alt text-danger me-2"></i>
                                    <div>
                                        <strong>{{ location.city }}, {{ location.country }}</strong>
                                        <div><small class="text-muted">ID: {{ location.location_id }}</small></div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <a href="{% url 'campaigns:add_location_to_target' campaign.id %}?location_id={{ location.location_id }}"
                                   class="btn btn-sm btn-success btn-icon">
                                   <i class="fas fa-plus-circle me-1"></i> Add
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% elif search_term %}
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <div class="empty-state-text">No locations found matching "{{ search_term }}".</div>
                    <p>Try a different search term or a broader location name.</p>
                </div>
                {% else %}
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-globe"></i>
                    </div>
                    <div class="empty-state-text">Enter a search term to find locations.</div>
                    <p>You can search by country name, city name, or region.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter functionality for locations
    const filterInput = document.getElementById('filterLocations');
    if (filterInput) {
        filterInput.addEventListener('keyup', function() {
            const filterTerm = this.value.toLowerCase();
            const locationItems = document.querySelectorAll('.location-item');

            locationItems.forEach(item => {
                const locationText = item.textContent.toLowerCase();
                if (locationText.includes(filterTerm)) {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        });
    }
});
</script>
{% endblock %}
