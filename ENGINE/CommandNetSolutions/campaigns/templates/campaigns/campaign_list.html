{% extends "campaigns/base.html" %}

{% block title %}Campaigns{% endblock %}

{% block campaign_content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">Campaigns</h1>
            <p class="page-subtitle">Manage your data collection campaigns</p>
        </div>
        <div>
            <a href="{% url 'campaigns:campaign_create' %}" class="btn btn-primary">
                <i class="fas fa-plus-circle me-1"></i> Create Campaign
            </a>
        </div>
    </div>

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>All Campaigns</h5>
            <div class="d-flex">
                <div class="search-box me-3">
                    <i class="fas fa-search"></i>
                    <input type="text" style="margin-bottom: 0rem;" class="form-control" id="searchCampaigns" placeholder="Search campaigns...">
                    {% if request.GET.search %}
                    <button type="button" class="clear-search" title="Clear search">
                        <i class="fas fa-times" aria-hidden="true"></i>
                    </button>
                    {% endif %}
                </div>
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-filter me-1"></i> Filter
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="filterDropdown">
                        <li><h6 class="dropdown-header">Status</h6></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:campaign_list' %}">All Statuses</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:campaign_list' %}?status=draft">Draft</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:campaign_list' %}?status=pending">Pending</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:campaign_list' %}?status=running">Running</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:campaign_list' %}?status=completed">Completed</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:campaign_list' %}?status=failed">Failed</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><h6 class="dropdown-header">Target Type</h6></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:campaign_list' %}">All Types</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:campaign_list' %}?target_type=location">Location Based</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:campaign_list' %}?target_type=username">Username Based</a></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:campaign_list' %}?target_type=mixed">Mixed (Both Types)</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><h6 class="dropdown-header">Favorites</h6></li>
                        <li><a class="dropdown-item" href="{% url 'campaigns:campaign_list' %}?is_favorite=true">Show Favorites</a></li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if campaigns %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 30px;"></th>
                            <th>Campaign</th>
                            <th>Target Type</th>
                            <th>Status</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for campaign in campaigns %}
                        <tr>
                            <td class="text-center">
                                {% if campaign.is_favorite and campaign.status != 'draft' %}
                                <i class="fas fa-star text-warning" title="Favorite Campaign"></i>
                                {% endif %}
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div>
                                        <div class="fw-bold">{{ campaign.name }}</div>
                                        <div class="small text-muted">{{ campaign.description|truncatechars:50 }}</div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% with has_location=campaign.location_targets.exists has_username=campaign.username_targets.exists %}
                                {% if has_location and has_username %}
                                    <span class="badge bg-light text-dark">
                                        Mixed (Both Types)
                                    </span>
                                {% elif has_location %}
                                    <span class="badge bg-light text-dark">
                                        Location Based
                                    </span>
                                {% elif has_username %}
                                    <span class="badge bg-light text-dark">
                                        Username Based
                                    </span>
                                {% else %}
                                    <span class="badge bg-light text-dark">
                                        {{ campaign.get_target_type_display }}
                                    </span>
                                {% endif %}
                                {% endwith %}
                            </td>

                            <td>
                                <span class="campaign-status status-{{ campaign.status }}">
                                    {{ campaign.get_status_display }}
                                </span>
                            </td>
                            <td>{{ campaign.created_at|date:"M d, Y" }}</td>
                            <td>
                                <div class="d-flex justify-content-end">
                                    <div class="btn-group action-buttons">
                                        {% if campaign.status == 'running' %}
                                        <a href="#" class="btn btn-sm btn-warning campaign-action-btn" data-action="pause" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}" data-bs-toggle="tooltip" title="Pause Campaign">
                                            <i class="fas fa-pause"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-danger campaign-action-btn" data-action="stop" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}" data-bs-toggle="tooltip" title="Stop Campaign">
                                            <i class="fas fa-stop"></i>
                                        </a>
                                        {% endif %}

                                        {% if campaign.status == 'paused' %}
                                        <a href="#" class="btn btn-sm btn-success campaign-action-btn" data-action="resume" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}" data-bs-toggle="tooltip" title="Resume Campaign">
                                            <i class="fas fa-play"></i>
                                        </a>
                                        <a href="#" class="btn btn-sm btn-danger campaign-action-btn" data-action="stop" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}" data-bs-toggle="tooltip" title="Stop Campaign">
                                            <i class="fas fa-stop"></i>
                                        </a>
                                        {% endif %}

                                        {% if campaign.status == 'pending' %}
                                        <a href="#" class="btn btn-sm btn-danger campaign-action-btn" data-action="stop" data-campaign-id="{{ campaign.id }}" data-campaign-name="{{ campaign.name }}" data-bs-toggle="tooltip" title="Stop Campaign">
                                            <i class="fas fa-stop"></i>
                                        </a>
                                        {% endif %}
                                        {% if campaign.status == 'draft' %}
                                        <a href="{% url 'campaigns:campaign_update' campaign.id %}" class="btn btn-sm btn-secondary" data-bs-toggle="tooltip" title="Edit Campaign">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="{% url 'campaigns:launch_campaign' campaign.id %}" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Launch Campaign">
                                            <i class="fas fa-rocket"></i>
                                        </a>
                                        {% endif %}
                                        {% if campaign.status != 'draft' %}
                                        <a href="{% url 'campaigns:toggle_favorite' campaign.id %}?next={{ request.get_full_path|urlencode }}" class="btn btn-sm {% if campaign.is_favorite %}btn-warning{% else %}btn-outline-warning{% endif %}" data-bs-toggle="tooltip" title="{% if campaign.is_favorite %}Remove from Favorites{% else %}Add to Favorites{% endif %}">
                                            <i class="fas fa-star"></i>
                                        </a>
                                        {% endif %}
                                        <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-sm btn-primary" data-bs-toggle="tooltip" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{% url 'campaigns:campaign_confirm_delete' campaign.id %}" class="btn btn-sm btn-danger" data-bs-toggle="tooltip" title="Delete Campaign">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% for key, value in current_filters.items %}&{{ key }}={{ value }}{% endfor %}" aria-label="First">
                            <i class="fas fa-angle-double-left"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in current_filters.items %}&{{ key }}={{ value }}{% endfor %}" aria-label="Previous">
                            <i class="fas fa-angle-left"></i>
                        </a>
                    </li>
                    {% endif %}

                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}{% for key, value in current_filters.items %}&{{ key }}={{ value }}{% endfor %}">{{ num }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in current_filters.items %}&{{ key }}={{ value }}{% endfor %}" aria-label="Next">
                            <i class="fas fa-angle-right"></i>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in current_filters.items %}&{{ key }}={{ value }}{% endfor %}" aria-label="Last">
                            <i class="fas fa-angle-double-right"></i>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}

            {% else %}
            <div class="empty-state">
                <div class="empty-state-icon">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <h4>No Campaigns Found</h4>
                <p>You haven't created any campaigns yet.</p>
                <a href="{% url 'campaigns:campaign_create' %}" class="btn btn-primary">
                    <i class="fas fa-plus-circle me-1"></i> Create Your First Campaign
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Search functionality
    const searchInput = document.getElementById('searchCampaigns');
    if (searchInput) {
        // Set initial value from URL if present
        const urlParams = new URLSearchParams(window.location.search);
        const searchParam = urlParams.get('search');
        if (searchParam) {
            searchInput.value = searchParam;
        }

        // Handle search input
        searchInput.addEventListener('keyup', function(e) {
            // If Enter key is pressed, submit the search
            if (e.key === 'Enter') {
                const searchTerm = this.value.trim();
                if (searchTerm) {
                    // Preserve existing filters
                    const currentParams = new URLSearchParams(window.location.search);
                    currentParams.set('search', searchTerm);
                    currentParams.delete('page'); // Reset to first page

                    // Redirect to the search URL
                    window.location.href = `?${currentParams.toString()}`;
                }
            }
        });

        // Add clear search button functionality
        const searchBox = searchInput.closest('.search-box');
        const clearButton = searchBox.querySelector('.clear-search');

        if (searchBox && clearButton) {
            // Add click handler to the existing button
            clearButton.addEventListener('click', function() {
                searchInput.value = '';

                // Remove search param and redirect
                const currentParams = new URLSearchParams(window.location.search);
                currentParams.delete('search');
                window.location.href = currentParams.toString() ? `?${currentParams.toString()}` : window.location.pathname;
            });
        }

        // Show/hide clear button based on input if it doesn't exist yet
        if (searchBox && !clearButton) {
            searchInput.addEventListener('input', function() {
                let existingButton = searchBox.querySelector('.clear-search');

                if (this.value && !existingButton) {
                    // Create button if it doesn't exist
                    const newClearButton = document.createElement('button');
                    newClearButton.className = 'clear-search';
                    newClearButton.innerHTML = '<i class="fas fa-times" aria-hidden="true"></i>';
                    newClearButton.title = 'Clear search';

                    // Add click handler
                    newClearButton.addEventListener('click', function() {
                        searchInput.value = '';

                        // Remove search param and redirect
                        const currentParams = new URLSearchParams(window.location.search);
                        currentParams.delete('search');
                        window.location.href = currentParams.toString() ? `?${currentParams.toString()}` : window.location.pathname;
                    });

                    // Append the button
                    searchBox.appendChild(newClearButton);
                } else if (!this.value && existingButton) {
                    // Remove button if input is empty
                    existingButton.remove();
                }
            });
        }
    }

    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Campaign action buttons (pause, resume, stop)
    const actionButtons = document.querySelectorAll('.campaign-action-btn');
    actionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            const action = this.dataset.action;
            const campaignId = this.dataset.campaignId;
            const campaignName = this.dataset.campaignName;

            let actionText = '';
            let actionUrl = '';

            switch(action) {
                case 'pause':
                    actionText = 'pause';
                    actionUrl = `/campaigns/${campaignId}/pause/`;
                    break;
                case 'resume':
                    actionText = 'resume';
                    actionUrl = `/campaigns/${campaignId}/resume/`;
                    break;
                case 'stop':
                    actionText = 'stop';
                    actionUrl = `/campaigns/${campaignId}/stop/`;
                    break;
                default:
                    return;
            }

            if (confirm(`Are you sure you want to ${actionText} the campaign "${campaignName}"?`)) {
                window.location.href = actionUrl;
            }
        });
    });
});
</script>
{% endblock %}
