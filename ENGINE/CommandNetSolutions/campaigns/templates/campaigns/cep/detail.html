{% extends "campaigns/base.html" %}
{% load static %}

{% block title %}CEP Workflow Details{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'campaigns/css/dashboard.css' %}">
{% endblock %}

{% block campaign_content %}
<div class="container-fluid dashboard-container">
    <div class="dashboard-header d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="dashboard-title">CEP Workflow Details</h1>
            <p class="text-muted mt-2 mb-0">
                <i class="fas fa-info-circle me-2"></i> Detailed information about this Customer Engagement Process workflow
            </p>
        </div>
        <a href="{% url 'campaigns:cep_dashboard' %}" class="btn btn-secondary btn-action shadow-sm">
            <i class="fas fa-arrow-left me-2"></i> Back to Dashboard
        </a>
    </div>

    {% if messages %}
    <div class="row mb-4">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show shadow-sm" role="alert">
                <div class="d-flex align-items-center">
                    {% if message.tags == 'success' %}
                    <i class="fas fa-check-circle fa-lg me-3"></i>
                    {% elif message.tags == 'error' or message.tags == 'danger' %}
                    <i class="fas fa-exclamation-circle fa-lg me-3"></i>
                    {% elif message.tags == 'warning' %}
                    <i class="fas fa-exclamation-triangle fa-lg me-3"></i>
                    {% else %}
                    <i class="fas fa-info-circle fa-lg me-3"></i>
                    {% endif %}
                    <div>{{ message }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <div class="row">
        <div class="col-xl-12 col-lg-12">
            <div class="card resource-card shadow mb-4">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-2"></i>
                        <h5 class="m-0 text-white">Workflow Information</h5>
                    </div>
                    <div class="action-buttons">
                        {% if workflow.status == 'pending' %}
                        <form method="post" action="{% url 'campaigns:cep_start' pk=workflow.id %}" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-success btn-action shadow-sm">
                                <i class="fas fa-play me-2"></i> Start
                            </button>
                        </form>
                        {% elif workflow.status == 'running' %}
                        <form method="post" action="{% url 'campaigns:cep_pause' pk=workflow.id %}" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-warning btn-action shadow-sm">
                                <i class="fas fa-pause me-2"></i> Pause
                            </button>
                        </form>
                        {% elif workflow.status == 'paused' %}
                        <form method="post" action="{% url 'campaigns:cep_resume' pk=workflow.id %}" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-success btn-action shadow-sm">
                                <i class="fas fa-play me-2"></i> Resume
                            </button>
                        </form>
                        {% endif %}

                        {% if workflow.status in 'pending,running,paused' %}
                        <form method="post" action="{% url 'campaigns:cep_stop' pk=workflow.id %}" class="d-inline ms-2">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-danger btn-action shadow-sm" onclick="return confirm('Are you sure you want to stop this workflow?');">
                                <i class="fas fa-stop me-2"></i> Stop
                            </button>
                        </form>
                        {% endif %}
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="fw-bold text-primary mb-3">
                                <i class="fas fa-project-diagram me-2"></i> Campaign: {{ campaign.name }}
                            </h5>
                            <div class="mb-4">
                                <p class="mb-2">
                                    <strong><i class="fas fa-info-circle me-2"></i>Status:</strong>
                                    <span class="badge bg-{% if workflow.status == 'running' %}success{% elif workflow.status == 'paused' %}warning{% elif workflow.status == 'completed' %}success{% elif workflow.status == 'failed' %}danger{% else %}info{% endif %} rounded-pill px-3 py-2">
                                        {{ workflow.get_status_display }}
                                    </span>
                                </p>
                                <p class="mb-2">
                                    <strong><i class="fas fa-crown me-2"></i>Subscription Tier:</strong>
                                    <span class="badge bg-{% if workflow.subscription_tier == 'bronze' %}warning text-dark{% elif workflow.subscription_tier == 'silver' %}secondary{% else %}warning text-dark{% endif %} rounded-pill px-3 py-2"
                                          {% if workflow.subscription_tier == 'gold' %}style="background-color: #FFD700 !important;"{% endif %}>
                                        {{ workflow.get_subscription_tier_display }}
                                    </span>
                                </p>
                                <p class="mb-2">
                                    <strong><i class="fas fa-list me-2"></i>Whitelist Count:</strong>
                                    <span class="badge bg-primary rounded-pill px-3 py-2">{{ workflow.whitelist_count }}</span>
                                </p>
                                <p class="mb-2">
                                    <strong><i class="fas fa-calendar-plus me-2"></i>Created:</strong>
                                    {{ workflow.created_at }}
                                </p>
                                {% if workflow.started_at %}
                                <p class="mb-2">
                                    <strong><i class="fas fa-play-circle me-2"></i>Started:</strong>
                                    {{ workflow.started_at }}
                                </p>
                                {% endif %}
                                {% if workflow.completed_at %}
                                <p class="mb-2">
                                    <strong><i class="fas fa-flag-checkered me-2"></i>Completed:</strong>
                                    {{ workflow.completed_at }}
                                </p>
                                {% endif %}
                            </div>

                            <div class="card shadow-sm mb-4">
                                <div class="card-header bg-info">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-layer-group me-2"></i>
                                        <h5 class="m-0 text-white">Tier Information</h5>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <p><strong>{{ tier_info.label }}:</strong> {{ tier_info.description }}</p>
                                    <p class="mb-2"><strong>Available Actions:</strong></p>
                                    <ul class="list-group">
                                        {% for action in tier_info.actions %}
                                        <li class="list-group-item d-flex align-items-center">
                                            {% if action == 'follow' %}
                                            <i class="fas fa-user-plus me-2 text-primary"></i>
                                            {% elif action == 'like' %}
                                            <i class="fas fa-heart me-2 text-danger"></i>
                                            {% elif action == 'comment' %}
                                            <i class="fas fa-comment me-2 text-success"></i>
                                            {% elif action == 'dm' %}
                                            <i class="fas fa-envelope me-2 text-warning"></i>
                                            {% else %}
                                            <i class="fas fa-check-circle me-2 text-info"></i>
                                            {% endif %}
                                            {{ action|title }}
                                        </li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="fw-bold text-primary mb-3">
                                <i class="fas fa-chart-line me-2"></i> Progress
                            </h5>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span><i class="fas fa-user-plus me-1"></i> Follow</span>
                                    <span>{{ workflow.follow_progress|floatformat:1 }}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ workflow.follow_progress }}%" aria-valuenow="{{ workflow.follow_progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span><i class="fas fa-heart me-1"></i> Like</span>
                                    <span>{{ workflow.like_progress|floatformat:1 }}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ workflow.like_progress }}%" aria-valuenow="{{ workflow.like_progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            {% if workflow.subscription_tier != 'bronze' %}
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span><i class="fas fa-comment me-1"></i> Comment</span>
                                    <span>{{ workflow.comment_progress|floatformat:1 }}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ workflow.comment_progress }}%" aria-valuenow="{{ workflow.comment_progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            {% endif %}
                            {% if workflow.subscription_tier == 'gold' %}
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span><i class="fas fa-envelope me-1"></i> DM</span>
                                    <span>{{ workflow.dm_progress|floatformat:1 }}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ workflow.dm_progress }}%" aria-valuenow="{{ workflow.dm_progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            {% endif %}
                            <div class="mt-4">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="fw-bold"><i class="fas fa-chart-line me-1"></i> Overall Progress</span>
                                    <span class="fw-bold">{{ overall_progress|floatformat:1 }}%</span>
                                </div>
                                <div class="progress" style="height: 15px;">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: {{ overall_progress }}%" aria-valuenow="{{ overall_progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>

                            {% if workflow.status == 'running' %}
                            <div class="alert alert-info mt-4 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle fa-2x me-3"></i>
                                    <div>This workflow is currently running. Progress will update automatically.</div>
                                </div>
                            </div>
                            {% elif workflow.status == 'paused' %}
                            <div class="alert alert-warning mt-4 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-pause-circle fa-2x me-3"></i>
                                    <div>This workflow is currently paused. Resume to continue processing.</div>
                                </div>
                            </div>
                            {% elif workflow.status == 'pending' %}
                            <div class="alert alert-info mt-4 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-clock fa-2x me-3"></i>
                                    <div>This workflow is pending. Click Start to begin processing.</div>
                                </div>
                            </div>
                            {% elif workflow.status == 'completed' %}
                            <div class="alert alert-success mt-4 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-check-circle fa-2x me-3"></i>
                                    <div>This workflow has completed successfully.</div>
                                </div>
                            </div>
                            {% elif workflow.status == 'failed' %}
                            <div class="alert alert-danger mt-4 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-exclamation-circle fa-2x me-3"></i>
                                    <div>This workflow has failed. Please check the logs for details.</div>
                                </div>
                            </div>
                            {% elif workflow.status == 'stopped' %}
                            <div class="alert alert-secondary mt-4 shadow-sm">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-stop-circle fa-2x me-3"></i>
                                    <div>This workflow was stopped manually.</div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-12 col-lg-12">
            <div class="card resource-card shadow mb-4">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-project-diagram me-2"></i>
                        <h5 class="m-0 text-white">Campaign Details</h5>
                    </div>
                    <a href="{% url 'campaigns:campaign_detail' pk=campaign.id %}" class="btn btn-light btn-action shadow-sm">
                        <i class="fas fa-external-link-alt me-2"></i> View Campaign
                    </a>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <h5 class="fw-bold text-primary mb-3">
                                    <i class="fas fa-info-circle me-2"></i> Basic Information
                                </h5>
                                <p class="mb-2">
                                    <strong><i class="fas fa-tag me-2"></i>Name:</strong>
                                    {{ campaign.name }}
                                </p>
                                <p class="mb-2">
                                    <strong><i class="fas fa-info-circle me-2"></i>Status:</strong>
                                    <span class="badge bg-{% if campaign.status == 'active' %}success{% elif campaign.status == 'paused' %}warning{% elif campaign.status == 'completed' %}info{% else %}secondary{% endif %} rounded-pill px-3 py-2">
                                        {{ campaign.get_status_display }}
                                    </span>
                                </p>
                                <p class="mb-2">
                                    <strong><i class="fas fa-bullseye me-2"></i>Target Type:</strong>
                                    {{ campaign.get_target_type_display }}
                                </p>
                                <p class="mb-2">
                                    <strong><i class="fas fa-calendar-alt me-2"></i>Created:</strong>
                                    {{ campaign.created_at }}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-4">
                                <h5 class="fw-bold text-primary mb-3">
                                    <i class="fas fa-cog me-2"></i> Additional Details
                                </h5>
                                <p class="mb-2">
                                    <strong><i class="fas fa-align-left me-2"></i>Description:</strong>
                                    {{ campaign.description|default:"No description" }}
                                </p>
                                <p class="mb-2">
                                    <strong><i class="fas fa-users me-2"></i>Audience Type:</strong>
                                    {{ campaign.get_audience_type_display }}
                                </p>
                                <p class="mb-2">
                                    <strong><i class="fas fa-user me-2"></i>Creator:</strong>
                                    {{ campaign.creator.username|default:"Unknown" }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize Bootstrap tooltips and popovers
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });

        // Auto-refresh workflow status every 10 seconds if running
        {% if workflow.status == 'running' %}
        function updateWorkflowStatus() {
            $.ajax({
                url: "{% url 'campaigns:api_cep_status' pk=workflow.id %}",
                type: "GET",
                dataType: "json",
                success: function(data) {
                    // Update status badge
                    let statusClass = "info";
                    if (data.status === "running") statusClass = "success";
                    if (data.status === "paused") statusClass = "warning";
                    if (data.status === "failed") statusClass = "danger";
                    if (data.status === "completed") statusClass = "success";
                    if (data.status === "stopped") statusClass = "secondary";

                    // Update progress bars
                    $(".progress-bar").eq(0).css("width", data.follow_progress + "%").attr("aria-valuenow", data.follow_progress);
                    $(".progress-bar").eq(1).css("width", data.like_progress + "%").attr("aria-valuenow", data.like_progress);
                    {% if workflow.subscription_tier != 'bronze' %}
                    $(".progress-bar").eq(2).css("width", data.comment_progress + "%").attr("aria-valuenow", data.comment_progress);
                    {% endif %}
                    {% if workflow.subscription_tier == 'gold' %}
                    $(".progress-bar").eq(3).css("width", data.dm_progress + "%").attr("aria-valuenow", data.dm_progress);
                    {% endif %}

                    // Update overall progress
                    $(".progress-bar.bg-primary").css("width", data.overall_progress + "%").attr("aria-valuenow", data.overall_progress);

                    // If status changed, refresh the page
                    if (data.status !== "running") {
                        location.reload();
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error updating workflow status:", error);
                }
            });
        }

        // Update status immediately and then every 10 seconds
        updateWorkflowStatus();
        setInterval(updateWorkflowStatus, 10000);
        {% endif %}
    });
</script>
{% endblock %}
