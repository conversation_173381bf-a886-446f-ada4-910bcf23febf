{% extends "campaigns/base.html" %}
{% load static %}

{% block title %}CEP Dashboard{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'campaigns/css/dashboard.css' %}">
{% endblock %}

{% block campaign_content %}
<div class="container-fluid dashboard-container">
    <div class="dashboard-header d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="dashboard-title">CEP Dashboard</h1>
            <p class="text-muted mt-2 mb-0">
                <i class="fas fa-info-circle me-2"></i> Customer Engagement Process management
            </p>
        </div>
        {% if not active_workflow %}
        <a href="{% url 'campaigns:cep_create' %}" class="btn btn-primary btn-action shadow-sm">
            <i class="fas fa-plus me-2"></i> Create CEP Workflow
        </a>
        {% endif %}
    </div>

    {% if messages %}
    <div class="row mb-4">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show shadow-sm" role="alert">
                <div class="d-flex align-items-center">
                    {% if message.tags == 'success' %}
                    <i class="fas fa-check-circle fa-lg me-3"></i>
                    {% elif message.tags == 'error' or message.tags == 'danger' %}
                    <i class="fas fa-exclamation-circle fa-lg me-3"></i>
                    {% elif message.tags == 'warning' %}
                    <i class="fas fa-exclamation-triangle fa-lg me-3"></i>
                    {% else %}
                    <i class="fas fa-info-circle fa-lg me-3"></i>
                    {% endif %}
                    <div>{{ message }}</div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <div class="row">
        <div class="col-xl-12 col-lg-12">
            <div class="card resource-card shadow mb-4">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-tasks me-2"></i>
                        <h5 class="m-0 text-white">Active CEP Workflow</h5>
                    </div>
                </div>
                <div class="card-body p-4">
                    {% if active_workflow %}
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="fw-bold text-primary mb-3">{{ active_workflow.campaign.name }}</h5>
                            <div class="mb-3">
                                <p class="mb-2">
                                    <strong><i class="fas fa-info-circle me-2"></i>Status:</strong>
                                    <span class="badge bg-{% if active_workflow.status == 'running' %}success{% elif active_workflow.status == 'paused' %}warning{% else %}info{% endif %} rounded-pill px-3 py-2">
                                        {{ active_workflow.get_status_display }}
                                    </span>
                                </p>
                                <p class="mb-2">
                                    <strong><i class="fas fa-crown me-2"></i>Subscription Tier:</strong>
                                    <span class="badge bg-{% if active_workflow.subscription_tier == 'bronze' %}warning{% elif active_workflow.subscription_tier == 'silver' %}secondary{% else %}warning text-dark{% endif %} rounded-pill px-3 py-2"
                                          {% if active_workflow.subscription_tier == 'gold' %}style="background-color: #FFD700 !important;"{% endif %}>
                                        {{ active_workflow.get_subscription_tier_display }}
                                    </span>
                                </p>
                                <p class="mb-2">
                                    <strong><i class="fas fa-list me-2"></i>Whitelist Count:</strong>
                                    <span class="badge bg-primary rounded-pill px-3 py-2">{{ active_workflow.whitelist_count }}</span>
                                </p>
                                {% if active_workflow.started_at %}
                                <p class="mb-2">
                                    <strong><i class="fas fa-calendar-alt me-2"></i>Started:</strong>
                                    {{ active_workflow.started_at }}
                                </p>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="fw-bold text-primary mb-3">Progress</h5>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span><i class="fas fa-user-plus me-1"></i> Follow</span>
                                    <span>{{ active_workflow.follow_progress|floatformat:1 }}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ active_workflow.follow_progress }}%" aria-valuenow="{{ active_workflow.follow_progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span><i class="fas fa-heart me-1"></i> Like</span>
                                    <span>{{ active_workflow.like_progress|floatformat:1 }}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ active_workflow.like_progress }}%" aria-valuenow="{{ active_workflow.like_progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            {% if active_workflow.subscription_tier != 'bronze' %}
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span><i class="fas fa-comment me-1"></i> Comment</span>
                                    <span>{{ active_workflow.comment_progress|floatformat:1 }}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ active_workflow.comment_progress }}%" aria-valuenow="{{ active_workflow.comment_progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            {% endif %}
                            {% if active_workflow.subscription_tier == 'gold' %}
                            <div class="mb-3">
                                <div class="d-flex justify-content-between mb-1">
                                    <span><i class="fas fa-envelope me-1"></i> DM</span>
                                    <span>{{ active_workflow.dm_progress|floatformat:1 }}%</span>
                                </div>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: {{ active_workflow.dm_progress }}%" aria-valuenow="{{ active_workflow.dm_progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                            {% endif %}
                            <div class="mt-4">
                                <div class="d-flex justify-content-between mb-1">
                                    <span class="fw-bold"><i class="fas fa-chart-line me-1"></i> Overall Progress</span>
                                    <span class="fw-bold">{{ active_workflow.overall_progress|floatformat:1 }}%</span>
                                </div>
                                <div class="progress" style="height: 15px;">
                                    <div class="progress-bar bg-primary" role="progressbar" style="width: {{ active_workflow.overall_progress }}%" aria-valuenow="{{ active_workflow.overall_progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            <a href="{% url 'campaigns:cep_detail' pk=active_workflow.id %}" class="btn btn-primary btn-action shadow-sm">
                                <i class="fas fa-eye me-2"></i> View Details
                            </a>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-tasks fa-4x text-muted mb-3"></i>
                        <p class="mb-4">No active CEP workflow.</p>
                        <a href="{% url 'campaigns:cep_create' %}" class="btn btn-primary btn-action shadow-sm">
                            <i class="fas fa-plus me-2"></i> Create CEP Workflow
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-xl-12 col-lg-12">
            <div class="card resource-card shadow mb-4">
                <div class="card-header d-flex align-items-center justify-content-between">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-history me-2"></i>
                        <h5 class="m-0 text-white">Recent CEP Workflows</h5>
                    </div>
                </div>
                <div class="card-body p-4">
                    {% if completed_workflows %}
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover shadow-sm" id="dataTable" width="100%" cellspacing="0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="text-center">Campaign</th>
                                    <th class="text-center">Subscription Tier</th>
                                    <th class="text-center">Status</th>
                                    <th class="text-center">Started</th>
                                    <th class="text-center">Completed</th>
                                    <th class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for workflow in completed_workflows %}
                                <tr>
                                    <td class="align-middle">
                                        <strong>{{ workflow.campaign.name }}</strong>
                                    </td>
                                    <td class="text-center align-middle">
                                        {% if workflow.subscription_tier == 'bronze' %}
                                            <span class="badge bg-warning text-dark rounded-pill px-3 py-2">{{ workflow.get_subscription_tier_display }}</span>
                                        {% elif workflow.subscription_tier == 'silver' %}
                                            <span class="badge bg-secondary text-white rounded-pill px-3 py-2">{{ workflow.get_subscription_tier_display }}</span>
                                        {% elif workflow.subscription_tier == 'gold' %}
                                            <span class="badge rounded-pill px-3 py-2" style="background-color: #FFD700; color: #212529;">{{ workflow.get_subscription_tier_display }}</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center align-middle">
                                        <span class="badge bg-{% if workflow.status == 'completed' %}success{% elif workflow.status == 'failed' %}danger{% else %}secondary{% endif %} rounded-pill px-3 py-2">
                                            {{ workflow.get_status_display }}
                                        </span>
                                    </td>
                                    <td class="text-center align-middle">
                                        {% if workflow.started_at %}
                                            <i class="fas fa-calendar-alt me-1 text-muted"></i> {{ workflow.started_at }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-center align-middle">
                                        {% if workflow.completed_at %}
                                            <i class="fas fa-flag-checkered me-1 text-muted"></i> {{ workflow.completed_at }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-center align-middle">
                                        <a href="{% url 'campaigns:cep_detail' pk=workflow.id %}" class="btn btn-primary btn-sm btn-action shadow-sm">
                                            <i class="fas fa-eye me-1"></i> View Details
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-history fa-4x text-muted mb-3"></i>
                        <p>No completed CEP workflows.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // Initialize Bootstrap tooltips and popovers
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });

        // Auto-refresh active workflow status every 10 seconds
        {% if active_workflow %}
        function updateWorkflowStatus() {
            $.ajax({
                url: "{% url 'campaigns:api_cep_status' pk=active_workflow.id %}",
                type: "GET",
                dataType: "json",
                success: function(data) {
                    // Update status badge
                    let statusClass = "info";
                    if (data.status === "running") statusClass = "success";
                    if (data.status === "paused") statusClass = "warning";
                    if (data.status === "failed") statusClass = "danger";
                    if (data.status === "completed") statusClass = "success";
                    if (data.status === "stopped") statusClass = "secondary";

                    // Update progress bars
                    $(".progress-bar").eq(0).css("width", data.follow_progress + "%").attr("aria-valuenow", data.follow_progress);
                    $(".progress-bar").eq(1).css("width", data.like_progress + "%").attr("aria-valuenow", data.like_progress);
                    {% if active_workflow.subscription_tier != 'bronze' %}
                    $(".progress-bar").eq(2).css("width", data.comment_progress + "%").attr("aria-valuenow", data.comment_progress);
                    {% endif %}
                    {% if active_workflow.subscription_tier == 'gold' %}
                    $(".progress-bar").eq(3).css("width", data.dm_progress + "%").attr("aria-valuenow", data.dm_progress);
                    {% endif %}

                    // Update overall progress
                    $(".progress-bar.bg-primary").css("width", data.overall_progress + "%").attr("aria-valuenow", data.overall_progress);

                    // If status changed to completed, refresh the page
                    if (data.status === "completed" || data.status === "failed" || data.status === "stopped") {
                        location.reload();
                    }
                },
                error: function(xhr, status, error) {
                    console.error("Error updating workflow status:", error);
                }
            });
        }

        // Update status immediately and then every 10 seconds
        updateWorkflowStatus();
        setInterval(updateWorkflowStatus, 10000);
        {% endif %}
    });
</script>
{% endblock %}
