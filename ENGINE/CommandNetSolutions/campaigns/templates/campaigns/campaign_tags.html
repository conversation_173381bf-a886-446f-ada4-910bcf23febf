{% extends "campaigns/base.html" %}
{% load static %}

{% block title %}Campaign Tags - {{ campaign.name }}{% endblock %}

{% block extra_css %}
<style>
.tag-option:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

.tag-group-option:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}

.color-indicator {
    display: inline-block;
    border: 1px solid #dee2e6;
}

#tag-search-results::-webkit-scrollbar,
#tag-groups-list::-webkit-scrollbar {
    width: 6px;
}

#tag-search-results::-webkit-scrollbar-track,
#tag-groups-list::-webkit-scrollbar-track {
    background: #f1f1f1;
}

#tag-search-results::-webkit-scrollbar-thumb,
#tag-groups-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#tag-search-results::-webkit-scrollbar-thumb:hover,
#tag-groups-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
</style>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/htmx.org@1.9.10"></script>
<script src="{% static 'campaigns/js/campaign_tags.js' %}"></script>
{% endblock %}

{% block campaign_content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">Campaign Tags</h1>
            <p class="page-subtitle">Manage tags for campaign: {{ campaign.name }}</p>
        </div>
        <div>
            <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back
            </a>
        </div>
    </div>

    <!-- About Tags Information Card - Moved to Top -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-light">
                <div class="card-header bg-light text-dark border-bottom">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2 text-muted"></i>About Tags</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6><i class="fas fa-tag me-1"></i> Individual Tags</h6>
                            <p class="text-muted">Tags help you categorize accounts based on specific criteria. When you analyze accounts, they will be tagged based on these criteria.</p>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-layer-group me-1"></i> Tag Groups</h6>
                            <p class="text-muted">Tag groups allow you to assign multiple related tags at once, making it easier to apply consistent tagging strategies.</p>
                        </div>
                        <div class="col-md-4">
                            <h6><i class="fas fa-check-circle me-1"></i> Required Tags</h6>
                            <p class="text-muted">Accounts that match required tags will be added to the campaign's whitelist. Optional tags are used for additional categorization.</p>
                        </div>
                    </div>
                    <hr>
                    <p class="mb-0">
                        <strong>Need more tags?</strong>
                        <a href="{% url 'campaigns:dynamic_tag_list' %}" class="btn btn-sm btn-outline-primary ms-2">
                            <i class="fas fa-plus me-1"></i> Manage Tags
                        </a>
                        <a href="{% url 'campaigns:tag_group_list' %}" class="btn btn-sm btn-outline-secondary ms-2">
                            <i class="fas fa-layer-group me-1"></i> Manage Tag Groups
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Tag Assignment Cards - Positioned Above Assigned Tags -->
    <div class="row mb-4">
        <!-- Individual Tag Assignment -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-plus-circle me-2"></i>Assign Individual Tag</h5>
                </div>
                <div class="card-body">
                    <!-- Search and Filter Controls -->
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-8">
                                <input type="text"
                                       id="tag-search"
                                       class="form-control"
                                       placeholder="Search tags by name or description..."
                                       hx-get="{% url 'campaigns:htmx_tag_search' campaign.id %}"
                                       hx-trigger="keyup changed delay:300ms"
                                       hx-target="#tag-search-results"
                                       hx-include="#category-filter"
                                       name="search">
                            </div>
                            <div class="col-md-4">
                                <select id="category-filter"
                                        class="form-select"
                                        hx-get="{% url 'campaigns:htmx_tag_search' campaign.id %}"
                                        hx-trigger="change"
                                        hx-target="#tag-search-results"
                                        hx-include="#tag-search"
                                        name="category">
                                    <option value="">All Categories</option>
                                    <!-- Categories will be loaded via JavaScript -->
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- Tag Search Results -->
                    <div id="tag-search-results"
                         class="border rounded"
                         style="max-height: 300px; overflow-y: auto;">
                        <div class="text-center text-muted p-4">
                            <i class="fas fa-search fa-2x mb-2"></i>
                            <p>Start typing to search for tags...</p>
                        </div>
                    </div>

                    <!-- Selected Tag Form -->
                    <div id="selected-tag-form" style="display: none;">
                        <hr>
                        <form id="assign-tag-form" action="{% url 'campaigns:create_campaign_tag' campaign.id %}" method="post">
                            {% csrf_token %}
                            <input type="hidden" id="selected-tag-id" name="tag" value="">

                            <div class="mb-3">
                                <label class="form-label">Selected Tag</label>
                                <div id="selected-tag-display" class="form-control-plaintext"></div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox"
                                       id="tag-is-required"
                                       name="is_required"
                                       class="form-check-input">
                                <label for="tag-is-required" class="form-check-label">Required for Whitelist</label>
                                <div class="form-text">If checked, accounts must match this tag to be included in the whitelist.</div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="button" id="cancel-tag-selection" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i> Cancel
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus-circle me-1"></i> Assign Tag
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tag Group Assignment -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-layer-group me-2"></i>Assign Tag Group</h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Assign all tags from a tag group at once.</p>

                    <!-- Tag Group Search -->
                    <div class="mb-3">
                        <input type="text"
                               id="tag-group-search"
                               class="form-control"
                               placeholder="Search tag groups by name or description..."
                               hx-get="{% url 'campaigns:htmx_tag_groups' campaign.id %}"
                               hx-trigger="keyup changed delay:300ms"
                               hx-target="#tag-groups-list"
                               hx-include="this"
                               name="tag-group-search">
                    </div>

                    <!-- Tag Groups List -->
                    <div id="tag-groups-list"
                         class="border rounded"
                         style="max-height: 300px; overflow-y: auto;"
                         hx-get="{% url 'campaigns:htmx_tag_groups' campaign.id %}"
                         hx-trigger="load">
                        <div class="text-center text-muted p-4">
                            <i class="fas fa-spinner fa-spin fa-2x mb-2"></i>
                            <p>Loading tag groups...</p>
                        </div>
                    </div>

                    <!-- Selected Tag Group Form -->
                    <div id="selected-tag-group-form" style="display: none;">
                        <hr>
                        <form id="assign-tag-group-form"
                              hx-post="{% url 'campaigns:htmx_assign_tag_group' campaign.id %}"
                              hx-target="#assigned-tags-container">
                            {% csrf_token %}
                            <input type="hidden" id="selected-tag-group-id" name="tag_group_id" value="">

                            <div class="mb-3">
                                <label class="form-label">Selected Tag Group</label>
                                <div id="selected-tag-group-display" class="form-control-plaintext"></div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox"
                                       id="tag-group-is-required"
                                       name="is_required"
                                       class="form-check-input">
                                <label for="tag-group-is-required" class="form-check-label">All tags required for Whitelist</label>
                                <div class="form-text">If checked, all tags from this group will be marked as required.</div>
                            </div>

                            <div class="d-flex justify-content-between">
                                <button type="button" id="cancel-tag-group-selection" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-1"></i> Cancel
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-layer-group me-1"></i> Assign Group
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Assigned Tags Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-tags me-2"></i>Assigned Tags</h5>
                    <div class="text-muted small">
                        <i class="fas fa-info-circle me-1"></i>
                        Changes are saved automatically
                    </div>
                </div>
                <div class="card-body" id="assigned-tags-container"
                     hx-on:htmx:after-swap="if (typeof initializeEventListeners === 'function') { initializeEventListeners(); updateBulkActions(); }">
                    {% include 'campaigns/htmx/assigned_tags_list.html' %}
                </div>
            </div>
        </div>
    </div>


</div>
{% endblock %}
