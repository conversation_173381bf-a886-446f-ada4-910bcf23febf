{% if tag_groups %}
    {% for tag_group in tag_groups %}
    <div class="tag-group-option d-flex justify-content-between align-items-center p-3 border rounded mb-2" 
         style="border-color: {{ tag_group.color }};">
        <div class="tag-group-info">
            <div class="d-flex align-items-center mb-1">
                <div class="color-indicator me-2" 
                     style="width: 12px; height: 12px; background-color: {{ tag_group.color }}; border-radius: 50%;"></div>
                <strong>{{ tag_group.name }}</strong>
                <span class="badge bg-light text-dark ms-2">{{ tag_group.tags.count }} tags</span>
            </div>
            {% if tag_group.description %}
                <div class="text-muted small">{{ tag_group.description|truncatechars:100 }}</div>
            {% endif %}
        </div>
        <button type="button" 
                class="btn btn-sm btn-outline-primary select-tag-group-btn"
                data-tag-group-id="{{ tag_group.id }}"
                data-tag-group-name="{{ tag_group.name }}">
            <i class="fas fa-plus"></i> Assign Group
        </button>
    </div>
    {% endfor %}
{% else %}
    <div class="text-center text-muted p-4">
        <i class="fas fa-layer-group fa-2x mb-2"></i>
        <p>No tag groups available.</p>
        <a href="{% url 'campaigns:tag_group_create' %}" class="btn btn-sm btn-primary">
            <i class="fas fa-plus"></i> Create Tag Group
        </a>
    </div>
{% endif %}
