{% extends "campaigns/base.html" %}

{% block title %}Remove Tag from Campaign{% endblock %}

{% block campaign_content %}
<div class="container">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="page-title">Remove Tag from Campaign</h1>
            <p class="page-subtitle">Remove tag from campaign: {{ campaign.name }}</p>
        </div>
        <div>
            <a href="{% url 'campaigns:campaign_tags' campaign.id %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-1"></i> Back to Tags
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-md-6 mx-auto">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Confirm Removal</h5>
                </div>
                <div class="card-body">
                    <p class="alert alert-warning">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        Are you sure you want to remove the tag <strong>{{ object.tag.name }}</strong> from this campaign?
                    </p>
                    
                    <p>This action will remove the tag from the campaign's tag list. This may affect which accounts are included in the whitelist during analysis.</p>
                    
                    <form method="post">
                        {% csrf_token %}
                        <div class="d-flex justify-content-end">
                            <a href="{% url 'campaigns:campaign_tags' campaign.id %}" class="btn btn-outline-secondary me-2">Cancel</a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i> Remove Tag
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
