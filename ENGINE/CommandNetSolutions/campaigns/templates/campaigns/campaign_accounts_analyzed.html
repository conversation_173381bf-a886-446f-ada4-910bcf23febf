{% extends "campaigns/base.html" %}
{% load campaign_tags %}

{% block title %}Analyzed Accounts - {{ campaign.name }}{% endblock %}

{% block campaign_content %}
<div class="d-flex justify-content-between align-items-start mb-4">
    <div>
        <h1 class="page-title">Analyzed Accounts</h1>
        <p class="page-subtitle">{{ campaign.name }}</p>
    </div>
    <div>
        <a href="{% url 'campaigns:campaign_detail' campaign.id %}" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-1"></i> Back to Campaign
        </a>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="campaign-card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Analyzed Accounts ({{ total_accounts }})
                </h5>
            </div>
            <div class="card-body">
                {% if accounts %}
                <!-- Filter Form -->
                <form method="get" class="mb-4">
                    <div class="row">
                        <div class="col-md-6">
                            {{ filter_form.search }}
                        </div>
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-1"></i> Search
                            </button>
                            <a href="{% url 'campaigns:campaign_accounts_analyzed' campaign.id %}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-1"></i> Clear
                            </a>
                        </div>
                    </div>
                </form>

                <!-- Accounts Table -->
                <div class="table-responsive">
                    <table class="table table-campaigns">
                        <thead>
                            <tr>
                                <th>Username</th>
                                <th>Full Name</th>
                                <th>Bio</th>
                                <th>Interests</th>
                                <th>Locations</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for account in accounts %}
                            <tr>
                                <td>
                                    <div>
                                        <div class="d-flex align-items-center mb-1">
                                            {% if account.is_verified %}
                                            <i class="fas fa-check-circle text-primary me-2" title="Verified Account"></i>
                                            {% endif %}
                                            <a href="https://www.instagram.com/{{ account.username }}/" target="_blank" class="text-decoration-none">
                                                <strong>@{{ account.username }}</strong>
                                            </a>
                                        </div>
                                        <small class="text-muted">
                                            {{ account.followers|floatformat:0 }} followers • {{ account.following|floatformat:0 }} following • {{ account.number_of_posts|floatformat:0 }} posts
                                        </small>
                                    </div>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ account.full_name|default:"-" }}</strong>
                                        {% if account.account_type %}
                                        <br><small class="text-muted">{{ account.account_type|title }}</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td style="max-width: 300px;">
                                    <div class="text-wrap">{{ account.bio|default:"-" }}</div>
                                </td>
                                <td>
                                    {% if account.interests %}
                                    <div class="interests-tags">
                                        {% for interest in account.interests %}
                                        <span class="badge bg-light text-dark me-1">{{ interest }}</span>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if account.locations %}
                                    <div class="locations-tags">
                                        {% for location in account.locations %}
                                        <span class="badge bg-warning text-dark me-1">{{ location }}</span>
                                        {% endfor %}
                                    </div>
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                {% if is_paginated %}
                <nav aria-label="Accounts pagination" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">First</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Previous</a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link">
                                Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                            </span>
                        </li>

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Next</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.search %}&search={{ request.GET.search }}{% endif %}">Last</a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

                {% else %}
                <div class="empty-state">
                    <div class="empty-state-icon">
                        <i class="fas fa-chart-line text-muted"></i>
                    </div>
                    <div class="empty-state-text">No accounts have been analyzed yet.</div>
                    <div class="empty-state-subtext">Run account analysis to see detailed profile information.</div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<style>
.bio-text {
    max-width: 200px;
    word-wrap: break-word;
}

.interests-tags, .locations-tags {
    max-width: 150px;
}

.interests-tags .badge, .locations-tags .badge {
    font-size: 0.7rem;
    margin-bottom: 2px;
}
</style>
{% endblock %}
