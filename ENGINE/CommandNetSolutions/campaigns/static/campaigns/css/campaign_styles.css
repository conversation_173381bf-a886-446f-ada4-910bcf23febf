/* Campaign Pages Common Styles */

/* Typography */
.page-title {
    color: #2c3e50;
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 25px;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 15px;
}

.section-title {
    font-size: 22px;
    font-weight: 600;
    color: #2c3e50;
    margin: 25px 0 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.subsection-title {
    font-size: 18px;
    font-weight: 600;
    color: #34495e;
    margin: 20px 0 15px;
}

.form-label {
    font-weight: 500;
    font-size: 15px;
    color: #34495e;
}

.form-text {
    color: #7f8c8d;
    font-size: 13px;
    margin-top: 5px;
}

/* Navigation */
.back-link {
    display: inline-block;
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
    margin-bottom: 25px;
    font-size: 16px;
    padding: 8px 0;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.back-link:hover {
    color: #2980b9;
    border-bottom: 2px solid #3498db;
    text-decoration: none;
}

.back-link i {
    margin-right: 5px;
}

/* Cards */
.card {
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    border: none;
    margin-bottom: 30px;
    overflow: hidden;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
    padding: 18px 20px;
}

.card-header h5 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
}

.card-body {
    padding: 25px;
}

/* Stats Cards */
.stats-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    padding: 25px 20px;
    text-align: center;
    margin-bottom: 25px;
    border-top: 4px solid #3498db;
    transition: transform 0.2s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
}

.stats-card.primary {
    border-top-color: #3498db;
}

.stats-card.secondary {
    border-top-color: #2ecc71;
}

.stats-title {
    font-size: 16px;
    color: #7f8c8d;
    margin-bottom: 15px;
    font-weight: 500;
}

.stats-value {
    font-size: 36px;
    font-weight: 700;
    color: #2c3e50;
}

/* Button Action Group */
.btn-action-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin: 20px 0;
    justify-content: flex-end;
}

.btn-action-group .btn {
    padding: 8px 16px;
    font-weight: 500;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease;
}

.btn-action-group .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Specific button overrides for campaign pages */
.campaign-page .btn-primary.btn-large {
    min-width: 160px;
    margin: 8px;
}

@media (max-width: 768px) {
    .campaign-page .btn-primary.btn-large {
        min-width: 140px;
        margin: 5px;
        display: block;
        width: 100%;
    }
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: 50px 20px;
    background-color: #f9f9f9;
    border-radius: 10px;
    margin: 20px 0;
}

.empty-state-icon {
    font-size: 60px;
    color: #bdc3c7;
    margin-bottom: 25px;
}

.empty-state h4 {
    color: #2c3e50;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 15px;
}

.empty-state p {
    color: #7f8c8d;
    font-size: 16px;
    margin-bottom: 25px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Form Elements */
.form-control {
    border-radius: 6px;
    border: 1px solid #ddd;
    padding: 10px 15px;
    height: auto;
    font-size: 15px;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-select {
    border-radius: 6px;
    border: 1px solid #ddd;
    padding: 10px 15px;
    height: auto;
    font-size: 15px;
}

.form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-check-input:checked {
    background-color: #3498db;
    border-color: #3498db;
}

/* Spacing */
.section-spacing {
    margin-bottom: 40px;
}

.form-group {
    margin-bottom: 20px;
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 6px 10px;
    margin-right: 5px;
    border-radius: 4px;
    font-size: 13px;
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    color: #34495e;
    border-bottom-width: 2px;
    background-color: #f8f9fa;
    padding: 12px 15px;
}

.table td {
    padding: 12px 15px;
    vertical-align: middle;
}

/* Favorite Icon Column */
.table td.text-center .fa-star {
    font-size: 16px;
    color: #f39c12;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .stats-card {
        margin-bottom: 15px;
    }

    .page-title {
        font-size: 28px;
    }

    .section-title {
        font-size: 20px;
    }
}
