/**
 * CommandNetSolutions Global JavaScript
 * 
 * This file contains global JavaScript functions used across the CommandNetSolutions platform.
 */

// Initialize Bootstrap tooltips and popovers
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Initialize popovers
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

/**
 * Format a date string to a human-readable format
 * @param {string} dateStr - The date string to format
 * @returns {string} - The formatted date string
 */
function formatDateTime(dateStr) {
    if (!dateStr) return '-';
    const date = new Date(dateStr);
    return date.toLocaleString();
}

/**
 * Format seconds to a human-readable time format
 * @param {number} seconds - The number of seconds
 * @returns {string} - The formatted time string
 */
function formatTimeRemaining(seconds) {
    if (seconds < 60) {
        return `${seconds}s`;
    } else if (seconds < 3600) {
        return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    } else {
        const hours = Math.floor(seconds / 3600);
        const minutes = Math.floor((seconds % 3600) / 60);
        return `${hours}h ${minutes}m`;
    }
}

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {string} type - The type of toast (success, error, warning, info)
 */
function showToast(message, type = 'success') {
    // Create toast container if it doesn't exist
    if ($('#toast-container').length === 0) {
        $('body').append('<div id="toast-container" class="position-fixed bottom-0 end-0 p-3" style="z-index: 11"></div>');
    }

    // Generate a unique ID for this toast
    const toastId = 'toast-' + new Date().getTime();

    // Determine toast class based on type
    let bgClass = 'bg-success text-white';
    let iconClass = 'fas fa-check-circle';

    if (type === 'error') {
        bgClass = 'bg-danger text-white';
        iconClass = 'fas fa-exclamation-circle';
    } else if (type === 'warning') {
        bgClass = 'bg-warning';
        iconClass = 'fas fa-exclamation-triangle';
    } else if (type === 'info') {
        bgClass = 'bg-info text-white';
        iconClass = 'fas fa-info-circle';
    }

    // Create toast HTML
    const toastHtml = `
        <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header ${bgClass}">
                <i class="${iconClass} me-2"></i>
                <strong class="me-auto">${type.charAt(0).toUpperCase() + type.slice(1)}</strong>
                <small>Just now</small>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                ${message}
            </div>
        </div>
    `;

    // Add toast to container
    $('#toast-container').append(toastHtml);

    // Initialize and show the toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 5000
    });

    toast.show();

    // Remove toast from DOM after it's hidden
    $(toastElement).on('hidden.bs.toast', function() {
        $(this).remove();
    });
}
