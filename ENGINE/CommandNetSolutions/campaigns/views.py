"""
Views for the campaigns app.
This file is a proxy to avoid circular imports.
"""
# Import views from the views directory
from campaigns.views.workflow_views import *
from campaigns.views.account_views import *
from campaigns.views.tag_views import *
from campaigns.views.dashboard_views import *

# Re-export all views
__all__ = [
    'CampaignListView',
    'CampaignDetailView',
    'CampaignCreateView',
    'CampaignUpdateView',
    'CampaignDeleteView',
    'AddLocationTargetView',
    'AddLocationToTargetView',
    'AddUsernameTargetView',
    'RemoveLocationTargetView',
    'RemoveUsernameTargetView',
    'LaunchCampaignView',
    'PauseCampaignView',
    'ResumeCampaignView',
    'StopCampaignView',
    'ToggleFavoriteView',
    'ExportCampaignView',
    'AnalyzeCampaignView',
    'DynamicTagListView',
    'DynamicTagCreateView',
    'DynamicTagUpdateView',
    'DynamicTagDeleteView',
    'TagGroupListView',
    'TagGroupCreateView',
    'TagGroupDetailView',
    'TagGroupUpdateView',
    'TagGroupDeleteView',
    'AddTagToGroupView',
    'RemoveTagFromGroupView',
    'LocationSearchView',
    'LocationCountriesView',
    'AnalyzeAccountsAPIView',
    'CampaignDashboardView',
    'CampaignAccountsView',
    'CampaignWhiteListView',
    'CampaignTagListView',
    'CampaignTagCreateView',
    'CampaignTagUpdateView',
    'CampaignTagDeleteView'
]
