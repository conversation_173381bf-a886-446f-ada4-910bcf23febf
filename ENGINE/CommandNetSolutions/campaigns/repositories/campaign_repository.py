"""
Repository for campaign data access.
"""
from django.db.models import Count, Avg, Q
from campaigns.models import Campaign


class CampaignRepository:
    """
    Repository for campaign data access.
    Abstracts database operations and provides caching.
    """

    def __init__(self, cache_service=None):
        """
        Initialize repository.

        Args:
            cache_service (CacheService): Optional cache service
        """
        self.cache_service = cache_service

    def get_by_id(self, campaign_id):
        """
        Get campaign by ID with caching.

        Args:
            campaign_id (uuid): Campaign ID

        Returns:
            Campaign: Campaign instance

        Raises:
            Campaign.DoesNotExist: If campaign not found
        """
        cache_key = f"campaign:{campaign_id}"

        # Try to get from cache first
        if self.cache_service:
            cached = self.cache_service.get(cache_key)
            if cached:
                return cached

        # Get from database with related data
        queryset = Campaign.objects.filter(id=campaign_id)

        # Apply optimization
        try:
            from campaigns.services import OptimizationService
            queryset = OptimizationService.optimize_campaign_query(queryset)
        except ImportError:
            # If optimization service is not available, use default prefetch
            queryset = queryset.prefetch_related(
                'location_targets',
                'username_targets',
                'results'
            )

        campaign = queryset.get()

        # Store in cache
        if self.cache_service:
            self.cache_service.set(cache_key, campaign)

        return campaign

    def get_all(self, filters=None, order_by=None, with_stats=False):
        """
        Get all campaigns with optional filtering and ordering.

        Args:
            filters (dict): Optional filters
            order_by (str): Optional ordering field
            with_stats (bool): Whether to include statistics

        Returns:
            QuerySet: Campaign queryset
        """
        # Start with all campaigns
        queryset = Campaign.objects.all()

        # Apply filters
        if filters:
            if 'status' in filters:
                queryset = queryset.filter(status=filters['status'])

            if 'target_type' in filters:
                queryset = queryset.filter(target_type=filters['target_type'])

            if 'creator' in filters:
                queryset = queryset.filter(creator=filters['creator'])

            if 'search' in filters:
                search = filters['search']
                queryset = queryset.filter(
                    Q(name__icontains=search) |
                    Q(description__icontains=search)
                )

        # Apply ordering
        if order_by:
            if order_by.startswith('-'):
                # Descending order
                field = order_by[1:]
                queryset = queryset.order_by(f"-{field}")
            else:
                # Ascending order
                queryset = queryset.order_by(order_by)
        else:
            # Default ordering
            queryset = queryset.order_by('-created_at')

        # Add statistics if requested
        if with_stats:
            queryset = queryset.annotate(
                account_count=Count('collected_accounts', distinct=True),
                whitelist_count=Count('collected_accounts__whitelistentry', distinct=True),
                avg_followers=Avg('collected_accounts__followers')
            )

        # Apply optimization
        try:
            from campaigns.services import OptimizationService
            queryset = OptimizationService.optimize_campaign_query(queryset)
        except ImportError:
            # If optimization service is not available, continue without it
            pass

        return queryset

    def save(self, campaign):
        """
        Save campaign and update cache.

        Args:
            campaign (Campaign): Campaign instance

        Returns:
            Campaign: Saved campaign instance
        """
        campaign.save()

        # Update cache
        if self.cache_service:
            cache_key = f"campaign:{campaign.id}"
            self.cache_service.set(cache_key, campaign)

        return campaign

    def delete(self, campaign_id):
        """
        Delete campaign and clear cache.

        Args:
            campaign_id (uuid): Campaign ID

        Raises:
            Campaign.DoesNotExist: If campaign not found
        """
        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)

        # Delete campaign
        campaign.delete()

        # Clear cache
        if self.cache_service:
            cache_key = f"campaign:{campaign_id}"
            self.cache_service.delete(cache_key)
