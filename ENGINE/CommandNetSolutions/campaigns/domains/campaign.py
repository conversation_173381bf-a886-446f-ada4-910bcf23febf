"""
Campaign Domain Service.
Responsible for campaign lifecycle, targeting, and configuration.
"""
import uuid
from django.utils import timezone
from campaigns.models import Campaign, LocationTarget, UsernameTarget


class CampaignDomain:
    """
    Domain service for campaign management.
    Encapsulates business logic for campaign operations.
    """
    
    def __init__(self, repository):
        self.repository = repository
    
    def create_campaign(self, campaign_data):
        """
        Create a new campaign with validation.
        
        Args:
            campaign_data (dict): Campaign data including name, description, etc.
            
        Returns:
            Campaign: Created campaign instance
            
        Raises:
            ValueError: If validation fails
        """
        # Validate required fields
        if not campaign_data.get('name'):
            raise ValueError("Campaign name is required")
            
        # Create campaign instance
        campaign = Campaign(
            id=uuid.uuid4(),
            name=campaign_data.get('name'),
            description=campaign_data.get('description', ''),
            target_type=campaign_data.get('target_type', 'location'),
            audience_type=campaign_data.get('audience_type', 'profile'),
            status='draft',
            creator=campaign_data.get('creator')
        )
        
        # Save campaign
        return self.repository.save(campaign)
    
    def update_campaign(self, campaign_id, campaign_data):
        """
        Update campaign with validation.
        
        Args:
            campaign_id (uuid): Campaign ID
            campaign_data (dict): Updated campaign data
            
        Returns:
            Campaign: Updated campaign instance
            
        Raises:
            ValueError: If validation fails
            Campaign.DoesNotExist: If campaign not found
        """
        # Get existing campaign
        campaign = self.repository.get_by_id(campaign_id)
        
        # Update fields
        if 'name' in campaign_data:
            campaign.name = campaign_data['name']
            
        if 'description' in campaign_data:
            campaign.description = campaign_data['description']
            
        if 'target_type' in campaign_data:
            campaign.target_type = campaign_data['target_type']
            
        if 'audience_type' in campaign_data:
            campaign.audience_type = campaign_data['audience_type']
            
        if 'scheduled_start' in campaign_data:
            campaign.scheduled_start = campaign_data['scheduled_start']
            
        if 'scheduled_end' in campaign_data:
            campaign.scheduled_end = campaign_data['scheduled_end']
        
        # Save campaign
        return self.repository.save(campaign)
    
    def add_location_target(self, campaign_id, location_data):
        """
        Add location target to campaign.
        
        Args:
            campaign_id (uuid): Campaign ID
            location_data (dict): Location data including location_id, country, city
            
        Returns:
            LocationTarget: Created location target
            
        Raises:
            ValueError: If validation fails
            Campaign.DoesNotExist: If campaign not found
        """
        # Get campaign
        campaign = self.repository.get_by_id(campaign_id)
        
        # Validate location data
        if not location_data.get('location_id'):
            raise ValueError("Location ID is required")
            
        # Create location target
        location_target = LocationTarget(
            campaign=campaign,
            location_id=location_data['location_id'],
            country=location_data.get('country', ''),
            city=location_data.get('city', '')
        )
        
        # Save location target
        location_target.save()
        
        return location_target
    
    def add_username_target(self, campaign_id, username_data):
        """
        Add username target to campaign.
        
        Args:
            campaign_id (uuid): Campaign ID
            username_data (dict): Username data including username, audience_type
            
        Returns:
            UsernameTarget: Created username target
            
        Raises:
            ValueError: If validation fails
            Campaign.DoesNotExist: If campaign not found
        """
        # Get campaign
        campaign = self.repository.get_by_id(campaign_id)
        
        # Validate username data
        if not username_data.get('username'):
            raise ValueError("Username is required")
            
        # Create username target
        username_target = UsernameTarget(
            campaign=campaign,
            username=username_data['username'],
            audience_type=username_data.get('audience_type', campaign.audience_type)
        )
        
        # Save username target
        username_target.save()
        
        return username_target
    
    def launch_campaign(self, campaign_id, airflow_service=None):
        """
        Launch campaign and trigger data collection.
        
        Args:
            campaign_id (uuid): Campaign ID
            airflow_service: Optional service for Airflow integration
            
        Returns:
            Campaign: Updated campaign instance
            
        Raises:
            ValueError: If campaign cannot be launched
            Campaign.DoesNotExist: If campaign not found
        """
        # Get campaign
        campaign = self.repository.get_by_id(campaign_id)
        
        # Validate campaign state
        if campaign.status not in ['draft', 'failed']:
            raise ValueError(f"Campaign cannot be launched from '{campaign.status}' status")
            
        # Check if campaign has targets
        if campaign.target_type == 'location' and not campaign.location_targets.exists():
            raise ValueError("Campaign has no location targets")
            
        if campaign.target_type == 'username' and not campaign.username_targets.exists():
            raise ValueError("Campaign has no username targets")
        
        # Update campaign status
        campaign.status = 'pending'
        campaign.updated_at = timezone.now()
        
        # If airflow service is provided, trigger DAG
        if airflow_service:
            # Prepare configuration
            conf = self._prepare_campaign_config(campaign)
            
            # Trigger DAG
            result = airflow_service.trigger_dag(
                dag_id='campaign_data_collection',
                conf=conf
            )
            
            # Update campaign with Airflow information
            campaign.airflow_dag_id = 'campaign_data_collection'
            campaign.airflow_run_id = result.get('dag_run_id')
        
        # Save campaign
        return self.repository.save(campaign)
    
    def _prepare_campaign_config(self, campaign):
        """
        Prepare campaign configuration for Airflow.
        
        Args:
            campaign (Campaign): Campaign instance
            
        Returns:
            dict: Configuration for Airflow DAG
        """
        conf = {
            'campaign_id': str(campaign.id),
            'target_type': campaign.target_type,
            'audience_type': campaign.audience_type,
        }
        
        # Add location targets
        if campaign.target_type == 'location':
            location_targets = []
            for target in campaign.location_targets.all():
                location_targets.append({
                    'location_id': target.location_id,
                    'country': target.country,
                    'city': target.city
                })
            conf['location_targets'] = location_targets
        
        # Add username targets
        if campaign.target_type == 'username':
            username_targets = []
            for target in campaign.username_targets.all():
                username_targets.append(target.username)
            conf['username_targets'] = username_targets
        
        return conf
