"""
Tag Domain Service.
Responsible for tag management and rule processing.
"""
import uuid
from campaigns.models import DynamicTag


class TagDomain:
    """
    Domain service for tag management.
    Encapsulates business logic for tag operations.
    """
    
    def __init__(self, repository):
        self.repository = repository
    
    def create_tag(self, tag_data):
        """
        Create a new dynamic tag.
        
        Args:
            tag_data (dict): Tag data including name, description, etc.
            
        Returns:
            DynamicTag: Created tag instance
            
        Raises:
            ValueError: If validation fails
        """
        # Validate required fields
        if not tag_data.get('name'):
            raise ValueError("Tag name is required")
            
        if not tag_data.get('field'):
            raise ValueError("Field is required")
            
        if not tag_data.get('pattern'):
            raise ValueError("Pattern is required")
        
        # Create tag instance
        tag = DynamicTag(
            id=uuid.uuid4(),
            name=tag_data.get('name'),
            description=tag_data.get('description', ''),
            tag_type=tag_data.get('tag_type', 'keyword'),
            field=tag_data.get('field'),
            pattern=tag_data.get('pattern'),
            is_global=tag_data.get('is_global', False)
        )
        
        # Save tag
        tag.save()
        
        return tag
    
    def update_tag(self, tag_id, tag_data):
        """
        Update dynamic tag.
        
        Args:
            tag_id (uuid): Tag ID
            tag_data (dict): Updated tag data
            
        Returns:
            DynamicTag: Updated tag instance
            
        Raises:
            ValueError: If validation fails
            DynamicTag.DoesNotExist: If tag not found
        """
        # Get existing tag
        tag = DynamicTag.objects.get(id=tag_id)
        
        # Update fields
        if 'name' in tag_data:
            tag.name = tag_data['name']
            
        if 'description' in tag_data:
            tag.description = tag_data['description']
            
        if 'tag_type' in tag_data:
            tag.tag_type = tag_data['tag_type']
            
        if 'field' in tag_data:
            tag.field = tag_data['field']
            
        if 'pattern' in tag_data:
            tag.pattern = tag_data['pattern']
            
        if 'is_global' in tag_data:
            tag.is_global = tag_data['is_global']
        
        # Save tag
        tag.save()
        
        return tag
    
    def delete_tag(self, tag_id):
        """
        Delete dynamic tag.
        
        Args:
            tag_id (uuid): Tag ID
            
        Raises:
            DynamicTag.DoesNotExist: If tag not found
        """
        # Get tag
        tag = DynamicTag.objects.get(id=tag_id)
        
        # Delete tag
        tag.delete()
    
    def assign_tag_to_campaign(self, tag_id, campaign_id):
        """
        Assign tag to campaign.
        
        Args:
            tag_id (uuid): Tag ID
            campaign_id (uuid): Campaign ID
            
        Raises:
            DynamicTag.DoesNotExist: If tag not found
            Campaign.DoesNotExist: If campaign not found
        """
        # Get tag
        tag = DynamicTag.objects.get(id=tag_id)
        
        # Get campaign analysis settings
        from campaigns.models import Campaign, CampaignAnalysisSettings
        campaign = Campaign.objects.get(id=campaign_id)
        
        # Get or create analysis settings
        analysis_settings, created = CampaignAnalysisSettings.objects.get_or_create(
            campaign=campaign,
            defaults={
                'id': uuid.uuid4(),
                'auto_analyze': True,
                'analysis_frequency': 'immediate'
            }
        )
        
        # Add tag to campaign
        analysis_settings.dynamic_tags.add(tag)
    
    def remove_tag_from_campaign(self, tag_id, campaign_id):
        """
        Remove tag from campaign.
        
        Args:
            tag_id (uuid): Tag ID
            campaign_id (uuid): Campaign ID
            
        Raises:
            DynamicTag.DoesNotExist: If tag not found
            Campaign.DoesNotExist: If campaign not found
        """
        # Get tag
        tag = DynamicTag.objects.get(id=tag_id)
        
        # Get campaign analysis settings
        from campaigns.models import Campaign, CampaignAnalysisSettings
        campaign = Campaign.objects.get(id=campaign_id)
        
        try:
            # Get analysis settings
            analysis_settings = CampaignAnalysisSettings.objects.get(campaign=campaign)
            
            # Remove tag from campaign
            analysis_settings.dynamic_tags.remove(tag)
        except CampaignAnalysisSettings.DoesNotExist:
            # No analysis settings, nothing to remove
            pass
