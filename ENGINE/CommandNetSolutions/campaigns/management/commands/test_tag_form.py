"""
Management command to test the DynamicTagForm.
"""
import json
import uuid
from django.core.management.base import BaseCommand
from campaigns.forms import DynamicTagForm
from campaigns.models import DynamicTag, CampaignTagRule, CampaignTagCondition


class Command(BaseCommand):
    help = 'Test the DynamicTagForm'

    def handle(self, *args, **options):
        self.stdout.write('Testing tag creation with a single condition...')
        self.test_create_tag()
        
        self.stdout.write('\nTesting tag update...')
        self.test_update_tag()

    def test_create_tag(self):
        """Test creating a tag with a single condition."""
        # Create form data
        form_data = {
            'name': 'Test Tag',
            'description': 'Test tag description',
            'is_global': True,
            'field': 'bio',
            'pattern': 'test',
            'tag_type': 'keyword',
            'conditions_json': json.dumps([{
                'id': 0,
                'field_category': 'text',
                'field': 'bio',
                'field_type': 'string',
                'operator': 'icontains',
                'value': 'test',
                'required': True
            }]),
            'condition_logic': 'all'
        }
        
        # Create the form
        form = DynamicTagForm(data=form_data)
        
        # Check if the form is valid
        if form.is_valid():
            self.stdout.write(self.style.SUCCESS('Form is valid'))
            
            # Save the form
            tag = form.save()
            self.stdout.write(f"Tag created: {tag.id}, {tag.name}")
            
            # Save the conditions
            success = form.save_conditions(tag)
            self.stdout.write(f"Conditions saved: {success}")
            
            # Check if the tag has the correct values
            self.stdout.write(f"Tag values: name={tag.name}, description={tag.description}, field={tag.field}, pattern={tag.pattern}")
            
            # Check if the tag rule was created
            try:
                pattern_data = json.loads(tag.pattern)
                if 'rule_id' in pattern_data:
                    rule_id = pattern_data['rule_id']
                    rule = CampaignTagRule.objects.get(id=rule_id)
                    self.stdout.write(f"Rule created: {rule.id}, {rule.name}")
                    
                    # Check if the condition was created
                    conditions = CampaignTagCondition.objects.filter(rule=rule)
                    self.stdout.write(f"Conditions count: {conditions.count()}")
                    
                    for i, condition in enumerate(conditions):
                        self.stdout.write(f"Condition {i+1}: field={condition.field}, operator={condition.operator}, value={condition.value}")
                else:
                    self.stdout.write(self.style.WARNING("No rule_id found in pattern data"))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error checking rule: {str(e)}"))
        else:
            self.stdout.write(self.style.ERROR("Form is invalid"))
            self.stdout.write(self.style.ERROR(f"Form errors: {form.errors}"))

    def test_update_tag(self):
        """Test updating a tag."""
        # First create a tag
        tag = DynamicTag.objects.create(
            id=uuid.uuid4(),
            name='Tag to Update',
            description='Original description',
            field='bio',
            pattern='{}',
            tag_type='keyword',
            is_global=True
        )
        self.stdout.write(f"Tag created: {tag.id}, {tag.name}")
        
        # Create a rule for the tag
        rule = CampaignTagRule.objects.create(
            id=uuid.uuid4(),
            name=tag.name,
            tag=tag.name,
            description=tag.description,
            active=True,
            is_global=tag.is_global,
            logic='all'
        )
        self.stdout.write(f"Rule created: {rule.id}, {rule.name}")
        
        # Create a condition for the rule
        condition = CampaignTagCondition.objects.create(
            id=uuid.uuid4(),
            rule=rule,
            field='bio',
            field_type='string',
            operator='icontains',
            value='"original"',
            score=1,
            required=True
        )
        self.stdout.write(f"Condition created: {condition.id}, {condition.field}")
        
        # Update the tag's pattern to reference the rule
        tag.pattern = json.dumps({'rule_id': str(rule.id), 'logic': 'all'})
        tag.save()
        self.stdout.write(f"Tag pattern updated: {tag.pattern}")
        
        # Now update the tag
        form_data = {
            'name': 'Tag to Update',
            'description': 'Updated description',
            'is_global': True,
            'field': 'bio',
            'pattern': 'updated',
            'tag_type': 'keyword',
            'conditions_json': json.dumps([{
                'id': 0,
                'field_category': 'text',
                'field': 'bio',
                'field_type': 'string',
                'operator': 'icontains',
                'value': 'updated',
                'required': True
            }]),
            'condition_logic': 'all'
        }
        
        # Create the form
        form = DynamicTagForm(data=form_data, instance=tag)
        
        # Check if the form is valid
        if form.is_valid():
            self.stdout.write(self.style.SUCCESS("Form is valid"))
            
            # Save the form
            tag = form.save()
            self.stdout.write(f"Tag updated: {tag.id}, {tag.name}, {tag.description}")
            
            # Save the conditions
            success = form.save_conditions(tag)
            self.stdout.write(f"Conditions saved: {success}")
            
            # Refresh the tag from the database
            tag.refresh_from_db()
            self.stdout.write(f"Tag after refresh: {tag.description}")
            
            # Check if the rule was updated
            try:
                pattern_data = json.loads(tag.pattern)
                if 'rule_id' in pattern_data:
                    rule_id = pattern_data['rule_id']
                    rule = CampaignTagRule.objects.get(id=rule_id)
                    self.stdout.write(f"Rule: {rule.id}, {rule.name}, {rule.description}")
                    
                    # Check if the condition was updated
                    conditions = CampaignTagCondition.objects.filter(rule=rule)
                    self.stdout.write(f"Conditions count: {conditions.count()}")
                    
                    for i, condition in enumerate(conditions):
                        self.stdout.write(f"Condition {i+1}: field={condition.field}, operator={condition.operator}, value={condition.value}")
                else:
                    self.stdout.write(self.style.WARNING("No rule_id found in pattern data"))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error checking rule: {str(e)}"))
        else:
            self.stdout.write(self.style.ERROR("Form is invalid"))
            self.stdout.write(self.style.ERROR(f"Form errors: {form.errors}"))
