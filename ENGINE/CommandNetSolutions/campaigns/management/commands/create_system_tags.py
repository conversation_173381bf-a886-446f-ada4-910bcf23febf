"""
Management command to create system tags with appropriate conditions.

This command creates a set of predefined system tags that cannot be deleted by users.
These tags are essential for the system's functionality and include proper conditions
for filtering accounts.

Usage:
    python manage.py create_system_tags [--force]
"""
import uuid
import json
import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from campaigns.models import (
    TagCategory,
    TagGroup,
    DynamicTag,
    CampaignTagRule,
    CampaignTagCondition
)

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Create predefined system tags that cannot be deleted by users"

    def add_arguments(self, parser):
        parser.add_argument(
            "--force",
            action="store_true",
            help="Force recreation of system tags even if they already exist",
        )

    def handle(self, *args, **options):
        force = options["force"]

        # Create system tags
        with transaction.atomic():
            # Create or get system tag category
            system_category = self.create_system_category()

            # Create or get system tag group
            system_group = self.create_system_group()

            # Create system tags
            self.create_system_tags(system_category, system_group, force)

        self.stdout.write(self.style.SUCCESS("Successfully created system tags."))

    def create_system_category(self):
        """Create a system tag category"""
        self.stdout.write("Creating system tag category...")

        category, created = TagCategory.objects.get_or_create(
            name="System",
            defaults={
                "id": uuid.uuid4(),
                "description": "System-defined tags that cannot be deleted",
                "color": "#007bff",
                "icon": "cog",
                "priority": 100  # Highest priority to appear first
            }
        )

        if created:
            self.stdout.write(self.style.SUCCESS("Created system tag category"))
        else:
            self.stdout.write("System tag category already exists")

        return category

    def create_system_group(self):
        """Create a system tag group"""
        self.stdout.write("Creating system tag group...")

        group, created = TagGroup.objects.get_or_create(
            name="System Tags",
            defaults={
                "id": uuid.uuid4(),
                "description": "System-defined tags that cannot be deleted",
                "color": "#007bff",
                "is_global": True
            }
        )

        if created:
            self.stdout.write(self.style.SUCCESS("Created system tag group"))
        else:
            self.stdout.write("System tag group already exists")

        return group

    def create_system_tags(self, category, group, force=False):
        """Create system tags with appropriate conditions"""
        self.stdout.write("Creating system tags with conditions...")
        logger.info("Creating system tags with conditions...")

        # Define system tags with their conditions
        system_tags = [
            # Audience Size Tags
            {
                "name": "Micro Influencer",
                "description": "Accounts with 1K-10K followers",
                "tag_type": "keyword",
                "field": "followers",
                "conditions": [
                    {
                        "field": "followers",
                        "field_type": "number",
                        "operator": "gte",
                        "value": "1000",
                        "required": True
                    },
                    {
                        "field": "followers",
                        "field_type": "number",
                        "operator": "lt",
                        "value": "10000",
                        "required": True
                    }
                ],
                "condition_logic": "all"
            },
            {
                "name": "Mid-Tier Influencer",
                "description": "Accounts with 10K-100K followers",
                "tag_type": "keyword",
                "field": "followers",
                "conditions": [
                    {
                        "field": "followers",
                        "field_type": "number",
                        "operator": "gte",
                        "value": "10000",
                        "required": True
                    },
                    {
                        "field": "followers",
                        "field_type": "number",
                        "operator": "lt",
                        "value": "100000",
                        "required": True
                    }
                ],
                "condition_logic": "all"
            },
            {
                "name": "Macro Influencer",
                "description": "Accounts with 100K-1M followers",
                "tag_type": "keyword",
                "field": "followers",
                "conditions": [
                    {
                        "field": "followers",
                        "field_type": "number",
                        "operator": "gte",
                        "value": "100000",
                        "required": True
                    },
                    {
                        "field": "followers",
                        "field_type": "number",
                        "operator": "lt",
                        "value": "1000000",
                        "required": True
                    }
                ],
                "condition_logic": "all"
            },
            {
                "name": "Mega Influencer",
                "description": "Accounts with 1M+ followers",
                "tag_type": "keyword",
                "field": "followers",
                "conditions": [
                    {
                        "field": "followers",
                        "field_type": "number",
                        "operator": "gte",
                        "value": "1000000",
                        "required": True
                    }
                ],
                "condition_logic": "all"
            },

            # Interest Tags
            {
                "name": "Fitness Enthusiast",
                "description": "Accounts focused on fitness and health",
                "tag_type": "keyword",
                "field": "bio",
                "conditions": [
                    {
                        "field": "bio",
                        "field_type": "string",
                        "operator": "icontains",
                        "value": "fitness",
                        "required": False
                    },
                    {
                        "field": "bio",
                        "field_type": "string",
                        "operator": "icontains",
                        "value": "workout",
                        "required": False
                    },
                    {
                        "field": "bio",
                        "field_type": "string",
                        "operator": "icontains",
                        "value": "gym",
                        "required": False
                    },
                    {
                        "field": "bio",
                        "field_type": "string",
                        "operator": "icontains",
                        "value": "health",
                        "required": False
                    }
                ],
                "condition_logic": "any"
            },
            {
                "name": "Fashion Influencer",
                "description": "Accounts focused on fashion and style",
                "tag_type": "keyword",
                "field": "bio",
                "conditions": [
                    {
                        "field": "bio",
                        "field_type": "string",
                        "operator": "icontains",
                        "value": "fashion",
                        "required": False
                    },
                    {
                        "field": "bio",
                        "field_type": "string",
                        "operator": "icontains",
                        "value": "style",
                        "required": False
                    },
                    {
                        "field": "bio",
                        "field_type": "string",
                        "operator": "icontains",
                        "value": "clothing",
                        "required": False
                    },
                    {
                        "field": "bio",
                        "field_type": "string",
                        "operator": "icontains",
                        "value": "outfit",
                        "required": False
                    }
                ],
                "condition_logic": "any"
            },
            {
                "name": "Travel Blogger",
                "description": "Accounts focused on travel and adventure",
                "tag_type": "keyword",
                "field": "bio",
                "conditions": [
                    {
                        "field": "bio",
                        "field_type": "string",
                        "operator": "icontains",
                        "value": "travel",
                        "required": False
                    },
                    {
                        "field": "bio",
                        "field_type": "string",
                        "operator": "icontains",
                        "value": "adventure",
                        "required": False
                    },
                    {
                        "field": "bio",
                        "field_type": "string",
                        "operator": "icontains",
                        "value": "wanderlust",
                        "required": False
                    },
                    {
                        "field": "bio",
                        "field_type": "string",
                        "operator": "icontains",
                        "value": "explore",
                        "required": False
                    }
                ],
                "condition_logic": "any"
            },

            # Account Quality Tags
            {
                "name": "Verified Account",
                "description": "Accounts that are verified",
                "tag_type": "keyword",
                "field": "is_verified",
                "conditions": [
                    {
                        "field": "is_verified",
                        "field_type": "boolean",
                        "operator": "is_true",
                        "value": "true",
                        "required": True
                    }
                ],
                "condition_logic": "all"
            },
            {
                "name": "Active Creator",
                "description": "Accounts with 100+ posts",
                "tag_type": "keyword",
                "field": "number_of_posts",
                "conditions": [
                    {
                        "field": "number_of_posts",
                        "field_type": "number",
                        "operator": "gte",
                        "value": "100",
                        "required": True
                    }
                ],
                "condition_logic": "all"
            }
        ]

        tags_created = 0
        tags_updated = 0

        for tag_data in system_tags:
            try:
                with transaction.atomic():
                    # Check if tag already exists
                    existing_tag = DynamicTag.objects.filter(name=tag_data["name"]).first()

                    if existing_tag and not force:
                        # Update the tag to make it a system tag if it's not already
                        if not existing_tag.is_system:
                            existing_tag.is_system = True
                            existing_tag.save(update_fields=["is_system"])
                            self.stdout.write(f"  - Updated tag '{tag_data['name']}' to be a system tag")
                            tags_updated += 1
                        continue

                    # Create or update the tag
                    tag_id = uuid.uuid4() if not existing_tag else existing_tag.id

                    tag, created = DynamicTag.objects.update_or_create(
                        name=tag_data["name"],
                        defaults={
                            "id": tag_id,
                            "description": tag_data["description"],
                            "category": category,
                            "tag_group": group,
                            "tag_type": tag_data["tag_type"],
                            "pattern": "{}",  # Will be updated after rule creation
                            "field": tag_data["field"],
                            "is_global": True,
                            "is_system": True,
                            "weight": 1.0
                        }
                    )

                    # Create rule for the tag
                    rule = CampaignTagRule.objects.create(
                        id=uuid.uuid4(),
                        name=f"Rule for {tag.name}",
                        tag=tag.name,
                        logic=tag_data["condition_logic"],
                        active=True,
                        is_global=True
                    )

                    # Update tag pattern with rule ID
                    pattern_data = {
                        'rule_id': str(rule.id),
                        'logic': tag_data["condition_logic"]
                    }
                    tag.pattern = json.dumps(pattern_data)
                    tag.save()

                    # Create conditions for the rule
                    for condition_data in tag_data["conditions"]:
                        CampaignTagCondition.objects.create(
                            id=uuid.uuid4(),
                            rule=rule,
                            field=condition_data["field"],
                            field_type=condition_data["field_type"],
                            operator=condition_data["operator"],
                            value=condition_data["value"],
                            required=condition_data["required"]
                        )

                    if created:
                        self.stdout.write(f"  - Created system tag: {tag.name} with {len(tag_data['conditions'])} conditions")
                        tags_created += 1
                    else:
                        self.stdout.write(f"  - Updated system tag: {tag.name} with {len(tag_data['conditions'])} conditions")
                        tags_updated += 1

                    logger.info(f"Created/updated tag {tag.name} with {len(tag_data['conditions'])} conditions")
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error creating tag {tag_data['name']}: {str(e)}"))
                logger.error(f"Error creating tag {tag_data['name']}: {str(e)}")

        self.stdout.write(self.style.SUCCESS(f"Created {tags_created} and updated {tags_updated} system tags"))
