"""
Management command to create a realistic test campaign with proper data relationships.

This script:
1. <PERSON>reates a realistic fitness campaign
2. Adds location and username targets
3. <PERSON><PERSON>s 50 collected accounts with realistic data
4. Creates campaign result showing 45 analyzed accounts
5. <PERSON>reates 12 whitelisted accounts

Usage:
    python manage.py create_test_campaign
"""
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from campaigns.models import Campaign, LocationTarget, UsernameTarget, CampaignResult
from instagram.models import Accounts, WhiteListEntry
from datetime import datetime, timedelta
from django.utils import timezone
import random


class Command(BaseCommand):
    help = 'Create a realistic test campaign with proper data relationships'

    def handle(self, *args, **options):
        # Get or create test user
        user, created = User.objects.get_or_create(
            username="test_user",
            defaults={
                "email": "<EMAIL>",
                "first_name": "Test",
                "last_name": "User"
            }
        )

        # Create campaign with realistic status
        campaign = Campaign.objects.create(
            name="Fitness Influencer Campaign",
            description="Campaign targeting fitness enthusiasts and health-conscious individuals in major US cities",
            status="running",
            target_type="mixed",
            audience_type="both",
            creator=user
        )

        self.stdout.write(f"Created campaign: {campaign.name} (ID: {campaign.id})")

        # Add location targets
        locations = [
            {"location_id": "213385402", "city": "New York", "country": "United States"},
            {"location_id": "213385405", "city": "Los Angeles", "country": "United States"},
            {"location_id": "213385407", "city": "Chicago", "country": "United States"},
            {"location_id": "213385409", "city": "Miami", "country": "United States"},
        ]

        for loc in locations:
            LocationTarget.objects.create(
                campaign=campaign,
                **loc
            )

        # Add username targets
        usernames = [
            {"username": "fitness_guru_official", "audience_type": "followers"},
            {"username": "healthy_lifestyle_coach", "audience_type": "both"},
            {"username": "gym_motivation_daily", "audience_type": "followers"},
        ]

        for user_data in usernames:
            UsernameTarget.objects.create(
                campaign=campaign,
                **user_data
            )

        # Create realistic account data (50 collected, 45 analyzed, 12 whitelisted)
        base_time = timezone.now() - timedelta(days=7)

        # Sample usernames with realistic fitness/health theme
        sample_usernames = [
            "fitlife_sarah", "gym_beast_mike", "yoga_zen_maria", "crossfit_champion",
            "healthy_eats_jen", "marathon_runner_tom", "pilates_queen_anna", "bodybuilder_max",
            "nutrition_expert_lisa", "cardio_king_james", "strength_coach_alex", "wellness_guru_kim",
            "fitness_model_emma", "personal_trainer_joe", "health_blogger_sam", "workout_warrior_chris",
            "diet_coach_rachel", "gym_owner_david", "fitness_influencer_kate", "health_advocate_mark",
            "yoga_instructor_lily", "crossfit_athlete_ryan", "nutrition_coach_sophie", "fitness_enthusiast_ben",
            "healthy_living_coach", "workout_motivation", "fitness_journey_blog", "health_tips_daily",
            "gym_life_official", "fitness_transformation", "healthy_recipes_chef", "workout_videos_pro",
            "fitness_gear_review", "health_science_facts", "gym_equipment_guide", "fitness_challenges",
            "healthy_lifestyle_tips", "workout_routines_pro", "fitness_nutrition_guide", "health_wellness_coach",
            "gym_workout_plans", "fitness_success_stories", "healthy_meal_prep", "workout_form_tips",
            "fitness_motivation_quotes", "health_research_updates", "gym_safety_tips", "fitness_community_hub",
            "healthy_snack_ideas", "workout_recovery_tips"
        ]

        accounts_created = []

        # Create 50 collected accounts
        for i in range(50):
            username = sample_usernames[i]
            collection_time = base_time + timedelta(hours=i*2, minutes=random.randint(0, 59))

            account = Accounts.objects.create(
                username=username,
                full_name=f"{username.replace('_', ' ').title()}",
                bio=f"Fitness enthusiast | Health coach | Inspiring others to live their best life 💪 #{random.choice(['fitness', 'health', 'wellness', 'gym'])}",
                followers=random.randint(1000, 50000),
                following=random.randint(200, 2000),
                number_of_posts=random.randint(50, 1000),
                account_type=random.choice(['personal', 'business']),
                is_verified=random.choice([True, False]) if random.random() > 0.8 else False,
                collection_date=collection_time,
                campaign_id=str(campaign.id),
                interests=['fitness', 'health', 'wellness', 'nutrition'],
                locations=['gym', 'fitness_center', 'health_club']
            )
            accounts_created.append(account)

        self.stdout.write(f"Created {len(accounts_created)} accounts")

        # Create campaign result with realistic numbers
        campaign_result = CampaignResult.objects.create(
            campaign=campaign,
            total_accounts_found=50,
            total_accounts_processed=45,  # 45 analyzed out of 50 collected
            last_processed_at=timezone.now() - timedelta(hours=2)
        )

        # Create whitelist entries for 12 accounts (realistic conversion rate)
        whitelisted_accounts = random.sample(accounts_created[:45], 12)  # Only from analyzed accounts

        for account in whitelisted_accounts:
            WhiteListEntry.objects.create(
                account=account,
                tags=['fitness_enthusiast', 'high_engagement'],
                dm=True,
                discover=True,
                comment=True,
                post_like=True,
                favorite=False,
                follow=True,
                is_auto=True
            )

        self.stdout.write(f"Created {len(whitelisted_accounts)} whitelist entries")
        self.stdout.write(f"Campaign statistics:")
        self.stdout.write(f"  - Collected: 50 accounts")
        self.stdout.write(f"  - Analyzed: 45 accounts")
        self.stdout.write(f"  - Whitelisted: 12 accounts")
        self.stdout.write(f"  - Conversion rate: {(12/45)*100:.1f}%")
        self.stdout.write(self.style.SUCCESS(f"Realistic campaign created successfully!"))
        self.stdout.write(f"Campaign ID: {campaign.id}")
        self.stdout.write(f"View at: /campaigns/{campaign.id}/")
