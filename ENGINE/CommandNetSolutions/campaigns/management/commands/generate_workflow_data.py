"""
Management command to generate demo workflow execution data.
"""
import os
import random
import uuid
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from campaigns.models import Campaign
from campaigns.models.workflow import WorkflowExecution, WorkflowProgressUpdate

class Command(BaseCommand):
    help = "Generate demo workflow execution data for testing"

    def add_arguments(self, parser):
        parser.add_argument(
            "--executions",
            type=int,
            default=10,
            help="Number of workflow executions to generate (default: 10)",
        )
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing workflow execution data before generating new data",
        )

    def handle(self, *args, **options):
        num_executions = options["executions"]
        clear_data = options["clear"]

        if clear_data:
            self.clear_existing_data()
            self.stdout.write(self.style.SUCCESS("Cleared existing workflow execution data"))

        # Get campaigns
        try:
            # Try to get campaigns using our custom manager
            campaigns = list(Campaign.objects.all())
            self.stdout.write(self.style.SUCCESS(f"Found {len(campaigns)} campaigns"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error getting campaigns: {str(e)}"))
            # Try a more direct approach using raw SQL
            from django.db import connection
            campaigns = []
            try:
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT id, name, description, status, target_type, audience_type,
                               created_at, updated_at, airflow_run_id, creator_id,
                               is_favorite, airflow_dag_id, dmp_conf
                        FROM campaigns_campaign
                    """)
                    rows = cursor.fetchall()
                    for row in rows:
                        campaign = Campaign()
                        campaign.id = row[0]
                        campaign.name = row[1]
                        campaign.description = row[2]
                        campaign.status = row[3]
                        campaign.target_type = row[4]
                        campaign.audience_type = row[5]
                        campaign.created_at = row[6]
                        campaign.updated_at = row[7]
                        campaign.airflow_run_id = row[8]
                        campaign.creator_id = row[9]
                        campaign.is_favorite = row[10]
                        campaign.airflow_dag_id = row[11]
                        campaign.dmp_conf = row[12]
                        campaigns.append(campaign)
                    self.stdout.write(self.style.SUCCESS(f"Found {len(campaigns)} campaigns using raw SQL"))
            except Exception as e2:
                self.stdout.write(self.style.ERROR(f"Error getting campaigns using raw SQL: {str(e2)}"))
                # Create a dummy campaign if we can't get any
                if not campaigns:
                    self.stdout.write(self.style.WARNING("Creating a dummy campaign for workflow generation"))
                    dummy_campaign = Campaign()
                    dummy_campaign.id = uuid.uuid4()
                    dummy_campaign.name = "Dummy Campaign"
                    dummy_campaign.description = "Dummy campaign for workflow generation"
                    dummy_campaign.status = "draft"
                    dummy_campaign.target_type = "location"
                    dummy_campaign.audience_type = "profile"
                    dummy_campaign.airflow_run_id = "pending"
                    dummy_campaign.is_favorite = False
                    dummy_campaign.dmp_conf = {}
                    campaigns.append(dummy_campaign)

        if not campaigns:
            self.stdout.write(self.style.ERROR("No campaigns found. Please create campaigns first."))
            return

        # Create workflow executions
        self.create_workflow_executions(num_executions, campaigns)

        self.stdout.write(self.style.SUCCESS(
            f"Successfully generated {num_executions} workflow executions"
        ))

    def clear_existing_data(self):
        """Clear existing workflow execution data"""
        WorkflowProgressUpdate.objects.all().delete()
        WorkflowExecution.objects.all().delete()

    def create_workflow_executions(self, num_executions, campaigns):
        """Create demo workflow executions with progress updates"""
        self.stdout.write(f"Creating {num_executions} demo workflow executions")

        # Workflow types
        workflow_types = ["collection", "analysis", "tagging", "follow", "like", "comment", "dm", "cep"]

        # Workflow statuses with weights
        statuses = ["pending", "running", "completed", "failed", "cancelled"]
        status_weights = [0.1, 0.2, 0.5, 0.1, 0.1]  # 10% pending, 20% running, etc.

        for i in range(num_executions):
            # Select a random campaign
            campaign = random.choice(campaigns)

            # Select a random workflow type
            workflow_type = random.choice(workflow_types)

            # Generate a workflow name
            workflow_name = f"campaign_{campaign.id}_{workflow_type}_{uuid.uuid4().hex[:8]}"

            # Generate a workflow path
            workflow_path = f"/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/INSTA/WORKFLOW/{workflow_name}.pygraph"

            # Select a random status
            status = random.choices(statuses, status_weights)[0]

            # Generate start time (between 7 days ago and now)
            start_time = timezone.now() - timedelta(days=random.uniform(0, 7))

            # Generate end time if completed, failed, or cancelled
            end_time = None
            if status in ["completed", "failed", "cancelled"]:
                # End time is between start time and now
                end_time = start_time + timedelta(minutes=random.uniform(5, 120))

            # Calculate duration
            duration = 0
            if end_time:
                duration = (end_time - start_time).total_seconds()

            # Generate total items
            total_items = random.randint(10, 200)

            # Generate processed items based on status
            processed_items = 0
            if status == "pending":
                processed_items = 0
            elif status == "running":
                processed_items = random.randint(1, total_items - 1)
            elif status in ["completed", "failed", "cancelled"]:
                processed_items = total_items if status == "completed" else random.randint(1, total_items)

            # Calculate progress
            progress = (processed_items / total_items * 100) if total_items > 0 else 0

            # Generate successful and failed items
            successful_items = random.randint(0, processed_items)
            failed_items = processed_items - successful_items

            # Generate error message if failed
            error_message = None
            if status == "failed":
                error_messages = [
                    "Connection error: Failed to connect to Instagram API",
                    "Rate limited: Too many requests",
                    "Authentication error: Invalid credentials",
                    "Timeout error: Operation timed out",
                    "Unexpected error: An unexpected error occurred"
                ]
                error_message = random.choice(error_messages)

            # Generate log file path
            log_file = f"/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/logs/{workflow_name}.log"

            # Generate parameters
            parameters = {
                "campaign_id": str(campaign.id),
                "workflow_type": workflow_type,
                "options": {
                    "batch_size": random.randint(10, 50),
                    "delay": random.randint(1, 10)
                }
            }

            # Generate results
            results = {}
            if status == "completed":
                results = {
                    "total_accounts": total_items,
                    "processed_accounts": processed_items,
                    "successful_accounts": successful_items,
                    "failed_accounts": failed_items,
                    "duration": duration
                }

            # Create workflow execution
            with transaction.atomic():
                workflow_execution = WorkflowExecution.objects.create(
                    id=uuid.uuid4(),
                    campaign=campaign,
                    workflow_name=workflow_name,
                    workflow_path=workflow_path,
                    workflow_type=workflow_type,
                    status=status,
                    start_time=start_time,
                    end_time=end_time,
                    duration=duration,
                    progress=progress,
                    total_items=total_items,
                    processed_items=processed_items,
                    successful_items=successful_items,
                    failed_items=failed_items,
                    error_message=error_message,
                    log_file=log_file,
                    parameters=parameters,
                    results=results
                )

                # Create progress updates
                self.create_progress_updates(workflow_execution)

    def create_progress_updates(self, workflow_execution):
        """Create progress updates for a workflow execution"""
        # Skip if pending
        if workflow_execution.status == "pending":
            return

        # Determine number of updates based on status and duration
        num_updates = 0
        if workflow_execution.status == "running":
            num_updates = random.randint(1, 5)
        elif workflow_execution.status in ["completed", "failed", "cancelled"]:
            # More updates for longer durations
            if workflow_execution.duration < 300:  # Less than 5 minutes
                num_updates = random.randint(1, 3)
            elif workflow_execution.duration < 1800:  # Less than 30 minutes
                num_updates = random.randint(3, 7)
            else:  # More than 30 minutes
                num_updates = random.randint(7, 15)

        # Create updates
        for i in range(num_updates):
            # Calculate timestamp
            if workflow_execution.end_time:
                # Distribute updates between start and end time
                progress_fraction = (i + 1) / (num_updates + 1)
                timestamp = workflow_execution.start_time + timedelta(
                    seconds=progress_fraction * workflow_execution.duration
                )
            else:
                # Distribute updates between start time and now
                progress_fraction = (i + 1) / (num_updates + 1)
                duration_so_far = (timezone.now() - workflow_execution.start_time).total_seconds()
                timestamp = workflow_execution.start_time + timedelta(
                    seconds=progress_fraction * duration_so_far
                )

            # Calculate processed items
            processed_items = int(progress_fraction * workflow_execution.processed_items)

            # Calculate successful and failed items
            successful_ratio = workflow_execution.successful_items / workflow_execution.processed_items if workflow_execution.processed_items > 0 else 0
            successful_items = int(processed_items * successful_ratio)
            failed_items = processed_items - successful_items

            # Calculate progress
            progress = (processed_items / workflow_execution.total_items * 100) if workflow_execution.total_items > 0 else 0

            # Generate message
            message = f"Processed {processed_items} of {workflow_execution.total_items} items"

            # Generate details
            details = {
                "processed_items": processed_items,
                "successful_items": successful_items,
                "failed_items": failed_items,
                "progress": progress
            }

            # Create progress update
            WorkflowProgressUpdate.objects.create(
                id=uuid.uuid4(),
                workflow_execution=workflow_execution,
                timestamp=timestamp,
                processed_items=processed_items,
                successful_items=successful_items,
                failed_items=failed_items,
                progress=progress,
                message=message,
                details=details
            )
