"""
Management command to empty the backend database.

This script:
1. <PERSON><PERSON>ves all campaigns
2. <PERSON><PERSON>ves all tag categories
3. <PERSON><PERSON>ves all tag groups
4. <PERSON>moves all tags
5. <PERSON>moves all campaign tags
6. Removes all tag analysis results
7. Removes all workflow executions
8. Removes all whitelist entries

Usage:
    python manage.py empty_backend
"""
import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth.models import User
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Empty the backend database'

    def add_arguments(self, parser):
        parser.add_argument(
            '--confirm',
            action='store_true',
            help='Confirm that you want to empty the backend database'
        )
        parser.add_argument(
            '--keep-users',
            action='store_true',
            help='Keep user accounts'
        )

    def handle(self, *args, **options):
        if not options['confirm']:
            self.stdout.write(self.style.WARNING(
                'This command will empty the backend database. '
                'Use --confirm to confirm that you want to proceed.'
            ))
            return

        keep_users = options['keep_users']
        self.stdout.write(self.style.SUCCESS('Starting to empty the backend database...'))

        try:
            # Import models here to avoid circular imports
            from campaigns.models import (
                Campaign, LocationTarget, UsernameTarget, TagAnalysisResult,
                CampaignROI, CampaignResult, TagCategory, TagGroup, DynamicTag,
                TagMetrics, CampaignTagRule, CampaignTagCondition, TagRuleCondition, CampaignTag,
                WorkflowExecution, WorkflowProgressUpdate
            )
            from instagram.models import Accounts, WhiteListEntry
            from taggit.models import Tag
            from instagram.models import CustomTaggedItem

            with transaction.atomic():
                # Delete workflow executions and progress updates
                workflow_count = WorkflowExecution.objects.count()
                WorkflowProgressUpdate.objects.all().delete()
                WorkflowExecution.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {workflow_count} workflow executions'))

                # Delete whitelist entries
                whitelist_count = WhiteListEntry.objects.count()
                WhiteListEntry.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {whitelist_count} whitelist entries'))

                # Delete accounts
                accounts_count = Accounts.objects.count()
                Accounts.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {accounts_count} accounts'))

                # Delete tag analysis results
                tag_analysis_count = TagAnalysisResult.objects.count()
                TagAnalysisResult.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {tag_analysis_count} tag analysis results'))

                # Delete campaign tags
                campaign_tag_count = CampaignTag.objects.count()
                CampaignTag.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {campaign_tag_count} campaign tags'))

                # Delete tag rule conditions
                tag_rule_condition_count = TagRuleCondition.objects.count()
                TagRuleCondition.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {tag_rule_condition_count} tag rule conditions'))

                # Delete campaign tag conditions
                campaign_tag_condition_count = CampaignTagCondition.objects.count()
                CampaignTagCondition.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {campaign_tag_condition_count} campaign tag conditions'))

                # Delete campaign tag rules
                campaign_tag_rule_count = CampaignTagRule.objects.count()
                CampaignTagRule.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {campaign_tag_rule_count} campaign tag rules'))

                # Delete tag metrics
                tag_metrics_count = TagMetrics.objects.count()
                TagMetrics.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {tag_metrics_count} tag metrics'))

                # Delete dynamic tags
                dynamic_tag_count = DynamicTag.objects.count()
                DynamicTag.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {dynamic_tag_count} dynamic tags'))

                # Delete tag groups
                tag_group_count = TagGroup.objects.count()
                TagGroup.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {tag_group_count} tag groups'))

                # Delete tag categories
                tag_category_count = TagCategory.objects.count()
                TagCategory.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {tag_category_count} tag categories'))

                # Delete taggit tags and tagged items
                content_type = ContentType.objects.get_for_model(Accounts)
                tagged_item_count = CustomTaggedItem.objects.filter(content_type=content_type).count()
                CustomTaggedItem.objects.filter(content_type=content_type).delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {tagged_item_count} custom tagged items'))

                tag_count = Tag.objects.count()
                Tag.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {tag_count} taggit tags'))

                # Delete campaign results and ROI
                campaign_result_count = CampaignResult.objects.count()
                CampaignResult.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {campaign_result_count} campaign results'))

                campaign_roi_count = CampaignROI.objects.count()
                CampaignROI.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {campaign_roi_count} campaign ROIs'))

                # Delete location and username targets
                location_target_count = LocationTarget.objects.count()
                LocationTarget.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {location_target_count} location targets'))

                username_target_count = UsernameTarget.objects.count()
                UsernameTarget.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {username_target_count} username targets'))

                # Delete campaigns
                campaign_count = Campaign.objects.count()
                Campaign.objects.all().delete()
                self.stdout.write(self.style.SUCCESS(f'Deleted {campaign_count} campaigns'))

                # Delete users if not keeping them
                if not keep_users:
                    try:
                        # Keep superusers
                        user_count = User.objects.filter(is_superuser=False).count()
                        User.objects.filter(is_superuser=False).delete()
                        self.stdout.write(self.style.SUCCESS(f'Deleted {user_count} non-superuser users'))
                    except Exception as e:
                        self.stdout.write(self.style.WARNING(f'Error deleting users: {str(e)}'))

            self.stdout.write(self.style.SUCCESS('Successfully emptied the backend database!'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error emptying backend database: {str(e)}'))
            raise
