import os
import csv
import zipfile
import json
from django.core.management.base import BaseCommand
from django.conf import settings
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Import Instagram locations from a ZIP file containing location data"

    def add_arguments(self, parser):
        parser.add_argument(
            "zip_file",
            type=str,
            help="Path to the ZIP file containing location data",
        )
        parser.add_argument(
            "--output",
            type=str,
            default=None,
            help="Path to save the output CSV file (default: campaigns/data/locations.csv)",
        )

    def handle(self, *args, **options):
        zip_file_path = options["zip_file"]
        output_file = options["output"]
        
        if not output_file:
            # Create data directory if it doesn't exist
            data_dir = os.path.join(settings.BASE_DIR, "campaigns", "data")
            os.makedirs(data_dir, exist_ok=True)
            output_file = os.path.join(data_dir, "locations.csv")
        
        self.stdout.write(f"Importing locations from {zip_file_path}")
        
        try:
            # Extract and process the ZIP file
            locations = self.extract_locations_from_zip(zip_file_path)
            
            # Write to CSV
            self.write_locations_to_csv(locations, output_file)
            
            self.stdout.write(self.style.SUCCESS(
                f"Successfully imported {len(locations)} locations to {output_file}"
            ))
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error importing locations: {str(e)}"))
            logger.exception("Error importing locations")
    
    def extract_locations_from_zip(self, zip_file_path):
        """
        Extract location data from the ZIP file.
        
        The function handles different possible formats within the ZIP file.
        """
        locations = []
        
        with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
            # List all files in the ZIP
            file_list = zip_ref.namelist()
            
            for file_name in file_list:
                # Skip directories and hidden files
                if file_name.endswith('/') or file_name.startswith('__MACOSX') or file_name.startswith('.'):
                    continue
                
                # Process based on file extension
                if file_name.endswith('.json'):
                    # JSON file
                    with zip_ref.open(file_name) as f:
                        data = json.load(f)
                        locations.extend(self.process_json_data(data))
                        
                elif file_name.endswith('.csv'):
                    # CSV file
                    with zip_ref.open(file_name) as f:
                        reader = csv.DictReader(f.read().decode('utf-8').splitlines())
                        for row in reader:
                            locations.append(self.process_csv_row(row))
                            
                else:
                    # Try to process as text file with location data
                    try:
                        with zip_ref.open(file_name) as f:
                            content = f.read().decode('utf-8')
                            # Try to parse as JSON
                            try:
                                data = json.loads(content)
                                locations.extend(self.process_json_data(data))
                            except json.JSONDecodeError:
                                # Not JSON, try as CSV
                                try:
                                    reader = csv.DictReader(content.splitlines())
                                    for row in reader:
                                        locations.append(self.process_csv_row(row))
                                except Exception:
                                    self.stdout.write(self.style.WARNING(
                                        f"Could not process file {file_name} as JSON or CSV"
                                    ))
                    except Exception as e:
                        self.stdout.write(self.style.WARNING(
                            f"Error processing file {file_name}: {str(e)}"
                        ))
        
        return locations
    
    def process_json_data(self, data):
        """
        Process JSON data into a standardized location format.
        Handles different possible JSON structures.
        """
        locations = []
        
        # If data is a list, process each item
        if isinstance(data, list):
            for item in data:
                location = self.extract_location_from_json(item)
                if location:
                    locations.append(location)
        # If data is a dict with 'locations' key
        elif isinstance(data, dict) and 'locations' in data:
            for item in data['locations']:
                location = self.extract_location_from_json(item)
                if location:
                    locations.append(location)
        # If data is a dict with location data directly
        elif isinstance(data, dict):
            location = self.extract_location_from_json(data)
            if location:
                locations.append(location)
        
        return locations
    
    def extract_location_from_json(self, item):
        """
        Extract location data from a JSON object.
        """
        # Try different possible field names
        location_id = (
            item.get('id') or 
            item.get('location_id') or 
            item.get('locationId') or
            item.get('pk')
        )
        
        country = (
            item.get('country') or 
            item.get('country_name') or
            item.get('countryName') or
            ''
        )
        
        city = (
            item.get('city') or 
            item.get('city_name') or
            item.get('cityName') or
            item.get('name') or
            ''
        )
        
        # Skip if we don't have the minimum required data
        if not location_id or (not country and not city):
            return None
        
        return {
            'location_id': str(location_id),
            'country': country,
            'city': city
        }
    
    def process_csv_row(self, row):
        """
        Process a CSV row into a standardized location format.
        """
        # Try different possible field names
        location_id = (
            row.get('id') or 
            row.get('location_id') or 
            row.get('locationId') or
            row.get('pk')
        )
        
        country = (
            row.get('country') or 
            row.get('country_name') or
            row.get('countryName') or
            ''
        )
        
        city = (
            row.get('city') or 
            row.get('city_name') or
            row.get('cityName') or
            row.get('name') or
            ''
        )
        
        return {
            'location_id': str(location_id),
            'country': country,
            'city': city
        }
    
    def write_locations_to_csv(self, locations, output_file):
        """
        Write the processed locations to a CSV file.
        """
        with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['location_id', 'country', 'city']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            
            writer.writeheader()
            for location in locations:
                writer.writerow(location)
