"""
Management command to enhance the CEP UI.

This script:
1. Updates the CEP dashboard template
2. Updates the CEP detail template
3. Updates the CEP create template

Usage:
    python manage.py enhance_cep_ui
"""
import os
import logging
from django.core.management.base import BaseCommand
from django.conf import settings

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Enhance the CEP UI'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Enhancing CEP UI...'))
        
        # Get template paths
        template_dir = os.path.join(settings.BASE_DIR, 'campaigns', 'templates', 'campaigns', 'cep')
        dashboard_template = os.path.join(template_dir, 'dashboard.html')
        detail_template = os.path.join(template_dir, 'detail.html')
        create_template = os.path.join(template_dir, 'create.html')
        
        # Update dashboard template
        self.stdout.write('Updating dashboard template...')
        self.update_dashboard_template(dashboard_template)
        
        # Update detail template
        self.stdout.write('Updating detail template...')
        self.update_detail_template(detail_template)
        
        # Update create template
        self.stdout.write('Updating create template...')
        self.update_create_template(create_template)
        
        self.stdout.write(self.style.SUCCESS('Successfully enhanced CEP UI!'))
    
    def update_dashboard_template(self, template_path):
        """
        Update the dashboard template with enhanced UI.
        """
        try:
            with open(template_path, 'r') as f:
                content = f.read()
            
            # Update active workflow card
            content = content.replace(
                '<div class="card shadow mb-4">',
                '<div class="card shadow mb-4 border-left-primary">'
            )
            
            # Update recent workflows card
            content = content.replace(
                '<div class="card shadow mb-4">',
                '<div class="card shadow mb-4 border-left-success">',
                1
            )
            
            # Update buttons
            content = content.replace(
                '<a href="{% url \'campaigns:cep_create\' %}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm">',
                '<a href="{% url \'campaigns:cep_create\' %}" class="d-none d-sm-inline-block btn btn-sm btn-primary shadow-sm mr-2">'
            )
            
            # Update table styling
            content = content.replace(
                '<table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">',
                '<table class="table table-bordered table-hover" id="dataTable" width="100%" cellspacing="0">'
            )
            
            # Add status badges
            content = content.replace(
                '<td>{{ workflow.get_status_display }}</td>',
                """<td>
                    {% if workflow.status == 'pending' %}
                        <span class="badge badge-info">{{ workflow.get_status_display }}</span>
                    {% elif workflow.status == 'running' %}
                        <span class="badge badge-success">{{ workflow.get_status_display }}</span>
                    {% elif workflow.status == 'paused' %}
                        <span class="badge badge-warning">{{ workflow.get_status_display }}</span>
                    {% elif workflow.status == 'completed' %}
                        <span class="badge badge-primary">{{ workflow.get_status_display }}</span>
                    {% elif workflow.status == 'failed' %}
                        <span class="badge badge-danger">{{ workflow.get_status_display }}</span>
                    {% else %}
                        <span class="badge badge-secondary">{{ workflow.get_status_display }}</span>
                    {% endif %}
                </td>"""
            )
            
            # Add subscription tier badges
            content = content.replace(
                '<td>{{ workflow.get_subscription_tier_display }}</td>',
                """<td>
                    {% if workflow.subscription_tier == 'bronze' %}
                        <span class="badge badge-warning">{{ workflow.get_subscription_tier_display }}</span>
                    {% elif workflow.subscription_tier == 'silver' %}
                        <span class="badge badge-secondary">{{ workflow.get_subscription_tier_display }}</span>
                    {% elif workflow.subscription_tier == 'gold' %}
                        <span class="badge badge-warning" style="background-color: #FFD700;">{{ workflow.get_subscription_tier_display }}</span>
                    {% endif %}
                </td>"""
            )
            
            # Update action buttons
            content = content.replace(
                '<a href="{% url \'campaigns:cep_detail\' pk=workflow.id %}" class="btn btn-sm btn-primary">View</a>',
                '<a href="{% url \'campaigns:cep_detail\' pk=workflow.id %}" class="btn btn-sm btn-primary"><i class="fas fa-eye fa-sm"></i> View</a>'
            )
            
            with open(template_path, 'w') as f:
                f.write(content)
                
            self.stdout.write(self.style.SUCCESS(f'Updated {template_path}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error updating dashboard template: {str(e)}'))
            logger.exception('Error updating dashboard template')
    
    def update_detail_template(self, template_path):
        """
        Update the detail template with enhanced UI.
        """
        try:
            with open(template_path, 'r') as f:
                content = f.read()
            
            # Update cards
            content = content.replace(
                '<div class="card shadow mb-4">',
                '<div class="card shadow mb-4 border-left-primary">'
            )
            
            # Update buttons
            content = content.replace(
                '<a href="{% url \'campaigns:cep_dashboard\' %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">',
                '<a href="{% url \'campaigns:cep_dashboard\' %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm mr-2">'
            )
            
            # Update progress bars
            content = content.replace(
                '<div class="progress-bar" role="progressbar"',
                '<div class="progress-bar bg-success" role="progressbar"'
            )
            
            # Add status badge
            content = content.replace(
                '<p><strong>Status:</strong> {{ workflow.get_status_display }}</p>',
                """<p><strong>Status:</strong> 
                    {% if workflow.status == 'pending' %}
                        <span class="badge badge-info">{{ workflow.get_status_display }}</span>
                    {% elif workflow.status == 'running' %}
                        <span class="badge badge-success">{{ workflow.get_status_display }}</span>
                    {% elif workflow.status == 'paused' %}
                        <span class="badge badge-warning">{{ workflow.get_status_display }}</span>
                    {% elif workflow.status == 'completed' %}
                        <span class="badge badge-primary">{{ workflow.get_status_display }}</span>
                    {% elif workflow.status == 'failed' %}
                        <span class="badge badge-danger">{{ workflow.get_status_display }}</span>
                    {% else %}
                        <span class="badge badge-secondary">{{ workflow.get_status_display }}</span>
                    {% endif %}
                </p>"""
            )
            
            # Add subscription tier badge
            content = content.replace(
                '<p><strong>Subscription Tier:</strong> {{ workflow.get_subscription_tier_display }}</p>',
                """<p><strong>Subscription Tier:</strong> 
                    {% if workflow.subscription_tier == 'bronze' %}
                        <span class="badge badge-warning">{{ workflow.get_subscription_tier_display }}</span>
                    {% elif workflow.subscription_tier == 'silver' %}
                        <span class="badge badge-secondary">{{ workflow.get_subscription_tier_display }}</span>
                    {% elif workflow.subscription_tier == 'gold' %}
                        <span class="badge badge-warning" style="background-color: #FFD700;">{{ workflow.get_subscription_tier_display }}</span>
                    {% endif %}
                </p>"""
            )
            
            with open(template_path, 'w') as f:
                f.write(content)
                
            self.stdout.write(self.style.SUCCESS(f'Updated {template_path}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error updating detail template: {str(e)}'))
            logger.exception('Error updating detail template')
    
    def update_create_template(self, template_path):
        """
        Update the create template with enhanced UI.
        """
        try:
            with open(template_path, 'r') as f:
                content = f.read()
            
            # Update cards
            content = content.replace(
                '<div class="card shadow mb-4">',
                '<div class="card shadow mb-4 border-left-primary">'
            )
            
            # Update buttons
            content = content.replace(
                '<a href="{% url \'campaigns:cep_dashboard\' %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm">',
                '<a href="{% url \'campaigns:cep_dashboard\' %}" class="d-none d-sm-inline-block btn btn-sm btn-secondary shadow-sm mr-2">'
            )
            
            # Update form styling
            content = content.replace(
                '<div class="form-group">',
                '<div class="form-group mb-4">'
            )
            
            # Update subscription tier selection
            content = content.replace(
                '<div class="form-group">',
                """<div class="form-group mb-4">
                    <label for="id_subscription_tier" class="font-weight-bold">Subscription Tier</label>
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header bg-warning text-white">
                                    <h6 class="m-0 font-weight-bold">Bronze</h6>
                                </div>
                                <div class="card-body">
                                    <p>Basic follow and like actions</p>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="bronze" name="subscription_tier" value="bronze" class="custom-control-input" checked>
                                        <label class="custom-control-label" for="bronze">Select Bronze</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="m-0 font-weight-bold">Silver</h6>
                                </div>
                                <div class="card-body">
                                    <p>Follow, like, and comment actions</p>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="silver" name="subscription_tier" value="silver" class="custom-control-input">
                                        <label class="custom-control-label" for="silver">Select Silver</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card mb-3">
                                <div class="card-header text-white" style="background-color: #FFD700;">
                                    <h6 class="m-0 font-weight-bold">Gold</h6>
                                </div>
                                <div class="card-body">
                                    <p>All engagement actions (follow, like, comment, DM)</p>
                                    <div class="custom-control custom-radio">
                                        <input type="radio" id="gold" name="subscription_tier" value="gold" class="custom-control-input">
                                        <label class="custom-control-label" for="gold">Select Gold</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>""",
                1
            )
            
            with open(template_path, 'w') as f:
                f.write(content)
                
            self.stdout.write(self.style.SUCCESS(f'Updated {template_path}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error updating create template: {str(e)}'))
            logger.exception('Error updating create template')
