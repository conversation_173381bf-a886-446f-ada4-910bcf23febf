"""
Management command to generate all demo data for testing.
"""
from django.core.management.base import BaseCommand
from django.core.management import call_command

class Command(BaseCommand):
    help = "Generate all demo data for testing the application"

    def add_arguments(self, parser):
        parser.add_argument(
            "--campaigns",
            type=int,
            default=5,
            help="Number of campaigns to generate (default: 5)",
        )
        parser.add_argument(
            "--accounts",
            type=int,
            default=100,
            help="Number of accounts to generate (default: 100)",
        )
        parser.add_argument(
            "--executions",
            type=int,
            default=10,
            help="Number of workflow executions to generate (default: 10)",
        )
        parser.add_argument(
            "--tag_results",
            type=int,
            default=100,
            help="Number of tag analysis results to generate (default: 100)",
        )
        parser.add_argument(
            "--tags",
            type=int,
            default=10,
            help="Number of dynamic tags to generate if none exist (default: 10)",
        )
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing data before generating new data",
        )

    def handle(self, *args, **options):
        num_campaigns = options["campaigns"]
        num_accounts = options["accounts"]
        num_executions = options["executions"]
        num_tag_results = options["tag_results"]
        num_tags = options["tags"]
        clear_data = options["clear"]
        
        self.stdout.write(self.style.SUCCESS("Starting demo data generation..."))
        
        # Generate base campaign data
        self.stdout.write(self.style.SUCCESS("Generating campaign data..."))
        call_command(
            "generate_demo_data",
            campaigns=num_campaigns,
            accounts=num_accounts,
            clear=clear_data
        )
        
        # Generate workflow execution data
        self.stdout.write(self.style.SUCCESS("Generating workflow execution data..."))
        call_command(
            "generate_workflow_data",
            executions=num_executions,
            clear=clear_data
        )
        
        # Generate tag analysis results
        self.stdout.write(self.style.SUCCESS("Generating tag analysis results..."))
        call_command(
            "generate_tag_analysis",
            results=num_tag_results,
            tags=num_tags,
            clear=clear_data
        )
        
        self.stdout.write(self.style.SUCCESS(
            f"Successfully generated all demo data:\n"
            f"- {num_campaigns} campaigns\n"
            f"- {num_accounts} accounts\n"
            f"- {num_executions} workflow executions\n"
            f"- {num_tag_results} tag analysis results"
        ))
