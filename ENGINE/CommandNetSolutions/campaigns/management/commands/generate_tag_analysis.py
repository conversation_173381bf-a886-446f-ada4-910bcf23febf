"""
Management command to generate demo tag analysis results.
"""
import random
import uuid
from django.core.management.base import BaseCommand
from django.db import transaction
from campaigns.models import Campaign, TagAnalysisResult, DynamicTag
from instagram.models import Accounts

class Command(BaseCommand):
    help = "Generate demo tag analysis results for testing"

    def add_arguments(self, parser):
        parser.add_argument(
            "--results",
            type=int,
            default=100,
            help="Number of tag analysis results to generate (default: 100)",
        )
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing tag analysis results before generating new data",
        )
        parser.add_argument(
            "--tags",
            type=int,
            default=10,
            help="Number of dynamic tags to generate if none exist (default: 10)",
        )

    def handle(self, *args, **options):
        num_results = options["results"]
        clear_data = options["clear"]
        num_tags = options["tags"]

        if clear_data:
            self.clear_existing_data()
            self.stdout.write(self.style.SUCCESS("Cleared existing tag analysis results"))

        # Get campaigns
        try:
            # Try to get campaigns using our custom manager
            campaigns = list(Campaign.objects.all())
            self.stdout.write(self.style.SUCCESS(f"Found {len(campaigns)} campaigns"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"Error getting campaigns: {str(e)}"))
            # Try a more direct approach using raw SQL
            from django.db import connection
            campaigns = []
            try:
                with connection.cursor() as cursor:
                    cursor.execute("""
                        SELECT id, name, description, status, target_type, audience_type,
                               created_at, updated_at, airflow_run_id, creator_id,
                               is_favorite, airflow_dag_id, dmp_conf
                        FROM campaigns_campaign
                    """)
                    rows = cursor.fetchall()
                    for row in rows:
                        campaign = Campaign()
                        campaign.id = row[0]
                        campaign.name = row[1]
                        campaign.description = row[2]
                        campaign.status = row[3]
                        campaign.target_type = row[4]
                        campaign.audience_type = row[5]
                        campaign.created_at = row[6]
                        campaign.updated_at = row[7]
                        campaign.airflow_run_id = row[8]
                        campaign.creator_id = row[9]
                        campaign.is_favorite = row[10]
                        campaign.airflow_dag_id = row[11]
                        campaign.dmp_conf = row[12]
                        campaigns.append(campaign)
                    self.stdout.write(self.style.SUCCESS(f"Found {len(campaigns)} campaigns using raw SQL"))
            except Exception as e2:
                self.stdout.write(self.style.ERROR(f"Error getting campaigns using raw SQL: {str(e2)}"))
                # Create a dummy campaign if we can't get any
                if not campaigns:
                    self.stdout.write(self.style.WARNING("Creating a dummy campaign for tag analysis"))
                    dummy_campaign = Campaign()
                    dummy_campaign.id = uuid.uuid4()
                    dummy_campaign.name = "Dummy Campaign"
                    dummy_campaign.description = "Dummy campaign for tag analysis"
                    dummy_campaign.status = "draft"
                    dummy_campaign.target_type = "location"
                    dummy_campaign.audience_type = "profile"
                    dummy_campaign.airflow_run_id = "pending"
                    dummy_campaign.is_favorite = False
                    dummy_campaign.dmp_conf = {}
                    campaigns.append(dummy_campaign)

        if not campaigns:
            self.stdout.write(self.style.ERROR("No campaigns found. Please create campaigns first."))
            return

        # Get accounts
        accounts = list(Accounts.objects.all())

        if not accounts:
            self.stdout.write(self.style.ERROR("No accounts found. Please create accounts first."))
            return

        # Get or create dynamic tags
        tags = list(DynamicTag.objects.all())

        if not tags:
            self.stdout.write(self.style.WARNING("No dynamic tags found. Creating demo tags."))
            tags = self.create_dynamic_tags(num_tags)

        # Create tag analysis results
        self.create_tag_analysis_results(num_results, campaigns, accounts, tags)

        self.stdout.write(self.style.SUCCESS(
            f"Successfully generated {num_results} tag analysis results"
        ))

    def clear_existing_data(self):
        """Clear existing tag analysis results"""
        TagAnalysisResult.objects.all().delete()

    def create_dynamic_tags(self, num_tags):
        """Create demo dynamic tags"""
        self.stdout.write(f"Creating {num_tags} demo dynamic tags")

        # Tag types
        tag_types = ["keyword", "regex", "sentiment", "category", "ml", "nlp"]

        # Fields
        fields = ["bio", "full_name", "interests", "account_type"]

        # Confidence levels
        confidence_levels = ["high", "medium", "low"]

        # Tag names and patterns
        tag_data = [
            {"name": "travel", "pattern": "travel|vacation|trip|journey|adventure"},
            {"name": "food", "pattern": "food|cooking|recipe|chef|restaurant"},
            {"name": "fitness", "pattern": "fitness|gym|workout|exercise|health"},
            {"name": "fashion", "pattern": "fashion|style|clothing|outfit|model"},
            {"name": "tech", "pattern": "tech|technology|computer|programming|developer"},
            {"name": "art", "pattern": "art|artist|creative|design|drawing"},
            {"name": "music", "pattern": "music|musician|band|concert|song"},
            {"name": "business", "pattern": "business|entrepreneur|startup|company|ceo"},
            {"name": "education", "pattern": "education|student|school|university|learning"},
            {"name": "family", "pattern": "family|parent|child|kid|baby"},
            {"name": "sports", "pattern": "sports|athlete|team|game|competition"},
            {"name": "beauty", "pattern": "beauty|makeup|cosmetics|skincare|hair"},
            {"name": "photography", "pattern": "photography|photographer|camera|photo|picture"},
            {"name": "nature", "pattern": "nature|outdoor|wildlife|animal|plant"},
            {"name": "gaming", "pattern": "gaming|gamer|videogame|esports|streamer"}
        ]

        tags = []

        with transaction.atomic():
            for i in range(min(num_tags, len(tag_data))):
                data = tag_data[i]

                tag = DynamicTag.objects.create(
                    id=uuid.uuid4(),
                    name=data["name"],
                    description=f"Demo tag for {data['name']} content",
                    tag_type=random.choice(tag_types),
                    pattern=data["pattern"],
                    field=random.choice(fields),
                    is_global=random.random() < 0.5,  # 50% chance of being global
                    confidence_level=random.choice(confidence_levels),
                    weight=random.uniform(0.5, 2.0)
                )

                tags.append(tag)

        return tags

    def create_tag_analysis_results(self, num_results, campaigns, accounts, tags):
        """Create demo tag analysis results"""
        self.stdout.write(f"Creating {num_results} demo tag analysis results")

        # Analysis modes
        analysis_modes = ["automatic", "manual", "hybrid"]

        # Create results
        with transaction.atomic():
            for i in range(num_results):
                # Select a random campaign
                campaign = random.choice(campaigns)

                # Select a random account
                account = random.choice(accounts)

                # Select a random tag
                tag = random.choice(tags)

                # Determine if tag matched
                matched = random.random() < 0.6  # 60% chance of matching

                # Generate confidence score
                confidence_score = random.uniform(0.5, 1.0) if matched else random.uniform(0.1, 0.4)

                # Generate match details
                match_details = {}
                if matched:
                    match_details = {
                        "field": tag.field,
                        "pattern": tag.pattern,
                        "value": f"Sample content containing {tag.name}",
                        "score": confidence_score
                    }

                # Create tag analysis result
                try:
                    TagAnalysisResult.objects.create(
                        id=uuid.uuid4(),
                        account=account,
                        campaign=campaign,
                        tag=tag,
                        matched=matched,
                        confidence_score=confidence_score,
                        match_details=match_details,
                        analysis_mode=random.choice(analysis_modes)
                    )
                except Exception as e:
                    # Skip if unique constraint violated (account-campaign-tag combination already exists)
                    self.stdout.write(self.style.WARNING(f"Skipped result: {str(e)}"))
