"""
Management command to populate the backend with predefined data.

This script:
1. Creates tag categories
2. Creates tag groups
3. Creates tags
4. Associates tags with categories and groups

Usage:
    python manage.py populate_backend
"""
import uuid
import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from django.contrib.auth.models import User
from django.utils import timezone

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Populate the backend with predefined data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before populating'
        )

    def handle(self, *args, **options):
        clear_existing = options['clear']

        if clear_existing:
            self.stdout.write(self.style.WARNING('Clearing existing data...'))
            from django.core.management import call_command
            call_command('empty_backend', confirm=True, keep_users=True)

        self.stdout.write(self.style.SUCCESS('Starting to populate the backend...'))

        try:
            # Import models
            from campaigns.models import (
                TagCategory, TagGroup, DynamicTag
            )

            # Get or create admin user
            admin_user, created = User.objects.get_or_create(
                username='admin',
                defaults={
                    'is_staff': True,
                    'is_superuser': True,
                    'email': '<EMAIL>'
                }
            )
            if created:
                admin_user.set_password('admin')
                admin_user.save()
                self.stdout.write(self.style.SUCCESS('Created admin user'))

            # Create tag categories
            categories = self.create_tag_categories()
            self.stdout.write(self.style.SUCCESS(f'Created {len(categories)} tag categories'))

            # Create tag groups
            tag_groups = self.create_tag_groups()
            self.stdout.write(self.style.SUCCESS(f'Created {len(tag_groups)} tag groups'))

            # Create tags
            tags = self.create_tags(categories, tag_groups)
            self.stdout.write(self.style.SUCCESS(f'Created {len(tags)} tags'))

            self.stdout.write(self.style.SUCCESS('Successfully populated the backend!'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error populating backend: {str(e)}'))
            raise

    def create_tag_categories(self):
        """Create predefined tag categories"""
        from campaigns.models import TagCategory

        categories = []
        category_data = [
            {'name': 'Engagement', 'description': 'Tags related to user engagement metrics', 'color': '#3498db', 'priority': 10, 'icon': 'chart-line'},
            {'name': 'Demographics', 'description': 'Tags related to user demographics', 'color': '#2ecc71', 'priority': 20, 'icon': 'users'},
            {'name': 'Content', 'description': 'Tags related to content types', 'color': '#e74c3c', 'priority': 30, 'icon': 'image'},
            {'name': 'Behavior', 'description': 'Tags related to user behavior', 'color': '#f39c12', 'priority': 40, 'icon': 'user-clock'},
            {'name': 'Interests', 'description': 'Tags related to user interests', 'color': '#9b59b6', 'priority': 50, 'icon': 'heart'},
            {'name': 'Location', 'description': 'Tags related to user location', 'color': '#1abc9c', 'priority': 60, 'icon': 'map-marker-alt'},
        ]

        for data in category_data:
            category, created = TagCategory.objects.get_or_create(
                name=data['name'],
                defaults={
                    'id': uuid.uuid4(),
                    'description': data['description'],
                    'color': data['color'],
                    'priority': data['priority'],
                    'icon': data['icon']
                }
            )
            categories.append(category)

        return categories

    def create_tag_groups(self):
        """Create predefined tag groups"""
        from campaigns.models import TagGroup

        groups = []
        group_data = [
            {'name': 'High Value', 'description': 'Tags for high-value accounts', 'color': '#3498db', 'is_global': True, 'priority': 10},
            {'name': 'Influencers', 'description': 'Tags for influencer accounts', 'color': '#2ecc71', 'is_global': True, 'priority': 20},
            {'name': 'Potential Customers', 'description': 'Tags for potential customer accounts', 'color': '#e74c3c', 'is_global': False, 'priority': 30},
            {'name': 'Competitors', 'description': 'Tags for competitor accounts', 'color': '#f39c12', 'is_global': False, 'priority': 40},
            {'name': 'Brand Advocates', 'description': 'Tags for brand advocate accounts', 'color': '#9b59b6', 'is_global': True, 'priority': 50},
            {'name': 'Content Creators', 'description': 'Tags for content creator accounts', 'color': '#1abc9c', 'is_global': True, 'priority': 60},
        ]

        for data in group_data:
            try:
                group, created = TagGroup.objects.get_or_create(
                    name=data['name'],
                    defaults={
                        'id': uuid.uuid4(),
                        'description': data['description'],
                        'color': data['color'],
                        'is_global': data['is_global'],
                        'priority': data['priority']
                    }
                )
                groups.append(group)
            except Exception as e:
                self.stdout.write(self.style.WARNING(f"Error creating tag group {data['name']}: {str(e)}"))

        return groups

    def create_tags(self, categories, tag_groups):
        """Create predefined tags"""
        from campaigns.models import DynamicTag

        tags = []
        tag_data = [
            # Engagement tags
            {'name': 'High Engagement', 'category': 'Engagement', 'group': 'High Value', 'tag_type': 'keyword', 'pattern': 'engagement > 5%', 'field': 'engagement_rate'},
            {'name': 'Active Commenter', 'category': 'Engagement', 'group': 'High Value', 'tag_type': 'keyword', 'pattern': 'comments > 10', 'field': 'comments_count'},
            {'name': 'High Likes', 'category': 'Engagement', 'group': 'High Value', 'tag_type': 'keyword', 'pattern': 'likes > 100', 'field': 'likes_count'},

            # Demographics tags
            {'name': 'Age 18-24', 'category': 'Demographics', 'group': 'Potential Customers', 'tag_type': 'regex', 'pattern': '18-24|college|university', 'field': 'bio'},
            {'name': 'Female', 'category': 'Demographics', 'group': 'Potential Customers', 'tag_type': 'keyword', 'pattern': 'she/her|woman|girl', 'field': 'bio'},
            {'name': 'Male', 'category': 'Demographics', 'group': 'Potential Customers', 'tag_type': 'keyword', 'pattern': 'he/him|man|boy', 'field': 'bio'},

            # Content tags
            {'name': 'Fashion Content', 'category': 'Content', 'group': 'Influencers', 'tag_type': 'keyword', 'pattern': 'fashion|style|outfit', 'field': 'bio'},
            {'name': 'Travel Content', 'category': 'Content', 'group': 'Influencers', 'tag_type': 'keyword', 'pattern': 'travel|wanderlust|adventure', 'field': 'bio'},
            {'name': 'Food Content', 'category': 'Content', 'group': 'Influencers', 'tag_type': 'keyword', 'pattern': 'food|foodie|cooking|chef', 'field': 'bio'},

            # Behavior tags
            {'name': 'Frequent Poster', 'category': 'Behavior', 'group': 'Content Creators', 'tag_type': 'keyword', 'pattern': 'posts > 100', 'field': 'posts_count'},
            {'name': 'New Account', 'category': 'Behavior', 'group': 'Potential Customers', 'tag_type': 'keyword', 'pattern': 'account_age < 90', 'field': 'account_age'},
            {'name': 'Consistent Poster', 'category': 'Behavior', 'group': 'Content Creators', 'tag_type': 'keyword', 'pattern': 'posting_frequency < 3', 'field': 'posting_frequency'},

            # Interests tags
            {'name': 'Fitness Enthusiast', 'category': 'Interests', 'group': 'Potential Customers', 'tag_type': 'keyword', 'pattern': 'fitness|gym|workout|exercise', 'field': 'bio'},
            {'name': 'Tech Enthusiast', 'category': 'Interests', 'group': 'Potential Customers', 'tag_type': 'keyword', 'pattern': 'tech|technology|coding|developer', 'field': 'bio'},
            {'name': 'Art Lover', 'category': 'Interests', 'group': 'Potential Customers', 'tag_type': 'keyword', 'pattern': 'art|artist|creative|design', 'field': 'bio'},

            # Location tags
            {'name': 'New York Based', 'category': 'Location', 'group': 'Potential Customers', 'tag_type': 'keyword', 'pattern': 'new york|nyc|brooklyn|manhattan', 'field': 'bio'},
            {'name': 'Los Angeles Based', 'category': 'Location', 'group': 'Potential Customers', 'tag_type': 'keyword', 'pattern': 'los angeles|la|hollywood|california', 'field': 'bio'},
            {'name': 'London Based', 'category': 'Location', 'group': 'Potential Customers', 'tag_type': 'keyword', 'pattern': 'london|uk|england|british', 'field': 'bio'},
        ]

        # Create a mapping of category names to category objects
        category_map = {category.name: category for category in categories}

        # Create a mapping of group names to group objects
        group_map = {group.name: group for group in tag_groups}

        for data in tag_data:
            # Get category and group objects
            category = category_map.get(data['category'])
            group = group_map.get(data['group'])

            if not category or not group:
                continue

            tag, created = DynamicTag.objects.get_or_create(
                name=data['name'],
                defaults={
                    'id': uuid.uuid4(),
                    'description': f"Tag for {data['name']}",
                    'category': category,
                    'tag_group': group,
                    'tag_type': data['tag_type'],
                    'pattern': data['pattern'],
                    'field': data['field'],
                    'is_global': group.is_global,
                    'weight': 1.0
                }
            )
            tags.append(tag)

        return tags
