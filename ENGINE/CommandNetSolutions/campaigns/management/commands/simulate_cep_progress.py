"""
Management command to simulate CEP workflow progress.

This script:
1. Updates the status of a CEP workflow
2. Updates progress metrics for different action types
3. Simulates workflow completion

Usage:
    python manage.py simulate_cep_progress --workflow-id <workflow_id> [--complete]
"""
import logging
import random
import time
from django.core.management.base import BaseCommand
from django.utils import timezone

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Simulate CEP workflow progress'

    def add_arguments(self, parser):
        parser.add_argument(
            '--workflow-id',
            type=str,
            required=True,
            help='ID of the CEP workflow to simulate progress for'
        )
        parser.add_argument(
            '--complete',
            action='store_true',
            help='Complete the workflow simulation'
        )
        parser.add_argument(
            '--steps',
            type=int,
            default=10,
            help='Number of progress steps to simulate'
        )

    def handle(self, *args, **options):
        workflow_id = options['workflow_id']
        complete = options['complete']
        steps = options['steps']
        
        self.stdout.write(self.style.SUCCESS(f'Simulating progress for CEP workflow {workflow_id}...'))
        
        try:
            # Import models
            from campaigns.models.cep import CEPWorkflow
            
            # Get workflow
            workflow = CEPWorkflow.objects.get(id=workflow_id)
            
            # Check if workflow is in a state that can be updated
            if workflow.status not in ['pending', 'running', 'paused']:
                self.stdout.write(self.style.ERROR(
                    f'Cannot simulate progress for workflow with status {workflow.get_status_display()}.'
                ))
                return
            
            # Start the workflow if it's pending
            if workflow.status == 'pending':
                workflow.start()
                self.stdout.write(f'Started workflow: {workflow.get_status_display()}')
            
            # Get available actions based on subscription tier
            available_actions = []
            if workflow.subscription_tier in ['bronze', 'silver', 'gold']:
                available_actions.extend(['follow', 'like'])
            if workflow.subscription_tier in ['silver', 'gold']:
                available_actions.append('comment')
            if workflow.subscription_tier in ['gold']:
                available_actions.append('dm')
            
            self.stdout.write(f'Available actions: {", ".join(available_actions)}')
            
            # Simulate progress
            for step in range(1, steps + 1):
                self.stdout.write(f'Simulating step {step}/{steps}...')
                
                # Update progress for each action type
                for action in available_actions:
                    current_progress = getattr(workflow, f'{action}_progress')
                    new_progress = min(100, current_progress + random.uniform(5, 15))
                    workflow.update_progress(action, new_progress)
                    self.stdout.write(f'  - {action.capitalize()} progress: {new_progress:.1f}%')
                
                # Pause to simulate real-time updates
                time.sleep(1)
            
            # Complete the workflow if requested
            if complete:
                # Set all progress to 100%
                for action in available_actions:
                    workflow.update_progress(action, 100.0)
                
                # Complete the workflow
                workflow.status = 'completed'
                workflow.completed_at = timezone.now()
                workflow.save()
                
                self.stdout.write(self.style.SUCCESS('Workflow completed successfully!'))
            
            self.stdout.write(self.style.SUCCESS(f'Successfully simulated progress for CEP workflow.'))
            self.stdout.write(f'Current status: {workflow.get_status_display()}')
            self.stdout.write(f'Overall progress: {workflow.overall_progress:.1f}%')
            
        except CEPWorkflow.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'CEP workflow with ID {workflow_id} does not exist.'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error simulating CEP workflow progress: {str(e)}'))
            logger.exception('Error simulating CEP workflow progress')
            raise
