"""
Management command to create a CEP workflow.

This script:
1. Creates a CEP workflow for a campaign
2. Sets the subscription tier
3. Initializes progress metrics

Usage:
    python manage.py create_cep_workflow --campaign-id <campaign_id> --tier <tier>
"""
import logging
from django.core.management.base import BaseCommand
from django.utils import timezone

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create a CEP workflow'

    def add_arguments(self, parser):
        parser.add_argument(
            '--campaign-id',
            type=str,
            required=True,
            help='ID of the campaign to create a CEP workflow for'
        )
        parser.add_argument(
            '--tier',
            type=str,
            choices=['bronze', 'silver', 'gold'],
            default='bronze',
            help='Subscription tier for the CEP workflow'
        )

    def handle(self, *args, **options):
        campaign_id = options['campaign_id']
        tier = options['tier']
        
        self.stdout.write(self.style.SUCCESS(f'Creating CEP workflow for campaign {campaign_id} with {tier} tier...'))
        
        try:
            # Import models
            from campaigns.models import Campaign
            from campaigns.models.cep import CEPWorkflow
            
            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)
            
            # Check if campaign already has a CEP workflow
            if hasattr(campaign, 'cep_workflow'):
                self.stdout.write(self.style.ERROR(f'Campaign {campaign.name} already has a CEP workflow.'))
                return
            
            # Check if there's already an active CEP workflow
            active_workflow = CEPWorkflow.objects.filter(
                status__in=['pending', 'running', 'paused']
            ).first()
            
            if active_workflow:
                self.stdout.write(self.style.ERROR(
                    f'Cannot create new CEP workflow. There is already an active workflow for campaign '
                    f'"{active_workflow.campaign.name}".'
                ))
                return
            
            # Create CEP workflow
            workflow = CEPWorkflow.objects.create(
                campaign=campaign,
                subscription_tier=tier,
                status='pending',
                whitelist_count=campaign.results.first().total_accounts_whitelisted if campaign.results.exists() else 0
            )
            
            self.stdout.write(self.style.SUCCESS(
                f'Successfully created CEP workflow for campaign {campaign.name} with {tier} tier.'
            ))
            self.stdout.write(f'Workflow ID: {workflow.id}')
            self.stdout.write(f'Status: {workflow.get_status_display()}')
            self.stdout.write(f'Whitelist count: {workflow.whitelist_count}')
            
        except Campaign.DoesNotExist:
            self.stdout.write(self.style.ERROR(f'Campaign with ID {campaign_id} does not exist.'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error creating CEP workflow: {str(e)}'))
            logger.exception('Error creating CEP workflow')
            raise
