import uuid
import json
from django.core.management.base import BaseCommand
from django.db import transaction
from campaigns.models import TagCategory, TagGroup, DynamicTag, CampaignTagRule, TagRuleCondition

class Command(BaseCommand):
    help = "Delete all existing tags and repopulate with predefined tags and tag groups"

    def add_arguments(self, parser):
        parser.add_argument(
            "--force",
            action="store_true",
            help="Force deletion without confirmation",
        )

    def handle(self, *args, **options):
        force = options["force"]

        if not force:
            self.stdout.write(self.style.WARNING(
                "This will delete ALL existing tags, tag groups, and tag categories. "
                "This action cannot be undone."
            ))
            confirm = input("Are you sure you want to continue? [y/N]: ")
            if confirm.lower() != 'y':
                self.stdout.write(self.style.SUCCESS("Operation cancelled."))
                return

        # Delete existing data
        self.clear_existing_data()

        # Create new data
        with transaction.atomic():
            # Create tag categories
            categories = self.create_tag_categories()

            # Create tag groups
            groups = self.create_tag_groups()

            # Create tags
            self.create_tags(categories, groups)

        self.stdout.write(self.style.SUCCESS("Successfully reset and repopulated tags."))

    def clear_existing_data(self):
        """Clear existing tag data"""
        self.stdout.write("Deleting existing tag data...")

        # Delete in proper order to avoid foreign key constraints
        self.stdout.write("Deleting existing tag conditions...")
        TagRuleCondition.objects.all().delete()

        self.stdout.write("Deleting existing tag rules...")
        CampaignTagRule.objects.all().delete()

        self.stdout.write("Deleting existing tags...")
        DynamicTag.objects.all().delete()

        self.stdout.write("Deleting existing tag groups...")
        TagGroup.objects.all().delete()

        self.stdout.write("Deleting existing tag categories...")
        TagCategory.objects.all().delete()

        self.stdout.write(self.style.SUCCESS("Existing tag data deleted."))

    def create_tag_categories(self):
        """Create predefined tag categories"""
        self.stdout.write("Creating tag categories...")

        categories = []
        category_data = [
            {"name": "Demographics", "description": "Tags related to demographic information", "color": "#6c757d", "icon": "users", "priority": 5},
            {"name": "Interests", "description": "Tags related to user interests and hobbies", "color": "#28a745", "icon": "heart", "priority": 4},
            {"name": "Behavior", "description": "Tags related to user behavior and activity", "color": "#007bff", "icon": "chart-line", "priority": 3},
            {"name": "Engagement", "description": "Tags related to user engagement metrics", "color": "#fd7e14", "icon": "comments", "priority": 2},
            {"name": "Business", "description": "Tags related to business and professional accounts", "color": "#6610f2", "icon": "briefcase", "priority": 1},
            {"name": "Content", "description": "Tags related to content type and quality", "color": "#e83e8c", "icon": "image", "priority": 0},
        ]

        for data in category_data:
            category = TagCategory.objects.create(
                id=uuid.uuid4(),
                name=data["name"],
                description=data["description"],
                color=data["color"],
                icon=data["icon"],
                priority=data["priority"]
            )
            categories.append(category)
            self.stdout.write(f"  - Created category: {category.name}")

        return {cat.name: cat for cat in categories}

    def create_tag_groups(self):
        """Create predefined tag groups"""
        self.stdout.write("Creating tag groups...")

        groups = []
        group_data = [
            {"name": "Marketing", "description": "Tags useful for marketing campaigns", "is_global": True},
            {"name": "Influencers", "description": "Tags for identifying and categorizing influencers", "is_global": True},
            {"name": "Engagement", "description": "Tags related to user engagement levels", "is_global": True},
            {"name": "Content Creators", "description": "Tags for identifying content creators by type", "is_global": True},
            {"name": "Audience Analysis", "description": "Tags for analyzing audience characteristics", "is_global": True},
        ]

        for data in group_data:
            group = TagGroup.objects.create(
                id=uuid.uuid4(),
                name=data["name"],
                description=data["description"],
                is_global=data["is_global"]
            )
            groups.append(group)
            self.stdout.write(f"  - Created group: {group.name}")

        return {group.name: group for group in groups}

    def create_tags(self, categories, groups):
        """Create predefined tags with conditions"""
        self.stdout.write("Creating predefined tags...")

        # Define tag data by category
        tag_data = {
            "Demographics": [
                {
                    "name": "age_18_24",
                    "description": "Users aged 18-24",
                    "is_global": True,
                    "groups": ["Audience Analysis"],
                    "conditions": [
                        {
                            "field_category": "text",
                            "field": "bio",
                            "field_type": "string",
                            "operator": "icontains",
                            "value": "college",
                            "required": False
                        },
                        {
                            "field_category": "text",
                            "field": "bio",
                            "field_type": "string",
                            "operator": "icontains",
                            "value": "university",
                            "required": False
                        }
                    ]
                },
                {
                    "name": "parents",
                    "description": "Users who are parents",
                    "is_global": True,
                    "groups": ["Audience Analysis", "Marketing"],
                    "conditions": [
                        {
                            "field_category": "text",
                            "field": "bio",
                            "field_type": "string",
                            "operator": "icontains",
                            "value": "parent",
                            "required": True
                        },
                        {
                            "field_category": "text",
                            "field": "bio",
                            "field_type": "string",
                            "operator": "icontains",
                            "value": "mom",
                            "required": False
                        },
                        {
                            "field_category": "text",
                            "field": "bio",
                            "field_type": "string",
                            "operator": "icontains",
                            "value": "dad",
                            "required": False
                        }
                    ]
                }
            ],
            "Interests": [
                {
                    "name": "travel",
                    "description": "Users interested in travel",
                    "is_global": True,
                    "groups": ["Marketing", "Content Creators"],
                    "conditions": [
                        {
                            "field_category": "text",
                            "field": "bio",
                            "field_type": "string",
                            "operator": "icontains",
                            "value": "travel",
                            "required": True
                        },
                        {
                            "field_category": "numeric",
                            "field": "followers",
                            "field_type": "number",
                            "operator": "gt",
                            "value": "1000",
                            "required": False
                        }
                    ]
                },
                {
                    "name": "fitness",
                    "description": "Users interested in fitness",
                    "is_global": True,
                    "groups": ["Marketing", "Content Creators"],
                    "conditions": [
                        {
                            "field_category": "text",
                            "field": "bio",
                            "field_type": "string",
                            "operator": "icontains",
                            "value": "fitness",
                            "required": True
                        },
                        {
                            "field_category": "text",
                            "field": "bio",
                            "field_type": "string",
                            "operator": "icontains",
                            "value": "gym",
                            "required": False
                        },
                        {
                            "field_category": "boolean",
                            "field": "is_verified",
                            "field_type": "boolean",
                            "operator": "is_true",
                            "value": "true",
                            "required": False
                        }
                    ]
                }
            ],
            "Engagement": [
                {
                    "name": "high_engagement",
                    "description": "Users with high engagement",
                    "is_global": True,
                    "groups": ["Influencers", "Engagement"],
                    "conditions": [
                        {
                            "field_category": "numeric",
                            "field": "followers",
                            "field_type": "number",
                            "operator": "gt",
                            "value": "10000",
                            "required": True
                        },
                        {
                            "field_category": "boolean",
                            "field": "is_verified",
                            "field_type": "boolean",
                            "operator": "is_true",
                            "value": "true",
                            "required": False
                        }
                    ]
                }
            ]
        }

        # Create tags
        tags_created = 0
        for category_name, tags in tag_data.items():
            category = categories.get(category_name)
            if not category:
                self.stdout.write(self.style.WARNING(f"Category '{category_name}' not found, skipping related tags."))
                continue

            for tag_info in tags:
                # Get tag groups
                tag_groups = []
                for group_name in tag_info.get("groups", []):
                    group = groups.get(group_name)
                    if group:
                        tag_groups.append(group)

                # Create tag
                tag = DynamicTag.objects.create(
                    id=uuid.uuid4(),
                    name=tag_info["name"],
                    description=tag_info["description"],
                    category=category,
                    tag_type="keyword",
                    field="bio",  # Default field
                    pattern="",  # Will be updated later
                    is_global=tag_info.get("is_global", True)
                )

                # Set the primary tag group (first in the list)
                if tag_groups:
                    tag.tag_group = tag_groups[0]
                    tag.save()

                # Create tag rule
                rule = CampaignTagRule.objects.create(
                    id=uuid.uuid4(),
                    name=tag_info["name"],
                    tag=tag_info["name"],
                    description=tag_info.get("description", ""),
                    active=True,
                    is_global=tag_info.get("is_global", True)
                )

                # Create conditions
                for i, condition_data in enumerate(tag_info.get("conditions", [])):
                    TagRuleCondition.objects.create(
                        id=uuid.uuid4(),
                        rule=rule,
                        field=condition_data.get("field", "bio"),
                        field_type=condition_data.get("field_type", "string"),
                        operator=condition_data.get("operator", "icontains"),
                        value=condition_data.get("value", ""),
                        score=1,
                        required=condition_data.get("required", True)
                    )

                # Update tag pattern with rule ID
                rule_data = {
                    "rule_id": str(rule.id),
                    "logic": "all"
                }
                tag.pattern = json.dumps(rule_data)
                tag.save(update_fields=["pattern"])

                # Add to all groups using the ManyToMany relationship
                for group in tag_groups:
                    group.tags.add(tag)

                tags_created += 1
                self.stdout.write(f"  - Created tag: {tag.name} with {len(tag_info.get('conditions', []))} conditions")

        self.stdout.write(self.style.SUCCESS(f"Created {tags_created} predefined tags"))
