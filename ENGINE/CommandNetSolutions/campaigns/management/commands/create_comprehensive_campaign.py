"""
Django management command to create a comprehensive campaign with realistic data
for testing tag application and whitelist generation.
"""
import random
import uuid
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone

from campaigns.models import Campaign, LocationTarget, UsernameTarget, DynamicTag, TagCategory, TagGroup
from instagram.models import Accounts, WhiteListEntry


class Command(BaseCommand):
    help = 'Create a comprehensive campaign with realistic, abundant data for testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--accounts',
            type=int,
            default=150,
            help='Number of accounts to create (default: 150)'
        )

    def handle(self, *args, **options):
        self.stdout.write("Creating comprehensive campaign with realistic data...")

        # Get or create test user
        user, created = User.objects.get_or_create(
            username="campaign_manager",
            defaults={
                "email": "<EMAIL>",
                "first_name": "Campaign",
                "last_name": "Manager"
            }
        )

        # Create campaign
        campaign = Campaign.objects.create(
            name="Global Tech & Lifestyle Influencers 2025",
            description="Comprehensive campaign targeting technology enthusiasts, lifestyle bloggers, entrepreneurs, and digital nomads across major global cities. Focus on authentic engagement with verified and high-quality accounts in the tech, business, travel, and lifestyle niches.",
            status="running",
            target_type="mixed",
            audience_type="both",
            creator=user
        )

        self.stdout.write(f"✅ Created campaign: {campaign.name}")
        self.stdout.write(f"   Campaign ID: {campaign.id}")

        # Add diverse location targets
        self.create_location_targets(campaign)

        # Add username targets
        self.create_username_targets(campaign)

        # Create tag categories and groups
        self.create_tag_system(campaign)

        # Create diverse accounts
        num_accounts = options['accounts']
        self.create_diverse_accounts(campaign, num_accounts)

        self.stdout.write(f"\n🎉 Campaign creation completed!")
        self.stdout.write(f"   Campaign: {campaign.name}")
        self.stdout.write(f"   Total Accounts: {num_accounts}")
        self.stdout.write(f"   Location Targets: {campaign.location_targets.count()}")
        self.stdout.write(f"   Username Targets: {campaign.username_targets.count()}")
        self.stdout.write(f"\n🔗 View campaign: http://127.0.0.1:8001/campaigns/{campaign.id}/")

    def create_location_targets(self, campaign):
        """Create diverse location targets"""
        locations = [
            # North America
            {"location_id": "*********", "city": "New York", "country": "United States"},
            {"location_id": "*********", "city": "Los Angeles", "country": "United States"},
            {"location_id": "*********", "city": "San Francisco", "country": "United States"},
            {"location_id": "*********", "city": "Austin", "country": "United States"},
            {"location_id": "*********", "city": "Toronto", "country": "Canada"},
            {"location_id": "*********", "city": "Vancouver", "country": "Canada"},

            # Europe
            {"location_id": "213385415", "city": "London", "country": "United Kingdom"},
            {"location_id": "213385417", "city": "Berlin", "country": "Germany"},
            {"location_id": "213385419", "city": "Amsterdam", "country": "Netherlands"},
            {"location_id": "*********", "city": "Stockholm", "country": "Sweden"},
            {"location_id": "*********", "city": "Paris", "country": "France"},
            {"location_id": "*********", "city": "Barcelona", "country": "Spain"},

            # Asia-Pacific
            {"location_id": "*********", "city": "Tokyo", "country": "Japan"},
            {"location_id": "*********", "city": "Singapore", "country": "Singapore"},
            {"location_id": "*********", "city": "Sydney", "country": "Australia"},
            {"location_id": "*********", "city": "Seoul", "country": "South Korea"},
            {"location_id": "*********", "city": "Hong Kong", "country": "Hong Kong"},
            {"location_id": "*********", "city": "Tel Aviv", "country": "Israel"},
        ]

        for loc in locations:
            LocationTarget.objects.create(campaign=campaign, **loc)

        self.stdout.write(f"✅ Created {len(locations)} location targets")

    def create_username_targets(self, campaign):
        """Create diverse username targets"""
        usernames = [
            # Tech Influencers
            {"username": "tech_guru_official", "audience_type": "followers"},
            {"username": "startup_founder_life", "audience_type": "both"},
            {"username": "digital_nomad_hub", "audience_type": "followers"},
            {"username": "ai_innovation_daily", "audience_type": "followers"},
            {"username": "crypto_insights_pro", "audience_type": "both"},

            # Lifestyle & Business
            {"username": "entrepreneur_mindset", "audience_type": "followers"},
            {"username": "productivity_hacks", "audience_type": "both"},
            {"username": "minimalist_lifestyle", "audience_type": "followers"},
            {"username": "travel_tech_nomad", "audience_type": "both"},
            {"username": "sustainable_living", "audience_type": "followers"},
        ]

        for username_data in usernames:
            UsernameTarget.objects.create(campaign=campaign, **username_data)

        self.stdout.write(f"✅ Created {len(usernames)} username targets")

    def create_tag_system(self, campaign):
        """Create comprehensive tag categories, groups, and tags"""
        # Create tag categories
        categories = [
            {"name": "Technology", "description": "Tech-related content and interests", "color": "#3498db"},
            {"name": "Business", "description": "Business and entrepreneurship", "color": "#2ecc71"},
            {"name": "Lifestyle", "description": "Lifestyle and personal interests", "color": "#e74c3c"},
            {"name": "Travel", "description": "Travel and location-based content", "color": "#f39c12"},
            {"name": "Content Quality", "description": "Account quality indicators", "color": "#9b59b6"},
        ]

        created_categories = {}
        for cat_data in categories:
            category, created = TagCategory.objects.get_or_create(
                name=cat_data["name"],
                defaults=cat_data
            )
            created_categories[cat_data["name"]] = category

        # Create tag groups
        groups = [
            {"name": "Tech Enthusiasts", "description": "Technology and innovation focused", "color": "#3498db"},
            {"name": "Business Leaders", "description": "Entrepreneurs and business professionals", "color": "#2ecc71"},
            {"name": "Digital Nomads", "description": "Remote work and travel lifestyle", "color": "#f39c12"},
            {"name": "High Quality", "description": "Premium account indicators", "color": "#9b59b6"},
        ]

        created_groups = {}
        for group_data in groups:
            group, created = TagGroup.objects.get_or_create(
                name=group_data["name"],
                defaults=group_data
            )
            created_groups[group_data["name"]] = group

        # Create comprehensive tags
        tags_data = [
            # Technology tags
            {"name": "AI Enthusiast", "description": "Interested in artificial intelligence",
             "category": "Technology", "group": "Tech Enthusiasts"},
            {"name": "Blockchain Expert", "description": "Cryptocurrency and blockchain content",
             "category": "Technology", "group": "Tech Enthusiasts"},
            {"name": "Software Developer", "description": "Programming and development content",
             "category": "Technology", "group": "Tech Enthusiasts"},
            {"name": "Tech Startup", "description": "Technology startup related",
             "category": "Technology", "group": "Tech Enthusiasts"},

            # Business tags
            {"name": "Entrepreneur", "description": "Business owner or startup founder",
             "category": "Business", "group": "Business Leaders"},
            {"name": "CEO/Founder", "description": "Company leadership role",
             "category": "Business", "group": "Business Leaders"},
            {"name": "Investor", "description": "Investment and finance focused",
             "category": "Business", "group": "Business Leaders"},
            {"name": "Business Coach", "description": "Business mentoring and coaching",
             "category": "Business", "group": "Business Leaders"},

            # Lifestyle tags
            {"name": "Digital Nomad", "description": "Remote work lifestyle",
             "category": "Lifestyle", "group": "Digital Nomads"},
            {"name": "Minimalist", "description": "Minimalist lifestyle content",
             "category": "Lifestyle", "group": "Digital Nomads"},
            {"name": "Productivity Expert", "description": "Productivity and efficiency content",
             "category": "Lifestyle", "group": "Digital Nomads"},
            {"name": "Wellness Advocate", "description": "Health and wellness focused",
             "category": "Lifestyle", "group": "Digital Nomads"},

            # Travel tags
            {"name": "Travel Blogger", "description": "Travel content creator",
             "category": "Travel", "group": "Digital Nomads"},
            {"name": "Location Independent", "description": "Works from anywhere",
             "category": "Travel", "group": "Digital Nomads"},

            # Quality indicators
            {"name": "Verified Account", "description": "Instagram verified account",
             "category": "Content Quality", "group": "High Quality"},
            {"name": "High Engagement", "description": "Strong engagement rates",
             "category": "Content Quality", "group": "High Quality"},
            {"name": "Authentic Content", "description": "Original, authentic content",
             "category": "Content Quality", "group": "High Quality"},
            {"name": "Professional Profile", "description": "Well-maintained professional profile",
             "category": "Content Quality", "group": "High Quality"},
        ]

        for tag_data in tags_data:
            category = created_categories[tag_data["category"]]
            group = created_groups[tag_data["group"]]

            DynamicTag.objects.get_or_create(
                name=tag_data["name"],
                defaults={
                    "description": tag_data["description"],
                    "category": category,
                    "tag_group": group,
                    "is_global": True,
                    "weight": random.uniform(0.8, 1.2)
                }
            )

        self.stdout.write(f"✅ Created tag system with {len(tags_data)} tags")

    def create_diverse_accounts(self, campaign, num_accounts):
        """Create diverse, realistic Instagram accounts"""

        # Account templates with realistic data
        account_templates = [
            # Tech Entrepreneurs
            {
                "username_prefix": "tech_founder",
                "full_names": ["Alex Chen", "Sarah Kim", "Marcus Johnson", "Elena Rodriguez", "David Park"],
                "bio_templates": [
                    "🚀 Building the future with AI | CEO @{company} | Angel Investor | {location}",
                    "💡 Tech entrepreneur | Founder of {company} | Passionate about innovation | {location}",
                    "🔥 Scaling startups | {company} CEO | Mentor | Speaker | Based in {location}",
                ],
                "interests": ["artificial intelligence", "startups", "venture capital", "innovation", "technology"],
                "account_type": "business",
                "follower_range": (5000, 50000),
                "following_range": (500, 2000),
                "posts_range": (100, 500),
                "verified_chance": 0.3,
                "tags": ["AI Enthusiast", "Entrepreneur", "Tech Startup", "CEO/Founder"]
            },

            # Digital Nomads
            {
                "username_prefix": "nomad_life",
                "full_names": ["Jake Wilson", "Maya Patel", "Chris Anderson", "Luna Martinez", "Sam Taylor"],
                "bio_templates": [
                    "🌍 Digital nomad | Remote work advocate | Currently in {location} | 📧 {email}",
                    "✈️ Location independent | Building {company} remotely | {location} this month",
                    "🏝️ Remote entrepreneur | Travel blogger | {location} → Next: TBD",
                ],
                "interests": ["remote work", "travel", "digital nomad", "productivity", "lifestyle"],
                "account_type": "personal",
                "follower_range": (2000, 25000),
                "following_range": (800, 3000),
                "posts_range": (200, 800),
                "verified_chance": 0.15,
                "tags": ["Digital Nomad", "Location Independent", "Travel Blogger", "Productivity Expert"]
            },

            # Business Coaches
            {
                "username_prefix": "business_coach",
                "full_names": ["Jennifer Smith", "Michael Brown", "Lisa Wang", "Robert Davis", "Amanda Lee"],
                "bio_templates": [
                    "💼 Business coach | Helping entrepreneurs scale | 📚 Author | {location}",
                    "🎯 Executive coach | Leadership development | Speaker | Based in {location}",
                    "📈 Business strategist | {company} founder | Mentor | {location}",
                ],
                "interests": ["business coaching", "leadership", "entrepreneurship", "strategy", "consulting"],
                "account_type": "business",
                "follower_range": (3000, 30000),
                "following_range": (400, 1500),
                "posts_range": (150, 600),
                "verified_chance": 0.2,
                "tags": ["Business Coach", "Entrepreneur", "Professional Profile", "High Engagement"]
            },

            # Software Developers
            {
                "username_prefix": "dev_life",
                "full_names": ["Kevin Zhang", "Rachel Green", "Tom Miller", "Priya Sharma", "Alex Thompson"],
                "bio_templates": [
                    "👨‍💻 Full-stack developer | Open source contributor | {location} | GitHub: @{username}",
                    "🔧 Software engineer @{company} | Python enthusiast | {location}",
                    "💻 Frontend developer | React specialist | Building cool stuff | {location}",
                ],
                "interests": ["programming", "software development", "open source", "technology", "coding"],
                "account_type": "personal",
                "follower_range": (1000, 15000),
                "following_range": (300, 1200),
                "posts_range": (80, 400),
                "verified_chance": 0.1,
                "tags": ["Software Developer", "Tech Startup", "AI Enthusiast", "Authentic Content"]
            },

            # Lifestyle Influencers
            {
                "username_prefix": "lifestyle_guru",
                "full_names": ["Sophie Clark", "Emma Johnson", "Olivia Brown", "Grace Wilson", "Chloe Davis"],
                "bio_templates": [
                    "🌱 Minimalist lifestyle | Wellness advocate | {location} | 📧 hello@{username}.com",
                    "✨ Living intentionally | Sustainable living | {location} | Blog: {username}.com",
                    "🧘‍♀️ Mindful living | Productivity tips | Based in {location} | DM for collabs",
                ],
                "interests": ["minimalism", "wellness", "sustainability", "mindfulness", "lifestyle"],
                "account_type": "personal",
                "follower_range": (5000, 40000),
                "following_range": (600, 2500),
                "posts_range": (300, 1000),
                "verified_chance": 0.25,
                "tags": ["Minimalist", "Wellness Advocate", "Authentic Content", "High Engagement"]
            },
        ]

        companies = ["TechFlow", "InnovateLab", "FutureStack", "CodeCraft", "DataDrive", "CloudSync", "AICore", "DevHub"]
        locations = ["San Francisco", "New York", "London", "Berlin", "Tokyo", "Singapore", "Austin", "Toronto"]

        created_accounts = 0
        accounts_per_template = num_accounts // len(account_templates)

        for template in account_templates:
            for i in range(accounts_per_template):
                # Generate unique username
                username = f"{template['username_prefix']}_{random.randint(100, 999)}"

                # Ensure username is unique
                while Accounts.objects.filter(username=username).exists():
                    username = f"{template['username_prefix']}_{random.randint(100, 999)}"

                # Select random data from template
                full_name = random.choice(template['full_names'])
                location = random.choice(locations)
                company = random.choice(companies)

                # Generate bio
                bio_template = random.choice(template['bio_templates'])
                bio = bio_template.format(
                    company=company,
                    location=location,
                    username=username,
                    email=f"hello@{username}.com"
                )

                # Generate follower/following counts
                followers = random.randint(*template['follower_range'])
                following = random.randint(*template['following_range'])
                posts = random.randint(*template['posts_range'])

                # Determine if verified
                is_verified = random.random() < template['verified_chance']

                # Create account
                account = Accounts.objects.create(
                    username=username,
                    full_name=full_name,
                    bio=bio,
                    followers=followers,
                    following=following,
                    number_of_posts=posts,
                    interests=template['interests'],
                    account_type=template['account_type'],
                    is_verified=is_verified,
                    campaign_id=str(campaign.id),
                    collection_date=timezone.now() - timedelta(hours=random.randint(1, 72)),
                    locations=[location] if random.random() > 0.3 else [location, random.choice(locations)]
                )

                # Apply tags to account
                self.apply_tags_to_account(account, template['tags'])

                created_accounts += 1

                if created_accounts % 20 == 0:
                    self.stdout.write(f"   Created {created_accounts}/{num_accounts} accounts...")

        # Create remaining accounts to reach target
        remaining = num_accounts - created_accounts
        if remaining > 0:
            for i in range(remaining):
                template = random.choice(account_templates)
                # Create account using random template (simplified)
                username = f"user_{random.randint(1000, 9999)}"
                while Accounts.objects.filter(username=username).exists():
                    username = f"user_{random.randint(1000, 9999)}"

                account = Accounts.objects.create(
                    username=username,
                    full_name=random.choice(template['full_names']),
                    bio=random.choice(template['bio_templates']).format(
                        company=random.choice(companies),
                        location=random.choice(locations),
                        username=username,
                        email=f"hello@{username}.com"
                    ),
                    followers=random.randint(*template['follower_range']),
                    following=random.randint(*template['following_range']),
                    number_of_posts=random.randint(*template['posts_range']),
                    interests=template['interests'],
                    account_type=template['account_type'],
                    is_verified=random.random() < template['verified_chance'],
                    campaign_id=str(campaign.id),
                    collection_date=timezone.now() - timedelta(hours=random.randint(1, 72)),
                    locations=[random.choice(locations)]
                )

                self.apply_tags_to_account(account, template['tags'])
                created_accounts += 1

        self.stdout.write(f"✅ Created {created_accounts} diverse accounts")

    def apply_tags_to_account(self, account, tag_names):
        """Apply tags to an account and potentially add to whitelist"""
        from taggit.models import Tag
        from instagram.models import CustomTaggedItem
        from django.contrib.contenttypes.models import ContentType

        # Get content type for Accounts
        content_type = ContentType.objects.get_for_model(Accounts)

        applied_tags = []
        for tag_name in tag_names:
            # Only apply tag with some probability to create variety
            if random.random() < 0.7:  # 70% chance to apply each tag
                tag, created = Tag.objects.get_or_create(name=tag_name)

                # Create tagged item
                CustomTaggedItem.objects.get_or_create(
                    tag=tag,
                    content_type=content_type,
                    object_id=account.username
                )
                applied_tags.append(tag_name)

        # Add to whitelist if account meets certain criteria
        if self.should_whitelist_account(account, applied_tags):
            self.add_to_whitelist(account, applied_tags)

    def should_whitelist_account(self, account, tags):
        """Determine if account should be added to whitelist"""
        # Whitelist criteria
        high_quality_tags = ["Verified Account", "High Engagement", "Professional Profile", "Authentic Content"]
        business_tags = ["CEO/Founder", "Entrepreneur", "Business Coach", "Investor"]

        # High chance for verified accounts
        if account.is_verified:
            return random.random() < 0.8

        # High chance for accounts with quality tags
        if any(tag in high_quality_tags for tag in tags):
            return random.random() < 0.6

        # Medium chance for business-related accounts
        if any(tag in business_tags for tag in tags):
            return random.random() < 0.4

        # Lower chance for high follower accounts
        if account.followers > 10000:
            return random.random() < 0.3

        # Base chance for other accounts
        return random.random() < 0.15

    def add_to_whitelist(self, account, tags):
        """Add account to whitelist with appropriate privileges"""
        # Determine privileges based on account type and tags
        privileges = {
            'dm': random.random() < 0.7,
            'discover': random.random() < 0.8,
            'comment': random.random() < 0.6,
            'post_like': random.random() < 0.9,
            'favorite': random.random() < 0.4,
            'follow': random.random() < 0.5,
            'is_auto': True
        }

        # Higher privileges for verified or high-quality accounts
        if account.is_verified or "Professional Profile" in tags:
            privileges.update({
                'dm': True,
                'discover': True,
                'comment': random.random() < 0.8,
                'follow': random.random() < 0.7
            })

        WhiteListEntry.objects.get_or_create(
            account=account,
            defaults={
                'tags': tags,
                **privileges
            }
        )
