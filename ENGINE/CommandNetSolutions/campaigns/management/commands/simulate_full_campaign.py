"""
Management command to simulate a full campaign lifecycle.

This script:
1. Creates a new campaign
2. Assigns tags to the campaign
3. Simulates the account collection phase
4. Simulates the tag analysis phase
5. Generates a whitelist
6. Updates campaign metrics

Usage:
    python manage.py simulate_full_campaign
"""
import random
import uuid
import time
import logging
import os
import json
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import transaction
from django.contrib.contenttypes.models import ContentType

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Simulate a full campaign lifecycle'

    def add_arguments(self, parser):
        parser.add_argument(
            '--name',
            type=str,
            default='Simulated Campaign',
            help='Name of the campaign to simulate'
        )
        parser.add_argument(
            '--accounts',
            type=int,
            default=100,
            help='Number of accounts to simulate collecting'
        )
        parser.add_argument(
            '--delay',
            type=float,
            default=0.5,
            help='Delay between simulation steps (seconds)'
        )
        parser.add_argument(
            '--use-airflow',
            action='store_true',
            help='Use Airflow DAGs for simulation instead of direct execution'
        )

    def handle(self, *args, **options):
        campaign_name = options['name']
        num_accounts = options['accounts']
        delay = options['delay']
        use_airflow = options['use_airflow']

        # Get admin user
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            admin_user = User.objects.create_superuser(
                username='admin',
                email='<EMAIL>',
                password='admin'
            )
            self.stdout.write(self.style.SUCCESS('Created admin user'))

        # Create campaign
        campaign = self.create_campaign(campaign_name, admin_user)
        self.stdout.write(self.style.SUCCESS(f'Created campaign: {campaign.name}'))

        # Import models
        from campaigns.models import (
            Campaign, LocationTarget, UsernameTarget, DynamicTag,
            TagGroup, TagCategory, CampaignTag, TagAnalysisResult,
            WorkflowExecution, WorkflowProgressUpdate
        )
        from instagram.models import Accounts, WhiteListEntry
        from taggit.models import Tag
        from instagram.models import CustomTaggedItem

        # Get existing tags
        tags = list(DynamicTag.objects.all())
        if len(tags) < 5:
            self.stdout.write(self.style.WARNING('Not enough tags found. Please run populate_backend first.'))
            return

        # Assign tags to campaign
        campaign_tags = self.assign_tags_to_campaign(campaign, tags)
        self.stdout.write(self.style.SUCCESS(f'Assigned {len(campaign_tags)} tags to campaign'))

        if use_airflow:
            # Use Airflow DAGs for simulation
            self.stdout.write(self.style.SUCCESS('Using Airflow DAGs for simulation...'))
            self.simulate_with_airflow(campaign, num_accounts)
        else:
            # Simulate account collection workflow
            self.stdout.write(self.style.SUCCESS('Starting account collection workflow...'))
            collection_workflow = self.simulate_collection_workflow(campaign, num_accounts, delay)

            # Simulate tag analysis workflow
            self.stdout.write(self.style.SUCCESS('Starting tag analysis workflow...'))
            analysis_workflow = self.simulate_analysis_workflow(campaign, delay)

            # Update campaign status
            campaign.status = 'completed'
            campaign.save()

        self.stdout.write(self.style.SUCCESS(f'Campaign simulation complete! Campaign ID: {campaign.id}'))
        self.stdout.write(self.style.SUCCESS(f'View the campaign at: /campaigns/{campaign.id}/'))

    def create_campaign(self, name, user):
        """Create a test campaign"""
        from campaigns.models import Campaign, LocationTarget, UsernameTarget

        # Check if campaign with this name already exists
        existing_campaigns = Campaign.objects.filter(name=name)
        if existing_campaigns.exists():
            # Append a number to make the name unique
            count = existing_campaigns.count()
            name = f"{name} {count + 1}"

        campaign = Campaign.objects.create(
            id=uuid.uuid4(),
            name=name,
            description=f"This is a simulated campaign created on {timezone.now().strftime('%Y-%m-%d')}",
            creator=user,
            status='draft',  # Start as draft
            target_type='mixed',
            audience_type='profile'
        )

        # Add a location target
        LocationTarget.objects.create(
            id=uuid.uuid4(),
            campaign=campaign,
            location_id='c2728325',
            country='United States',
            city='New York'
        )

        # Add a username target
        UsernameTarget.objects.create(
            id=uuid.uuid4(),
            campaign=campaign,
            username='instagram',
            audience_type='profile'
        )

        return campaign

    def assign_tags_to_campaign(self, campaign, tags):
        """Assign tags to the campaign"""
        from campaigns.models import CampaignTag

        campaign_tags = []

        # Assign half of the tags to the campaign
        for tag in random.sample(tags, min(len(tags) // 2, 4)):
            campaign_tag, created = CampaignTag.objects.get_or_create(
                campaign=campaign,
                tag=tag,
                defaults={
                    'id': uuid.uuid4(),
                    'is_required': random.choice([True, False])
                }
            )
            campaign_tags.append(campaign_tag)

        return campaign_tags

    def simulate_with_airflow(self, campaign, num_accounts):
        """Simulate campaign using Airflow DAGs"""
        from campaigns.services.airflow_service import AirflowService

        # Initialize Airflow service
        airflow_service = AirflowService()

        # Update campaign status to running
        campaign.status = 'running'
        campaign.save()

        # Create a Python script to simulate the PyFlow workflow
        self.create_simulation_scripts(campaign, num_accounts)

        # Trigger data collection DAG
        self.stdout.write(self.style.SUCCESS('Triggering data collection DAG...'))
        collection_response = airflow_service.trigger_campaign_data_collection(
            campaign_id=str(campaign.id),
            target_type='mixed',
            audience_type='profile',
            location_targets=[{'location_id': 'c2728325', 'city': 'New York', 'country': 'United States'}],
            username_targets=['instagram']
        )

        self.stdout.write(self.style.SUCCESS(f'Data collection DAG triggered: {collection_response}'))

        # Wait for a moment to simulate DAG execution
        time.sleep(5)

        # Trigger tagging DAG
        self.stdout.write(self.style.SUCCESS('Triggering tagging DAG...'))
        tagging_response = airflow_service.trigger_campaign_tagging(
            campaign_id=str(campaign.id),
            analysis_settings={
                'enable_tagging': True,
                'min_followers': 100,
                'max_followers': 10000
            }
        )

        self.stdout.write(self.style.SUCCESS(f'Tagging DAG triggered: {tagging_response}'))

    def simulate_collection_workflow(self, campaign, num_accounts, delay):
        """Simulate the account collection workflow"""
        from campaigns.models import WorkflowExecution, WorkflowProgressUpdate
        from instagram.models import Accounts

        # Create workflow execution
        workflow = WorkflowExecution.objects.create(
            id=uuid.uuid4(),
            campaign_id=str(campaign.id),
            workflow_name='account_collection.pygraph',
            workflow_path='/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/INSTA/WORKFLOW/account_collection.pygraph',
            workflow_type='collection',
            status='running',
            start_time=timezone.now(),
            total_items=num_accounts
        )

        # Bio templates for more realistic data
        bio_templates = [
            "📸 {interest} photographer | {location} based | {age} y/o",
            "{pronoun} | {age} | {location} | Passionate about {interest}",
            "{interest} enthusiast from {location} | {pronoun}",
            "Living in {location} | {age} | Lover of {interest}",
            "{pronoun} sharing {interest} content | {location}",
            "{age} year old {interest} blogger | {location} | {pronoun}",
            "Just a {age} y/o {pronoun} who loves {interest} | {location}",
            "{location} | {age} | {pronoun} | {interest} addict",
            "✨ {interest} | {location} | {emoji} | Available for collabs",
            "{emoji} {interest} creator {emoji} | {location} | DM for business inquiries",
            "{pronoun} • {age} • {location} • {interest} {emoji}",
            "CEO of {interest} {emoji} | Based in {location} | {age}",
            "{interest.upper()} LOVER {emoji} | {location} | {pronoun}",
            "{emoji} {interest} {emoji} | {location} | {website}",
            "Professional {interest} {job_title} | {location} | {website}",
            "{emoji} {interest} {emoji} | {pronoun} | {location} | {hashtags}",
            # Edge cases with special characters and emojis
            "✨🌟 {interest} 🌟✨ | {location} | {age} | {emoji}{emoji}{emoji}",
            "{interest.upper()} 🔥 | {pronoun} | {location} | #️⃣{hashtags}",
            "《 {interest} 》| {location} | {age} | {emoji}",
            "★彡 {interest} 彡★ | {location} | {pronoun}",
        ]

        interests = [
            "fashion", "travel", "food", "fitness", "beauty", "photography",
            "art", "music", "dance", "technology", "gaming", "sports",
            "lifestyle", "wellness", "yoga", "vegan", "sustainable", "DIY",
            "home decor", "parenting", "education", "finance", "crypto",
            "marketing", "business", "entrepreneurship", "coaching", "mindfulness"
        ]

        locations = [
            "NYC", "LA", "London", "Paris", "Tokyo", "Sydney", "Berlin",
            "Toronto", "Miami", "Chicago", "Seattle", "Boston", "Barcelona",
            "Amsterdam", "Dubai", "Singapore", "Hong Kong", "Seoul", "Mumbai",
            "Cape Town", "Rio", "Mexico City", "Stockholm", "Vienna", "Prague"
        ]

        pronouns = [
            "she/her", "he/him", "they/them", "she/they", "he/they",
            "all pronouns", "any pronouns"
        ]

        emojis = [
            "💫", "✨", "🌟", "⭐", "🔥", "💯", "🌈", "🌸", "🌺", "🌻", "🌼", "🍀",
            "🌱", "🌿", "🍃", "🌊", "🌞", "🌙", "💖", "💕", "💓", "💗", "💘", "💝",
            "💪", "👑", "🎯", "🎨", "📸", "🎬", "🎵", "🎮", "⚽", "🏋️", "🧘", "🍕"
        ]

        job_titles = [
            "Specialist", "Expert", "Consultant", "Coach", "Influencer", "Creator",
            "Enthusiast", "Professional", "Guru", "Advocate", "Ambassador"
        ]

        websites = [
            "linktr.ee/user", "bio.site/user", "instagram.com/user",
            "tiktok.com/@user", "youtube.com/c/user", "twitter.com/user"
        ]

        hashtags = [
            "content #creator", "#lifestyle #blogger", "#influencer",
            "#sponsored #ad", "#fashion #style", "#travel #wanderlust",
            "#fitness #health", "#beauty #makeup", "#food #foodie"
        ]

        # Account types with weighted distribution
        account_types = ["personal"] * 70 + ["business"] * 20 + ["creator"] * 10

        # Create accounts in batches to simulate progress
        batch_size = max(5, num_accounts // 10)
        num_batches = (num_accounts + batch_size - 1) // batch_size

        for batch in range(num_batches):
            batch_accounts = []
            start_idx = batch * batch_size
            end_idx = min((batch + 1) * batch_size, num_accounts)

            for i in range(start_idx, end_idx):
                username = f"sim_user_{campaign.id}_{i}"

                # Generate a realistic bio with more variety
                interest = random.choice(interests)
                location = random.choice(locations)
                age = random.randint(18, 45)
                pronoun = random.choice(pronouns)
                emoji = random.choice(emojis)
                job_title = random.choice(job_titles)
                website = random.choice(websites).replace('user', f"user{i}")
                hashtag = random.choice(hashtags)

                # Create more diverse edge cases for testing
                if i % 20 == 0:  # Mega influencer with very high followers
                    followers_count = random.randint(1000000, ********)
                    following_count = random.randint(100, 1000)
                    posts_count = random.randint(500, 5000)
                    account_type = "creator"
                    is_verified = True
                    bio = f"📱 Verified Creator | {random.choice(interests)} & {random.choice(interests)} | {random.choice(locations)} | {random.choice(emojis)} | {followers_count//1000}K"
                elif i % 15 == 0:  # Very low engagement account
                    followers_count = random.randint(10, 50)
                    following_count = random.randint(500, 2000)
                    posts_count = random.randint(0, 5)
                    account_type = "personal"
                    is_verified = False
                elif i % 12 == 0:  # Business account with moderate followers
                    followers_count = random.randint(5000, 50000)
                    following_count = random.randint(500, 2000)
                    posts_count = random.randint(100, 500)
                    account_type = "business"
                    is_verified = random.random() < 0.3  # 30% chance of being verified
                    bio = f"🏢 Official Business Account | {random.choice(interests)} | {random.choice(locations)} | Contact: <EMAIL>"
                elif i % 10 == 0:  # Account with unusual characters and emojis
                    followers_count = random.randint(1000, 20000)
                    following_count = random.randint(100, 1000)
                    posts_count = random.randint(50, 300)
                    account_type = random.choice(account_types)
                    is_verified = random.random() < 0.1
                    # Create a bio with lots of emojis and special characters
                    emoji_string = ''.join(random.choices(emojis, k=10))
                    bio = f"{emoji_string}\n★彡 {random.choice(interests).upper()} 彡★\n{random.choice(locations)} | {random.choice(pronouns)}\n《 DM for collabs 》"
                elif i % 8 == 0:  # Account with multiple languages
                    followers_count = random.randint(500, 15000)
                    following_count = random.randint(100, 1500)
                    posts_count = random.randint(30, 200)
                    account_type = random.choice(account_types)
                    is_verified = random.random() < 0.1
                    # Create a bio with multiple languages
                    languages = [
                        f"English: {random.choice(interests)} lover",
                        f"Español: Amante de {random.choice(interests)}",
                        f"Français: Passionné de {random.choice(interests)}",
                        f"Deutsch: {random.choice(interests)} Liebhaber",
                        f"日本語: {random.choice(interests)}が大好き",
                        f"中文: 热爱{random.choice(interests)}"
                    ]
                    selected_langs = random.sample(languages, k=random.randint(2, 4))
                    bio = f"{random.choice(emojis)} Multilingual {random.choice(emojis)}\n" + "\n".join(selected_langs) + f"\n{random.choice(locations)} {random.choice(emojis)}"
                elif i % 6 == 0:  # Account with very high engagement but few followers
                    followers_count = random.randint(100, 1000)
                    following_count = random.randint(50, 500)
                    posts_count = random.randint(300, 1000)
                    account_type = "creator"
                    is_verified = False
                    bio = f"🔥 High Engagement Creator | {random.choice(interests)} | {random.choice(locations)} | Engagement rate: {random.randint(10, 30)}%"
                elif i % 5 == 0:  # Account with unusual metrics ratio
                    followers_count = random.randint(100, 5000)
                    following_count = random.randint(5000, 10000)  # Following much higher than followers
                    posts_count = random.randint(10, 50)
                    account_type = "personal"
                    is_verified = False
                    bio = f"{random.choice(pronouns)} | {random.choice(interests)} | {random.choice(locations)} | Following: {following_count}"
                else:  # Normal distribution
                    followers_count = random.randint(100, 10000)
                    following_count = random.randint(100, 2000)
                    posts_count = random.randint(10, 500)
                    account_type = random.choice(account_types)
                    is_verified = random.random() < 0.1  # 10% chance of being verified

                # Format the bio with all the variables
                try:
                    bio_template = random.choice(bio_templates)
                    # Only include variables that are actually defined
                    format_vars = {
                        'interest': interest,
                        'location': location,
                        'age': age,
                        'pronoun': pronoun
                    }

                    # Add optional variables if they exist
                    if 'emoji' in locals():
                        format_vars['emoji'] = emoji
                    if 'job_title' in locals():
                        format_vars['job_title'] = job_title
                    if 'website' in locals():
                        format_vars['website'] = website
                    if 'hashtag' in locals():
                        format_vars['hashtags'] = hashtag

                    bio = bio_template.format(**format_vars)
                except (KeyError, AttributeError):
                    # Fallback for templates with missing keys or other formatting errors
                    bio = f"{interest} enthusiast | {location} | {age} y/o"

                # Generate random interests and locations
                user_interests = random.sample(interests, k=min(3, len(interests)))
                user_locations = [location]  # Use the same location as in the bio

                # Create account with realistic metrics
                account = Accounts.objects.create(
                    username=username,
                    full_name=f"Simulated User {i}",
                    bio=bio,
                    followers=followers_count,
                    following=following_count,
                    number_of_posts=posts_count,
                    account_type=account_type,
                    is_verified=is_verified,
                    campaign_id=str(campaign.id),
                    collection_date=timezone.now(),
                    interests=user_interests,
                    locations=user_locations,
                    links=[website] if random.random() > 0.5 else []  # 50% chance of having a link
                )
                batch_accounts.append(account)

            # Update workflow progress
            progress = (batch + 1) / num_batches * 100
            WorkflowProgressUpdate.objects.create(
                id=uuid.uuid4(),
                workflow_execution=workflow,
                progress=progress,
                processed_items=end_idx,
                successful_items=end_idx,
                failed_items=0,
                message=f"Collected {end_idx} of {num_accounts} accounts",
                timestamp=timezone.now()
            )

            self.stdout.write(f"Collected {end_idx} of {num_accounts} accounts ({progress:.1f}%)")
            time.sleep(delay)

        # Complete workflow
        workflow.status = 'completed'
        workflow.end_time = timezone.now()
        workflow.duration = (workflow.end_time - workflow.start_time).total_seconds()
        workflow.progress = 100.0
        workflow.processed_items = workflow.total_items
        workflow.successful_items = workflow.total_items
        workflow.save()

        return workflow

    def simulate_analysis_workflow(self, campaign, delay):
        """Simulate the tag analysis workflow"""
        from campaigns.models import (
            CampaignTag, TagAnalysisResult, WorkflowExecution, WorkflowProgressUpdate
        )
        from instagram.models import Accounts, WhiteListEntry
        from taggit.models import Tag
        from instagram.models import CustomTaggedItem

        # Get accounts for this campaign
        accounts = Accounts.objects.filter(campaign_id=str(campaign.id))
        total_accounts = accounts.count()

        if total_accounts == 0:
            self.stdout.write(self.style.WARNING('No accounts found for this campaign'))
            return None

        # Create workflow execution
        workflow = WorkflowExecution.objects.create(
            id=uuid.uuid4(),
            campaign_id=str(campaign.id),
            workflow_name='tag_analysis.pygraph',
            workflow_path='/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/INSTA/WORKFLOW/tag_analysis.pygraph',
            workflow_type='analysis',
            status='running',
            start_time=timezone.now(),
            total_items=total_accounts
        )

        # Get tags for this campaign
        campaign_tags = CampaignTag.objects.filter(campaign=campaign)
        tags = [ct.tag for ct in campaign_tags]

        if not tags:
            self.stdout.write(self.style.WARNING('No tags assigned to this campaign'))
            return None

        # Get content type for Accounts model
        content_type = ContentType.objects.get_for_model(Accounts)

        # Process accounts in batches
        batch_size = max(5, total_accounts // 10)
        num_batches = (total_accounts + batch_size - 1) // batch_size

        whitelist_entries = []

        for batch_idx, batch_start in enumerate(range(0, total_accounts, batch_size)):
            batch_end = min(batch_start + batch_size, total_accounts)
            batch_accounts = accounts[batch_start:batch_end]

            for account in batch_accounts:
                # Randomly select 1-3 tags to apply to this account
                num_tags = random.randint(1, min(3, len(tags)))
                selected_tags = random.sample(tags, num_tags)

                account_matched_tags = []

                for tag in selected_tags:
                    # Determine if tag matches based on account data
                    matched = False
                    confidence = 0.0
                    match_details = {}

                    # More sophisticated matching logic based on tag field and pattern
                    if tag.field == 'bio':
                        # For bio field, check for keywords in the bio text
                        keywords = tag.pattern.lower().split('|')
                        matched_keywords = [kw for kw in keywords if kw.strip() in account.bio.lower()]

                        if matched_keywords:
                            matched = True
                            # Higher confidence if multiple keywords match
                            confidence = min(0.6 + (len(matched_keywords) / len(keywords)) * 0.4, 1.0)
                            match_details = {
                                'matched_keywords': matched_keywords,
                                'total_keywords': len(keywords),
                                'match_text': f"Found {len(matched_keywords)} of {len(keywords)} keywords in bio"
                            }

                    elif tag.field == 'number_of_posts' and 'posts > ' in tag.pattern:
                        # For posts count, check if the account has more posts than the threshold
                        threshold = int(tag.pattern.split('posts > ')[1])
                        if account.number_of_posts > threshold:
                            matched = True
                            # Higher confidence for accounts with significantly more posts
                            ratio = min(account.number_of_posts / threshold, 3) / 3  # Cap at 3x threshold
                            confidence = 0.7 + (ratio * 0.3)
                            match_details = {
                                'threshold': threshold,
                                'actual': account.number_of_posts,
                                'ratio': account.number_of_posts / threshold,
                                'match_text': f"Account has {account.number_of_posts} posts, above threshold of {threshold}"
                            }

                    elif tag.field == 'followers' and 'followers > ' in tag.pattern:
                        # For followers count, check if the account has more followers than the threshold
                        threshold = int(tag.pattern.split('followers > ')[1])
                        if account.followers > threshold:
                            matched = True
                            # Higher confidence for accounts with significantly more followers
                            ratio = min(account.followers / threshold, 5) / 5  # Cap at 5x threshold
                            confidence = 0.7 + (ratio * 0.3)
                            match_details = {
                                'threshold': threshold,
                                'actual': account.followers,
                                'ratio': account.followers / threshold,
                                'match_text': f"Account has {account.followers} followers, above threshold of {threshold}"
                            }

                    elif tag.field == 'engagement_rate':
                        # Simulate engagement rate based on followers and posts
                        likes_per_post = random.randint(
                            int(account.followers * 0.01),  # Min 1% engagement
                            int(account.followers * 0.2)    # Max 20% engagement
                        )
                        comments_per_post = random.randint(
                            int(likes_per_post * 0.01),     # Min 1% of likes are comments
                            int(likes_per_post * 0.1)       # Max 10% of likes are comments
                        )
                        engagement_rate = ((likes_per_post + comments_per_post) / account.followers) * 100

                        if 'engagement > ' in tag.pattern:
                            threshold = float(tag.pattern.split('engagement > ')[1].rstrip('%'))
                            if engagement_rate > threshold:
                                matched = True
                                # Higher confidence for accounts with significantly higher engagement
                                ratio = min(engagement_rate / threshold, 3) / 3  # Cap at 3x threshold
                                confidence = 0.7 + (ratio * 0.3)
                                match_details = {
                                    'threshold': threshold,
                                    'actual': engagement_rate,
                                    'likes_per_post': likes_per_post,
                                    'comments_per_post': comments_per_post,
                                    'ratio': engagement_rate / threshold,
                                    'match_text': f"Account has {engagement_rate:.2f}% engagement rate, above threshold of {threshold}%"
                                }

                    elif tag.field == 'interests':
                        # Check if any of the account's interests match the pattern
                        pattern_interests = tag.pattern.lower().split('|')
                        account_interests = [interest.lower() for interest in account.interests]
                        matched_interests = [interest for interest in pattern_interests if interest.strip() in account_interests]

                        if matched_interests:
                            matched = True
                            # Higher confidence if multiple interests match
                            confidence = min(0.6 + (len(matched_interests) / len(pattern_interests)) * 0.4, 1.0)
                            match_details = {
                                'matched_interests': matched_interests,
                                'account_interests': account_interests,
                                'pattern_interests': pattern_interests,
                                'match_text': f"Found {len(matched_interests)} matching interests"
                            }

                    elif tag.field == 'locations':
                        # Check if any of the account's locations match the pattern
                        pattern_locations = tag.pattern.lower().split('|')
                        account_locations = [location.lower() for location in account.locations]
                        matched_locations = [location for location in pattern_locations if any(loc.strip() in account_loc for account_loc in account_locations for loc in pattern_locations)]

                        if matched_locations:
                            matched = True
                            confidence = 0.9  # High confidence for location matches
                            match_details = {
                                'matched_locations': matched_locations,
                                'account_locations': account_locations,
                                'pattern_locations': pattern_locations,
                                'match_text': f"Found {len(matched_locations)} matching locations"
                            }

                    else:
                        # For other fields or if the pattern format is not recognized,
                        # use a more generic approach with weighted randomness
                        # Higher chance of matching for verified accounts or accounts with many followers
                        match_probability = 0.3  # Base probability

                        # Adjust probability based on account metrics
                        if account.is_verified:
                            match_probability += 0.2

                        if account.followers > 5000:
                            match_probability += 0.2
                        elif account.followers > 1000:
                            match_probability += 0.1

                        if account.number_of_posts > 100:
                            match_probability += 0.1

                        # Final random check
                        if random.random() < match_probability:
                            matched = True
                            confidence = random.uniform(0.6, 0.9)
                            match_details = {
                                'field': tag.field,
                                'pattern': tag.pattern,
                                'match_probability': match_probability,
                                'match_text': f"Generic match with {match_probability:.2f} probability"
                            }

                    # Create tag analysis result
                    if matched:
                        # Ensure match_details has the basic information
                        if 'field' not in match_details:
                            match_details['field'] = tag.field
                        if 'pattern' not in match_details:
                            match_details['pattern'] = tag.pattern
                        if 'match_text' not in match_details:
                            match_details['match_text'] = f"Matched in {tag.field}"

                        TagAnalysisResult.objects.create(
                            id=uuid.uuid4(),
                            account_id=account.username,
                            campaign=campaign,
                            tag=tag,
                            matched=True,
                            confidence_score=confidence,
                            match_details=match_details
                        )

                        # Create a taggit Tag and CustomTaggedItem
                        taggit_tag, _ = Tag.objects.get_or_create(name=tag.name)

                        CustomTaggedItem.objects.create(
                            tag=taggit_tag,
                            content_type=content_type,
                            object_id=account.username
                        )

                        account_matched_tags.append(tag)

                # Check if account should be whitelisted
                required_tags = [ct.tag for ct in campaign_tags if ct.is_required]
                if any(tag in account_matched_tags for tag in required_tags) or random.random() < 0.2:
                    # Get tag names for the whitelist entry
                    tag_names = [tag.name for tag in account_matched_tags]

                    whitelist_entry = WhiteListEntry.objects.create(
                        account=account,
                        tags=tag_names,
                        dm=random.choice([True, False]),
                        discover=random.choice([True, False]),
                        comment=random.choice([True, False]),
                        post_like=random.choice([True, False]),
                        favorite=random.choice([True, False]),
                        follow=random.choice([True, False]),
                        is_auto=True
                    )
                    whitelist_entries.append(whitelist_entry)

            # Update workflow progress
            progress = (batch_idx + 1) / num_batches * 100
            WorkflowProgressUpdate.objects.create(
                id=uuid.uuid4(),
                workflow_execution=workflow,
                progress=progress,
                processed_items=batch_end,
                successful_items=batch_end,
                failed_items=0,
                message=f"Analyzed {batch_end} of {total_accounts} accounts",
                timestamp=timezone.now()
            )

            self.stdout.write(f"Analyzed {batch_end} of {total_accounts} accounts ({progress:.1f}%)")
            time.sleep(delay)

        # Complete workflow
        workflow.status = 'completed'
        workflow.end_time = timezone.now()
        workflow.duration = (workflow.end_time - workflow.start_time).total_seconds()
        workflow.progress = 100.0
        workflow.processed_items = workflow.total_items
        workflow.successful_items = workflow.total_items
        workflow.save()

        self.stdout.write(self.style.SUCCESS(f'Added {len(whitelist_entries)} accounts to whitelist'))

        return workflow

    def create_simulation_scripts(self, campaign, num_accounts):
        """Create Python scripts to simulate PyFlow workflows"""
        # Create directory for simulation scripts if it doesn't exist
        script_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'scripts')
        os.makedirs(script_dir, exist_ok=True)

        # Create account collection simulation script
        collection_script_path = os.path.join(script_dir, 'simulate_account_collection.py')
        with open(collection_script_path, 'w') as f:
            f.write('''"""
Script to simulate PyFlow account collection workflow.

This script:
1. Creates dummy Instagram accounts
2. Associates them with the campaign
3. Updates workflow progress

Usage:
    python simulate_account_collection.py --campaign_id <campaign_id> --num_accounts <num_accounts>
"""
import random
import uuid
import time
import argparse
import os
import sys
import django
from django.utils import timezone

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")
django.setup()

# Import models
from campaigns.models import Campaign, WorkflowExecution, WorkflowProgressUpdate
from instagram.models import Accounts

def main():
    parser = argparse.ArgumentParser(description='Simulate PyFlow account collection workflow')
    parser.add_argument('--campaign_id', required=True, help='Campaign ID')
    parser.add_argument('--num_accounts', type=int, default=100, help='Number of accounts to simulate')
    args = parser.parse_args()

    campaign_id = args.campaign_id
    num_accounts = args.num_accounts

    print(f"Simulating account collection for campaign {campaign_id}")
    print(f"Creating {num_accounts} dummy accounts")

    # Get campaign
    try:
        campaign = Campaign.objects.get(id=campaign_id)
    except Campaign.DoesNotExist:
        print(f"Campaign {campaign_id} not found")
        return

    # Create workflow execution
    workflow = WorkflowExecution.objects.create(
        id=uuid.uuid4(),
        campaign_id=campaign_id,
        workflow_name='account_collection.pygraph',
        workflow_path='/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/INSTA/WORKFLOW/account_collection.pygraph',
        workflow_type='collection',
        status='running',
        start_time=timezone.now(),
        total_items=num_accounts
    )

    # Bio templates for more realistic data
    bio_templates = [
        "📸 {interest} photographer | {location} based | {age} y/o",
        "{pronoun} | {age} | {location} | Passionate about {interest}",
        "{interest} enthusiast from {location} | {pronoun}",
        "Living in {location} | {age} | Lover of {interest}",
        "{pronoun} sharing {interest} content | {location}",
        "{age} year old {interest} blogger | {location} | {pronoun}",
        "Just a {age} y/o {pronoun} who loves {interest} | {location}",
        "{location} | {age} | {pronoun} | {interest} addict",
    ]

    interests = [
        "fashion", "travel", "food", "fitness", "beauty", "photography",
        "art", "music", "dance", "technology", "gaming", "sports"
    ]

    locations = [
        "NYC", "LA", "London", "Paris", "Tokyo", "Sydney", "Berlin",
        "Toronto", "Miami", "Chicago", "Seattle", "Boston"
    ]

    pronouns = [
        "she/her", "he/him", "they/them"
    ]

    # Create accounts in batches to simulate progress
    batch_size = max(5, num_accounts // 10)
    num_batches = (num_accounts + batch_size - 1) // batch_size

    for batch in range(num_batches):
        batch_accounts = []
        start_idx = batch * batch_size
        end_idx = min((batch + 1) * batch_size, num_accounts)

        for i in range(start_idx, end_idx):
            username = f"sim_user_{campaign_id}_{i}"

            # Generate a realistic bio
            interest = random.choice(interests)
            location = random.choice(locations)
            age = random.randint(18, 45)
            pronoun = random.choice(pronouns)

            bio_template = random.choice(bio_templates)
            bio = bio_template.format(
                interest=interest,
                location=location,
                age=age,
                pronoun=pronoun
            )

            # Create account with realistic metrics
            account = Accounts.objects.create(
                username=username,
                full_name=f"Simulated User {i}",
                bio=bio,
                followers=random.randint(100, 10000),
                following=random.randint(100, 2000),
                posts_count=random.randint(10, 500),
                is_private=random.choice([True, False]),
                is_verified=random.choice([True, False, False, False]),  # Make verified accounts less common
                campaign_id=campaign_id,
                collection_date=timezone.now()
            )
            batch_accounts.append(account)

        # Update workflow progress
        progress = (batch + 1) / num_batches * 100
        WorkflowProgressUpdate.objects.create(
            id=uuid.uuid4(),
            workflow_execution=workflow,
            progress=progress,
            processed_items=end_idx,
            successful_items=end_idx,
            failed_items=0,
            message=f"Collected {end_idx} of {num_accounts} accounts",
            timestamp=timezone.now()
        )

        print(f"Collected {end_idx} of {num_accounts} accounts ({progress:.1f}%)")
        time.sleep(0.5)

    # Complete workflow
    workflow.status = 'completed'
    workflow.end_time = timezone.now()
    workflow.duration = (workflow.end_time - workflow.start_time).total_seconds()
    workflow.progress = 100.0
    workflow.processed_items = workflow.total_items
    workflow.successful_items = workflow.total_items
    workflow.save()

    print(f"Account collection complete. Created {num_accounts} accounts.")

if __name__ == "__main__":
    main()
''')

        # Create tag analysis simulation script
        analysis_script_path = os.path.join(script_dir, 'simulate_tag_analysis.py')
        with open(analysis_script_path, 'w') as f:
            f.write('''"""
Script to simulate PyFlow tag analysis workflow.

This script:
1. Analyzes accounts based on campaign tags
2. Applies tags to accounts
3. Generates a whitelist
4. Updates workflow progress

Usage:
    python simulate_tag_analysis.py --campaign_id <campaign_id>
"""
import random
import uuid
import time
import argparse
import os
import sys
import django
from django.utils import timezone
from django.contrib.contenttypes.models import ContentType

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")
django.setup()

# Import models
from campaigns.models import (
    Campaign, CampaignTag, TagAnalysisResult,
    WorkflowExecution, WorkflowProgressUpdate
)
from instagram.models import Accounts, WhiteListEntry
from taggit.models import Tag
from instagram.models import CustomTaggedItem

def main():
    parser = argparse.ArgumentParser(description='Simulate PyFlow tag analysis workflow')
    parser.add_argument('--campaign_id', required=True, help='Campaign ID')
    parser.add_argument('--min_followers', type=int, default=0, help='Minimum followers')
    parser.add_argument('--max_followers', type=int, default=0, help='Maximum followers')
    parser.add_argument('--enable_tagging', type=bool, default=True, help='Enable tagging')
    args = parser.parse_args()

    campaign_id = args.campaign_id
    min_followers = args.min_followers
    max_followers = args.max_followers
    enable_tagging = args.enable_tagging

    print(f"Simulating tag analysis for campaign {campaign_id}")

    # Get campaign
    try:
        campaign = Campaign.objects.get(id=campaign_id)
    except Campaign.DoesNotExist:
        print(f"Campaign {campaign_id} not found")
        return

    # Get accounts for this campaign
    accounts_query = Accounts.objects.filter(campaign_id=campaign_id)

    # Apply filters if provided
    if min_followers > 0:
        accounts_query = accounts_query.filter(followers__gte=min_followers)

    if max_followers > 0:
        accounts_query = accounts_query.filter(followers__lte=max_followers)

    accounts = list(accounts_query)
    total_accounts = len(accounts)

    if total_accounts == 0:
        print("No accounts found for this campaign")
        return

    print(f"Analyzing {total_accounts} accounts")

    # Create workflow execution
    workflow = WorkflowExecution.objects.create(
        id=uuid.uuid4(),
        campaign_id=campaign_id,
        workflow_name='tag_analysis.pygraph',
        workflow_path='/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/INSTA/WORKFLOW/tag_analysis.pygraph',
        workflow_type='analysis',
        status='running',
        start_time=timezone.now(),
        total_items=total_accounts
    )

    # Get tags for this campaign
    campaign_tags = CampaignTag.objects.filter(campaign=campaign)
    tags = [ct.tag for ct in campaign_tags]

    if not tags:
        print("No tags assigned to this campaign")
        return

    # Get content type for Accounts model
    content_type = ContentType.objects.get_for_model(Accounts)

    # Process accounts in batches
    batch_size = max(5, total_accounts // 10)
    num_batches = (total_accounts + batch_size - 1) // batch_size

    whitelist_entries = []

    for batch_idx, batch_start in enumerate(range(0, total_accounts, batch_size)):
        batch_end = min(batch_start + batch_size, total_accounts)
        batch_accounts = accounts[batch_start:batch_end]

        for account in batch_accounts:
            # Randomly select 1-3 tags to apply to this account
            num_tags = random.randint(1, min(3, len(tags)))
            selected_tags = random.sample(tags, num_tags)

            account_matched_tags = []

            for tag in selected_tags:
                # Determine if tag matches based on account data
                matched = False
                confidence = 0.0

                # Simple matching logic based on tag field and pattern
                if tag.field == 'bio' and any(keyword in account.bio.lower() for keyword in tag.pattern.lower().split('|')):
                    matched = True
                    confidence = random.uniform(0.7, 1.0)
                elif tag.field == 'posts_count' and 'posts > ' in tag.pattern:
                    threshold = int(tag.pattern.split('posts > ')[1])
                    if account.posts_count > threshold:
                        matched = True
                        confidence = random.uniform(0.8, 1.0)
                elif tag.field == 'engagement_rate':
                    # Simulate engagement rate
                    engagement_rate = random.uniform(0.5, 10.0)
                    if 'engagement > ' in tag.pattern:
                        threshold = float(tag.pattern.split('engagement > ')[1].rstrip('%'))
                        if engagement_rate > threshold:
                            matched = True
                            confidence = random.uniform(0.7, 1.0)
                else:
                    # Random match for other fields
                    matched = random.random() > 0.7
                    if matched:
                        confidence = random.uniform(0.6, 1.0)

                # Create tag analysis result
                if matched:
                    TagAnalysisResult.objects.create(
                        id=uuid.uuid4(),
                        account_id=account.username,
                        campaign=campaign,
                        tag=tag,
                        matched=True,
                        confidence_score=confidence,
                        match_details={
                            'field': tag.field,
                            'pattern': tag.pattern,
                            'match_text': f"Matched in {tag.field}"
                        }
                    )

                    # Create a taggit Tag and CustomTaggedItem
                    taggit_tag, _ = Tag.objects.get_or_create(name=tag.name)

                    CustomTaggedItem.objects.create(
                        tag=taggit_tag,
                        content_type=content_type,
                        object_id=account.username
                    )

                    account_matched_tags.append(tag)

            # Check if account should be whitelisted
            required_tags = [ct.tag for ct in campaign_tags if ct.is_required]
            if any(tag in account_matched_tags for tag in required_tags) or random.random() < 0.2:
                # Get tag names for the whitelist entry
                tag_names = [tag.name for tag in account_matched_tags]

                whitelist_entry = WhiteListEntry.objects.create(
                    account=account,
                    tags=tag_names,
                    dm=random.choice([True, False]),
                    discover=random.choice([True, False]),
                    comment=random.choice([True, False]),
                    post_like=random.choice([True, False]),
                    favorite=random.choice([True, False]),
                    follow=random.choice([True, False]),
                    is_auto=True
                )
                whitelist_entries.append(whitelist_entry)

        # Update workflow progress
        progress = (batch_idx + 1) / num_batches * 100
        WorkflowProgressUpdate.objects.create(
            id=uuid.uuid4(),
            workflow_execution=workflow,
            progress=progress,
            processed_items=batch_end,
            successful_items=batch_end,
            failed_items=0,
            message=f"Analyzed {batch_end} of {total_accounts} accounts",
            timestamp=timezone.now()
        )

        print(f"Analyzed {batch_end} of {total_accounts} accounts ({progress:.1f}%)")
        time.sleep(0.5)

    # Complete workflow
    workflow.status = 'completed'
    workflow.end_time = timezone.now()
    workflow.duration = (workflow.end_time - workflow.start_time).total_seconds()
    workflow.progress = 100.0
    workflow.processed_items = workflow.total_items
    workflow.successful_items = workflow.total_items
    workflow.save()

    print(f"Tag analysis complete. Added {len(whitelist_entries)} accounts to whitelist.")

    # Update campaign status
    campaign.status = 'completed'
    campaign.save()

if __name__ == "__main__":
    main()
''')

        self.stdout.write(self.style.SUCCESS(f'Created simulation scripts in {script_dir}'))
        return script_dir
