"""
Management command to inspect the TagGroup model.

This script:
1. Prints the fields of the TagGroup model
2. Prints the database schema for the TagGroup table

Usage:
    python manage.py inspect_tag_group
"""
from django.core.management.base import BaseCommand
from django.db import connection
from campaigns.models import TagGroup

class Command(BaseCommand):
    help = 'Inspect the TagGroup model'

    def handle(self, *args, **options):
        self.stdout.write('Inspecting TagGroup model...')
        
        # Print model fields
        self.stdout.write('\nModel fields:')
        for field in TagGroup._meta.get_fields():
            self.stdout.write(f'- {field.name}: {field.__class__.__name__}')
        
        # Print database schema
        self.stdout.write('\nDatabase schema:')
        with connection.cursor() as cursor:
            cursor.execute("""
                SELECT column_name, data_type, is_nullable
                FROM information_schema.columns
                WHERE table_name = 'campaigns_tag_group'
                ORDER BY ordinal_position;
            """)
            columns = cursor.fetchall()
            
            for column in columns:
                self.stdout.write(f'- {column[0]}: {column[1]} (nullable: {column[2]})')
        
        self.stdout.write(self.style.SUCCESS('\nInspection complete!'))
