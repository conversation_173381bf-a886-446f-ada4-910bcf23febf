"""
Management command to populate the database with a comprehensive tagging framework.

This script:
1. Creates tag categories for different niches
2. Creates tag groups with appropriate colors
3. Creates tags with multiple conditions
4. Associates tags with categories and groups

Usage:
    python manage.py populate_tag_framework
"""
import uuid
import logging
from django.core.management.base import BaseCommand
from django.db import transaction, connection
from django.utils import timezone

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Populate the database with a comprehensive tagging framework'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing data before populating'
        )

    def handle(self, *args, **options):
        clear_existing = options['clear']

        if clear_existing:
            self.stdout.write(self.style.WARNING('Clearing existing data...'))
            from django.core.management import call_command
            call_command('empty_backend', confirm=True, keep_users=True)

        self.stdout.write(self.style.SUCCESS('Starting to populate the tagging framework...'))

        try:
            # Import models
            from campaigns.models import (
                TagCategory, TagGroup, DynamicTag, CampaignTagRule, TagRuleCondition
            )

            with transaction.atomic():
                # Create tag categories
                self.stdout.write('Creating tag categories...')

                # Main niche categories
                fitness_category = TagCategory.objects.create(
                    name='Fitness',
                    description='Tags related to fitness and workout content',
                    color='#28a745',  # Bootstrap success
                    priority=100
                )

                fashion_category = TagCategory.objects.create(
                    name='Fashion',
                    description='Tags related to fashion and style content',
                    color='#dc3545',  # Bootstrap danger
                    priority=90
                )

                tech_category = TagCategory.objects.create(
                    name='Technology',
                    description='Tags related to technology and gadgets',
                    color='#007bff',  # Bootstrap primary
                    priority=80
                )

                travel_category = TagCategory.objects.create(
                    name='Travel',
                    description='Tags related to travel and destinations',
                    color='#17a2b8',  # Bootstrap info
                    priority=70
                )

                food_category = TagCategory.objects.create(
                    name='Food',
                    description='Tags related to food and culinary content',
                    color='#ffc107',  # Bootstrap warning
                    priority=60
                )

                # Create tag type categories
                engagement_category = TagCategory.objects.create(
                    name='Engagement',
                    description='Tags related to account engagement metrics',
                    color='#6610f2',  # Bootstrap purple
                    priority=50
                )

                demographics_category = TagCategory.objects.create(
                    name='Demographics',
                    description='Tags related to demographic information',
                    color='#fd7e14',  # Bootstrap orange
                    priority=40
                )

                content_category = TagCategory.objects.create(
                    name='Content Type',
                    description='Tags related to content type and style',
                    color='#20c997',  # Bootstrap teal
                    priority=30
                )

                behavior_category = TagCategory.objects.create(
                    name='Behavior',
                    description='Tags related to account behavior patterns',
                    color='#e83e8c',  # Bootstrap pink
                    priority=20
                )

                quality_category = TagCategory.objects.create(
                    name='Quality',
                    description='Tags related to account quality metrics',
                    color='#6c757d',  # Bootstrap secondary
                    priority=10
                )

                # Create tag groups
                self.stdout.write('Creating tag groups...')

                # Create tag groups using raw SQL
                self.stdout.write('Creating tag groups using raw SQL...')

                # Helper function to create a tag group
                def create_tag_group(name, description, color, is_global, priority):
                    group_id = uuid.uuid4()
                    with connection.cursor() as cursor:
                        cursor.execute("""
                            INSERT INTO campaigns_tag_group
                            (id, name, description, color, is_global, priority, created_at, updated_at)
                            VALUES (%s, %s, %s, %s, %s, %s, NOW(), NOW())
                            RETURNING id
                        """, [
                            str(group_id), name, description, color, is_global, priority
                        ])
                        result = cursor.fetchone()
                        return TagGroup.objects.get(id=result[0])

                # Fitness tag groups
                workout_group = create_tag_group(
                    name='Workout Types',
                    description='Different types of workouts and fitness activities',
                    color='primary',
                    is_global=True,
                    priority=100
                )

                fitness_level_group = create_tag_group(
                    name='Fitness Level',
                    description='Different levels of fitness expertise',
                    color='success',
                    is_global=True,
                    priority=95
                )

                training_style_group = create_tag_group(
                    name='Training Style',
                    description='Different approaches to fitness training',
                    color='info',
                    is_global=True,
                    priority=90
                )

                # Fashion tag groups
                style_group = create_tag_group(
                    name='Style Preferences',
                    description='Different fashion styles and preferences',
                    color='danger',
                    is_global=True,
                    priority=85
                )

                brand_group = create_tag_group(
                    name='Brand Affinity',
                    description='Affinity for different fashion brands',
                    color='warning',
                    is_global=True,
                    priority=80
                )

                fashion_content_group = create_tag_group(
                    name='Fashion Content',
                    description='Types of fashion content created',
                    color='dark',
                    is_global=True,
                    priority=75
                )

                # Technology tag groups
                tech_interest_group = create_tag_group(
                    name='Tech Interests',
                    description='Different technology interests and niches',
                    color='primary',
                    is_global=True,
                    priority=70
                )

                device_group = create_tag_group(
                    name='Device Usage',
                    description='Types of devices used or discussed',
                    color='secondary',
                    is_global=True,
                    priority=65
                )

                tech_content_group = create_tag_group(
                    name='Tech Content',
                    description='Types of technology content created',
                    color='info',
                    is_global=True,
                    priority=60
                )

                # Travel tag groups
                destination_group = create_tag_group(
                    name='Destination Types',
                    description='Types of travel destinations',
                    color='info',
                    is_global=True,
                    priority=55
                )

                travel_frequency_group = create_tag_group(
                    name='Travel Frequency',
                    description='How often the account posts about travel',
                    color='primary',
                    is_global=True,
                    priority=50
                )

                travel_content_group = create_tag_group(
                    name='Travel Content Quality',
                    description='Quality of travel content',
                    color='success',
                    is_global=True,
                    priority=45
                )

                # Food tag groups
                cuisine_group = create_tag_group(
                    name='Cuisine Preferences',
                    description='Different types of cuisine',
                    color='warning',
                    is_global=True,
                    priority=40
                )

                dietary_group = create_tag_group(
                    name='Dietary Restrictions',
                    description='Different dietary preferences and restrictions',
                    color='danger',
                    is_global=True,
                    priority=35
                )

                food_content_group = create_tag_group(
                    name='Food Content Style',
                    description='Style of food content',
                    color='dark',
                    is_global=True,
                    priority=30
                )

                # Engagement tag groups
                follower_group = create_tag_group(
                    name='Follower Metrics',
                    description='Metrics related to followers',
                    color='purple',
                    is_global=True,
                    priority=25
                )

                interaction_group = create_tag_group(
                    name='Interaction Metrics',
                    description='Metrics related to post interactions',
                    color='pink',
                    is_global=True,
                    priority=20
                )

                # Create tags and conditions
                self.stdout.write('Creating tags and conditions...')

                # Helper function to create a tag with conditions
                def create_tag_with_conditions(name, description, category, tag_group, is_system=False, conditions=None):
                    tag = DynamicTag.objects.create(
                        name=name,
                        description=description,
                        category=category,
                        tag_group=tag_group,
                        tag_type='keyword',
                        pattern='',  # Will be set by conditions
                        field='',    # Will be set by conditions
                        is_global=True,
                        is_system=is_system
                    )

                    if conditions:
                        # Create a tag rule for this tag
                        tag_rule = CampaignTagRule.objects.create(
                            name=f"Rule for {name}",
                            tag=name,
                            description=f"Conditions for {name} tag",
                            active=True,
                            is_global=True
                        )

                        # Create conditions for the tag rule
                        for condition in conditions:
                            TagRuleCondition.objects.create(
                                rule=tag_rule,
                                field=condition['field'],
                                field_type=condition.get('field_type', 'string'),
                                operator=condition['operator'],
                                value=condition['value'],
                                score=condition.get('score', 1),
                                required=condition.get('required', True)
                            )

                    return tag

                # Fitness Tags
                create_tag_with_conditions(
                    name="Weightlifting",
                    description="Accounts focused on weightlifting and strength training",
                    category=fitness_category,
                    tag_group=workout_group,
                    is_system=True,
                    conditions=[
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'weightlifting'},
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'strength training'},
                        {'field': 'posts', 'field_type': 'string', 'operator': 'contains', 'value': 'gym'}
                    ]
                )

                create_tag_with_conditions(
                    name="Yoga",
                    description="Accounts focused on yoga and mindfulness",
                    category=fitness_category,
                    tag_group=workout_group,
                    is_system=True,
                    conditions=[
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'yoga'},
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'mindfulness'},
                        {'field': 'posts', 'field_type': 'string', 'operator': 'contains', 'value': 'meditation'}
                    ]
                )

                create_tag_with_conditions(
                    name="Advanced Fitness",
                    description="Accounts with advanced fitness content",
                    category=fitness_category,
                    tag_group=fitness_level_group,
                    is_system=True,
                    conditions=[
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'professional'},
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'athlete'},
                        {'field': 'follower_count', 'field_type': 'number', 'operator': 'greater_than', 'value': 10000}
                    ]
                )

                # Fashion Tags
                create_tag_with_conditions(
                    name="Streetwear",
                    description="Accounts focused on streetwear fashion",
                    category=fashion_category,
                    tag_group=style_group,
                    is_system=True,
                    conditions=[
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'streetwear'},
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'urban'},
                        {'field': 'posts', 'field_type': 'string', 'operator': 'contains', 'value': 'style'}
                    ]
                )

                create_tag_with_conditions(
                    name="Luxury Fashion",
                    description="Accounts focused on luxury fashion brands",
                    category=fashion_category,
                    tag_group=brand_group,
                    is_system=True,
                    conditions=[
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'luxury'},
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'designer'},
                        {'field': 'posts', 'field_type': 'string', 'operator': 'contains', 'value': 'brand'}
                    ]
                )

                # Technology Tags
                create_tag_with_conditions(
                    name="Mobile Tech",
                    description="Accounts focused on mobile technology",
                    category=tech_category,
                    tag_group=tech_interest_group,
                    is_system=True,
                    conditions=[
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'mobile'},
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'smartphone'},
                        {'field': 'posts', 'field_type': 'string', 'operator': 'contains', 'value': 'app'}
                    ]
                )

                create_tag_with_conditions(
                    name="Tech Reviewer",
                    description="Accounts that review technology products",
                    category=tech_category,
                    tag_group=tech_content_group,
                    is_system=True,
                    conditions=[
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'review'},
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'tech'},
                        {'field': 'posts', 'field_type': 'string', 'operator': 'contains', 'value': 'unboxing'}
                    ]
                )

                # Travel Tags
                create_tag_with_conditions(
                    name="Beach Traveler",
                    description="Accounts focused on beach destinations",
                    category=travel_category,
                    tag_group=destination_group,
                    is_system=True,
                    conditions=[
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'beach'},
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'ocean'},
                        {'field': 'posts', 'field_type': 'string', 'operator': 'contains', 'value': 'island'}
                    ]
                )

                create_tag_with_conditions(
                    name="Frequent Traveler",
                    description="Accounts that travel frequently",
                    category=travel_category,
                    tag_group=travel_frequency_group,
                    is_system=True,
                    conditions=[
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'nomad'},
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'traveler'},
                        {'field': 'post_frequency', 'field_type': 'number', 'operator': 'greater_than', 'value': 10}
                    ]
                )

                # Food Tags
                create_tag_with_conditions(
                    name="Vegan Food",
                    description="Accounts focused on vegan cuisine",
                    category=food_category,
                    tag_group=dietary_group,
                    is_system=True,
                    conditions=[
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'vegan'},
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'plant-based'},
                        {'field': 'posts', 'field_type': 'string', 'operator': 'contains', 'value': 'vegetarian'}
                    ]
                )

                create_tag_with_conditions(
                    name="Food Photographer",
                    description="Accounts with high-quality food photography",
                    category=food_category,
                    tag_group=food_content_group,
                    is_system=True,
                    conditions=[
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'photographer'},
                        {'field': 'bio', 'field_type': 'string', 'operator': 'contains', 'value': 'food'},
                        {'field': 'posts', 'field_type': 'string', 'operator': 'contains', 'value': 'foodie'}
                    ]
                )

                # Engagement Tags
                create_tag_with_conditions(
                    name="High Engagement",
                    description="Accounts with high engagement rates",
                    category=engagement_category,
                    tag_group=interaction_group,
                    is_system=True,
                    conditions=[
                        {'field': 'engagement_rate', 'field_type': 'number', 'operator': 'greater_than', 'value': 0.05},
                        {'field': 'likes_per_post', 'field_type': 'number', 'operator': 'greater_than', 'value': 1000},
                        {'field': 'comments_per_post', 'field_type': 'number', 'operator': 'greater_than', 'value': 50}
                    ]
                )

                create_tag_with_conditions(
                    name="Growing Account",
                    description="Accounts with rapidly growing followers",
                    category=engagement_category,
                    tag_group=follower_group,
                    is_system=True,
                    conditions=[
                        {'field': 'follower_growth_rate', 'field_type': 'number', 'operator': 'greater_than', 'value': 0.1},
                        {'field': 'account_age', 'field_type': 'number', 'operator': 'less_than', 'value': 365},
                        {'field': 'follower_count', 'field_type': 'number', 'operator': 'greater_than', 'value': 1000}
                    ]
                )

                # Quality Tags
                create_tag_with_conditions(
                    name="Authentic Content",
                    description="Accounts with authentic, non-sponsored content",
                    category=quality_category,
                    tag_group=interaction_group,
                    is_system=True,
                    conditions=[
                        {'field': 'sponsored_post_ratio', 'field_type': 'number', 'operator': 'less_than', 'value': 0.2},
                        {'field': 'bio', 'field_type': 'string', 'operator': 'not_contains', 'value': 'sponsored'},
                        {'field': 'engagement_rate', 'field_type': 'number', 'operator': 'greater_than', 'value': 0.03}
                    ]
                )

                self.stdout.write(self.style.SUCCESS('Successfully populated the tagging framework!'))

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error populating the tagging framework: {str(e)}'))
            logger.exception('Error populating the tagging framework')
            raise
