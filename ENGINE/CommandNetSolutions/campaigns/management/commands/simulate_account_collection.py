"""
Management command to simulate account collection and whitelist generation.

This script:
1. Creates dummy Instagram accounts for a campaign
2. Applies tags to accounts based on tag conditions
3. Generates a whitelist based on tag matches

Usage:
    python manage.py simulate_account_collection --campaign-id <campaign_id> --accounts <num_accounts>
"""
import uuid
import random
import logging
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Simulate account collection and whitelist generation'

    def add_arguments(self, parser):
        parser.add_argument(
            '--campaign-id',
            type=str,
            required=True,
            help='ID of the campaign to simulate account collection for'
        )
        parser.add_argument(
            '--accounts',
            type=int,
            default=50,
            help='Number of dummy accounts to create'
        )

    def handle(self, *args, **options):
        campaign_id = options['campaign_id']
        num_accounts = options['accounts']

        self.stdout.write(self.style.SUCCESS(f'Simulating account collection for campaign {campaign_id}...'))

        try:
            # Import models
            from campaigns.models import (
                Campaign, DynamicTag, TagAnalysisResult, CampaignResult
            )
            from instagram.models import Accounts, WhiteListEntry

            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)

            # Get campaign tags
            campaign_tags = [ct.tag for ct in campaign.campaign_tags.all()]
            required_tags = [ct.tag for ct in campaign.campaign_tags.filter(is_required=True)]

            if not campaign_tags:
                self.stdout.write(self.style.ERROR('Campaign has no tags. Please add tags to the campaign first.'))
                return

            self.stdout.write(f'Campaign has {len(campaign_tags)} tags, {len(required_tags)} of which are required.')

            with transaction.atomic():
                # Create dummy accounts
                self.stdout.write(f'Creating {num_accounts} dummy accounts...')

                accounts = []
                for i in range(num_accounts):
                    # Generate random account data
                    username = f"user_{i}_{uuid.uuid4().hex[:8]}"
                    follower_count = random.randint(100, 10000)
                    following_count = random.randint(100, 1000)
                    post_count = random.randint(10, 500)
                    engagement_rate = random.uniform(0.01, 0.1)

                    # Create interests and bio based on random tags
                    interests = []
                    if random.random() < 0.3:
                        interests.append('fitness')
                    if random.random() < 0.3:
                        interests.append('travel')
                    if random.random() < 0.3:
                        interests.append('technology')
                    if random.random() < 0.3:
                        interests.append('fashion')
                    if random.random() < 0.3:
                        interests.append('food')

                    bio_elements = []
                    if random.random() < 0.2:
                        bio_elements.append('fitness enthusiast')
                    if random.random() < 0.2:
                        bio_elements.append('traveler')
                    if random.random() < 0.2:
                        bio_elements.append('tech lover')
                    if random.random() < 0.2:
                        bio_elements.append('fashion addict')
                    if random.random() < 0.2:
                        bio_elements.append('foodie')

                    bio = ' | '.join(bio_elements) if bio_elements else 'Instagram user'

                    # Create account
                    account = Accounts.objects.create(
                        username=username,
                        full_name=f"User {i}",
                        followers=follower_count,
                        following=following_count,
                        number_of_posts=post_count,
                        bio=bio,
                        interests=interests,
                        campaign_id=str(campaign.id),
                        is_verified=random.random() < 0.1,
                        account_type='personal' if random.random() < 0.7 else 'business',
                        collection_date=timezone.now()
                    )
                    accounts.append(account)

                # Apply tags to accounts
                self.stdout.write('Applying tags to accounts...')

                tag_match_counts = {tag.id: 0 for tag in campaign_tags}
                accounts_with_required_tags = set()

                for account in accounts:
                    account_matches_required = True

                    for tag in campaign_tags:
                        # Simulate tag matching based on account data
                        matched = False
                        confidence_score = 0.0

                        # Fitness tags
                        if tag.category and tag.category.name == 'Fitness':
                            if 'fitness' in account.interests or 'fitness' in account.bio.lower():
                                matched = True
                                confidence_score = random.uniform(0.7, 1.0)

                        # Travel tags
                        elif tag.category and tag.category.name == 'Travel':
                            if 'travel' in account.interests or 'traveler' in account.bio.lower():
                                matched = True
                                confidence_score = random.uniform(0.7, 1.0)

                        # Technology tags
                        elif tag.category and tag.category.name == 'Technology':
                            if 'technology' in account.interests or 'tech' in account.bio.lower():
                                matched = True
                                confidence_score = random.uniform(0.7, 1.0)

                        # Engagement tags
                        elif tag.category and tag.category.name == 'Engagement':
                            # Calculate engagement rate based on followers and posts
                            if account.followers > 0 and account.number_of_posts > 0:
                                # Simulate engagement rate based on followers
                                if account.followers > 5000:
                                    matched = True
                                    confidence_score = random.uniform(0.7, 1.0)

                        # Create tag analysis result
                        TagAnalysisResult.objects.create(
                            account_id=account.username,
                            campaign=campaign,
                            tag=tag,
                            matched=matched,
                            confidence_score=confidence_score,
                            match_details={'simulated': True}
                        )

                        if matched:
                            tag_match_counts[tag.id] += 1

                        # Check if this is a required tag
                        if tag in required_tags and not matched:
                            account_matches_required = False

                    if account_matches_required:
                        accounts_with_required_tags.add(account.username)

                # Generate whitelist
                self.stdout.write('Generating whitelist...')

                whitelist_count = 0
                for account in accounts:
                    if account.username in accounts_with_required_tags:
                        # Create whitelist entry with default privileges
                        WhiteListEntry.objects.create(
                            account=account,
                            dm=True,
                            discover=True,
                            comment=True,
                            post_like=True,
                            follow=True,
                            favorite=False,
                            is_auto=True,
                            tags=['simulated']
                        )
                        whitelist_count += 1

                # Create campaign result
                CampaignResult.objects.create(
                    campaign=campaign,
                    total_accounts_found=len(accounts),
                    total_accounts_processed=len(accounts),
                    total_accounts_pending=0,
                    total_accounts_tagged=sum(1 for count in tag_match_counts.values() if count > 0),
                    total_accounts_whitelisted=whitelist_count,
                    average_confidence_score=0.8,
                    analysis_duration=random.uniform(10, 60),
                    engagement_rate=0.05,  # Fixed value since we don't have real engagement data
                    last_processed_at=timezone.now()
                )

                self.stdout.write(self.style.SUCCESS(f'Successfully simulated account collection for campaign {campaign.name}:'))
                self.stdout.write(f'- Created {len(accounts)} dummy accounts')
                self.stdout.write(f'- Applied tags to accounts')
                self.stdout.write(f'- Generated whitelist with {whitelist_count} accounts')

                # Print tag match counts
                self.stdout.write('\nTag match counts:')
                for tag in campaign_tags:
                    self.stdout.write(f'- {tag.name}: {tag_match_counts[tag.id]} matches')

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error simulating account collection: {str(e)}'))
            logger.exception('Error simulating account collection')
            raise
