"""
Management command to run PyFlow workflows.
"""
import os
import subprocess
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings


class Command(BaseCommand):
    help = 'Run PyFlow workflows'

    def add_arguments(self, parser):
        parser.add_argument('--workflow', type=str, help='Path to workflow file')
        parser.add_argument('--campaign', type=str, help='Campaign ID')
        parser.add_argument('--mode', type=str, choices=['run', 'edit'], default='run', help='Run or edit mode')
        parser.add_argument('--headless', action='store_true', help='Run in headless mode (no GUI)')
        parser.add_argument('--template', type=str, help='Template name to use')

    def handle(self, *args, **options):
        # Define PyFlow paths
        pyflow_engine_dir = os.path.join(settings.BASE_DIR, '..', '..')
        pyflow_workflow_dir = os.path.join(pyflow_engine_dir, 'INSTA', 'WORKFLOW')
        pyflow_template_dir = os.path.join(settings.BASE_DIR, 'campaigns', 'pyflow_templates')

        # Change to PyFlow engine directory
        os.chdir(pyflow_engine_dir)

        # Build command
        cmd = ['python', '-m', 'pyflow']

        # Add mode
        if options['mode'] == 'edit':
            cmd.extend(['-m', 'edit'])
        else:
            cmd.extend(['-m', 'run'])

        # Add workflow file
        if options['workflow']:
            workflow_path = options['workflow']
            if not os.path.isabs(workflow_path):
                # If not absolute path, assume it's relative to workflow dir
                workflow_path = os.path.join(pyflow_workflow_dir, workflow_path)
            cmd.extend(['-f', workflow_path])
        elif options['template'] and options['campaign']:
            # Use template and campaign ID to create workflow
            from campaigns.services import PyFlowService
            service = PyFlowService()
            
            # Get template
            template_name = options['template']
            campaign_id = options['campaign']
            
            if template_name == 'collection':
                # Get campaign targets
                from campaigns.models import Campaign
                try:
                    campaign = Campaign.objects.get(id=campaign_id)
                    
                    # Get targets
                    targets = []
                    
                    if campaign.target_type == 'location':
                        for location_target in campaign.location_targets.all():
                            targets.append({
                                'type': 'location',
                                'location_id': location_target.location_id,
                                'location_name': location_target.location_name
                            })
                    else:
                        for username_target in campaign.username_targets.all():
                            targets.append({
                                'type': 'username',
                                'username': username_target.username,
                                'audience_type': username_target.audience_type
                            })
                    
                    # Create workflow
                    result = service.create_account_collection_workflow(
                        str(campaign.id),
                        targets,
                        {'audience_type': campaign.audience_type}
                    )
                    
                    if result.get('success'):
                        workflow_path = result.get('workflow_path')
                        cmd.extend(['-f', workflow_path])
                        self.stdout.write(self.style.SUCCESS(f"Created workflow: {workflow_path}"))
                    else:
                        raise CommandError(f"Failed to create workflow: {result.get('error')}")
                    
                except Campaign.DoesNotExist:
                    raise CommandError(f"Campaign not found: {campaign_id}")
                
            elif template_name == 'analysis':
                # Create analysis workflow
                result = service.create_account_analysis_workflow(
                    campaign_id,
                    {},  # Default settings
                    {}   # Default options
                )
                
                if result.get('success'):
                    workflow_path = result.get('workflow_path')
                    cmd.extend(['-f', workflow_path])
                    self.stdout.write(self.style.SUCCESS(f"Created workflow: {workflow_path}"))
                else:
                    raise CommandError(f"Failed to create workflow: {result.get('error')}")
            else:
                raise CommandError(f"Unknown template: {template_name}")
        else:
            raise CommandError("Either workflow or template and campaign must be specified")

        # Add headless mode if specified
        if options['headless']:
            cmd.append('--headless')

        # Run command
        self.stdout.write(f"Running command: {' '.join(cmd)}")
        
        try:
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
            
            # Stream output
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    self.stdout.write(output.strip())
            
            # Get return code
            return_code = process.poll()
            
            if return_code == 0:
                self.stdout.write(self.style.SUCCESS('PyFlow workflow completed successfully'))
            else:
                stderr = process.stderr.read()
                self.stdout.write(self.style.ERROR(f'PyFlow workflow failed with code {return_code}'))
                self.stdout.write(self.style.ERROR(stderr))
                
        except Exception as e:
            raise CommandError(f"Error running PyFlow: {str(e)}")
