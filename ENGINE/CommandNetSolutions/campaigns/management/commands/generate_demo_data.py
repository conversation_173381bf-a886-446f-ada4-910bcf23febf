import os
import csv
import random
import uuid
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.conf import settings
from django.contrib.auth.models import User
from django.utils import timezone
from campaigns.models import Campaign, LocationTarget, UsernameTarget, CampaignResult, CampaignAnalysisSettings
from instagram.models import Accounts, WhiteListEntry, ScoringProfile, AutoTagRule, ScoringTier, ScoringProfileRule

class Command(BaseCommand):
    help = "Generate demo data for testing the application"

    def add_arguments(self, parser):
        parser.add_argument(
            "--campaigns",
            type=int,
            default=5,
            help="Number of campaigns to generate (default: 5)",
        )
        parser.add_argument(
            "--accounts",
            type=int,
            default=100,
            help="Number of accounts to generate (default: 100)",
        )
        parser.add_argument(
            "--clear",
            action="store_true",
            help="Clear existing data before generating new data",
        )

    def handle(self, *args, **options):
        num_campaigns = options["campaigns"]
        num_accounts = options["accounts"]
        clear_data = options["clear"]

        if clear_data:
            self.clear_existing_data()
            self.stdout.write(self.style.SUCCESS("Cleared existing data"))

        # Create demo locations file if it doesn't exist
        self.create_demo_locations()

        # Create demo scoring profiles and tag rules
        scoring_profiles = self.create_scoring_profiles()

        # Create demo campaigns
        campaigns = self.create_campaigns(num_campaigns)

        # Create demo accounts
        self.create_accounts(num_accounts, campaigns)

        # Create analysis settings for campaigns
        self.create_analysis_settings(campaigns, scoring_profiles)

        self.stdout.write(self.style.SUCCESS(
            f"Successfully generated {num_campaigns} campaigns and {num_accounts} accounts"
        ))

    def clear_existing_data(self):
        """Clear existing data from the database using Django ORM"""
        # Clear Instagram-related data first
        self.stdout.write("Clearing Instagram-related data...")
        try:
            from instagram.models import WhiteListEntry
            WhiteListEntry.objects.all().delete()
            self.stdout.write(self.style.SUCCESS("Cleared WhiteListEntry data"))
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"Error clearing WhiteListEntry data: {str(e)}"))

        try:
            from instagram.models import Accounts
            Accounts.objects.all().delete()
            self.stdout.write(self.style.SUCCESS("Cleared Accounts data"))
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"Error clearing Accounts data: {str(e)}"))

        # Clear campaign-related data
        self.stdout.write("Clearing campaign-related data...")
        try:
            CampaignAnalysisSettings.objects.all().delete()
            self.stdout.write(self.style.SUCCESS("Cleared CampaignAnalysisSettings data"))
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"Error clearing CampaignAnalysisSettings data: {str(e)}"))

        try:
            CampaignResult.objects.all().delete()
            self.stdout.write(self.style.SUCCESS("Cleared CampaignResult data"))
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"Error clearing CampaignResult data: {str(e)}"))

        try:
            LocationTarget.objects.all().delete()
            self.stdout.write(self.style.SUCCESS("Cleared LocationTarget data"))
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"Error clearing LocationTarget data: {str(e)}"))

        try:
            UsernameTarget.objects.all().delete()
            self.stdout.write(self.style.SUCCESS("Cleared UsernameTarget data"))
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"Error clearing UsernameTarget data: {str(e)}"))

        try:
            Campaign.objects.all().delete()
            self.stdout.write(self.style.SUCCESS("Cleared Campaign data"))
        except Exception as e:
            self.stdout.write(self.style.WARNING(f"Error clearing Campaign data: {str(e)}"))

        self.stdout.write(self.style.SUCCESS("Finished clearing existing data"))

    def create_demo_locations(self):
        """Create a demo locations.csv file if it doesn't exist"""
        data_dir = os.path.join(settings.BASE_DIR, "campaigns", "data")
        os.makedirs(data_dir, exist_ok=True)
        locations_file = os.path.join(data_dir, "locations.csv")

        # Only create if it doesn't exist
        if not os.path.exists(locations_file):
            self.stdout.write("Creating demo locations file")

            # Sample locations data
            locations = [
                {"location_id": "1234567890", "country": "United States", "city": "New York"},
                {"location_id": "2345678901", "country": "United States", "city": "Los Angeles"},
                {"location_id": "3456789012", "country": "United States", "city": "Chicago"},
                {"location_id": "**********", "country": "United Kingdom", "city": "London"},
                {"location_id": "**********", "country": "United Kingdom", "city": "Manchester"},
                {"location_id": "**********", "country": "France", "city": "Paris"},
                {"location_id": "7890123456", "country": "Germany", "city": "Berlin"},
                {"location_id": "8901234567", "country": "Japan", "city": "Tokyo"},
                {"location_id": "9012345678", "country": "Australia", "city": "Sydney"},
                {"location_id": "0123456789", "country": "Canada", "city": "Toronto"},
                {"location_id": "1122334455", "country": "Spain", "city": "Madrid"},
                {"location_id": "**********", "country": "Italy", "city": "Rome"},
                {"location_id": "**********", "country": "Brazil", "city": "Rio de Janeiro"},
                {"location_id": "**********", "country": "South Korea", "city": "Seoul"},
                {"location_id": "**********", "country": "India", "city": "Mumbai"},
            ]

            with open(locations_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['location_id', 'country', 'city']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                writer.writeheader()
                for location in locations:
                    writer.writerow(location)

            self.stdout.write(self.style.SUCCESS(f"Created demo locations file with {len(locations)} locations"))
        else:
            self.stdout.write("Locations file already exists")

    def create_scoring_profiles(self):
        """Create demo scoring profiles and tag rules"""
        # Create tag rules if they don't exist
        tag_rules = []

        if AutoTagRule.objects.count() == 0:
            self.stdout.write("Creating demo tag rules")

            tag_rules_data = [
                {"tag": "influencer", "active": True},
                {"tag": "business", "active": True},
                {"tag": "personal", "active": True},
                {"tag": "high_engagement", "active": True},
                {"tag": "low_followers", "active": True},
            ]

            for rule_data in tag_rules_data:
                rule = AutoTagRule.objects.create(**rule_data)
                tag_rules.append(rule)
        else:
            tag_rules = list(AutoTagRule.objects.all())

        # Create scoring profiles if they don't exist
        scoring_profiles = []

        if ScoringProfile.objects.count() == 0:
            self.stdout.write("Creating demo scoring profiles")

            profile_data = [
                {"name": "Influencer Profile", "active": True},
                {"name": "Business Profile", "active": False},
                {"name": "General Profile", "active": False},
            ]

            for profile_data in profile_data:
                profile = ScoringProfile.objects.create(**profile_data)

                # Create tiers for each profile
                ScoringTier.objects.create(
                    scoring_profile=profile,
                    min_score=0,
                    max_score=50,
                    privileges={"dm": False, "comment": True, "post_like": True}
                )

                ScoringTier.objects.create(
                    scoring_profile=profile,
                    min_score=51,
                    max_score=100,
                    privileges={"dm": True, "comment": True, "post_like": True, "follow": True}
                )

                # Link tag rules to profile
                for rule in tag_rules:
                    ScoringProfileRule.objects.create(
                        profile=profile,
                        rule=rule,
                        additional_points=random.randint(5, 20)
                    )

                scoring_profiles.append(profile)
        else:
            scoring_profiles = list(ScoringProfile.objects.all())

        return scoring_profiles

    def create_campaigns(self, num_campaigns):
        """Create demo campaigns with targets"""
        self.stdout.write(f"Creating {num_campaigns} demo campaigns")

        # Get or create admin user
        admin_user, _ = User.objects.get_or_create(
            username="admin",
            defaults={"is_staff": True, "is_superuser": True}
        )

        # Get locations from CSV file
        locations = self.get_locations_from_csv()

        # Sample usernames
        sample_usernames = [
            "instagram", "cristiano", "leomessi", "beyonce", "taylorswift",
            "selenagomez", "arianagrande", "therock", "kyliejenner", "kimkardashian",
            "justinbieber", "kendalljenner", "natgeo", "nike", "khloekardashian",
            "mileycyrus", "katyperry", "jlo", "kourtneykardash", "kevinhart4real"
        ]

        # Campaign statuses with weights
        statuses = ["draft", "pending", "running", "completed", "failed"]
        status_weights = [0.2, 0.2, 0.3, 0.2, 0.1]  # 20% draft, 20% pending, etc.

        campaigns = []

        for i in range(num_campaigns):
            # Create campaign with only the fields that exist in the database
            try:
                campaign = Campaign.objects.create(
                    id=uuid.uuid4(),
                    name=f"Demo Campaign {i+1}",
                    description=f"This is a demo campaign for testing purposes #{i+1}",
                    status=random.choices(statuses, status_weights)[0],
                    target_type=random.choice(["location", "username"]),
                    audience_type=random.choice(["profile", "followers", "following", "both"]),
                    creator=admin_user,
                    airflow_run_id="pending" if random.random() < 0.7 else f"manual_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    airflow_dag_id="campaign_data_collection",
                    is_favorite=random.random() < 0.2,  # 20% chance of being a favorite
                    dmp_conf={}  # Empty JSON object
                )
                self.stdout.write(self.style.SUCCESS(f"Created campaign: {campaign.name}"))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error creating campaign: {str(e)}"))
                # Try a more minimal approach
                try:
                    campaign = Campaign()
                    campaign.id = uuid.uuid4()
                    campaign.name = f"Demo Campaign {i+1}"
                    campaign.description = f"This is a demo campaign for testing purposes #{i+1}"
                    campaign.status = random.choices(statuses, status_weights)[0]
                    campaign.target_type = random.choice(["location", "username"])
                    campaign.audience_type = random.choice(["profile", "followers", "following", "both"])
                    campaign.creator = admin_user
                    campaign.airflow_run_id = "pending" if random.random() < 0.7 else f"manual_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    campaign.airflow_dag_id = "campaign_data_collection"
                    campaign.is_favorite = random.random() < 0.2
                    campaign.dmp_conf = {}
                    campaign.save()
                    self.stdout.write(self.style.SUCCESS(f"Created campaign (fallback method): {campaign.name}"))
                except Exception as e2:
                    self.stdout.write(self.style.ERROR(f"Error creating campaign (fallback method): {str(e2)}"))
                    # If we can't create the campaign, skip this iteration
                    continue

            # Create campaign result
            CampaignResult.objects.create(
                id=uuid.uuid4(),
                campaign=campaign,
                total_accounts_found=random.randint(50, 200),
                total_accounts_processed=random.randint(0, 50),
                last_processed_at=timezone.now() if random.random() < 0.7 else None
            )

            # Add targets based on target type
            if campaign.target_type == "location":
                # Add 1-3 random locations
                for _ in range(random.randint(1, 3)):
                    if locations:
                        location = random.choice(locations)
                        LocationTarget.objects.create(
                            id=uuid.uuid4(),
                            campaign=campaign,
                            country=location["country"],
                            city=location["city"],
                            location_id=location["location_id"]
                        )
            else:
                # Add 1-5 random usernames
                for _ in range(random.randint(1, 5)):
                    username = random.choice(sample_usernames)
                    UsernameTarget.objects.create(
                        id=uuid.uuid4(),
                        campaign=campaign,
                        username=username,
                        audience_type=campaign.audience_type
                    )

            campaigns.append(campaign)

        return campaigns

    def create_accounts(self, num_accounts, campaigns):
        """Create demo Instagram accounts linked to campaigns"""
        self.stdout.write(f"Creating {num_accounts} demo accounts")

        # Sample account data
        first_names = ["John", "Jane", "Michael", "Emily", "David", "Sarah", "James", "Emma", "Robert", "Olivia"]
        last_names = ["Smith", "Johnson", "Williams", "Jones", "Brown", "Davis", "Miller", "Wilson", "Moore", "Taylor"]

        for i in range(num_accounts):
            # Create a random username
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            username = f"{first_name.lower()}{last_name.lower()}{random.randint(1, 999)}"

            # Assign to a random campaign
            campaign = random.choice(campaigns) if campaigns else None

            # Create account
            account = Accounts.objects.create(
                username=username,
                full_name=f"{first_name} {last_name}",
                bio=f"This is a demo account for {first_name} {last_name}",
                followers=random.randint(100, 10000),
                following=random.randint(100, 1000),
                number_of_posts=random.randint(10, 500),
                account_type=random.choice(["personal", "business"]),
                is_verified=random.random() < 0.1,  # 10% chance of being verified
                campaign=campaign,
                collection_date=timezone.now() if campaign else None
            )

            # Add to whitelist with 30% probability
            if random.random() < 0.3:
                WhiteListEntry.objects.create(
                    account=account,
                    tags=["demo", random.choice(["influencer", "business", "personal"])],
                    dm=random.random() < 0.5,
                    discover=random.random() < 0.5,
                    comment=random.random() < 0.5,
                    post_like=random.random() < 0.7,
                    favorite=random.random() < 0.3,
                    follow=random.random() < 0.4,
                    is_auto=True
                )

    def create_analysis_settings(self, campaigns, scoring_profiles):
        """Create analysis settings for campaigns"""
        self.stdout.write("Creating analysis settings for campaigns")

        for campaign in campaigns:
            # Create analysis settings with 80% probability
            if random.random() < 0.8:
                # Create workflow tracking data
                current_time = timezone.now().isoformat()
                workflow_name = f"analysis_{current_time}"

                # Create sample workflow data
                active_workflows = {}
                completed_workflows = {}
                workflow_statistics = {
                    "last_analysis": {
                        "timestamp": current_time,
                        "total_accounts": random.randint(50, 200),
                        "total_tagged": random.randint(20, 100),
                        "total_matched": random.randint(10, 50),
                        "avg_confidence": random.uniform(0.5, 0.9)
                    }
                }

                # Add completed workflows
                for i in range(random.randint(0, 3)):
                    workflow_id = str(uuid.uuid4())
                    completed_time = (timezone.now() - timedelta(days=i)).isoformat()
                    completed_workflows[workflow_id] = {
                        'name': f"workflow_{i}_{completed_time}",
                        'type': random.choice(['analysis', 'collection', 'engagement']),
                        'completed_at': completed_time,
                        'status': 'completed',
                        'results': {
                            'total_accounts': random.randint(50, 200),
                            'total_tagged': random.randint(20, 100),
                            'total_matched': random.randint(10, 50),
                            'avg_confidence': random.uniform(0.5, 0.9)
                        }
                    }

                # Create the analysis settings
                CampaignAnalysisSettings.objects.create(
                    id=uuid.uuid4(),
                    campaign=campaign,
                    auto_analyze=random.random() < 0.7,
                    analysis_frequency=random.choice(["immediate", "hourly", "daily", "manual"]),
                    min_followers=random.choice([None, 100, 500, 1000]),
                    max_followers=random.choice([None, 5000, 10000, 50000]),
                    target_tags=random.sample(["influencer", "business", "personal", "high_engagement"], k=random.randint(0, 3)),
                    active_workflows=active_workflows,
                    completed_workflows=completed_workflows,
                    workflow_statistics=workflow_statistics
                )

    def get_locations_from_csv(self):
        """Get locations from the CSV file"""
        locations = []
        locations_file = os.path.join(settings.BASE_DIR, "campaigns", "data", "locations.csv")

        if os.path.exists(locations_file):
            try:
                with open(locations_file, 'r', encoding='utf-8') as csvfile:
                    reader = csv.DictReader(csvfile)
                    for row in reader:
                        locations.append(row)
            except Exception as e:
                self.stderr.write(f"Error reading locations file: {str(e)}")

        return locations
