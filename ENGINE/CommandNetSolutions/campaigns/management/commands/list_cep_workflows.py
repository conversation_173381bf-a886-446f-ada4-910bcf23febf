"""
Management command to list CEP workflows.

This script:
1. Lists all CEP workflows
2. Shows their status, subscription tier, and campaign

Usage:
    python manage.py list_cep_workflows
"""
import logging
from django.core.management.base import BaseCommand

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'List CEP workflows'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Listing CEP workflows...'))
        
        try:
            # Import models
            from campaigns.models.cep import CEPWorkflow
            
            # Get all workflows
            workflows = CEPWorkflow.objects.all().order_by('-created_at')
            
            if not workflows:
                self.stdout.write('No CEP workflows found.')
                return
            
            # Print header
            self.stdout.write(f"{'ID':<36} | {'Campaign':<20} | {'Status':<10} | {'Tier':<10} | {'Created':<20}")
            self.stdout.write('-' * 100)
            
            # Print workflows
            for workflow in workflows:
                self.stdout.write(
                    f"{str(workflow.id):<36} | "
                    f"{workflow.campaign.name[:20]:<20} | "
                    f"{workflow.get_status_display():<10} | "
                    f"{workflow.get_subscription_tier_display():<10} | "
                    f"{workflow.created_at.strftime('%Y-%m-%d %H:%M:%S'):<20}"
                )
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error listing CEP workflows: {str(e)}'))
            logger.exception('Error listing CEP workflows')
            raise
