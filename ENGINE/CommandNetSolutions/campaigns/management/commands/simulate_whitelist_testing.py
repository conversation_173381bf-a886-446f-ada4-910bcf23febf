"""
Django management command to simulate comprehensive whitelist testing.

This command implements the CORRECTED architecture where:
1. Simulates realistic account collection based on campaign targets
2. Applies tag filtering DIRECTLY as synchronous filtering (not separate workflow)
3. Generates WhiteListEntry records immediately for accounts that pass filtering
4. Creates comprehensive test data for whitelist analytics

ARCHITECTURAL CORRECTION:
- Tag analysis is now a direct filtering process, not an asynchronous workflow
- No intermediate TagAnalysisResult storage for basic filtering
- Whitelist entries are generated directly from filtered accounts
"""

import random
import json
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction

from campaigns.models import Campaign, WorkflowExecution, TagAnalysisResult
from campaigns.services.tag_filtering_service import TagFilteringService
from instagram.models import Accounts, WhiteListEntry


class Command(BaseCommand):
    help = 'Simulate comprehensive whitelist testing for a campaign'

    def add_arguments(self, parser):
        parser.add_argument(
            'campaign_id',
            type=str,
            help='Campaign ID to simulate'
        )
        parser.add_argument(
            '--num-accounts',
            type=int,
            default=150,
            help='Number of accounts to simulate (default: 150)'
        )
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing accounts and whitelist entries for this campaign'
        )

    def handle(self, *args, **options):
        campaign_id = options['campaign_id']
        num_accounts = options['num_accounts']
        clear_existing = options['clear_existing']

        self.stdout.write(
            self.style.SUCCESS(f'Starting whitelist testing simulation for campaign {campaign_id}')
        )

        try:
            campaign = Campaign.objects.get(id=campaign_id)
        except Campaign.DoesNotExist:
            self.stdout.write(
                self.style.ERROR(f'Campaign {campaign_id} does not exist')
            )
            return

        if clear_existing:
            self.clear_existing_data(campaign)

        with transaction.atomic():
            # Step 1: Simulate account collection
            accounts = self.simulate_account_collection(campaign, num_accounts)

            # Step 2: Apply CORRECTED tag filtering (synchronous, direct filtering)
            filtering_service = TagFilteringService()
            filtering_results = filtering_service.filter_and_whitelist_accounts(campaign, accounts)

            # Step 3: Create workflow execution records
            self.create_workflow_records(
                campaign,
                len(accounts),
                filtering_results['filtered_accounts'],
                filtering_results['statistics']
            )

        self.stdout.write(
            self.style.SUCCESS(
                f'Simulation completed:\n'
                f'- Created {len(accounts)} accounts\n'
                f'- Applied {filtering_results["tag_rules_applied"]} tag filtering rules\n'
                f'- Generated {filtering_results["filtered_accounts"]} whitelist entries\n'
                f'- Conversion rate: {filtering_results["statistics"]["conversion_rate"]:.1f}%\n'
                f'- Campaign status updated to completed'
            )
        )

    def clear_existing_data(self, campaign):
        """Clear existing simulation data for the campaign"""
        self.stdout.write('Clearing existing data...')

        # Delete in order of foreign key dependencies
        # Clean up old TagAnalysisResult records first (from previous incorrect architecture)
        TagAnalysisResult.objects.filter(campaign=campaign).delete()
        WhiteListEntry.objects.filter(account__campaign_id=str(campaign.id)).delete()
        Accounts.objects.filter(campaign_id=str(campaign.id)).delete()
        WorkflowExecution.objects.filter(campaign_id=str(campaign.id)).delete()

    def simulate_account_collection(self, campaign, num_accounts):
        """Simulate realistic account collection based on campaign targets"""
        self.stdout.write(f'Simulating collection of {num_accounts} accounts...')

        accounts = []

        # Get campaign targets for context
        username_targets = campaign.username_targets.all()
        location_targets = campaign.location_targets.all()

        # Define account templates based on campaign type
        if 'fitness' in campaign.name.lower():
            account_templates = self.get_fitness_account_templates()
        elif 'fashion' in campaign.name.lower():
            account_templates = self.get_fashion_account_templates()
        elif 'tech' in campaign.name.lower():
            account_templates = self.get_tech_account_templates()
        else:
            account_templates = self.get_mixed_account_templates()

        for i in range(num_accounts):
            template = random.choice(account_templates)

            account = Accounts.objects.create(
                username=f"{template['username_base']}_{i+1}",
                full_name=f"{template['username_base'].replace('_', ' ').title()} {i+1}",
                bio=random.choice(template['bios']),
                followers=random.randint(template['followers_min'], template['followers_max']),
                following=random.randint(template['following_min'], template['following_max']),
                number_of_posts=random.randint(template['posts_min'], template['posts_max']),
                interests=random.choice(template['interests']),
                account_type=random.choice(template['account_types']),
                is_verified=random.random() < template['verified_rate'],
                campaign_id=str(campaign.id),
                collection_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                locations=random.sample(template['locations'], random.randint(1, 3))
            )
            accounts.append(account)

        return accounts

    def get_fitness_account_templates(self):
        """Get fitness-specific account templates"""
        return [
            {
                'username_base': 'fitness_coach',
                'bios': [
                    "Certified personal trainer 💪 Helping you reach your fitness goals #fitness #gym",
                    "Fitness coach | Transformation specialist | DM for training plans 🏋️‍♂️",
                    "NASM certified trainer | Nutrition coach | Online coaching available 💊",
                ],
                'followers_min': 5000, 'followers_max': 100000,
                'following_min': 500, 'following_max': 3000,
                'posts_min': 200, 'posts_max': 1000,
                'interests': [['fitness', 'health', 'nutrition'], ['fitness', 'gym', 'strength']],
                'account_types': ['creator', 'business', 'personal'],
                'verified_rate': 0.15,
                'locations': ['Los Angeles', 'Miami', 'New York', 'Austin']
            },
            {
                'username_base': 'yoga_instructor',
                'bios': [
                    "Yoga instructor 🧘‍♀️ Mind-body wellness | Online classes available",
                    "RYT 200 certified | Mindfulness coach | Retreat leader 🌱",
                    "Yoga teacher | Meditation guide | Holistic wellness advocate ✨",
                ],
                'followers_min': 2000, 'followers_max': 50000,
                'following_min': 300, 'following_max': 2000,
                'posts_min': 150, 'posts_max': 800,
                'interests': [['yoga', 'mindfulness', 'wellness'], ['fitness', 'meditation', 'health']],
                'account_types': ['creator', 'personal'],
                'verified_rate': 0.08,
                'locations': ['Los Angeles', 'San Francisco', 'Portland', 'Boulder']
            }
        ]

    def get_fashion_account_templates(self):
        """Get fashion-specific account templates"""
        return [
            {
                'username_base': 'fashion_blogger',
                'bios': [
                    "Fashion blogger ✨ Daily outfit inspiration | Shop my looks ⬇️ #fashion #style",
                    "Style influencer | Fashion tips & trends | Collaboration inquiries: DM 💄",
                    "Fashion enthusiast | Outfit posts | Style inspiration daily 👗",
                ],
                'followers_min': 10000, 'followers_max': 500000,
                'following_min': 1000, 'following_max': 5000,
                'posts_min': 300, 'posts_max': 2000,
                'interests': [['fashion', 'style', 'beauty'], ['fashion', 'luxury', 'designer']],
                'account_types': ['creator', 'business', 'personal'],
                'verified_rate': 0.12,
                'locations': ['Milan', 'Paris', 'New York', 'London']
            },
            {
                'username_base': 'style_consultant',
                'bios': [
                    "Personal stylist | Fashion consultant | Making style accessible 💫",
                    "Professional stylist | Wardrobe consultant | Book consultations via DM 📧",
                    "Style expert | Personal shopping | Fashion advice | DM for services 🛍️",
                ],
                'followers_min': 5000, 'followers_max': 80000,
                'following_min': 800, 'following_max': 3000,
                'posts_min': 200, 'posts_max': 1200,
                'interests': [['fashion', 'style', 'consulting'], ['fashion', 'business', 'personal']],
                'account_types': ['business', 'creator'],
                'verified_rate': 0.10,
                'locations': ['New York', 'Los Angeles', 'Chicago', 'Miami']
            }
        ]

    def get_tech_account_templates(self):
        """Get tech-specific account templates"""
        return [
            {
                'username_base': 'tech_reviewer',
                'bios': [
                    "Tech reviewer | Latest gadgets & reviews | YouTube: TechReviews 📱",
                    "Technology enthusiast | Product reviews | Tech news & updates 💻",
                    "Gadget reviewer | Tech tips | Unboxing videos | Subscribe for more! 🔔",
                ],
                'followers_min': 15000, 'followers_max': 200000,
                'following_min': 500, 'following_max': 2000,
                'posts_min': 100, 'posts_max': 800,
                'interests': [['technology', 'gadgets', 'reviews'], ['tech', 'innovation', 'startup']],
                'account_types': ['creator', 'business'],
                'verified_rate': 0.18,
                'locations': ['San Francisco', 'Seattle', 'Austin', 'Boston']
            }
        ]

    def get_mixed_account_templates(self):
        """Get mixed account templates for general campaigns"""
        return self.get_fitness_account_templates() + self.get_fashion_account_templates() + self.get_tech_account_templates()

    # OLD METHODS REMOVED - REPLACED WITH TagFilteringService
    # The corrected architecture uses direct filtering instead of separate tag analysis

    # All old tag analysis methods removed - using TagFilteringService instead

    def create_workflow_records(self, campaign, num_accounts, num_whitelist, statistics):
        """Create workflow execution records for tracking"""
        self.stdout.write('Creating workflow execution records...')

        # Create collection workflow
        collection_workflow = WorkflowExecution.objects.create(
            campaign_id=str(campaign.id),
            workflow_name='account_collection_simulation',
            workflow_path='/simulated/account_collection.pygraph',
            workflow_type='collection',
            status='completed',
            start_time=timezone.now() - timedelta(hours=2),
            end_time=timezone.now() - timedelta(hours=1),
            duration=3600,  # 1 hour
            total_items=num_accounts,
            processed_items=num_accounts,
            successful_items=num_accounts,
            failed_items=0,
            progress=100.0
        )

        # Create CORRECTED filtering workflow (not separate analysis workflow)
        filtering_workflow = WorkflowExecution.objects.create(
            campaign_id=str(campaign.id),
            workflow_name='tag_filtering_simulation',
            workflow_path='/simulated/tag_filtering.pygraph',
            workflow_type='filtering',
            status='completed',
            start_time=timezone.now() - timedelta(hours=1),
            end_time=timezone.now() - timedelta(minutes=30),
            duration=1800,  # 30 minutes
            total_items=num_accounts,
            processed_items=num_accounts,
            successful_items=num_whitelist,
            failed_items=num_accounts - num_whitelist,
            progress=100.0
        )

        # Update campaign status
        campaign.status = 'completed'
        campaign.save()

        return [collection_workflow, filtering_workflow]
