"""
Django management command to create test campaigns with realistic data.

This command:
1. Cleans existing campaign and account data
2. Creates three test campaigns with different statuses
3. Generates realistic Instagram account data for testing
4. Creates whitelist entries for testing tag analysis
"""

import random
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction

from campaigns.models import Campaign, LocationTarget, UsernameTarget
from instagram.models import Accounts, WhiteListEntry
from django.contrib.auth.models import User


class Command(BaseCommand):
    help = 'Create test campaigns with realistic data for development and testing'

    def add_arguments(self, parser):
        parser.add_argument(
            '--skip-cleanup',
            action='store_true',
            help='Skip cleaning existing data',
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting test campaign creation...'))

        if not options['skip_cleanup']:
            self.clean_existing_data()

        # Create or get a test user for campaigns
        self.test_user = self.get_or_create_test_user()

        with transaction.atomic():
            # Create the three test campaigns
            campaign1 = self.create_tech_influencers_draft()
            campaign2 = self.create_fashion_campaign_active()
            campaign3 = self.create_fitness_campaign_complete()

            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully created test campaigns:\n'
                    f'1. {campaign1.name} (ID: {campaign1.id})\n'
                    f'2. {campaign2.name} (ID: {campaign2.id})\n'
                    f'3. {campaign3.name} (ID: {campaign3.id})'
                )
            )

    def get_or_create_test_user(self):
        """Get or create a test user for campaigns"""
        user, created = User.objects.get_or_create(
            username='test_campaign_user',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )
        if created:
            self.stdout.write('Created test user for campaigns')
        return user

    def clean_existing_data(self):
        """Clean existing test data"""
        self.stdout.write('Cleaning existing data...')

        # Import models to handle foreign key constraints
        from campaigns.models import TagAnalysisResult, WorkflowExecution, CampaignTag

        # Delete in order of foreign key dependencies

        # Delete tag analysis results first (foreign key constraint)
        TagAnalysisResult.objects.all().delete()

        # Delete workflow executions (foreign key constraint)
        WorkflowExecution.objects.all().delete()

        # Delete campaign tags (foreign key constraint)
        CampaignTag.objects.all().delete()

        # Delete whitelist entries (foreign key constraint)
        WhiteListEntry.objects.all().delete()

        # Delete accounts with campaign_id (keep accounts without campaign_id)
        deleted_accounts = Accounts.objects.filter(campaign_id__isnull=False).delete()
        self.stdout.write(f'Deleted {deleted_accounts[0]} campaign accounts')

        # Delete campaign targets
        LocationTarget.objects.all().delete()
        UsernameTarget.objects.all().delete()

        # Delete campaigns
        deleted_campaigns = Campaign.objects.all().delete()
        self.stdout.write(f'Deleted {deleted_campaigns[0]} campaigns')

    def create_tech_influencers_draft(self):
        """Create Campaign 1: Tech Influencers Draft"""
        self.stdout.write('Creating Tech Influencers Draft campaign...')

        campaign = Campaign.objects.create(
            name="Tech Influencers Draft",
            description="Draft campaign targeting technology influencers",
            status='draft',
            target_type='mixed',
            audience_type='both',
            creator=self.test_user
        )

        # Username targets with mixed audience types
        username_targets = [
            ('tech_guru_alex', 'profile'),
            ('coding_master_sarah', 'followers'),
            ('startup_founder_mike', 'following'),
        ]

        for username, audience_type in username_targets:
            UsernameTarget.objects.create(
                campaign=campaign,
                username=username,
                audience_type=audience_type,
                processed=False
            )

        # Location targets (tech hubs)
        location_targets = [
            ('United States', 'San Francisco', '*********'),
            ('United States', 'New York', '*********'),
            ('United States', 'Austin', '*********'),
        ]

        for country, city, location_id in location_targets:
            LocationTarget.objects.create(
                campaign=campaign,
                country=country,
                city=city,
                location_id=location_id
            )

        return campaign

    def create_fashion_campaign_active(self):
        """Create Campaign 2: Fashion Campaign Active with 75 accounts"""
        self.stdout.write('Creating Fashion Campaign Active with accounts...')

        campaign = Campaign.objects.create(
            name="Fashion Campaign Active",
            description="Active campaign collecting fashion accounts",
            status='running',
            target_type='mixed',
            audience_type='both',
            creator=self.test_user
        )

        # Username targets with varied audience types
        username_targets = [
            ('fashionista_emma', 'profile'),
            ('style_blogger_james', 'followers'),
            ('fashion_designer_lisa', 'following'),
            ('runway_model_alex', 'both'),
        ]

        for username, audience_type in username_targets:
            UsernameTarget.objects.create(
                campaign=campaign,
                username=username,
                audience_type=audience_type,
                processed=random.choice([True, False])
            )

        # Location targets (fashion cities)
        location_targets = [
            ('Italy', 'Milan', '*********'),
            ('France', 'Paris', '*********'),
            ('United States', 'Los Angeles', '*********'),
            ('United Kingdom', 'London', '*********'),
        ]

        for country, city, location_id in location_targets:
            LocationTarget.objects.create(
                campaign=campaign,
                country=country,
                city=city,
                location_id=location_id
            )

        # Create 75 fashion accounts
        self.create_fashion_accounts(campaign, 75)

        return campaign

    def create_fitness_campaign_complete(self):
        """Create Campaign 3: Fitness Campaign Complete with 150 accounts and whitelist"""
        self.stdout.write('Creating Fitness Campaign Complete with accounts and whitelist...')

        campaign = Campaign.objects.create(
            name="Fitness Campaign Complete",
            description="Completed campaign with fitness accounts",
            status='completed',
            target_type='mixed',
            audience_type='both',
            creator=self.test_user
        )

        # Username targets across all audience types
        username_targets = [
            ('fitness_coach_john', 'profile'),
            ('gym_warrior_sarah', 'followers'),
            ('yoga_master_lisa', 'following'),
            ('crossfit_champion_mike', 'both'),
            ('nutrition_expert_anna', 'profile'),
        ]

        for username, audience_type in username_targets:
            UsernameTarget.objects.create(
                campaign=campaign,
                username=username,
                audience_type=audience_type,
                processed=True
            )

        # Location targets (fitness-related locations)
        location_targets = [
            ('United States', 'Los Angeles', '*********'),
            ('United States', 'Miami', '*********'),
            ('Australia', 'Sydney', '*********'),
            ('Brazil', 'Rio de Janeiro', '*********'),
            ('Germany', 'Berlin', '*********'),
        ]

        for country, city, location_id in location_targets:
            LocationTarget.objects.create(
                campaign=campaign,
                country=country,
                city=city,
                location_id=location_id
            )

        # Create 150 fitness accounts
        accounts = self.create_fitness_accounts(campaign, 150)

        # Create 25 whitelist entries from random accounts
        self.create_whitelist_entries(accounts, 25)

        return campaign

    def create_fashion_accounts(self, campaign, count):
        """Create fashion-related Instagram accounts"""
        self.stdout.write(f'Creating {count} fashion accounts...')

        fashion_usernames = [
            'fashionista_sarah', 'style_blogger_mike', 'runway_model_emma', 'fashion_designer_alex',
            'outfit_inspiration_lisa', 'trendy_fashionista', 'style_maven_john', 'fashion_forward_anna',
            'chic_stylist_david', 'glamour_girl_sophie', 'fashion_enthusiast_tom', 'style_icon_maria',
            'boutique_owner_james', 'fashion_photographer_kate', 'model_scout_ryan', 'designer_dreams_lucy',
            'fashion_week_insider', 'style_consultant_mark', 'vintage_fashion_lover', 'haute_couture_fan',
            'street_style_star', 'fashion_blogger_elite', 'style_influencer_pro', 'fashion_trendsetter',
            'luxury_fashion_addict', 'sustainable_fashion_advocate', 'fashion_startup_founder'
        ]

        fashion_bios = [
            "Fashion blogger sharing daily outfit inspiration ✨ #fashion #style #ootd",
            "Professional stylist | Fashion consultant | DM for collaborations 💄",
            "Runway model | Fashion week regular | Represented by @agency #model",
            "Fashion designer creating sustainable luxury pieces 🌱 #sustainablefashion",
            "Style influencer | Fashion tips & trends | Shop my looks ⬇️ #fashionista",
            "Boutique owner | Curating unique fashion finds | Online store 👗",
            "Fashion photographer capturing style moments 📸 #fashionphotography",
            "Personal shopper | Style consultant | Making fashion accessible 💫",
            "Vintage fashion collector | Thrift finds | Sustainable style advocate",
            "Fashion week attendee | Industry insider | Latest trends & news",
            "Luxury fashion enthusiast | Designer pieces | High-end style",
            "Street style photographer | Urban fashion | Real style inspiration",
            "Fashion startup founder | Disrupting the industry | Tech meets fashion",
            "Model agency scout | Discovering new talent | Fashion industry professional",
            "Fashion editor | Style writer | Trend forecaster | Industry expert"
        ]

        fashion_interests = [
            ['fashion', 'style', 'beauty'],
            ['fashion', 'luxury', 'designer'],
            ['fashion', 'sustainable', 'ethical'],
            ['fashion', 'vintage', 'thrift'],
            ['fashion', 'streetstyle', 'urban'],
            ['fashion', 'runway', 'model'],
            ['fashion', 'photography', 'art'],
            ['fashion', 'business', 'entrepreneur']
        ]

        fashion_locations = ['Milan', 'Paris', 'New York', 'London', 'Los Angeles']

        accounts = []
        for i in range(count):
            username = f"{random.choice(fashion_usernames)}_{i+1}" if i >= len(fashion_usernames) else fashion_usernames[i]

            account = Accounts.objects.create(
                username=username,
                full_name=f"{username.replace('_', ' ').title()}",
                bio=random.choice(fashion_bios),
                followers=random.randint(1000, 500000),
                following=random.randint(100, 5000),
                number_of_posts=random.randint(50, 2000),
                interests=random.choice(fashion_interests),
                account_type=random.choice(['personal', 'business', 'creator']),
                is_verified=random.choice([True, False]) if random.random() < 0.1 else False,
                campaign_id=str(campaign.id),
                collection_date=timezone.now() - timedelta(days=random.randint(1, 30)),
                locations=random.sample(fashion_locations, random.randint(1, 3))
            )
            accounts.append(account)

        return accounts

    def create_fitness_accounts(self, campaign, count):
        """Create fitness-related Instagram accounts"""
        self.stdout.write(f'Creating {count} fitness accounts...')

        fitness_usernames = [
            'gym_warrior_john', 'yoga_master_lisa', 'crossfit_champion_mike', 'fitness_coach_sarah',
            'nutrition_expert_anna', 'personal_trainer_alex', 'marathon_runner_emma', 'bodybuilder_david',
            'pilates_instructor_sophie', 'fitness_influencer_tom', 'health_coach_maria', 'workout_enthusiast_james',
            'strength_trainer_kate', 'cardio_queen_ryan', 'fitness_model_lucy', 'gym_owner_mark',
            'sports_nutritionist', 'fitness_photographer', 'wellness_advocate', 'athletic_performance_coach',
            'functional_fitness_expert', 'outdoor_fitness_lover', 'home_workout_guru', 'fitness_app_founder',
            'sports_medicine_doc', 'fitness_equipment_reviewer', 'workout_program_creator'
        ]

        fitness_bios = [
            "Certified personal trainer | Helping you reach your fitness goals 💪 #fitness #gym",
            "Yoga instructor | Mind-body wellness | Online classes available 🧘‍♀️ #yoga",
            "CrossFit athlete | Functional fitness | Train hard, recover harder 🏋️‍♂️ #crossfit",
            "Nutrition coach | Healthy lifestyle advocate | Meal prep tips 🥗 #nutrition",
            "Fitness model | Workout routines | Supplement reviews 💊 #fitnessmotivation",
            "Marathon runner | Endurance training | Running tips & motivation 🏃‍♀️ #running",
            "Bodybuilding competitor | Muscle building | Transformation coach 💪 #bodybuilding",
            "Pilates instructor | Core strength | Flexibility training 🤸‍♀️ #pilates",
            "Gym owner | Fitness community builder | Equipment expert 🏋️ #gymlife",
            "Sports nutritionist | Performance optimization | Evidence-based advice 📊 #sportsnutrition",
            "Wellness coach | Holistic health | Mind-body connection 🌱 #wellness",
            "Fitness photographer | Capturing strength | Athletic portraits 📸 #fitnessphotography",
            "Home workout specialist | No gym needed | Bodyweight training 🏠 #homeworkout",
            "Outdoor fitness enthusiast | Nature workouts | Adventure training 🌲 #outdoorfitness",
            "Functional movement specialist | Injury prevention | Movement quality 🔄 #functionalfitness"
        ]

        fitness_interests = [
            ['fitness', 'health', 'nutrition'],
            ['fitness', 'gym', 'strength'],
            ['fitness', 'cardio', 'endurance'],
            ['fitness', 'yoga', 'mindfulness'],
            ['fitness', 'crossfit', 'functional'],
            ['fitness', 'bodybuilding', 'muscle'],
            ['fitness', 'running', 'marathon'],
            ['fitness', 'wellness', 'lifestyle']
        ]

        fitness_locations = ['Los Angeles', 'Miami', 'Sydney', 'Rio de Janeiro', 'Berlin']

        accounts = []
        for i in range(count):
            username = f"{random.choice(fitness_usernames)}_{i+1}" if i >= len(fitness_usernames) else fitness_usernames[i]

            # Weight toward creator and business accounts for fitness
            account_type_weights = ['creator'] * 4 + ['business'] * 3 + ['personal'] * 2

            account = Accounts.objects.create(
                username=username,
                full_name=f"{username.replace('_', ' ').title()}",
                bio=random.choice(fitness_bios),
                followers=random.randint(5000, 1000000),
                following=random.randint(200, 8000),
                number_of_posts=random.randint(100, 3000),
                interests=random.choice(fitness_interests),
                account_type=random.choice(account_type_weights),
                is_verified=random.choice([True, False]) if random.random() < 0.15 else False,
                campaign_id=str(campaign.id),
                collection_date=timezone.now() - timedelta(days=random.randint(1, 60)),
                locations=random.sample(fitness_locations, random.randint(1, 3))
            )
            accounts.append(account)

        return accounts

    def create_whitelist_entries(self, accounts, count):
        """Create whitelist entries for random accounts"""
        self.stdout.write(f'Creating {count} whitelist entries...')

        selected_accounts = random.sample(accounts, min(count, len(accounts)))

        for account in selected_accounts:
            WhiteListEntry.objects.create(
                account=account,
                tags=['fitness_influencer', 'high_engagement', 'verified_account'] if account.is_verified
                     else ['fitness_enthusiast', 'active_user'],
                dm=random.choice([True, False]),
                discover=random.choice([True, False]),
                comment=random.choice([True, False]),
                post_like=random.choice([True, False]),
                favorite=random.choice([True, False]),
                follow=random.choice([True, False]),
                is_auto=True
            )