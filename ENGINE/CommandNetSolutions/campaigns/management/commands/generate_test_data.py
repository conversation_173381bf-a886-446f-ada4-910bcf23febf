"""
Management command to generate test data for the campaigns app.

This script creates:
1. Demo tags and tag groups
2. A test campaign
3. Dummy Instagram accounts
4. Simulates account collection
5. Applies tags to accounts
6. Generates a whitelist

Usage:
    python manage.py generate_test_data
"""
import random
import uuid
import datetime
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from django.db import transaction
from django.contrib.contenttypes.models import ContentType

from campaigns.models import (
    Campaign, LocationTarget, UsernameTarget, DynamicTag,
    TagGroup, TagCategory, CampaignTag, TagAnalysisResult
)
from instagram.models import Accounts, WhiteListEntry
from taggit.models import Tag
from instagram.models import CustomTaggedItem

class Command(BaseCommand):
    help = 'Generate test data for the campaigns app'

    def add_arguments(self, parser):
        parser.add_argument(
            '--accounts',
            type=int,
            default=50,
            help='Number of dummy accounts to create'
        )
        parser.add_argument(
            '--campaign-name',
            type=str,
            default='Demo Campaign',
            help='Name of the test campaign'
        )
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing test data before generating new data'
        )

    def handle(self, *args, **options):
        num_accounts = options['accounts']
        campaign_name = options['campaign_name']
        clear_existing = options['clear']

        if clear_existing:
            self.clear_test_data()
            self.stdout.write(self.style.SUCCESS('Cleared existing test data'))

        # Get or create admin user
        admin_user, created = User.objects.get_or_create(
            username='admin',
            defaults={
                'is_staff': True,
                'is_superuser': True,
                'email': '<EMAIL>'
            }
        )
        if created:
            admin_user.set_password('admin')
            admin_user.save()
            self.stdout.write(self.style.SUCCESS('Created admin user'))

        # Create tag categories
        categories = self.create_tag_categories()
        self.stdout.write(self.style.SUCCESS('Created tag categories'))

        # Create tag groups
        tag_groups = self.create_tag_groups()
        self.stdout.write(self.style.SUCCESS('Created tag groups'))

        # Create tags
        tags = self.create_tags(categories, tag_groups)
        self.stdout.write(self.style.SUCCESS('Created tags'))

        # Create campaign
        campaign = self.create_campaign(campaign_name, admin_user)
        self.stdout.write(self.style.SUCCESS(f'Created campaign: {campaign.name}'))

        # Assign tags to campaign
        campaign_tags = self.assign_tags_to_campaign(campaign, tags)
        self.stdout.write(self.style.SUCCESS(f'Assigned {len(campaign_tags)} tags to campaign'))

        # Create dummy accounts
        accounts = self.create_dummy_accounts(num_accounts, campaign)
        self.stdout.write(self.style.SUCCESS(f'Created {len(accounts)} dummy accounts'))

        # Apply tags to accounts
        self.apply_tags_to_accounts(campaign, accounts, tags)
        self.stdout.write(self.style.SUCCESS('Applied tags to accounts'))

        # Generate whitelist
        whitelist_entries = self.generate_whitelist(campaign, accounts, tags)
        self.stdout.write(self.style.SUCCESS(f'Generated whitelist with {len(whitelist_entries)} entries'))

        self.stdout.write(self.style.SUCCESS('Test data generation complete!'))

    def clear_test_data(self):
        """Clear existing test data"""
        with transaction.atomic():
            # Delete test campaigns
            Campaign.objects.filter(name__startswith='Demo').delete()

            # Delete test accounts
            Accounts.objects.filter(username__startswith='test_user').delete()

            # Delete test tags
            DynamicTag.objects.filter(name__startswith='Test Tag').delete()

            # Delete test tag groups
            TagGroup.objects.filter(name__startswith='Test Group').delete()

            # Delete test tag categories
            TagCategory.objects.filter(name__startswith='Test Category').delete()

    def create_tag_categories(self):
        """Create tag categories for testing"""
        categories = []
        category_data = [
            {'name': 'Test Category - Engagement', 'color': '#3498db', 'priority': 10},
            {'name': 'Test Category - Demographics', 'color': '#2ecc71', 'priority': 20},
            {'name': 'Test Category - Content', 'color': '#e74c3c', 'priority': 30},
            {'name': 'Test Category - Behavior', 'color': '#f39c12', 'priority': 40},
        ]

        for data in category_data:
            category, created = TagCategory.objects.get_or_create(
                name=data['name'],
                defaults={
                    'id': uuid.uuid4(),
                    'description': f"Test category for {data['name']}",
                    'color': data['color'],
                    'priority': data['priority']
                }
            )
            categories.append(category)

        return categories

    def create_tag_groups(self):
        """Create tag groups for testing"""
        groups = []
        group_data = [
            {'name': 'Test Group - High Value', 'is_global': True},
            {'name': 'Test Group - Influencers', 'is_global': True},
            {'name': 'Test Group - Potential Customers', 'is_global': False},
            {'name': 'Test Group - Competitors', 'is_global': False},
        ]

        for data in group_data:
            group, created = TagGroup.objects.get_or_create(
                name=data['name'],
                defaults={
                    'id': uuid.uuid4(),
                    'description': f"Test group for {data['name']}",
                    'is_global': data['is_global']
                }
            )
            groups.append(group)

        return groups

    def create_tags(self, categories, tag_groups):
        """Create tags for testing"""
        tags = []
        tag_data = [
            # Engagement tags
            {'name': 'Test Tag - High Engagement', 'category': 0, 'group': 0, 'tag_type': 'keyword', 'pattern': 'engagement > 5%', 'field': 'engagement_rate'},
            {'name': 'Test Tag - Active Commenter', 'category': 0, 'group': 0, 'tag_type': 'keyword', 'pattern': 'comments > 10', 'field': 'comments_count'},

            # Demographics tags
            {'name': 'Test Tag - Age 18-24', 'category': 1, 'group': 2, 'tag_type': 'regex', 'pattern': '18-24|college|university', 'field': 'bio'},
            {'name': 'Test Tag - Female', 'category': 1, 'group': 2, 'tag_type': 'keyword', 'pattern': 'she/her|woman|girl', 'field': 'bio'},

            # Content tags
            {'name': 'Test Tag - Fashion Content', 'category': 2, 'group': 1, 'tag_type': 'keyword', 'pattern': 'fashion|style|outfit', 'field': 'bio'},
            {'name': 'Test Tag - Travel Content', 'category': 2, 'group': 1, 'tag_type': 'keyword', 'pattern': 'travel|wanderlust|adventure', 'field': 'bio'},

            # Behavior tags
            {'name': 'Test Tag - Frequent Poster', 'category': 3, 'group': 3, 'tag_type': 'keyword', 'pattern': 'posts > 100', 'field': 'posts_count'},
            {'name': 'Test Tag - New Account', 'category': 3, 'group': 3, 'tag_type': 'keyword', 'pattern': 'account_age < 90', 'field': 'account_age'},
        ]

        for data in tag_data:
            category = categories[data['category']] if data['category'] < len(categories) else None
            group = tag_groups[data['group']] if data['group'] < len(tag_groups) else None

            tag, created = DynamicTag.objects.get_or_create(
                name=data['name'],
                defaults={
                    'id': uuid.uuid4(),
                    'description': f"Test tag for {data['name']}",
                    'category': category,
                    'tag_group': group,
                    'tag_type': data['tag_type'],
                    'pattern': data['pattern'],
                    'field': data['field'],
                    'is_global': group.is_global if group else False
                }
            )
            tags.append(tag)

        return tags

    def create_campaign(self, name, user):
        """Create a test campaign"""
        # Check if campaign with this name already exists
        existing_campaigns = Campaign.objects.filter(name=name)
        if existing_campaigns.exists():
            # Append a number to make the name unique
            count = existing_campaigns.count()
            name = f"{name} {count + 1}"

        campaign = Campaign.objects.create(
            id=uuid.uuid4(),
            name=name,
            description=f"This is a demo campaign for testing purposes #{random.randint(1, 100)}",
            creator=user,
            status='completed',  # Set as completed for testing
            target_type='mixed',
            audience_type='profile'
        )

        # Add a location target
        LocationTarget.objects.create(
            id=uuid.uuid4(),
            campaign=campaign,
            location_id='c2728325',
            country='United States',
            city='New York'
        )

        # Add a username target
        UsernameTarget.objects.create(
            id=uuid.uuid4(),
            campaign=campaign,
            username='instagram',
            audience_type='profile'
        )

        return campaign

    def assign_tags_to_campaign(self, campaign, tags):
        """Assign tags to the campaign"""
        campaign_tags = []

        # Assign half of the tags to the campaign
        for tag in random.sample(tags, len(tags) // 2):
            campaign_tag = CampaignTag.objects.create(
                id=uuid.uuid4(),
                campaign=campaign,
                tag=tag,
                is_required=random.choice([True, False])
            )
            campaign_tags.append(campaign_tag)

        return campaign_tags

    def create_dummy_accounts(self, num_accounts, campaign):
        """Create dummy Instagram accounts"""
        accounts = []

        # Bio templates for more realistic data
        bio_templates = [
            "📸 {interest} photographer | {location} based | {age} y/o",
            "{pronoun} | {age} | {location} | Passionate about {interest}",
            "{interest} enthusiast from {location} | {pronoun}",
            "Living in {location} | {age} | Lover of {interest}",
            "{pronoun} sharing {interest} content | {location}",
            "{age} year old {interest} blogger | {location} | {pronoun}",
            "Just a {age} y/o {pronoun} who loves {interest} | {location}",
            "{location} | {age} | {pronoun} | {interest} addict",
            "📱 {interest} content creator | {profession} by day | {location}",
            "{education} student majoring in {field} | {age} | {location}",
            "{pronoun} • {age} • {location} • {interest} & {secondary_interest}",
            "Exploring {location} through {interest} | {profession} | {pronoun}",
            "{field} {profession} with a passion for {interest} | {location} 📍",
            "{interest} | {secondary_interest} | {hobby} | {location} native",
            "📍 {location} | {zodiac} | {age} | {interest} enthusiast",
            "{pronoun} {profession} sharing {interest} journey | Based in {location}",
            "{field} graduate from {education} | {age} | {interest} lover",
            "{interest} addict with {followers_count}k followers | {location} | {age}",
            "Posting about {interest}, {secondary_interest} & {hobby} | {location}",
            "Mom/Dad to {pet_count} {pet_type}s | {profession} | {interest} fan",
        ]

        interests = [
            "fashion", "travel", "food", "fitness", "beauty", "photography",
            "art", "music", "dance", "technology", "gaming", "sports",
            "sustainability", "wellness", "mindfulness", "cooking", "baking",
            "DIY", "home decor", "gardening", "reading", "writing", "podcasting",
            "investing", "crypto", "NFTs", "startups", "marketing", "design",
            "filmmaking", "acting", "comedy", "politics", "activism", "education",
            "parenting", "pets", "cars", "motorcycles", "skateboarding", "surfing",
            "hiking", "camping", "yoga", "meditation", "CrossFit", "weightlifting"
        ]

        locations = [
            "NYC", "LA", "London", "Paris", "Tokyo", "Sydney", "Berlin",
            "Toronto", "Miami", "Chicago", "Seattle", "Boston", "San Francisco",
            "Austin", "Denver", "Portland", "Nashville", "Atlanta", "Dallas",
            "Barcelona", "Madrid", "Rome", "Milan", "Amsterdam", "Copenhagen",
            "Stockholm", "Oslo", "Helsinki", "Dublin", "Edinburgh", "Manchester",
            "Melbourne", "Brisbane", "Auckland", "Wellington", "Vancouver",
            "Montreal", "Calgary", "Mexico City", "São Paulo", "Buenos Aires",
            "Bogotá", "Lima", "Santiago", "Dubai", "Singapore", "Hong Kong",
            "Seoul", "Bangkok", "Kuala Lumpur", "Jakarta", "Mumbai", "Delhi"
        ]

        pronouns = [
            "she/her", "he/him", "they/them", "she/they", "he/they",
            "all pronouns", "ze/zir", "ze/hir"
        ]

        professions = [
            "Software Engineer", "Designer", "Photographer", "Writer", "Artist",
            "Teacher", "Doctor", "Nurse", "Chef", "Lawyer", "Accountant", "Consultant",
            "Marketing Specialist", "Product Manager", "UX Designer", "Data Scientist",
            "Entrepreneur", "Student", "Freelancer", "Content Creator", "Influencer",
            "Musician", "Actor", "Model", "Athlete", "Coach", "Therapist", "Architect",
            "Engineer", "Scientist", "Researcher", "Professor", "Journalist"
        ]

        education_institutions = [
            "Harvard", "Stanford", "MIT", "Oxford", "Cambridge", "Yale", "Princeton",
            "Columbia", "NYU", "UCLA", "Berkeley", "USC", "UPenn", "Cornell",
            "University of Michigan", "University of Chicago", "University of Toronto",
            "LSE", "Imperial College", "UCL", "King's College", "Edinburgh University",
            "McGill University", "University of Sydney", "University of Melbourne"
        ]

        fields = [
            "Computer Science", "Business", "Marketing", "Psychology", "Biology",
            "Chemistry", "Physics", "Mathematics", "Engineering", "Architecture",
            "Medicine", "Law", "Economics", "Finance", "Political Science",
            "Sociology", "Anthropology", "History", "English Literature", "Philosophy",
            "Communications", "Journalism", "Education", "Nursing", "Public Health"
        ]

        hobbies = [
            "reading", "writing", "painting", "drawing", "singing", "playing guitar",
            "playing piano", "cooking", "baking", "gardening", "hiking", "camping",
            "cycling", "running", "swimming", "yoga", "meditation", "knitting",
            "sewing", "woodworking", "pottery", "calligraphy", "photography",
            "filmmaking", "gaming", "collecting vinyl", "bird watching", "astronomy"
        ]

        zodiac_signs = [
            "Aries", "Taurus", "Gemini", "Cancer", "Leo", "Virgo",
            "Libra", "Scorpio", "Sagittarius", "Capricorn", "Aquarius", "Pisces"
        ]

        pet_types = [
            "cat", "dog", "bird", "fish", "rabbit", "hamster", "guinea pig",
            "turtle", "lizard", "snake", "ferret", "hedgehog", "chinchilla"
        ]

        for i in range(num_accounts):
            username = f"test_user_{i}_{uuid.uuid4().hex[:8]}"

            # Generate basic profile data
            interest = random.choice(interests)
            secondary_interest = random.choice([i for i in interests if i != interest])
            location = random.choice(locations)
            age = random.randint(18, 45)
            pronoun = random.choice(pronouns)
            profession = random.choice(professions)
            education = random.choice(education_institutions)
            field = random.choice(fields)
            hobby = random.choice(hobbies)
            zodiac = random.choice(zodiac_signs)
            pet_type = random.choice(pet_types)
            pet_count = random.randint(1, 5)

            # Generate follower metrics with more realistic distribution
            # Use a log-normal distribution for followers to simulate real-world distribution
            followers_count = int(min(random.lognormvariate(8, 1.2), 1000000))
            # Calculate following based on followers with some randomness
            following_ratio = random.uniform(0.1, 2.0)
            following_count = int(min(followers_count * following_ratio, 7500))
            # Calculate posts based on account age simulation
            account_age_days = random.randint(30, 3650)  # 1 month to 10 years
            post_frequency = random.uniform(0.01, 1.0)  # Posts per day
            posts_count = int(account_age_days * post_frequency)

            # Generate engagement metrics
            likes_per_post = int(followers_count * random.uniform(0.01, 0.2))
            comments_per_post = int(likes_per_post * random.uniform(0.01, 0.1))

            # Format followers for bio
            followers_in_k = round(followers_count / 1000, 1)

            # Generate a realistic bio with all the new data
            bio_template = random.choice(bio_templates)
            try:
                bio = bio_template.format(
                    interest=interest,
                    secondary_interest=secondary_interest,
                    location=location,
                    age=age,
                    pronoun=pronoun,
                    profession=profession,
                    education=education,
                    field=field,
                    hobby=hobby,
                    zodiac=zodiac,
                    pet_type=pet_type,
                    pet_count=pet_count,
                    followers_count=followers_in_k
                )
            except KeyError:
                # Fallback if template has placeholders we didn't provide
                bio = f"{pronoun} | {age} | {location} | {interest} enthusiast"

            # Generate account type with realistic distribution
            account_types = ['personal', 'business', 'creator']
            account_type_weights = [0.7, 0.2, 0.1]  # 70% personal, 20% business, 10% creator
            account_type = random.choices(account_types, weights=account_type_weights)[0]

            # Generate verification status with realistic distribution
            is_verified = random.random() < 0.05  # 5% chance of being verified

            # Generate interests and locations arrays
            user_interests = [interest, secondary_interest]
            if random.random() < 0.5:
                user_interests.append(random.choice([i for i in interests if i not in user_interests]))

            user_locations = [location]
            if random.random() < 0.3:
                user_locations.append(random.choice([l for l in locations if l != location]))

            # Generate links
            domains = ["instagram.com", "linktr.ee", "twitter.com", "tiktok.com", "youtube.com",
                      "facebook.com", "linkedin.com", "github.com", "medium.com", "substack.com"]
            links = []
            num_links = random.choices([0, 1, 2, 3], weights=[0.2, 0.5, 0.2, 0.1])[0]
            for _ in range(num_links):
                domain = random.choice(domains)
                links.append(f"https://{domain}/{username}")

            # Create first and last name
            first_names = ["Alex", "Jordan", "Taylor", "Morgan", "Casey", "Riley", "Avery",
                          "Quinn", "Blake", "Cameron", "Jamie", "Jesse", "Kai", "Reese", "Skyler"]
            last_names = ["Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller",
                         "Davis", "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez"]
            first_name = random.choice(first_names)
            last_name = random.choice(last_names)
            full_name = f"{first_name} {last_name}"

            # Create account with enhanced realistic metrics
            account = Accounts.objects.create(
                username=username,
                full_name=full_name,
                bio=bio,
                followers=followers_count,
                following=following_count,
                number_of_posts=posts_count,
                account_type=account_type,
                is_verified=is_verified,
                campaign_id=str(campaign.id),
                collection_date=timezone.now(),
                interests=user_interests,
                locations=user_locations,
                links=links
            )
            accounts.append(account)

        return accounts

    def apply_tags_to_accounts(self, campaign, accounts, tags):
        """Apply tags to accounts based on their bio and metrics"""
        content_type = ContentType.objects.get_for_model(Accounts)

        for account in accounts:
            # Randomly select 1-3 tags to apply to this account
            num_tags = random.randint(1, 3)
            selected_tags = random.sample(tags, num_tags)

            for tag in selected_tags:
                # Create a tag analysis result
                TagAnalysisResult.objects.create(
                    id=uuid.uuid4(),
                    account_id=account.username,
                    campaign=campaign,
                    tag=tag,
                    matched=True,
                    confidence_score=random.uniform(0.7, 1.0),
                    match_details={
                        'field': tag.field,
                        'pattern': tag.pattern,
                        'match_text': f"Matched in {tag.field}"
                    }
                )

                # Create a taggit Tag and CustomTaggedItem
                taggit_tag, _ = Tag.objects.get_or_create(name=tag.name)

                CustomTaggedItem.objects.create(
                    tag=taggit_tag,
                    content_type=content_type,
                    object_id=account.username
                )

    def generate_whitelist(self, campaign, accounts, tags):
        """Generate whitelist entries for accounts that match required tags"""
        whitelist_entries = []

        # Get required tags for this campaign
        required_tags = CampaignTag.objects.filter(
            campaign=campaign,
            is_required=True
        ).values_list('tag_id', flat=True)

        # For each account, check if it matches any required tags
        for account in accounts:
            # Get tags that matched for this account
            matched_tags = TagAnalysisResult.objects.filter(
                account_id=account.username,
                campaign=campaign,
                matched=True
            ).values_list('tag_id', flat=True)

            # If account matches any required tag, add to whitelist
            # For demo purposes, also randomly add some accounts
            if set(matched_tags).intersection(set(required_tags)) or random.random() < 0.3:
                whitelist_entry = WhiteListEntry.objects.create(
                    username=account.username,
                    account=account,
                    campaign_id=str(campaign.id),
                    added_date=timezone.now(),
                    added_by='system'
                )
                whitelist_entries.append(whitelist_entry)

        return whitelist_entries
