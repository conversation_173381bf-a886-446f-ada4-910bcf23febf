"""
Management command to check the system tags in the database.

This command:
1. Lists all system tags
2. Verifies that system tags cannot be deleted

Usage:
    python manage.py check_system_tags
"""
from django.core.management.base import BaseCommand
from campaigns.models import DynamicTag

class Command(BaseCommand):
    help = "Check the system tags in the database"

    def handle(self, *args, **options):
        self.stdout.write("Starting system tags check...\n")
        
        # List system tags
        system_tags = self.list_system_tags()
        
        # Test deleting a system tag
        self.test_delete_system_tag(system_tags)
        
        self.stdout.write(self.style.SUCCESS("\nSystem tags check completed!"))

    def list_system_tags(self):
        """List all system tags"""
        self.stdout.write("Listing system tags...")
        
        # Get all system tags
        system_tags = DynamicTag.objects.filter(is_system=True)
        
        if system_tags.exists():
            self.stdout.write(f"Found {system_tags.count()} system tags:")
            for tag in system_tags:
                self.stdout.write(f"  - {tag.name}: {tag.description}")
        else:
            self.stdout.write("No system tags found.")
        
        return system_tags

    def test_delete_system_tag(self, system_tags):
        """Test deleting a system tag (should fail)"""
        if not system_tags.exists():
            self.stdout.write("No system tags to test deletion.")
            return
        
        self.stdout.write("\nTesting system tag deletion protection...")
        
        # Try to delete a system tag
        tag = system_tags.first()
        self.stdout.write(f"Attempting to delete system tag: {tag.name}")
        
        try:
            # This should fail if our protection is working
            tag.delete()
            self.stdout.write(self.style.WARNING("WARNING: System tag was deleted! Protection is not working."))
        except Exception as e:
            self.stdout.write(self.style.SUCCESS(f"Good! System tag deletion was prevented: {e}"))
        
        # Check if the tag still exists
        if DynamicTag.objects.filter(id=tag.id).exists():
            self.stdout.write(self.style.SUCCESS(f"System tag '{tag.name}' still exists as expected."))
        else:
            self.stdout.write(self.style.WARNING(f"WARNING: System tag '{tag.name}' was deleted!"))
