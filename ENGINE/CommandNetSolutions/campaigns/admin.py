from django.contrib import admin
from wagtail.admin.panels import FieldPanel, InlinePanel, MultiFieldPanel
from wagtail.snippets.models import register_snippet
from wagtail.snippets.views.snippets import SnippetViewSet

from campaigns.models import Campaign, LocationTarget, UsernameTarget, CampaignResult


class LocationTargetInline(admin.TabularInline):
    model = LocationTarget
    extra = 1
    readonly_fields = ('id', 'created_at')


class UsernameTargetInline(admin.TabularInline):
    model = UsernameTarget
    extra = 1
    readonly_fields = ('id', 'created_at')


class CampaignResultInline(admin.StackedInline):
    model = CampaignResult
    can_delete = False
    readonly_fields = ('id', 'total_accounts_found', 'total_accounts_processed', 'total_accounts_pending', 'last_processed_at', 'created_at')
    max_num = 1
    min_num = 1


@admin.register(Campaign)
class CampaignAdmin(admin.ModelAdmin):
    list_display = ('name', 'status', 'target_type', 'audience_type', 'created_at')
    list_filter = ('status', 'target_type', 'audience_type')
    search_fields = ('name', 'description')
    readonly_fields = ('id', 'created_at', 'updated_at', 'airflow_dag_id', 'airflow_run_id', 'creator', 'dmp_conf')
    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'status')
        }),
        ('Configuration', {
            'fields': ('target_type', 'audience_type', 'dmp_conf')
        }),
        ('Airflow Information', {
            'fields': ('airflow_dag_id', 'airflow_run_id'),
            'classes': ('collapse',),
        }),
        ('System Information', {
            'fields': ('id', 'creator', 'created_at', 'updated_at'),
            'classes': ('collapse',),
        }),
    )

    def get_inlines(self, request, obj=None):
        inlines = [CampaignResultInline]
        if obj:
            if obj.target_type == 'location':
                inlines.append(LocationTargetInline)
            elif obj.target_type == 'username':
                inlines.append(UsernameTargetInline)
        return inlines

    def save_model(self, request, obj, form, change):
        if not change:  # Only set creator on new campaigns
            obj.creator = request.user
        super().save_model(request, obj, form, change)
        # Create a result object if it doesn't exist
        CampaignResult.objects.get_or_create(campaign=obj)


# Wagtail Snippet Registration
class CampaignViewSet(SnippetViewSet):
    model = Campaign
    icon = 'bullhorn'
    list_display = ('name', 'status', 'target_type', 'audience_type', 'created_at')
    list_filter = ('status', 'target_type', 'audience_type')
    search_fields = ('name', 'description')

    panels = [
        MultiFieldPanel([
            FieldPanel('name'),
            FieldPanel('description'),
            FieldPanel('status'),
        ], heading="Basic Information"),
        MultiFieldPanel([
            FieldPanel('target_type'),
            FieldPanel('audience_type'),
        ], heading="Campaign Configuration"),
    ]


register_snippet(CampaignViewSet)
