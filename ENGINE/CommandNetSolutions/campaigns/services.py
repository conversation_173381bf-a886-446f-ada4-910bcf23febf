import json
import requests
import logging
import csv
import os
from datetime import datetime
from django.conf import settings
from django.utils import timezone

logger = logging.getLogger(__name__)

class AirflowService:
    """
    Service for interacting with Airflow API.
    """
    def __init__(self):
        self.base_url = getattr(settings, 'AIRFLOW_API_URL', 'http://localhost:8080/api/v1')
        self.username = getattr(settings, 'AIRFLOW_USERNAME', 'airflow')
        self.password = getattr(settings, 'AIRFLOW_PASSWORD', 'airflow')

    def trigger_dag(self, dag_id, conf=None):
        """
        Trigger a DAG run with optional configuration.

        Args:
            dag_id (str): The ID of the DAG to trigger
            conf (dict, optional): Configuration parameters to pass to the DAG

        Returns:
            dict: Response from Airflow API or a mock response if Airflow is not available
        """
        url = f"{self.base_url}/dags/{dag_id}/dagRuns"

        # Generate a unique run ID
        run_id = f"manual_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        payload = {
            "dag_run_id": run_id,
            "logical_date": datetime.now().isoformat(),
        }

        if conf:
            payload["conf"] = conf

        try:
            response = requests.post(
                url,
                json=payload,
                auth=(self.username, self.password),
                headers={"Content-Type": "application/json"},
                timeout=5  # Add a timeout to prevent long waits
            )

            if response.status_code in (200, 201):
                return response.json()
            else:
                logger.error(f"Failed to trigger DAG {dag_id}: {response.status_code} - {response.text}")
                # Return a mock response with the run_id so the campaign can still be created
                logger.info(f"Returning mock response with run_id: {run_id}")
                return {"dag_run_id": run_id}

        except Exception as e:
            logger.exception(f"Error triggering DAG {dag_id}: {str(e)}")
            # Return a mock response with the run_id so the campaign can still be created
            logger.info(f"Returning mock response with run_id: {run_id} after exception")
            return {"dag_run_id": run_id}

    def get_dag_run_status(self, dag_id, run_id):
        """
        Get the status of a DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to check

        Returns:
            str: Status of the DAG run or None if failed
        """
        url = f"{self.base_url}/dags/{dag_id}/dagRuns/{run_id}"

        try:
            response = requests.get(
                url,
                auth=(self.username, self.password)
            )

            if response.status_code == 200:
                return response.json().get('state')
            else:
                logger.error(f"Failed to get DAG run status: {response.status_code} - {response.text}")
                return None

        except Exception as e:
            logger.exception(f"Error getting DAG run status: {str(e)}")
            return None

    def pause_dag_run(self, dag_id, run_id):
        """
        Pause a DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to pause

        Returns:
            dict: Response with pause status
        """
        url = f"{self.base_url}/dags/{dag_id}/dagRuns/{run_id}/actions/pause"

        try:
            response = requests.post(
                url,
                auth=(self.username, self.password),
                headers={"Content-Type": "application/json"},
                timeout=5
            )

            if response.status_code in (200, 201, 204):
                logger.info(f"Successfully paused DAG run {dag_id}/{run_id}")
                return {
                    'success': True,
                    'dag_id': dag_id,
                    'run_id': run_id,
                    'message': 'DAG run paused successfully'
                }
            else:
                logger.error(f"Failed to pause DAG run {dag_id}/{run_id}: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f"Failed to pause DAG run: {response.text}",
                    'dag_id': dag_id,
                    'run_id': run_id
                }

        except Exception as e:
            logger.exception(f"Error pausing DAG run: {str(e)}")
            return {
                'success': False,
                'error': f"Error pausing DAG run: {str(e)}",
                'dag_id': dag_id,
                'run_id': run_id
            }

    def resume_dag_run(self, dag_id, run_id):
        """
        Resume a paused DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to resume

        Returns:
            dict: Response with resume status
        """
        url = f"{self.base_url}/dags/{dag_id}/dagRuns/{run_id}/actions/resume"

        try:
            response = requests.post(
                url,
                auth=(self.username, self.password),
                headers={"Content-Type": "application/json"},
                timeout=5
            )

            if response.status_code in (200, 201, 204):
                logger.info(f"Successfully resumed DAG run {dag_id}/{run_id}")
                return {
                    'success': True,
                    'dag_id': dag_id,
                    'run_id': run_id,
                    'message': 'DAG run resumed successfully'
                }
            else:
                logger.error(f"Failed to resume DAG run {dag_id}/{run_id}: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f"Failed to resume DAG run: {response.text}",
                    'dag_id': dag_id,
                    'run_id': run_id
                }

        except Exception as e:
            logger.exception(f"Error resuming DAG run: {str(e)}")
            return {
                'success': False,
                'error': f"Error resuming DAG run: {str(e)}",
                'dag_id': dag_id,
                'run_id': run_id
            }

    def stop_dag_run(self, dag_id, run_id):
        """
        Stop a DAG run.

        Args:
            dag_id (str): The ID of the DAG
            run_id (str): The run ID to stop

        Returns:
            dict: Response with stop status
        """
        url = f"{self.base_url}/dags/{dag_id}/dagRuns/{run_id}/actions/cancel"

        try:
            response = requests.post(
                url,
                auth=(self.username, self.password),
                headers={"Content-Type": "application/json"},
                timeout=5
            )

            if response.status_code in (200, 201, 204):
                logger.info(f"Successfully stopped DAG run {dag_id}/{run_id}")
                return {
                    'success': True,
                    'dag_id': dag_id,
                    'run_id': run_id,
                    'message': 'DAG run stopped successfully'
                }
            else:
                logger.error(f"Failed to stop DAG run {dag_id}/{run_id}: {response.status_code} - {response.text}")
                return {
                    'success': False,
                    'error': f"Failed to stop DAG run: {response.text}",
                    'dag_id': dag_id,
                    'run_id': run_id
                }

        except Exception as e:
            logger.exception(f"Error stopping DAG run: {str(e)}")
            return {
                'success': False,
                'error': f"Error stopping DAG run: {str(e)}",
                'dag_id': dag_id,
                'run_id': run_id
            }


class LocationService:
    """
    Service for handling location data.
    """
    def __init__(self):
        self.locations_file = os.path.join(settings.BASE_DIR, 'campaigns', 'data', 'locations.csv')

    def search_locations(self, search_term):
        """
        Search for locations by country or city name.

        Args:
            search_term (str): The search term to look for

        Returns:
            list: List of matching locations
        """
        results = []
        search_term = search_term.lower()

        try:
            if os.path.exists(self.locations_file):
                with open(self.locations_file, 'r', encoding='utf-8') as csvfile:
                    reader = csv.DictReader(csvfile)
                    for row in reader:
                        if (search_term in row['country'].lower() or
                            search_term in row['city'].lower()):
                            results.append(row)
            else:
                # If file doesn't exist, return some dummy data
                logger.warning(f"Locations file not found: {self.locations_file}")
                if 'new york' in search_term or 'united states' in search_term:
                    results.append({
                        'country': 'United States',
                        'city': 'New York',
                        'location_id': 'US_NY_123'
                    })
                elif 'london' in search_term or 'united kingdom' in search_term:
                    results.append({
                        'country': 'United Kingdom',
                        'city': 'London',
                        'location_id': 'UK_LDN_456'
                    })
        except Exception as e:
            logger.exception(f"Error searching locations: {str(e)}")

        return results

    def get_location_by_id(self, location_id):
        """
        Get location details by ID.

        Args:
            location_id (str): The location ID to look for

        Returns:
            dict: Location details or None if not found
        """
        try:
            if os.path.exists(self.locations_file):
                with open(self.locations_file, 'r', encoding='utf-8') as csvfile:
                    reader = csv.DictReader(csvfile)
                    for row in reader:
                        if row['location_id'] == location_id:
                            return row
            else:
                # If file doesn't exist, return some dummy data
                logger.warning(f"Locations file not found: {self.locations_file}")
                if location_id == 'US_NY_123':
                    return {
                        'country': 'United States',
                        'city': 'New York',
                        'location_id': 'US_NY_123'
                    }
                elif location_id == 'UK_LDN_456':
                    return {
                        'country': 'United Kingdom',
                        'city': 'London',
                        'location_id': 'UK_LDN_456'
                    }
        except Exception as e:
            logger.exception(f"Error getting location by ID: {str(e)}")

        return None


class CampaignAnalysisService:
    """
    Service for analyzing accounts collected by campaigns.
    """
    def __init__(self):
        # Import here to avoid circular imports
        from instagram.tagging_engine import AutoTaggingSystem
        self.tagging_system = AutoTaggingSystem()

    def _process_dynamic_tags(self, account, dynamic_tags):
        """
        Process dynamic tags for an account.

        Args:
            account: The account to process
            dynamic_tags: QuerySet of DynamicTag objects

        Returns:
            list: List of tag names that matched
        """
        import re

        matched_tags = []

        for tag in dynamic_tags:
            field_value = getattr(account, tag.field, None)

            # Skip if field value is None
            if field_value is None:
                continue

            # Convert to string if it's not already
            if isinstance(field_value, list):
                # For array fields like interests, locations, etc.
                field_value = ' '.join(str(item) for item in field_value if item)
            else:
                field_value = str(field_value)

            # Process based on tag type
            if tag.tag_type == 'keyword':
                # Split pattern into keywords
                keywords = [k.strip().lower() for k in tag.pattern.split(',')]
                if any(keyword in field_value.lower() for keyword in keywords):
                    matched_tags.append(f"dynamic_{tag.name}")

            elif tag.tag_type == 'regex':
                try:
                    if re.search(tag.pattern, field_value, re.IGNORECASE):
                        matched_tags.append(f"dynamic_{tag.name}")
                except:
                    # Invalid regex pattern
                    pass

            elif tag.tag_type == 'sentiment':
                # Basic sentiment analysis (placeholder for more sophisticated analysis)
                try:
                    threshold = float(tag.pattern)
                    # Simple sentiment scoring based on positive/negative word lists
                    positive_words = ['good', 'great', 'excellent', 'amazing', 'love', 'best', 'happy']
                    negative_words = ['bad', 'worst', 'hate', 'terrible', 'awful', 'poor']

                    words = field_value.lower().split()
                    positive_count = sum(1 for word in words if word in positive_words)
                    negative_count = sum(1 for word in words if word in negative_words)

                    if words:
                        sentiment_score = (positive_count - negative_count) / len(words)
                        if sentiment_score >= threshold:
                            matched_tags.append(f"dynamic_{tag.name}")
                except:
                    # Invalid threshold or other error
                    pass

            elif tag.tag_type == 'category':
                # Category classification (placeholder for more sophisticated classification)
                categories = [c.strip().lower() for c in tag.pattern.split(',')]

                # Simple category matching based on keyword presence
                for category in categories:
                    if category in field_value.lower():
                        matched_tags.append(f"dynamic_{tag.name}_{category}")

        return matched_tags

    def analyze_campaign_accounts(self, campaign_id, batch_size=100):
        """
        Analyze accounts collected by a campaign.

        Args:
            campaign_id: UUID of the campaign
            batch_size: Number of accounts to process in each batch

        Returns:
            dict: Summary of analysis results
        """
        from instagram.models import Accounts, WhiteListEntry
        from campaigns.models import Campaign, CampaignResult

        try:
            campaign = Campaign.objects.get(id=campaign_id)

            # Get accounts collected by this campaign
            accounts = Accounts.objects.filter(campaign_id=str(campaign.id))

            total = accounts.count()
            processed = 0
            white_listed = 0

            # Get campaign analysis settings if they exist
            try:
                analysis_settings = campaign.analysis_settings
                min_followers = analysis_settings.min_followers
                max_followers = analysis_settings.max_followers

                # Apply follower filters if specified
                if min_followers is not None:
                    accounts = accounts.filter(followers__gte=min_followers)
                if max_followers is not None:
                    accounts = accounts.filter(followers__lte=max_followers)

                # Update total after filtering
                total = accounts.count()
            except:
                # If no analysis settings, proceed with all accounts
                pass

            # Process in batches to reduce memory usage
            for i in range(0, total, batch_size):
                batch = accounts[i:i+batch_size]

                for account in batch:
                    # Process account using the tagging system
                    result = self.tagging_system.process_account(account)

                    # Apply dynamic tags
                    dynamic_tags = []
                    try:
                        # Get all global tags
                        from campaigns.models import DynamicTag
                        from django.db import models
                        global_tags = DynamicTag.objects.filter(
                            models.Q(is_global=True) |
                            models.Q(tag_group__is_global=True)
                        )

                        if global_tags.exists():
                            dynamic_tags = self._process_dynamic_tags(account, global_tags)
                            # Add dynamic tags to the result
                            result['tags'].extend(dynamic_tags)

                        # Also process campaign-specific tags if analysis_settings exists
                        if hasattr(campaign, 'analysis_settings') and hasattr(campaign.analysis_settings, 'enable_dynamic_tagging') and campaign.analysis_settings.enable_dynamic_tagging and campaign.analysis_settings.dynamic_tags.exists():
                            campaign_dynamic_tags = self._process_dynamic_tags(account, campaign.analysis_settings.dynamic_tags.all())
                            # Add campaign-specific dynamic tags to the result
                            result['tags'].extend(campaign_dynamic_tags)
                    except Exception as e:
                        logger.warning(f"Error processing dynamic tags: {str(e)}")

                    # Check if account matches campaign tags
                    from campaigns.models import CampaignTag
                    campaign_tags = CampaignTag.objects.filter(campaign=campaign).select_related('tag')

                    # If no campaign tags are defined, use the old behavior
                    if not campaign_tags.exists():
                        qualifies = bool(result.get('privileges'))
                    else:
                        # Check if account matches required tags
                        required_tags = campaign_tags.filter(is_required=True)
                        optional_tags = campaign_tags.filter(is_required=False)

                        # Get all tag names from the result
                        account_tag_names = set(result['tags'])

                        # Check if account matches all required tags
                        required_matched = True
                        for campaign_tag in required_tags:
                            tag_name = campaign_tag.tag.name
                            if tag_name not in account_tag_names:
                                required_matched = False
                                break

                        # Check if account matches at least one optional tag (if there are any)
                        optional_matched = False
                        if optional_tags.exists():
                            for campaign_tag in optional_tags:
                                tag_name = campaign_tag.tag.name
                                if tag_name in account_tag_names:
                                    optional_matched = True
                                    break
                        else:
                            # If no optional tags are defined, consider this condition met
                            optional_matched = True

                        # Account qualifies if it matches all required tags and at least one optional tag
                        qualifies = required_matched and optional_matched

                    if qualifies:
                        WhiteListEntry.objects.update_or_create(
                            account=account,
                            defaults={
                                'tags': result['tags'],
                                'is_auto': True,
                                **result.get('privileges', {})
                            }
                        )
                        white_listed += 1
                    else:
                        # Remove from white list if it exists
                        WhiteListEntry.objects.filter(account=account).delete()

                    processed += 1

            # Update campaign results
            campaign_result, created = CampaignResult.objects.get_or_create(campaign=campaign)
            campaign_result.total_accounts_processed = processed
            campaign_result.last_processed_at = timezone.now()
            campaign_result.save()

            return {
                'total': total,
                'processed': processed,
                'white_listed': white_listed,
                'percentage': (white_listed / total * 100) if total > 0 else 0
            }

        except Exception as e:
            logger.exception(f"Error analyzing campaign accounts: {str(e)}")
            return {
                'total': 0,
                'processed': 0,
                'white_listed': 0,
                'percentage': 0,
                'error': str(e)
            }

    def get_campaign_analysis_stats(self, campaign_id):
        """
        Get analysis statistics for a campaign.

        Args:
            campaign_id: UUID of the campaign

        Returns:
            dict: Analysis statistics
        """
        from instagram.models import Accounts, WhiteListEntry
        from campaigns.models import Campaign, CampaignResult

        try:
            campaign = Campaign.objects.get(id=campaign_id)

            # Get campaign result
            result, created = CampaignResult.objects.get_or_create(campaign=campaign)

            # Get total accounts collected by this campaign
            total_accounts = Accounts.objects.filter(campaign_id=str(campaign.id)).count()

            # Get white listed accounts
            white_listed = WhiteListEntry.objects.filter(
                account__campaign_id=str(campaign.id)
            ).count()

            return {
                'total': total_accounts,
                'processed': result.total_accounts_processed,
                'white_listed': white_listed,
                'percentage': (white_listed / total_accounts * 100) if total_accounts > 0 else 0,
                'last_processed_at': result.last_processed_at
            }

        except Exception as e:
            logger.exception(f"Error getting campaign analysis stats: {str(e)}")
            return {
                'total': 0,
                'processed': 0,
                'white_listed': 0,
                'percentage': 0,
                'last_processed_at': None,
                'error': str(e)
            }
