[MASTER]
# Use the global pylintrc file
rcfile=../../../.pylintrc

# Python code to execute, usually for sys.path manipulation such as pygtk.require().
init-hook='import sys; import os; from pylint.config import find_pylintrc; sys.path.append(os.path.dirname(find_pylintrc()))'

# List of plugins (as comma separated values of python modules names) to load.
load-plugins=pylint_django

# Django settings module for the project
django-settings-module=CommandNetSolutions.settings

[MESSAGES CONTROL]
# Disable specific messages for the campaigns app
disable=missing-docstring,
        invalid-name,
        too-many-locals,
        too-many-arguments,
        too-many-instance-attributes,
        too-many-public-methods,
        too-few-public-methods,
        protected-access,
        no-self-use,
        duplicate-code,
        line-too-long,
        too-many-lines,
        fixme,
        broad-except,
        no-member,
        unused-argument,
        no-else-return,
        import-outside-toplevel,
        consider-using-f-string,
        # Add any campaign-specific rules to disable here
        # For example:
        # model-missing-unicode,
        # model-no-explicit-unicode,
