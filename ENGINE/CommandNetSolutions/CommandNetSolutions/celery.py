"""
Celery configuration for asynchronous task processing.
"""
import os
from celery import Celery

# Set the default Django settings module
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CommandNetSolutions.settings.dev')

# Create Celery app
app = Celery('CommandNetSolutions')

# Load configuration from Django settings
app.config_from_object('django.conf:settings', namespace='CELERY')

# Auto-discover tasks in all installed apps
app.autodiscover_tasks()

@app.task(bind=True)
def debug_task(self):
    """
    Debug task to verify Celery is working.
    """
    print(f'Request: {self.request!r}')
