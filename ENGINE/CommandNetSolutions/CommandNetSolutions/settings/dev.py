# CommandNetSolutions/settings/dev.py

from .base import *

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = "django-insecure-u$(9g((!e9c!uh6$u9jr=yrr7q07dz67di)q0*nd@mp6mt_4_i"

# SECURITY WARNING: define the correct hosts in production!
ALLOWED_HOSTS = ["*"]

EMAIL_BACKEND = "django.core.mail.backends.console.EmailBackend"

SPECTACULAR_SETTINGS = {
    'TITLE': 'CommandNet API',
    'DESCRIPTION': 'API documentation for CommandNet Solutions',
    'VERSION': '1.0.0',    # 'OPENAPI_VERSION': '3.0.3',
    'OPENAPI_VERSION': '3.1.0',
    'SERVE_INCLUDE_SCHEMA': False,
    'SCHEMA_PATH_PREFIX': '/api',  # Include only paths starting with '/api'
    'EXCLUDE_PATH': [
        r'^admin/.*$',         # Exclude any path starting with 'admin/'
        r'^django-admin/.*$',  # Exclude any path starting with 'django-admin/'
        r'^wagtail/.*$',       # Exclude any path starting with 'wagtail/'
    ],
    'PREPROCESSING_HOOKS': [
        'instagram.schema_hooks.spectacular_preprocessing_filter',
    ],
    'SERVERS': [
        {
            'url': 'https://3fe2-77-246-75-93.ngrok-free.app',
            'description': 'Primary API Server',
        },
    ],
    'COMPONENT_SPLIT_REQUEST': True,
    'CONTACT': {
        'email': '<EMAIL>',
    },
    'LICENSE': {
        'name': 'BSD License',
    },
    'SECURITY': [
        {'jwtAuth': []},
    ],
    'SECURITY_SCHEMES': {
        'jwtAuth': {
            'type': 'http',
            'scheme': 'bearer',
            'bearerFormat': 'JWT',
        },
    },
}

# Import Redis settings
try:
    from ..settings_redis import *
except ImportError:
    pass

try:
    from .local import *
except ImportError:
    pass
