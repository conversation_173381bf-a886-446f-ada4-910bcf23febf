"""
Redis configuration settings for the Campaign Analysis System.

This module provides Redis configuration with fallback options for when Redis is not available.
"""
import os
import logging

logger = logging.getLogger(__name__)

# Check if Redis should be enabled
# Set REDIS_ENABLED=False in environment to disable Redis
REDIS_ENABLED = os.environ.get('REDIS_ENABLED', 'True').lower() in ('true', 't', '1', 'yes', 'y')

# Redis connection settings
REDIS_HOST = os.environ.get('REDIS_HOST', 'localhost')
REDIS_PORT = int(os.environ.get('REDIS_PORT', 6379))
REDIS_DB = int(os.environ.get('REDIS_DB', 0))
REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD', None)
REDIS_SOCKET_TIMEOUT = int(os.environ.get('REDIS_SOCKET_TIMEOUT', 5))
REDIS_SOCKET_CONNECT_TIMEOUT = int(os.environ.get('REDIS_SOCKET_CONNECT_TIMEOUT', 5))
REDIS_RETRY_ON_TIMEOUT = os.environ.get('REDIS_RETRY_ON_TIMEOUT', 'True').lower() in ('true', 't', '1', 'yes', 'y')
REDIS_MAX_CONNECTIONS = int(os.environ.get('REDIS_MAX_CONNECTIONS', 10))

# Redis connection URL (used by some libraries)
if REDIS_PASSWORD:
    REDIS_URL = f"redis://:{REDIS_PASSWORD}@{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
else:
    REDIS_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"

# Redis cache settings
REDIS_CACHE_DB = int(os.environ.get('REDIS_CACHE_DB', 1))  # Use a different DB for cache

# Define cache backends
REDIS_CACHE_BACKEND = {
    'BACKEND': 'django_redis.cache.RedisCache',
    'LOCATION': f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_CACHE_DB}",
    'OPTIONS': {
        'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        # The DefaultParser already uses hiredis if available, so we don't need to specify it
        # 'PARSER_CLASS': 'redis.connection.HiredisParser',  # Removed - incorrect path in redis-py 6.x
        'SOCKET_CONNECT_TIMEOUT': REDIS_SOCKET_TIMEOUT,
        'SOCKET_TIMEOUT': REDIS_SOCKET_TIMEOUT,
        'COMPRESSOR': 'django_redis.compressors.zlib.ZlibCompressor',
        'IGNORE_EXCEPTIONS': True,
    },
    'KEY_PREFIX': 'campaigns'
}

LOCMEM_CACHE_BACKEND = {
    'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
    'LOCATION': 'campaigns-cache',
    'TIMEOUT': 300,  # 5 minutes
    'OPTIONS': {
        'MAX_ENTRIES': 1000,
        'CULL_FREQUENCY': 3,  # 1/3 of entries culled when max reached
    }
}

# Configure cache based on Redis availability
try:
    if REDIS_ENABLED:
        # Test Redis connection
        import redis
        client = redis.Redis(
            host=REDIS_HOST,
            port=REDIS_PORT,
            db=REDIS_CACHE_DB,
            password=REDIS_PASSWORD,
            socket_timeout=REDIS_SOCKET_CONNECT_TIMEOUT
        )
        client.ping()  # Will raise an exception if Redis is not available

        # Redis is available, use it for cache
        CACHES = {
            'default': REDIS_CACHE_BACKEND
        }
        logger.info(f"Using Redis cache at {REDIS_HOST}:{REDIS_PORT}/{REDIS_CACHE_DB}")
    else:
        # Redis is disabled, use local memory cache
        CACHES = {
            'default': LOCMEM_CACHE_BACKEND
        }
        logger.info("Redis is disabled, using local memory cache")
except Exception as e:
    # Redis is not available, use local memory cache
    CACHES = {
        'default': LOCMEM_CACHE_BACKEND
    }
    logger.warning(f"Redis connection failed: {str(e)}. Using local memory cache instead.")

# Session settings - use database if Redis is not available
if REDIS_ENABLED and CACHES['default']['BACKEND'] == 'django_redis.cache.RedisCache':
    SESSION_ENGINE = 'django.contrib.sessions.backends.cache'
    SESSION_CACHE_ALIAS = 'default'
else:
    SESSION_ENGINE = 'django.contrib.sessions.backends.db'

# Redis lock settings
REDIS_LOCK_TIMEOUT = int(os.environ.get('REDIS_LOCK_TIMEOUT', 60))  # Lock timeout in seconds
REDIS_LOCK_SLEEP = float(os.environ.get('REDIS_LOCK_SLEEP', 0.1))   # Sleep time between lock attempts

# Celery settings - use Redis if available, otherwise use database
if REDIS_ENABLED:
    CELERY_BROKER_URL = REDIS_URL
    CELERY_RESULT_BACKEND = REDIS_URL
else:
    CELERY_BROKER_URL = 'django-db'
    CELERY_RESULT_BACKEND = 'django-db'

CELERY_ACCEPT_CONTENT = ['json']
CELERY_TASK_SERIALIZER = 'json'
CELERY_RESULT_SERIALIZER = 'json'
