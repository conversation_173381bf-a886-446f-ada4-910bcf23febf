#!/bin/bash

# Activate the virtual environment
source venv/bin/activate

# Run the simulation command
echo "Starting campaign simulation..."
python manage.py simulate_campaign --name "Demo Campaign" --accounts 50 --delay 0.2

# Print instructions
echo ""
echo "Simulation complete!"
echo "You can now view the campaign in the web interface."
echo "The campaign should have collected accounts and generated a whitelist."
echo ""
echo "To run the server: python manage.py runserver 0.0.0.0:8002"
