repos:
-   repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.5.0
    hooks:
    -   id: trailing-whitespace
    -   id: end-of-file-fixer
    -   id: check-yaml
    -   id: check-added-large-files
    -   id: check-json
    -   id: check-toml
    -   id: debug-statements

-   repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.1.11
    hooks:
    -   id: ruff
        args: [--fix, --exit-non-zero-on-fix]
    -   id: ruff-format

-   repo: https://github.com/Riverside-Healthcare/djLint
    rev: v1.34.1
    hooks:
    -   id: djlint-django
        args: [--reformat]

-   repo: https://github.com/pycqa/pylint
    rev: v3.0.3
    hooks:
    -   id: pylint
        name: pylint
        entry: pylint
        language: system
        types: [python]
        args: [
            "--rcfile=.pylintrc",
            "--load-plugins=pylint_django",
            "--django-settings-module=CommandNetSolutions.settings",
        ]
        additional_dependencies: [pylint-django]

-   repo: https://github.com/sqlfluff/sqlfluff
    rev: 2.3.5
    hooks:
    -   id: sqlfluff-lint
        additional_dependencies: [
            "sqlfluff-templater-dbt>=2.3.5",
            "sqlfluff-templater-jinja2>=2.3.5",
        ]
    -   id: sqlfluff-fix
        additional_dependencies: [
            "sqlfluff-templater-dbt>=2.3.5",
            "sqlfluff-templater-jinja2>=2.3.5",
        ]
