# Code Quality Tools

This document describes the code quality tools used in the CommandNetSolutions project and how to use them.

## Overview

The project uses the following code quality tools:

1. **Ruff**: A fast Python linter and formatter
2. **Pylint**: A comprehensive Python linter with Django support
3. **SQLFluff**: A SQL linter and formatter
4. **djLint**: A linter and formatter for Django templates
5. **Pre-commit**: Git hooks to enforce code quality

## Installation

To install all the code quality tools, run:

```bash
pip install -r requirements-dev.txt
pre-commit install
```

This will install all the necessary dependencies and set up the pre-commit hooks.

## Pylint

Pylint is a comprehensive Python linter that checks for errors, enforces coding standards, and looks for code smells.

### Configuration

Pylint is configured in two places:
- `.pylintrc`: The main configuration file
- `pyproject.toml`: Additional configuration in the `[tool.pylint.*]` sections

### Running Pylint

To run Pylint on the entire project:

```bash
pylint --rcfile=.pylintrc --load-plugins=pylint_django --django-settings-module=CommandNetSolutions.settings ENGINE/CommandNetSolutions/
```

To run Pylint on a specific app:

```bash
pylint --rcfile=.pylintrc --load-plugins=pylint_django --django-settings-module=CommandNetSolutions.settings ENGINE/CommandNetSolutions/campaigns/
```

### Common Pylint Rules

- **C**: Convention (coding style)
- **R**: Refactor (code smell)
- **W**: Warning (potential issue)
- **E**: Error (definite issue)
- **F**: Fatal (critical issue)

## SQLFluff

SQLFluff is a SQL linter and formatter that helps maintain consistent SQL code quality.

### Configuration

SQLFluff is configured in two places:
- `.sqlfluff`: The main configuration file
- `pyproject.toml`: Additional configuration in the `[tool.sqlfluff.*]` sections

### Running SQLFluff

To lint SQL files:

```bash
sqlfluff lint path/to/your/sql/files/
```

To automatically fix SQL files:

```bash
sqlfluff fix path/to/your/sql/files/
```

### Common SQLFluff Rules

- **L001**: Unnecessary trailing whitespace
- **L003**: Indentation not consistent with previous lines
- **L010**: Keywords should be capitalized
- **L011**: Implicit/explicit aliasing
- **L014**: Unquoted identifiers
- **L030**: Function names
- **L031**: Avoid table aliases in from clauses and join conditions
- **L034**: Use wildcards then simple targets in select statements

## Ruff

Ruff is a fast Python linter and formatter that combines many linting rules from other tools.

### Configuration

Ruff is configured in `pyproject.toml` in the `[tool.ruff.*]` sections.

### Running Ruff

To check your code with Ruff:

```bash
ruff check .
```

To automatically fix issues:

```bash
ruff check --fix .
```

To format your code:

```bash
ruff format .
```

## djLint

djLint is a linter and formatter for Django templates.

### Configuration

djLint is configured in `.djlintrc`.

### Running djLint

To check your templates:

```bash
djlint --check ENGINE/CommandNetSolutions/campaigns/templates/
```

To reformat your templates:

```bash
djlint --reformat ENGINE/CommandNetSolutions/campaigns/templates/
```

## Pre-commit

Pre-commit runs all the linters and formatters automatically before each commit.

### Configuration

Pre-commit is configured in `.pre-commit-config.yaml`.

### Running Pre-commit

Pre-commit runs automatically when you commit changes. To run it manually:

```bash
pre-commit run --all-files
```

To run a specific hook:

```bash
pre-commit run pylint --all-files
```

## Continuous Integration

These code quality tools are also integrated into the CI/CD pipeline to ensure code quality is maintained across all contributions.

## Best Practices

1. **Run linters locally**: Always run linters before committing to catch issues early
2. **Fix one issue at a time**: When dealing with a large number of linting issues, fix them incrementally
3. **Understand the rules**: Take time to understand why a linter is flagging an issue
4. **Use inline disables sparingly**: Only disable linting rules when absolutely necessary and document why
5. **Keep configurations in sync**: Ensure all developers use the same linting configurations

## Disabling Rules

Sometimes it's necessary to disable a linting rule for a specific line or file. Here's how to do it for each tool:

### Pylint

```python
# pylint: disable=missing-docstring
def some_function():
    pass
```

### SQLFluff

```sql
-- noqa: L031
SELECT * FROM my_table AS t
```

### Ruff

```python
# ruff: noqa: E501
very_long_line = "This is a very long line that exceeds the line length limit but we're ignoring it for a good reason"
```

### djLint

```html
<!-- djlint:ignore H006 -->
<div class="some-class"></div>
```
