#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run the campaign simulation process.

This script:
1. Empties the backend database
2. Populates the backend with predefined tags and tag groups
3. Creates a new campaign
4. Simulates the campaign lifecycle

Usage:
    python run_campaign_simulation.py
"""
import os
import sys
import argparse
import subprocess
import time

def run_command(command, description):
    """Run a command and print its output"""
    print(f"\n=== {description} ===\n")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    print(result.stdout)
    if result.stderr:
        print(f"Error: {result.stderr}")
    if result.returncode != 0:
        print(f"Command failed with return code {result.returncode}")
        sys.exit(1)
    return result

def main():
    parser = argparse.ArgumentParser(description='Run campaign simulation process')
    parser.add_argument('--skip-empty', action='store_true', help='Skip emptying the backend database')
    parser.add_argument('--skip-populate', action='store_true', help='Skip populating the backend with predefined data')
    parser.add_argument('--accounts', type=int, default=100, help='Number of accounts to simulate')
    parser.add_argument('--use-airflow', action='store_true', help='Use Airflow DAGs for simulation')
    args = parser.parse_args()

    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # Activate the virtual environment if needed
    if os.path.exists(os.path.join(current_dir, 'venv')):
        activate_script = os.path.join(current_dir, 'venv', 'bin', 'activate')
        if os.path.exists(activate_script):
            print("Activating virtual environment...")
            os.system(f"source {activate_script}")

    # Empty the backend database
    if not args.skip_empty:
        run_command(
            "python manage.py empty_backend --confirm",
            "Emptying backend database"
        )

    # Populate the backend with predefined data
    if not args.skip_populate:
        run_command(
            "python manage.py populate_backend",
            "Populating backend with predefined data"
        )

    # Simulate a full campaign
    if args.use_airflow:
        # Create a campaign first
        result = run_command(
            "python manage.py simulate_full_campaign --name 'Airflow Simulated Campaign' --accounts 0",
            "Creating campaign for Airflow simulation"
        )

        # Extract the campaign ID from the output
        campaign_id = None
        for line in result.stdout.splitlines():
            if "Campaign ID:" in line:
                campaign_id = line.split("Campaign ID:")[1].strip()
                break

        if not campaign_id:
            print("Could not extract campaign ID from output")
            sys.exit(1)

        # Trigger the Airflow DAG
        run_command(
            f"airflow dags trigger simulate_campaign_workflow -c '{{\"campaign_id\": \"{campaign_id}\", \"num_accounts\": {args.accounts}}}'",
            "Triggering Airflow DAG"
        )

        # Wait for the DAG to complete
        print("\n=== Waiting for Airflow DAG to complete ===\n")
        print("You can check the progress in the Airflow UI")
        print("The DAG will run in the background")
    else:
        # Run the simulation directly
        run_command(
            f"python manage.py simulate_full_campaign --name 'Direct Simulated Campaign' --accounts {args.accounts}",
            "Simulating full campaign"
        )

    print("\n=== Campaign simulation complete ===\n")
    print("You can now view the campaign in the web interface")

if __name__ == "__main__":
    main()
