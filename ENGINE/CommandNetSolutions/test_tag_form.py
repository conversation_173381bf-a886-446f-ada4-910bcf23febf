"""
Test script for the DynamicTagForm.
"""
import os
import sys
import json
import uuid
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CommandNetSolutions.settings')
django.setup()

# Import the form and models
from campaigns.forms import DynamicTagForm
from campaigns.models import DynamicTag, CampaignTagRule, CampaignTagCondition

def test_create_tag():
    """Test creating a tag with a single condition."""
    print("Testing tag creation with a single condition...")
    
    # Create form data
    form_data = {
        'name': 'Test Tag',
        'description': 'Test tag description',
        'is_global': True,
        'field': 'bio',
        'pattern': 'test',
        'tag_type': 'keyword',
        'conditions_json': json.dumps([{
            'id': 0,
            'field_category': 'text',
            'field': 'bio',
            'field_type': 'string',
            'operator': 'icontains',
            'value': 'test',
            'required': True
        }]),
        'condition_logic': 'all'
    }
    
    # Create the form
    form = DynamicTagForm(data=form_data)
    
    # Check if the form is valid
    if form.is_valid():
        print("Form is valid")
        
        # Save the form
        tag = form.save()
        print(f"Tag created: {tag.id}, {tag.name}")
        
        # Save the conditions
        success = form.save_conditions(tag)
        print(f"Conditions saved: {success}")
        
        # Check if the tag has the correct values
        print(f"Tag values: name={tag.name}, description={tag.description}, field={tag.field}, pattern={tag.pattern}")
        
        # Check if the tag rule was created
        try:
            pattern_data = json.loads(tag.pattern)
            if 'rule_id' in pattern_data:
                rule_id = pattern_data['rule_id']
                rule = CampaignTagRule.objects.get(id=rule_id)
                print(f"Rule created: {rule.id}, {rule.name}")
                
                # Check if the condition was created
                conditions = CampaignTagCondition.objects.filter(rule=rule)
                print(f"Conditions count: {conditions.count()}")
                
                for i, condition in enumerate(conditions):
                    print(f"Condition {i+1}: field={condition.field}, operator={condition.operator}, value={condition.value}")
            else:
                print("No rule_id found in pattern data")
        except Exception as e:
            print(f"Error checking rule: {str(e)}")
    else:
        print("Form is invalid")
        print(f"Form errors: {form.errors}")

def test_update_tag():
    """Test updating a tag."""
    print("\nTesting tag update...")
    
    # First create a tag
    tag = DynamicTag.objects.create(
        id=uuid.uuid4(),
        name='Tag to Update',
        description='Original description',
        field='bio',
        pattern='{}',
        tag_type='keyword',
        is_global=True
    )
    print(f"Tag created: {tag.id}, {tag.name}")
    
    # Create a rule for the tag
    rule = CampaignTagRule.objects.create(
        id=uuid.uuid4(),
        name=tag.name,
        tag=tag.name,
        description=tag.description,
        active=True,
        is_global=tag.is_global,
        logic='all'
    )
    print(f"Rule created: {rule.id}, {rule.name}")
    
    # Create a condition for the rule
    condition = CampaignTagCondition.objects.create(
        id=uuid.uuid4(),
        rule=rule,
        field='bio',
        field_type='string',
        operator='icontains',
        value='"original"',
        score=1,
        required=True
    )
    print(f"Condition created: {condition.id}, {condition.field}")
    
    # Update the tag's pattern to reference the rule
    tag.pattern = json.dumps({'rule_id': str(rule.id), 'logic': 'all'})
    tag.save()
    print(f"Tag pattern updated: {tag.pattern}")
    
    # Now update the tag
    form_data = {
        'name': 'Tag to Update',
        'description': 'Updated description',
        'is_global': True,
        'field': 'bio',
        'pattern': 'updated',
        'tag_type': 'keyword',
        'conditions_json': json.dumps([{
            'id': 0,
            'field_category': 'text',
            'field': 'bio',
            'field_type': 'string',
            'operator': 'icontains',
            'value': 'updated',
            'required': True
        }]),
        'condition_logic': 'all'
    }
    
    # Create the form
    form = DynamicTagForm(data=form_data, instance=tag)
    
    # Check if the form is valid
    if form.is_valid():
        print("Form is valid")
        
        # Save the form
        tag = form.save()
        print(f"Tag updated: {tag.id}, {tag.name}, {tag.description}")
        
        # Save the conditions
        success = form.save_conditions(tag)
        print(f"Conditions saved: {success}")
        
        # Refresh the tag from the database
        tag.refresh_from_db()
        print(f"Tag after refresh: {tag.description}")
        
        # Check if the rule was updated
        try:
            pattern_data = json.loads(tag.pattern)
            if 'rule_id' in pattern_data:
                rule_id = pattern_data['rule_id']
                rule = CampaignTagRule.objects.get(id=rule_id)
                print(f"Rule: {rule.id}, {rule.name}, {rule.description}")
                
                # Check if the condition was updated
                conditions = CampaignTagCondition.objects.filter(rule=rule)
                print(f"Conditions count: {conditions.count()}")
                
                for i, condition in enumerate(conditions):
                    print(f"Condition {i+1}: field={condition.field}, operator={condition.operator}, value={condition.value}")
            else:
                print("No rule_id found in pattern data")
        except Exception as e:
            print(f"Error checking rule: {str(e)}")
    else:
        print("Form is invalid")
        print(f"Form errors: {form.errors}")

if __name__ == "__main__":
    test_create_tag()
    test_update_tag()
