#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to check the system tags in the database.

This script:
1. Lists all system tags
2. Verifies that system tags cannot be deleted

Usage:
    python check_system_tags.py
"""
import os
import sys
import django

# Setup Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CommandNetSolutions.settings')
django.setup()

# Import models
from campaigns.models import DynamicTag

def list_system_tags():
    """List all system tags"""
    print("Listing system tags...")
    
    # Get all system tags
    system_tags = DynamicTag.objects.filter(is_system=True)
    
    if system_tags.exists():
        print(f"Found {system_tags.count()} system tags:")
        for tag in system_tags:
            print(f"  - {tag.name}: {tag.description}")
    else:
        print("No system tags found.")
    
    return system_tags

def test_delete_system_tag(system_tags):
    """Test deleting a system tag (should fail)"""
    if not system_tags.exists():
        print("No system tags to test deletion.")
        return
    
    print("\nTesting system tag deletion protection...")
    
    # Try to delete a system tag
    tag = system_tags.first()
    print(f"Attempting to delete system tag: {tag.name}")
    
    try:
        # This should fail if our protection is working
        tag.delete()
        print("WARNING: System tag was deleted! Protection is not working.")
    except Exception as e:
        print(f"Good! System tag deletion was prevented: {e}")
    
    # Check if the tag still exists
    if DynamicTag.objects.filter(id=tag.id).exists():
        print(f"System tag '{tag.name}' still exists as expected.")
    else:
        print(f"WARNING: System tag '{tag.name}' was deleted!")

def main():
    """Main function to run the test"""
    print("Starting system tags check...\n")
    
    # List system tags
    system_tags = list_system_tags()
    
    # Test deleting a system tag
    test_delete_system_tag(system_tags)
    
    print("\nSystem tags check completed!")

if __name__ == "__main__":
    main()
