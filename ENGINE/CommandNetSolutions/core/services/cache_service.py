"""
Cache service for storing and retrieving data.
"""
from django.core.cache import cache


class CacheService:
    """
    Service for caching data.
    Provides consistent interface for caching operations.
    """
    
    def __init__(self, prefix="app", timeout=3600):
        """
        Initialize cache service.
        
        Args:
            prefix (str): Prefix for cache keys
            timeout (int): Default cache timeout in seconds
        """
        self.prefix = prefix
        self.timeout = timeout
    
    def get(self, key):
        """
        Get value from cache.
        
        Args:
            key (str): Cache key
            
        Returns:
            object: Cached value or None if not found
        """
        return cache.get(f"{self.prefix}:{key}")
    
    def set(self, key, value, timeout=None):
        """
        Set value in cache.
        
        Args:
            key (str): Cache key
            value (object): Value to cache
            timeout (int): Optional timeout in seconds
        """
        timeout = timeout or self.timeout
        cache.set(f"{self.prefix}:{key}", value, timeout)
    
    def delete(self, key):
        """
        Delete value from cache.
        
        Args:
            key (str): Cache key
        """
        cache.delete(f"{self.prefix}:{key}")
    
    def clear_prefix(self):
        """
        Clear all keys with prefix.
        Note: This is a best-effort operation and may not clear all keys
        depending on the cache backend.
        """
        # This is a simplified implementation
        # For a more robust solution, we would need to track keys
        # or use a cache backend that supports pattern-based deletion
        pass
