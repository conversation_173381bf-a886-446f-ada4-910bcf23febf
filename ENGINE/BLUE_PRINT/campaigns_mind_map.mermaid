mindmap
  root((Campaigns App))
    Campaign Management
      Creation
        Basic Info
          Name
          Description
        Target Configuration
          Location-based
          Username-based
          Mixed targets
        Analysis Settings
          Follower Thresholds
          Tag Configuration
          Analysis Frequency
      Monitoring
        Progress Tracking
        Status Updates
        Real-time Notifications
      Results
        Account Lists
        Tag Analysis
        Whitelist Generation
        Export Functionality
      Favorites
        Toggle Favorite Status
        Quick Access to Favorites
    
    Workflow Execution
      Airflow Integration
        DAG Orchestration
        Error Handling
        Retry Logic
      PyFlow Integration
        Workflow Generation
        Execution Tracking
        Compound Node Optimization
      Progress Tracking
        Real-time Updates
        Execution Metrics
        Performance Analytics
    
    Tag System
      Tag Creation
        Individual Tags
        Bulk Creation
        Tag Builder Interface
      Tag Organization
        Categories
        Groups
        Related Tags
      Tag Application
        Campaign Assignment
        Rule Definition
        Condition Configuration
      Tag Analysis
        Auto-tagging
        Manual Review
        Performance Metrics
    
    Data Collection
      Account Discovery
        Location-based
        Username-based
      Profile Analysis
        Bio Analysis
        Interest Extraction
        Follower Analysis
      Data Storage
        Caching Strategy
        Database Optimization
        Result Indexing
    
    User Experience
      Dashboard
        Campaign Overview
        Recent Campaigns
        Quick Stats
      Campaign Interface
        Creation Wizard
        Monitoring View
        Results View
      Tag Management
        Tag Library
        Tag Builder
        Tag Assignment
    
    Architecture
      Domain-Driven Design
        Domain Services
        Repositories
        Entities
      Asynchronous Processing
        Message Queue
        Background Tasks
        Real-time Updates
      Caching
        Tag Cache
        Location Cache
        Result Cache
      Error Handling
        Validation
        Recovery
        Notification
    
    Integration
      Instagram
        Profile Data
        Engagement Actions
      Airflow
        Workflow Orchestration
        Scheduling
        Monitoring
      PyFlow
        Visual Workflows
        Custom Nodes
        Execution Engine
