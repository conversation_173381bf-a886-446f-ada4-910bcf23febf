classDiagram
    %% Campaign and Target Models
    Campaign "1" -- "0..*" LocationTarget : has
    Campaign "1" -- "0..*" UsernameTarget : has
    Campaign "1" -- "1" CampaignAnalysisSettings : has
    Campaign "1" -- "0..*" CampaignResult : has
    Campaign "1" -- "0..*" WorkflowExecution : has
    Campaign "1" -- "0..*" CampaignMetrics : has
    Campaign "1" -- "1" AccountWhitelist : has
    User "1" -- "0..*" Campaign : creates

    %% Tag Models
    DynamicTag "0..*" -- "0..*" DynamicTag : related to
    TagCategory "1" -- "0..*" DynamicTag : categorizes
    TagGroup "1" -- "0..*" DynamicTag : groups
    CampaignAnalysisSettings "1" -- "0..*" DynamicTag : uses
    CampaignAnalysisSettings "1" -- "0..*" TagGroup : uses

    %% Analysis Results
    CampaignResult "1" -- "1" Account : references
    TagAnalysisResult "0..*" -- "1" Account : analyzes
    TagAnalysisResult "0..*" -- "1" Campaign : belongs to
    TagAnalysisResult "0..*" -- "1" DynamicTag : applies

    %% Workflow Tracking
    WorkflowExecution "1" -- "0..*" WorkflowLog : has
    WorkflowExecution "1" -- "0..*" WorkflowError : has
    WorkflowExecution "1" -- "0..*" WorkflowMetrics : has

    %% Whitelist
    AccountWhitelist "1" -- "0..*" Account : contains

    %% Campaign Model
    class Campaign {
        +UUID id
        +String name
        +String description
        +JSON dmp_conf
        +String status
        +String target_type
        +String audience_type
        +DateTime created_at
        +DateTime updated_at
        +String airflow_run_id
        +String airflow_dag_id
        +Boolean is_favorite
        +ForeignKey creator
        +JSON active_workflows
        +JSON completed_workflows
        +JSON workflow_statistics
    }

    %% Target Models
    class LocationTarget {
        +UUID id
        +ForeignKey campaign
        +String location_id
        +String country
        +String city
        +Boolean processed
        +DateTime created_at
    }

    class UsernameTarget {
        +UUID id
        +ForeignKey campaign
        +String username
        +String audience_type
        +Boolean processed
        +DateTime created_at
    }

    %% Analysis Settings
    class CampaignAnalysisSettings {
        +UUID id
        +ForeignKey campaign
        +Boolean auto_analyze
        +String analysis_frequency
        +Integer min_followers
        +Integer max_followers
        +Array target_tags
        +Boolean enable_tagging
        +JSON active_workflows
        +JSON completed_workflows
        +JSON workflow_statistics
        +DateTime created_at
        +DateTime updated_at
    }

    %% Tag Models
    class DynamicTag {
        +UUID id
        +String name
        +String description
        +ForeignKey category
        +ForeignKey tag_group
        +String tag_type
        +String pattern
        +String field
        +Boolean is_global
        +String confidence_level
        +Float weight
        +DateTime created_at
        +DateTime updated_at
    }

    class TagGroup {
        +UUID id
        +String name
        +String description
        +Boolean is_public
        +DateTime created_at
        +DateTime updated_at
    }

    class TagCategory {
        +UUID id
        +String name
        +String description
        +String color
        +Integer priority
        +DateTime created_at
        +DateTime updated_at
    }

    %% Result Models
    class CampaignResult {
        +UUID id
        +ForeignKey campaign
        +ForeignKey account
        +Boolean whitelisted
        +Float score
        +JSON tag_matches
        +DateTime created_at
        +DateTime updated_at
    }

    class TagAnalysisResult {
        +UUID id
        +ForeignKey account
        +ForeignKey campaign
        +ForeignKey tag
        +Boolean matched
        +Float confidence_score
        +DateTime created_at
        +DateTime updated_at
    }

    %% Workflow Models
    class WorkflowExecution {
        +UUID id
        +ForeignKey campaign
        +String workflow_name
        +String workflow_path
        +String workflow_type
        +String status
        +DateTime start_time
        +DateTime end_time
        +Float duration
        +Float progress
        +Integer total_items
        +Integer processed_items
        +Integer successful_items
        +Integer failed_items
        +String error_message
        +String log_file
        +JSON parameters
        +JSON results
        +Integer retry_count
        +DateTime last_retry
        +DateTime created_at
        +DateTime updated_at
    }

    %% New Models for Enhanced Tracking
    class WorkflowLog {
        +UUID id
        +ForeignKey workflow_execution
        +DateTime timestamp
        +String log_level
        +String message
        +JSON context
    }

    class WorkflowError {
        +UUID id
        +ForeignKey workflow_execution
        +DateTime timestamp
        +String error_type
        +String error_message
        +String stack_trace
        +Boolean resolved
        +String resolution_notes
    }

    class WorkflowMetrics {
        +UUID id
        +ForeignKey workflow_execution
        +DateTime timestamp
        +String metric_name
        +Float metric_value
        +String metric_unit
        +JSON dimensions
    }

    class CampaignMetrics {
        +UUID id
        +ForeignKey campaign
        +DateTime timestamp
        +Integer accounts_found
        +Integer accounts_processed
        +Integer accounts_whitelisted
        +Integer tags_applied
        +Float average_confidence
        +JSON performance_metrics
    }

    class AccountWhitelist {
        +UUID id
        +ForeignKey campaign
        +DateTime generated_at
        +Integer account_count
        +Boolean is_active
        +String generation_method
        +JSON filter_criteria
    }

    %% External Models
    class User {
        +Integer id
        +String username
        +String email
    }

    class Account {
        +UUID id
        +String username
        +JSON profile_data
        +Integer follower_count
        +Integer following_count
        +DateTime last_updated
    }
