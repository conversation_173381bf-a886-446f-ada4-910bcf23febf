---
title: Bot Worker Resource Management System
---
flowchart TD
    %% Main Components
    Airflow[Airflow DAG]
    ResourceMgr[Resource Manager Service]
    Redis[(Redis Queue System)]
    PyFlow[PyFlow Workflow]
    WorkflowExec[Workflow Execution]
    
    %% Workflow Registration
    Airflow -->|1. Register Workflow| ResourceMgr
    ResourceMgr -->|2. Store Workflow Info| Redis
    
    %% Decision Point
    ResourceMgr -->|3. Check Bot Availability| BotCheck{Bot Worker\nAvailable?}
    
    %% Bot Available Path
    BotCheck -->|Yes| SetActive[4a. Set as Active Workflow]
    SetActive -->|5a. Store Active Status| Redis
    SetActive -->|6a. Start Immediately| PyFlow
    
    %% Bot Busy Path
    BotCheck -->|No| QueueWorkflow[4b. Add to Priority Queue]
    QueueWorkflow -->|5b. Calculate Priority| PriorityCalc[Priority Calculation]
    PriorityCalc -->|6b. Insert in Queue| Redis
    
    %% Queue Processing
    Redis -->|7. Monitor Queue| Scheduler[Workflow Scheduler]
    Scheduler -->|8. When Bot Available| NextWorkflow[Get Next Workflow]
    NextWorkflow -->|9. Highest Priority First| Redis
    NextWorkflow -->|10. Start Next Workflow| PyFlow
    
    %% Workflow Execution
    PyFlow -->|11. Execute Workflow| WorkflowExec
    WorkflowExec -->|12. Track Progress| ProgressTrack[Progress Tracking]
    ProgressTrack -->|13. Store Updates| Redis
    
    %% Workflow Completion
    WorkflowExec -->|14. Complete Workflow| Complete{Workflow\nCompleted?}
    Complete -->|Yes| ReleaseBot[15a. Release Bot Worker]
    Complete -->|No| ContinueExec[15b. Continue Execution]
    
    %% Release Bot
    ReleaseBot -->|16. Clear Active Workflow| Redis
    ReleaseBot -->|17. Trigger Next Workflow| Scheduler
    
    %% API Rate Limiting
    PyFlow -->|Check Rate Limits| RateLimiter[API Rate Limiter]
    RateLimiter -->|Store Usage Counters| Redis
    RateLimiter -->|Enforce Limits| PyFlow
    
    %% Optimization
    ResourceMgr -->|Periodically Check| Optimizer[Resource Optimizer]
    Optimizer -->|Reorder Queue| Redis
    Optimizer -->|Pause/Resume Workflows| PyFlow
    
    %% Subgraphs
    subgraph "Queue Management"
        QueueWorkflow
        PriorityCalc
        Scheduler
        NextWorkflow
    end
    
    subgraph "Execution Control"
        WorkflowExec
        ProgressTrack
        Complete
        ReleaseBot
        ContinueExec
    end
    
    subgraph "Rate Limiting"
        RateLimiter
    end
    
    %% Styling
    classDef primary fill:#f9f,stroke:#333,stroke-width:2px;
    classDef secondary fill:#bbf,stroke:#333,stroke-width:1px;
    classDef decision fill:#ffd,stroke:#333,stroke-width:1px;
    classDef storage fill:#bfb,stroke:#333,stroke-width:1px;
    
    class Airflow,ResourceMgr,PyFlow primary;
    class SetActive,QueueWorkflow,PriorityCalc,Scheduler,NextWorkflow,WorkflowExec,ProgressTrack,ReleaseBot,ContinueExec,RateLimiter,Optimizer secondary;
    class BotCheck,Complete decision;
    class Redis storage;
