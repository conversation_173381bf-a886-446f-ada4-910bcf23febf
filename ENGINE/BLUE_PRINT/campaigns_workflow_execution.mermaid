sequenceDiagram
    participant User
    participant CampaignApp as Campaign App
    participant AirflowService
    participant AirflowDAG as Airflow DAG
    participant PyFlowService
    participant PyFlowWorkflow as PyFlow Workflow
    participant Database
    participant NotificationSystem

    %% Campaign Creation
    User->>CampaignApp: Create Campaign
    CampaignApp->>Database: Save Campaign
    User->>CampaignApp: Add Targets
    CampaignApp->>Database: Save Targets
    User->>CampaignApp: Configure Analysis Settings
    CampaignApp->>Database: Save Analysis Settings
    User->>CampaignApp: Add Tags/Tag Rules
    CampaignApp->>Database: Save Tag Configuration
    User->>CampaignApp: Launch Campaign

    %% Campaign Launch
    CampaignApp->>AirflowService: Trigger DAG
    AirflowService->>AirflowDAG: Execute campaign_data_collection DAG
    AirflowService-->>NotificationSystem: Notify Campaign Launch
    NotificationSystem-->>User: Display Launch Notification

    %% DAG Execution
    AirflowDAG->>Database: Update Campaign Status (running)
    AirflowDAG->>AirflowDAG: Validate Campaign Config

    %% Error Handling - Config Validation
    alt Config Invalid
        AirflowDAG-->>Database: Update Campaign Status (failed)
        AirflowDAG-->>NotificationSystem: Notify Validation Error
        NotificationSystem-->>User: Display Error Notification
    else Config Valid
        AirflowDAG->>AirflowDAG: Process Targets
    end

    %% PyFlow Integration
    AirflowDAG->>PyFlowService: Create Workflow
    PyFlowService->>PyFlowService: Generate Workflow File
    PyFlowService->>Database: Create WorkflowExecution Record
    PyFlowService->>PyFlowWorkflow: Execute Workflow

    %% Workflow Tracking
    PyFlowService->>Database: Update Active Workflows

    %% Workflow Execution
    PyFlowWorkflow->>PyFlowWorkflow: Process Accounts
    PyFlowWorkflow->>Database: Save Accounts
    PyFlowWorkflow->>PyFlowService: Report Progress
    PyFlowService->>Database: Update WorkflowExecution Progress
    PyFlowService->>Database: Update Workflow Statistics

    %% Real-time Progress Updates
    PyFlowService-->>NotificationSystem: Update Progress
    NotificationSystem-->>User: Display Progress Updates

    %% Error Handling - Workflow Execution
    alt Execution Error
        PyFlowWorkflow-->>PyFlowService: Report Error
        PyFlowService-->>Database: Update WorkflowExecution (failed)
        PyFlowService-->>AirflowDAG: Return Error
        AirflowDAG-->>AirflowDAG: Retry Logic

        opt Max Retries Exceeded
            AirflowDAG-->>Database: Update Campaign Status (failed)
            AirflowDAG-->>NotificationSystem: Notify Execution Error
            NotificationSystem-->>User: Display Error Notification
        end
    else Execution Successful
        %% Workflow Completion
        PyFlowWorkflow->>PyFlowService: Complete Workflow
        PyFlowService->>Database: Update WorkflowExecution (completed)
        PyFlowService->>Database: Move from Active to Completed Workflows
        PyFlowService->>AirflowDAG: Return Results
    end

    %% Campaign Update
    AirflowDAG->>Database: Update Campaign Results
    AirflowDAG->>Database: Update Campaign Status (completed)
    AirflowDAG-->>NotificationSystem: Notify Collection Complete
    NotificationSystem-->>User: Display Completion Notification

    %% Optional Tagging Phase
    AirflowDAG->>AirflowService: Trigger campaign_tagging DAG
    AirflowService->>AirflowDAG: Execute campaign_tagging DAG

    %% Tag Analysis
    AirflowDAG->>Database: Fetch Campaign Tags and Rules
    AirflowDAG->>PyFlowService: Create Analysis Workflow
    PyFlowService->>PyFlowWorkflow: Execute Analysis Workflow
    PyFlowWorkflow->>PyFlowWorkflow: Apply Tag Rules to Accounts
    PyFlowWorkflow->>Database: Save Tag Analysis Results
    PyFlowWorkflow->>Database: Generate Account Whitelist

    %% Whitelist Notification
    PyFlowWorkflow-->>NotificationSystem: Notify Whitelist Generated
    NotificationSystem-->>User: Display Whitelist Notification

    %% Optional CEP Phase
    opt User Initiates CEP
        User->>CampaignApp: Execute CEP on Whitelist
        CampaignApp->>AirflowService: Trigger campaign_cep DAG
        AirflowService->>AirflowDAG: Execute campaign_cep DAG
        AirflowDAG->>PyFlowService: Create Engagement Workflow
        PyFlowService->>PyFlowWorkflow: Execute Engagement Actions
        PyFlowWorkflow->>Database: Update Engagement Results
    end

    %% User Viewing Results
    User->>CampaignApp: View Campaign Results
    CampaignApp->>Database: Fetch Campaign Results
    CampaignApp->>User: Display Results

    %% User Viewing Whitelist
    User->>CampaignApp: View Whitelisted Accounts
    CampaignApp->>Database: Fetch Whitelist
    CampaignApp->>User: Display Whitelisted Accounts

    %% Campaign Favoriting
    User->>CampaignApp: Toggle Favorite Status
    CampaignApp->>Database: Update Campaign Favorite Status
    CampaignApp-->>User: Confirm Favorite Status Change

    %% Optional Export
    User->>CampaignApp: Export Campaign Data
    CampaignApp->>Database: Fetch All Campaign Data
    CampaignApp->>User: Download Export File
