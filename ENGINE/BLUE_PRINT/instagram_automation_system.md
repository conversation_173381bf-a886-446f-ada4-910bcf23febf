# Instagram Automation System: Comprehensive Data Flow

This document provides a detailed explanation of the complete data flow in the Instagram automation system, from campaign creation to data collection, analysis, and storage.

## 1. Campaign Creation and Initialization

### 1.1 Django Models Involved

The campaign creation process centers around these key models:

- **Campaign**: The core model storing campaign configuration, status, and metadata
- **LocationTarget**: Stores location-based targets (city, country, location_id)
- **UsernameTarget**: Stores username-based targets with audience type
- **TagGroup**: Groups of related tags to apply during analysis
- **DynamicTag**: Individual tag definitions with patterns and fields to match

### 1.2 Validation Process

Campaign validation ensures:
- At least one target (location or username) is specified
- Required fields are present (name, target_type, audience_type)
- Target types are valid (location, username, or mixed)
- Audience types are valid (profile, followers, following, both)

### 1.3 Data Structure

Campaign data is structured as:
- Core campaign fields stored in the Campaign model
- Targets stored in related models (LocationTarget, UsernameTarget)
- Configuration stored in JSON format in the `dmp_conf` field
- Status tracking via the `status` field (draft, pending, running, etc.)
- Airflow integration via `airflow_run_id` and `airflow_dag_id` fields

## 2. Data Transfer to Airflow

### 2.1 Communication Mechanism

The campaigns app communicates with Airflow through:
- **AirflowService**: A Django service that interfaces with Airflow's REST API
- Direct database connections for status updates and result retrieval

### 2.2 API Endpoints

The system uses these endpoints:
- `dags/{dag_id}/dagRuns`: To trigger DAG execution
- `dags/{dag_id}/dagRuns/{run_id}`: To check DAG status

### 2.3 Data Serialization

Campaign data is serialized as:
- JSON configuration passed to Airflow DAGs
- Campaign ID used as the primary reference between systems
- Target data converted to appropriate formats for PyFlow workflows

## 3. Airflow DAG Execution

### 3.1 DAG Structure

The system uses three primary DAGs:
- **campaign_data_collection.py**: Collects account data based on targets
- **campaign_tagging.py**: Analyzes and tags collected accounts
- **campaign_cep.py**: Executes engagement actions on whitelisted accounts

### 3.2 PyFlow Invocation

Airflow invokes PyFlow through:
- PythonOperators that execute shell commands
- Direct subprocess calls to the PyFlow command-line interface
- Command format: `pyflow -m run -f {workflow_file} --campaign_id {campaign_id} [additional parameters]`

### 3.3 Parameter Passing

Parameters are passed from Airflow to PyFlow as:
- Command-line arguments (--campaign_id, --location_ids, --usernames)
- Environment variables for system-wide configuration
- Redis for dynamic configuration and inter-process communication

## 4. Data Collection Workflow

### 4.1 Workflow Execution Steps

The collection workflow follows these steps:
1. Initialize workflow with campaign parameters
2. Process location or username targets
3. Connect to Instagram API through PyFlow nodes
4. Collect account data based on target type
5. Store collected data in the database
6. Track progress and update workflow status

### 4.2 Instagram Data Access

Instagram data is accessed through:
- PyFlow's specialized Instagram nodes (InstagramCollectAccounts)
- Rate-limited API requests to prevent blocking
- Proxy rotation for high-volume collection

### 4.3 Error Handling

The system implements error handling through:
- Retry mechanisms for transient failures
- Error logging and reporting
- Workflow status updates for failed operations

## 5. Bot Worker Resource Management

### 5.1 Queue System

The resource management system uses:
- Redis-based queue for workflow scheduling
- Priority queue implementation (WorkflowQueueEntry)
- Sequential execution enforcement

### 5.2 Priority Calculation

Priorities are calculated based on:
- Workflow type (collection > analysis > engagement)
- Submission time (FIFO within same priority)
- Campaign importance (configurable)

### 5.3 Redis Communication

Components communicate through Redis:
- Workflow registration and status tracking
- Queue management and scheduling
- Control messages for workflow pause/resume/cancel

## 6. Progress Tracking

### 6.1 LogProgressNode Function

The LogProgressNode:
- Logs standardized progress messages
- Format: `PROGRESS:{current}/{total}`
- Emits events for workflow monitoring

### 6.2 QueueMessageNode Function

The QueueMessageNode:
- Queues discovered usernames for processing
- Uses Redis for inter-process communication
- Enables parallel discovery and processing

### 6.3 Progress Data Flow

Progress data flows:
1. PyFlow nodes log progress to log files
2. WorkflowProgressService parses log files
3. Progress updates stored in WorkflowProgressUpdate model
4. Campaign status updated based on workflow progress
5. Dashboard displays real-time progress

## 7. Data Storage and Accessibility

### 7.1 Data Storage

Collected Instagram data is stored in:
- PostgreSQL database (primary storage)
- Accounts model (username as primary key)
- Related models for tags and whitelist entries

### 7.2 Database Schema

The core schema includes:
- **Accounts**: Stores Instagram account data (username, bio, followers, etc.)
- **WhiteListEntry**: Links accounts to privileges based on tags
- **WorkflowExecution**: Tracks workflow execution and progress
- **TagAnalysisResult**: Stores results of tag application

### 7.3 Data Access

The campaigns app accesses data through:
- Django ORM queries
- Custom services (DashboardService, WhitelistService)
- Cached queries for performance optimization

## 8. System Integration Points

### 8.1 Django to Airflow

- AirflowService handles communication with Airflow API
- Campaign status updates flow bidirectionally
- XCom used for data sharing between Airflow tasks

### 8.2 Airflow to PyFlow

- Airflow DAGs invoke PyFlow workflows via subprocess
- Command-line parameters pass configuration
- Log files provide feedback mechanism

### 8.3 PyFlow to Database

- PyFlow nodes connect directly to the database
- Django models accessed through Python imports
- Transaction management ensures data consistency

## 9. Visualization and Reporting

### 9.1 Dashboard Components

The system provides visualization through:
- Campaign dashboard with status charts
- Account collection progress tracking
- Tag performance metrics
- Whitelist generation statistics

### 9.2 Data Export

Results can be exported as:
- CSV files for account data
- Excel reports for campaign analysis
- JSON for system integration

## 10. Complete Workflow Summary

1. User creates campaign with targets in Django app
2. Campaign is validated and stored in database
3. User launches campaign, triggering Airflow DAG
4. Airflow orchestrates workflow execution
5. Resource manager schedules workflow on bot worker
6. PyFlow executes data collection workflow
7. Collected accounts stored in database
8. Progress tracked and displayed in dashboard
9. Analysis workflow applies tags to accounts
10. Whitelist generated based on tag matches
11. Results displayed to user in dashboard
12. Optional engagement workflows executed on whitelist
