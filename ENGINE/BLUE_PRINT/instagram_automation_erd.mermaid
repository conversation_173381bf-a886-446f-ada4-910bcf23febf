---
title: Instagram Automation System - Entity Relationship Diagram
---
erDiagram
    %% Main Models
    Campaign ||--o{ LocationTarget : "targets"
    Campaign ||--o{ UsernameTarget : "targets"
    Campaign ||--o{ WorkflowExecution : "executes"
    Campaign ||--o{ CampaignTag : "applies"
    Campaign ||--o{ TagGroup : "uses"
    
    %% Workflow Models
    WorkflowExecution ||--o{ WorkflowProgressUpdate : "tracks"
    
    %% Tag Models
    TagGroup ||--o{ DynamicTag : "contains"
    TagCategory ||--o{ DynamicTag : "categorizes"
    DynamicTag ||--o{ TagMetrics : "measures"
    DynamicTag ||--o{ TagRuleCondition : "defines"
    
    %% Instagram Models
    Accounts ||--o{ WhiteListEntry : "qualifies"
    
    %% Campaign Model
    Campaign {
        UUID id PK
        string name
        string description
        JSON dmp_conf
        string status
        string target_type
        string audience_type
        datetime created_at
        datetime updated_at
        string airflow_run_id
        string airflow_dag_id
        boolean is_favorite
        UUID creator_id FK
    }
    
    %% Target Models
    LocationTarget {
        UUID id PK
        UUID campaign_id FK
        string location_id
        string country
        string city
        boolean processed
        datetime created_at
    }
    
    UsernameTarget {
        UUID id PK
        UUID campaign_id FK
        string username
        string audience_type
        boolean processed
        datetime created_at
    }
    
    %% Workflow Models
    WorkflowExecution {
        UUID id PK
        string campaign_id FK
        string workflow_name
        string workflow_path
        string workflow_type
        string status
        datetime start_time
        datetime end_time
        float duration
        float progress
        integer total_items
        integer processed_items
        integer successful_items
        integer failed_items
        string error_message
        string log_file
        JSON parameters
        JSON results
        datetime created_at
        datetime updated_at
    }
    
    WorkflowProgressUpdate {
        UUID id PK
        UUID workflow_execution_id FK
        datetime timestamp
        integer processed_items
        integer successful_items
        integer failed_items
        float progress
        string message
        JSON details
    }
    
    %% Tag Models
    TagCategory {
        UUID id PK
        string name
        string description
        string color
        boolean is_system
        datetime created_at
        datetime updated_at
    }
    
    TagGroup {
        UUID id PK
        string name
        string description
        string color
        boolean is_global
        datetime created_at
        datetime updated_at
    }
    
    DynamicTag {
        UUID id PK
        string name
        string description
        UUID category_id FK
        UUID tag_group_id FK
        string tag_type
        string pattern
        string field
        boolean is_global
        boolean is_system
        float weight
        datetime created_at
        datetime updated_at
    }
    
    TagMetrics {
        UUID id PK
        UUID tag_id FK
        integer usage_count
        integer match_count
        float conversion_rate
        float precision
        datetime last_updated
    }
    
    TagRuleCondition {
        UUID id PK
        UUID tag_id FK
        string field
        string operator
        string value
        boolean case_sensitive
        integer order
    }
    
    CampaignTag {
        UUID id PK
        UUID campaign_id FK
        UUID tag_id FK
        boolean is_active
        datetime created_at
    }
    
    %% Instagram Models
    Accounts {
        string username PK
        string full_name
        text bio
        bigint followers
        bigint following
        bigint number_of_posts
        array interests
        string account_type
        string phone_number
        boolean avoid
        boolean is_verified
        datetime last_modified
        array links
        array locations
        string campaign_id
        datetime collection_date
    }
    
    WhiteListEntry {
        string account_id PK,FK
        array tags
        boolean dm
        boolean discover
        boolean comment
        boolean post_like
        boolean favorite
        boolean follow
        boolean is_auto
        datetime last_updated
    }
