graph TD
    %% Main Components with Enhanced Architecture
    UI[User Interface Layer] --> Controllers
    Controllers --> Services
    Services --> Domains
    Domains --> Repositories
    Repositories --> Models

    %% User Interface Layer
    UI --> Views[Views/Templates]
    UI --> Forms[Forms]
    UI --> API[API Endpoints]
    UI --> ReactComponents[React Components]

    %% Controllers Layer
    Controllers --> CampaignViews[Campaign Views]
    Controllers --> TagViews[Tag Views]
    Controllers --> AnalysisViews[Analysis Views]
    Controllers --> APIViews[API Views]
    Controllers --> WebhookHandlers[Webhook Handlers]

    %% Services Layer
    Services --> CampaignService[Campaign Service]
    Services --> TagService[Tag Service]
    Services --> AnalysisService[Analysis Service]
    Services --> AirflowService[Airflow Service]
    Services --> PyFlowService[PyFlow Service]
    Services --> NotificationService[Notification Service]
    Services --> CacheService[Cache Service]
    Services --> ExportService[Export Service]

    %% Domain Layer
    Domains --> CampaignDomain[Campaign Domain]
    Domains --> TagDomain[Tag Domain]
    Domains --> AnalysisDomain[Analysis Domain]
    Domains --> WorkflowDomain[Workflow Domain]

    %% Repository Layer
    Repositories --> CampaignRepo[Campaign Repository]
    Repositories --> TagRepo[Tag Repository]
    Repositories --> ResultRepo[Result Repository]
    Repositories --> WorkflowRepo[Workflow Repository]

    %% Models Layer
    Models --> Campaign[Campaign Model]
    Models --> LocationTarget[Location Target Model]
    Models --> UsernameTarget[Username Target Model]
    Models --> CampaignAnalysisSettings[Analysis Settings Model]
    Models --> DynamicTag[Dynamic Tag Model]
    Models --> TagGroup[Tag Group Model]
    Models --> TagCategory[Tag Category Model]
    Models --> CampaignResult[Campaign Result Model]
    Models --> WorkflowExecution[Workflow Execution Model]
    Models --> TagAnalysisResult[Tag Analysis Result Model]
    Models --> WorkflowLog[Workflow Log Model]
    Models --> AccountWhitelist[Account Whitelist Model]

    %% External Integrations
    AirflowService --> Airflow[Airflow DAGs]
    PyFlowService --> PyFlow[PyFlow Workflows]
    NotificationService --> MessageQueue[Message Queue]

    %% Message Queue Integration
    MessageQueue --> WorkerProcesses[Worker Processes]
    WorkerProcesses --> BackgroundTasks[Background Tasks]

    %% Cache Integration
    CacheService --> Redis[Redis Cache]
    Redis --> TagCache[Tag Cache]
    Redis --> LocationCache[Location Cache]
    Redis --> ResultCache[Result Cache]

    %% Airflow DAGs
    Airflow --> DataCollectionDAG[Campaign Data Collection DAG]
    Airflow --> TaggingDAG[Campaign Tagging DAG]
    Airflow --> CEPDAG[Campaign CEP DAG]
    Airflow --> MonitoringDAG[Monitoring DAG]

    %% PyFlow Workflows
    PyFlow --> CollectionWorkflow[Account Collection Workflow]
    PyFlow --> AnalysisWorkflow[Account Analysis Workflow]
    PyFlow --> EngagementWorkflow[Engagement Workflow]
    PyFlow --> OptimizedCompoundNodes[Optimized Compound Nodes]

    %% Instagram Integration
    AnalysisService --> TaggingEngine[Instagram Tagging Engine]

    %% Monitoring & Analytics
    MonitoringDAG --> MetricsCollection[Metrics Collection]
    MetricsCollection --> Dashboard[Analytics Dashboard]

    %% Error Handling & Retry Logic
    WorkflowDomain --> RetryManager[Retry Manager]
    RetryManager --> ErrorHandling[Error Handling]

    %% Workflow Tracking System
    WorkflowDomain --> ProgressTracker[Progress Tracker]
    ProgressTracker --> RealTimeUpdates[Real-time Updates]

    %% Legend
    classDef uiLayer fill:#d4f1f9,stroke:#333,stroke-width:1px;
    classDef controllerLayer fill:#fff2cc,stroke:#333,stroke-width:1px;
    classDef serviceLayer fill:#d5e8d4,stroke:#333,stroke-width:1px;
    classDef domainLayer fill:#e1d5e7,stroke:#333,stroke-width:1px;
    classDef repositoryLayer fill:#f8cecc,stroke:#333,stroke-width:1px;
    classDef modelLayer fill:#dae8fc,stroke:#333,stroke-width:1px;
    classDef externalSystem fill:#ffe6cc,stroke:#333,stroke-width:1px;
    classDef cacheSystem fill:#d0cee2,stroke:#333,stroke-width:1px;
    classDef messagingSystem fill:#fad9d5,stroke:#333,stroke-width:1px;
    classDef monitoringSystem fill:#b1ddf0,stroke:#333,stroke-width:1px;

    class UI,Views,Forms,API,ReactComponents uiLayer;
    class Controllers,CampaignViews,TagViews,AnalysisViews,APIViews,WebhookHandlers controllerLayer;
    class Services,CampaignService,TagService,AnalysisService,AirflowService,PyFlowService,NotificationService,CacheService,ExportService serviceLayer;
    class Domains,CampaignDomain,TagDomain,AnalysisDomain,WorkflowDomain domainLayer;
    class Repositories,CampaignRepo,TagRepo,ResultRepo,WorkflowRepo repositoryLayer;
    class Models,Campaign,LocationTarget,UsernameTarget,CampaignAnalysisSettings,DynamicTag,TagGroup,TagCategory,CampaignResult,WorkflowExecution,TagAnalysisResult,WorkflowLog,AccountWhitelist modelLayer;
    class Airflow,PyFlow,DataCollectionDAG,TaggingDAG,CEPDAG,CollectionWorkflow,AnalysisWorkflow,EngagementWorkflow,TaggingEngine,OptimizedCompoundNodes,MonitoringDAG externalSystem;
    class Redis,TagCache,LocationCache,ResultCache cacheSystem;
    class MessageQueue,WorkerProcesses,BackgroundTasks messagingSystem;
    class MetricsCollection,Dashboard,ProgressTracker,RealTimeUpdates,RetryManager,ErrorHandling monitoringSystem;
