# Campaign System Blueprint

This directory contains the blueprint documentation for the Campaign System, including architecture diagrams, data models, and workflow charts.

## Mermaid Diagrams

The following Mermaid diagrams are available:

### Architecture Diagrams

- `campaigns_architecture_evolution.mermaid`: Shows the evolution of the campaign system architecture over time.
- `campaigns_block_diagram.mermaid`: High-level block diagram of the campaign system components.
- `campaigns_component_dependencies.mermaid`: Detailed component dependencies in the campaign system.
- `enhanced_campaigns_block_diagram.mermaid`: Enhanced version of the block diagram with additional details.
- `instagram_automation_components.mermaid`: Component diagram showing the system architecture of the Instagram automation system.
- `instagram_automation_data_flow.mermaid`: High-level data flow diagram showing the entire Instagram automation process.

### Data Models

- `campaigns_data_model.mermaid`: Original data model for the campaign system.
- `updated_campaigns_data_model.mermaid`: Updated data model that reflects the current state of the models in the campaigns app.
- `enhanced_campaigns_data_model.mermaid`: Enhanced data model with additional details and relationships.
- `instagram_automation_erd.mermaid`: Entity-relationship diagram showing the database schema for the Instagram automation system.

### Workflow and Process Diagrams

- `campaigns_flow_chart.mermaid`: Flow chart of the campaign process from creation to results.
- `enhanced_campaigns_flow_chart.mermaid`: Enhanced flow chart with additional details and optimizations.
- `campaigns_mind_map.mermaid`: Mind map of the campaign system concepts and features.
- `campaigns_tag_system.mermaid`: Diagram of the tag system architecture and workflow.
- `campaigns_user_journey.mermaid`: User journey through the campaign system.
- `campaigns_workflow_tracking.mermaid`: Diagram of the workflow tracking system.
- `campaigns_workflow_execution.mermaid`: Detailed workflow execution process.
- `enhanced_campaigns_workflow_execution.mermaid`: Enhanced workflow execution with additional details.
- `instagram_automation_sequence.mermaid`: Sequence diagram showing the Instagram automation workflow execution.
- `bot_worker_resource_management.mermaid`: Detailed diagram of the bot worker queue system.

## Documentation

Detailed documentation is available:

- `campaign_simulation_system.md`: Documentation for the campaign simulation system.
- `campaign_simulation_user_guide.md`: User guide for the campaign simulation system.
- `instagram_automation_system.md`: Comprehensive explanation of the complete data flow in the Instagram automation system.

## Kanban Board

The `KANBAN` directory contains the Kanban board for tracking development tasks:

- `README.md`: Instructions for using the Kanban board.
- Various task files organized by status (BACKLOG, READY, IN_PROGRESS, REVIEW, DONE).

## Notes on Data Model Updates

The `updated_campaigns_data_model.mermaid` file reflects the current state of the models in the campaigns app, with the following key differences from the original data model:

1. Removed `CampaignAnalysisSettings` model, replaced with `CampaignTag` for associating tags with campaigns.
2. Added `CampaignTagRule`, `TagRuleCondition`, and `CampaignTagCondition` models for tag rule management.
3. Added `CampaignROI` model for tracking campaign return on investment.
4. Added `Notification` model for the notification system.
5. Updated field lists to match the current implementation.
6. Simplified `WorkflowExecution` relationships to include only `WorkflowProgressUpdate`.

These changes reflect the current implementation of the campaigns app and should be used as the reference for future development.

## Notes on Instagram Automation System Documentation

The Instagram automation system documentation provides a comprehensive overview of the data flow from campaign creation to data collection and storage. Key components include:

1. **Campaign Creation and Initialization**: Details the Django models involved, validation process, and data structure.
2. **Data Transfer to Airflow**: Explains the communication mechanism, API endpoints, and data serialization.
3. **Airflow DAG Execution**: Describes the DAG structure, PyFlow invocation, and parameter passing.
4. **Data Collection Workflow**: Outlines the workflow execution steps, Instagram data access, and error handling.
5. **Bot Worker Resource Management**: Details the queue system, priority calculation, and Redis-based communication.
6. **Progress Tracking**: Explains how LogProgressNode and QueueMessageNode function, and how progress data flows back to the Django application.
7. **Data Storage and Accessibility**: Describes where and how collected Instagram data is stored, the database schema, and data access methods.

The diagrams provide visual representations of these components and their interactions, making it easier to understand the system architecture and data flow.
