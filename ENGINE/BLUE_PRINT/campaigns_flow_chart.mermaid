graph TD
    %% Main Campaign Flow
    A[User Creates Campaign] --> B[Configure Campaign Settings]
    B --> C{Target Type?}
    C -->|Location Based| D[Add Location Targets]
    C -->|Username Based| E[Add Username Targets]
    C -->|Mixed| F[Add Both Target Types]

    D --> G[Configure Analysis Settings]
    E --> G
    F --> G

    G --> H[Add Tags/Tag Rules]
    H --> H1[Select from Tag Library]
    H --> H2[Create Custom Tags]
    H --> H3[Use Tag Groups]

    H1 --> I[Launch Campaign]
    H2 --> I
    H3 --> I

    %% Airflow Integration
    I --> J[Trigger Airflow DAG]
    J --> K[campaign_data_collection DAG]

    %% Error Handling
    J --> J1{Validation Check}
    J1 -->|Failed| J2[Display Error]
    J2 --> J3[Fix Configuration]
    J3 --> I
    J1 -->|Passed| K

    %% Phase A: Data Collection
    K --> L[Validate Campaign Config]
    L --> M{Target Type?}
    M -->|Location| N[Process Location Targets]
    M -->|Username| O[Process Username Targets]
    M -->|Mixed| P[Process Both Target Types]

    N --> Q[Run PyFlow Workflow]
    O --> Q
    P --> Q

    %% Workflow Tracking
    Q --> Q1[Create WorkflowExecution Record]
    Q1 --> Q2[Update Active Workflows]
    Q2 --> Q3[Track Progress in Real-time]
    Q3 --> Q4[Handle Errors & Retries]

    Q4 -->|Success| R[Update Campaign Results]
    Q4 -->|Failure after Retries| Q5[Mark Campaign as Failed]
    Q5 --> Q6[Notify User of Failure]

    %% Phase B: Analysis & Tagging
    R --> S[Trigger campaign_tagging DAG]
    S --> T[Analyze Collected Accounts]
    T --> T1[Fetch Campaign Tags and Rules]
    T1 --> T2[Apply Tag Rules to Accounts]
    T2 --> U[Save Tag Analysis Results]
    U --> V[Generate Account Whitelist]

    %% Whitelist Management
    V --> V1[Create AccountWhitelist Record]
    V1 --> V2[Notify User of Whitelist]
    V2 --> V3[User Reviews Whitelist]

    %% Phase C: Customer Engagement (Optional)
    V3 --> W{Execute CEP?}
    W -->|Yes| W1[Trigger campaign_cep DAG]
    W1 --> X[Execute Engagement Workflows]
    X --> Y[Update Campaign Statistics]
    W -->|No| Z[View Campaign Results]

    %% Campaign Monitoring & Results
    Y --> Z
    Z --> Z1[View Detailed Analytics]
    Z1 --> AA[Export Campaign Data]

    %% Campaign Management
    A --> AB[View Campaign List]
    AB --> AC[View Campaign Details]
    AC --> AD[Toggle Favorite Status]
    AD --> AD1[Update is_favorite Flag]

    %% Tag Management
    AE[Create/Manage Tags] --> AF[Create Tag]
    AE --> AG[Create Tag Group]
    AF --> AH[Assign Tags to Campaign]
    AG --> AI[Add Tags to Group]
    AI --> AH

    %% Cache Integration
    D --> D1[Use Location Cache]
    H1 --> H1a[Use Tag Cache]
    Z --> Z2[Use Result Cache]

    %% Asynchronous Processing
    Q3 --> Q3a[Send Progress Updates]
    Q3a --> Q3b[Update UI in Real-time]

    %% Optimized Workflows
    Q --> Q7[Use Optimized Compound Nodes]
    Q7 --> Q8[Pattern Recognition]
    Q8 --> Q9[Workflow Optimization]

    %% Legend
    classDef userAction fill:#d4f1f9,stroke:#333,stroke-width:1px;
    classDef systemProcess fill:#e1d5e7,stroke:#333,stroke-width:1px;
    classDef decision fill:#fff2cc,stroke:#333,stroke-width:1px;
    classDef airflowProcess fill:#d5e8d4,stroke:#333,stroke-width:1px;
    classDef pyflowProcess fill:#ffe6cc,stroke:#333,stroke-width:1px;
    classDef errorHandling fill:#f8cecc,stroke:#333,stroke-width:1px;
    classDef cacheProcess fill:#d0cee2,stroke:#333,stroke-width:1px;
    classDef asyncProcess fill:#fad9d5,stroke:#333,stroke-width:1px;
    classDef optimizationProcess fill:#b1ddf0,stroke:#333,stroke-width:1px;

    class A,B,D,E,F,G,H,H1,H2,H3,AB,AC,AD,AE,AF,AG,AI,Z,Z1,AA,V3,J3 userAction;
    class I,R,T,T1,T2,U,V,V1,Y,AD1 systemProcess;
    class C,M,J1,W decision;
    class J,K,L,N,O,P,S,W1 airflowProcess;
    class Q,Q1,Q2,Q3,X,Q7,Q8,Q9 pyflowProcess;
    class J2,Q4,Q5,Q6 errorHandling;
    class D1,H1a,Z2 cacheProcess;
    class Q3a,Q3b,V2 asyncProcess;
    class Q7,Q8,Q9 optimizationProcess;
