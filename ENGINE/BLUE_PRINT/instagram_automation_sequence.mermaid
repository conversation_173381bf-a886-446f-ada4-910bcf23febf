---
title: Instagram Automation System - Sequence Diagram
---
sequenceDiagram
    participant User
    participant CampaignApp as Campaigns App
    participant AirflowService as Airflow Service
    participant AirflowDAG as Airflow DAG
    participant ResourceMgr as Resource Manager
    participant Py<PERSON>low as PyFlow Workflow
    participant ProgressTracker as Progress Tracker
    participant Instagram as Instagram API
    participant DB as Database
    
    %% Campaign Creation
    User->>CampaignApp: Create Campaign
    CampaignApp->>CampaignApp: Validate Campaign
    CampaignApp->>DB: Save Campaign (status: draft)
    CampaignApp-->>User: Display Campaign
    
    %% Campaign Launch
    User->>CampaignApp: Launch Campaign
    CampaignApp->>DB: Update Campaign (status: pending)
    CampaignApp->>AirflowService: Trigger DAG
    
    %% Airflow Orchestration
    AirflowService->>AirflowDAG: Execute campaign_data_collection DAG
    AirflowDAG->>AirflowDAG: Validate Campaign Config
    AirflowDAG->>DB: Update Campaign (status: running)
    
    %% Process Targets
    alt Location-based or Mixed Campaign
        AirflowDAG->>AirflowDAG: Process Location Targets
    end
    alt Username-based or Mixed Campaign
        AirflowDAG->>AirflowDAG: Process Username Targets
    end
    
    %% Resource Management
    AirflowDAG->>ResourceMgr: Register Workflow
    ResourceMgr->>ResourceMgr: Check Bot Worker Availability
    
    alt Bot Worker Available
        ResourceMgr->>PyFlow: Start Workflow Immediately
    else Bot Worker Busy
        ResourceMgr->>ResourceMgr: Add to Queue
        ResourceMgr-->>AirflowDAG: Return Queue Position
        ResourceMgr->>ResourceMgr: Wait for Bot Worker
        ResourceMgr->>PyFlow: Start Workflow When Available
    end
    
    %% Workflow Execution
    PyFlow->>ProgressTracker: Create Workflow Execution Record
    PyFlow->>PyFlow: Initialize Workflow
    
    %% Data Collection
    loop For Each Target
        PyFlow->>Instagram: Fetch Account Data
        Instagram-->>PyFlow: Return Account Data
        PyFlow->>DB: Store Account Data
        PyFlow->>ProgressTracker: Update Progress
        ProgressTracker->>DB: Save Progress Update
    end
    
    %% Workflow Completion
    PyFlow->>ProgressTracker: Mark Workflow Completed
    ProgressTracker->>DB: Update Workflow Status
    ProgressTracker->>ResourceMgr: Release Bot Worker
    ResourceMgr->>ResourceMgr: Start Next Workflow from Queue
    
    %% Campaign Update
    AirflowDAG->>DB: Update Campaign Results
    AirflowDAG->>DB: Update Campaign (status: completed)
    
    %% Account Analysis (Separate DAG)
    User->>CampaignApp: Analyze Accounts
    CampaignApp->>AirflowService: Trigger campaign_tagging DAG
    AirflowService->>AirflowDAG: Execute campaign_tagging DAG
    
    %% Tagging Process
    AirflowDAG->>ResourceMgr: Register Analysis Workflow
    ResourceMgr->>PyFlow: Start Analysis Workflow
    
    loop For Each Account
        PyFlow->>DB: Fetch Account
        PyFlow->>PyFlow: Apply Tag Rules
        PyFlow->>DB: Store Tag Results
        PyFlow->>ProgressTracker: Update Progress
    end
    
    %% Whitelist Generation
    PyFlow->>PyFlow: Filter Accounts for Whitelist
    PyFlow->>DB: Create Whitelist Entries
    PyFlow->>ProgressTracker: Mark Analysis Completed
    
    %% Final Updates
    AirflowDAG->>DB: Update Campaign Analysis Results
    AirflowDAG->>DB: Update Campaign (status: completed)
    
    %% User Views Results
    User->>CampaignApp: View Campaign Results
    CampaignApp->>DB: Fetch Campaign Data
    CampaignApp->>DB: Fetch Collected Accounts
    CampaignApp->>DB: Fetch Whitelist
    CampaignApp-->>User: Display Results Dashboard
