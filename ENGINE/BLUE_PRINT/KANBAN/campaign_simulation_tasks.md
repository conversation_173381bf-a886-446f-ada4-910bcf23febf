# Campaign Simulation System - Kanban Board

## To Do

- [ ] Add more realistic data generation for simulated accounts
- [ ] Add more edge cases for testing the tag matching logic
- [ ] Improve the visualization of simulation results
- [ ] Add more export formats (Excel, PDF, etc.)
- [ ] Add more Airflow integration, such as SLAs, retries, and more sophisticated error handling
- [ ] Add support for simulating engagement workflows
- [ ] Add support for simulating multiple campaigns simultaneously
- [ ] Add support for simulating campaign ROI
- [ ] Add support for simulating campaign metrics
- [ ] Add support for simulating campaign reports

## In Progress

- [ ] Add more unit tests for the tag matching logic
- [ ] Add more integration tests for the full campaign workflow
- [ ] Improve error handling in the simulation scripts
- [ ] Add more documentation for the simulation system

## Done

- [x] Create management command to empty the backend
- [x] Create management command to populate the backend with predefined data
- [x] Create management command to simulate a full campaign
- [x] Create simulation scripts to simulate PyFlow workflows
- [x] Create Airflow DAG to simulate the campaign workflow
- [x] Create simulation dashboard to view simulation results
- [x] Create run simulation page to configure and run a simulation
- [x] Create export functionality to export simulation results
- [x] Add unit tests for the tag matching logic
- [x] Add integration tests for the full campaign workflow
- [x] Add documentation for the simulation system
- [x] Add user guide for running simulations
- [x] Improve the simulation with more realistic data
- [x] Add edge cases for testing the tag matching logic
- [x] Enhance the Airflow DAG with better error handling and notifications
- [x] Improve the UI with a simulation dashboard and run simulation page

## Backlog

- [ ] Add support for simulating A/B testing
- [ ] Add support for simulating campaign optimization
- [ ] Add support for simulating campaign targeting
- [ ] Add support for simulating campaign scheduling
- [ ] Add support for simulating campaign budgeting
- [ ] Add support for simulating campaign performance
- [ ] Add support for simulating campaign analytics
- [ ] Add support for simulating campaign insights
- [ ] Add support for simulating campaign recommendations
- [ ] Add support for simulating campaign automation
