graph TD
    %% Core Components
    UI[User Interface] --> CampaignController[Campaign Controller]
    UI --> TagController[Tag Controller]
    UI --> AnalysisController[Analysis Controller]
    
    %% Controllers
    CampaignController --> CampaignService[Campaign Service]
    TagController --> TagService[Tag Service]
    AnalysisController --> AnalysisService[Analysis Service]
    
    %% Services
    CampaignService --> CampaignDomain[Campaign Domain]
    CampaignService --> AirflowService[Airflow Service]
    TagService --> TagDomain[Tag Domain]
    AnalysisService --> AnalysisDomain[Analysis Domain]
    AnalysisService --> PyFlowService[PyFlow Service]
    
    %% Domains
    CampaignDomain --> CampaignRepository[Campaign Repository]
    TagDomain --> TagRepository[Tag Repository]
    AnalysisDomain --> AnalysisRepository[Analysis Repository]
    
    %% Repositories
    CampaignRepository --> Database[(Database)]
    TagRepository --> Database
    AnalysisRepository --> Database
    
    %% External Services
    AirflowService --> AirflowAPI[Airflow API]
    PyFlowService --> PyFlowEngine[PyFlow Engine]
    
    %% Airflow Components
    AirflowAPI --> DataCollectionDAG[Data Collection DAG]
    AirflowAPI --> TaggingDAG[Tagging DAG]
    AirflowAPI --> CEPDAG[CEP DAG]
    
    %% PyFlow Components
    PyFlowEngine --> CollectionWorkflow[Collection Workflow]
    PyFlowEngine --> AnalysisWorkflow[Analysis Workflow]
    PyFlowEngine --> EngagementWorkflow[Engagement Workflow]
    
    %% Instagram Integration
    AnalysisService --> InstagramAPI[Instagram API]
    InstagramAPI --> ProfileData[Profile Data]
    InstagramAPI --> EngagementActions[Engagement Actions]
    
    %% Caching
    CampaignService --> CacheService[Cache Service]
    TagService --> CacheService
    AnalysisService --> CacheService
    CacheService --> RedisCache[(Redis Cache)]
    
    %% Messaging
    CampaignService --> MessageBroker[Message Broker]
    TagService --> MessageBroker
    AnalysisService --> MessageBroker
    MessageBroker --> NotificationService[Notification Service]
    NotificationService --> UI
    
    %% Dependencies
    CampaignService -.-> TagService
    AnalysisService -.-> CampaignService
    AnalysisService -.-> TagService
    DataCollectionDAG -.-> PyFlowEngine
    TaggingDAG -.-> PyFlowEngine
    CEPDAG -.-> PyFlowEngine
    
    %% Legend
    classDef ui fill:#d4f1f9,stroke:#333,stroke-width:1px;
    classDef controller fill:#fff2cc,stroke:#333,stroke-width:1px;
    classDef service fill:#d5e8d4,stroke:#333,stroke-width:1px;
    classDef domain fill:#e1d5e7,stroke:#333,stroke-width:1px;
    classDef repository fill:#f8cecc,stroke:#333,stroke-width:1px;
    classDef database fill:#dae8fc,stroke:#333,stroke-width:1px;
    classDef external fill:#ffe6cc,stroke:#333,stroke-width:1px;
    classDef dependency fill:#f5f5f5,stroke:#666666,stroke-width:1px,stroke-dasharray: 5 5;
    
    class UI ui;
    class CampaignController,TagController,AnalysisController controller;
    class CampaignService,TagService,AnalysisService,AirflowService,PyFlowService,CacheService,NotificationService service;
    class CampaignDomain,TagDomain,AnalysisDomain domain;
    class CampaignRepository,TagRepository,AnalysisRepository repository;
    class Database,RedisCache database;
    class AirflowAPI,PyFlowEngine,InstagramAPI,DataCollectionDAG,TaggingDAG,CEPDAG,CollectionWorkflow,AnalysisWorkflow,EngagementWorkflow,ProfileData,EngagementActions,MessageBroker external;
    class CampaignService-->TagService,AnalysisService-->CampaignService,AnalysisService-->TagService,DataCollectionDAG-->PyFlowEngine,TaggingDAG-->PyFlowEngine,CEPDAG-->PyFlowEngine dependency;
