---
title: Instagram Automation System - Component Diagram
---
flowchart TB
    %% Main Components
    User([User])
    Django[Django Web Application]
    Airflow[Airflow Orchestration]
    PyFlow[PyFlow Framework]
    Redis[(Redis Queue System)]
    PostgreSQL[(PostgreSQL Database)]
    Instagram[Instagram API]
    
    %% Django Subcomponents
    subgraph Django
        direction TB
        CampaignApp[Campaigns App]
        InstagramApp[Instagram App]
        DashboardService[Dashboard Service]
        AirflowService[Airflow Service]
        ResourceService[Resource Manager Service]
        ProgressService[Progress Tracking Service]
    end
    
    %% Airflow Subcomponents
    subgraph Airflow
        direction TB
        CollectionDAG[Data Collection DAG]
        TaggingDAG[Account Tagging DAG]
        CEPDDAG[Customer Engagement DAG]
    end
    
    %% PyFlow Subcomponents
    subgraph PyFlow
        direction TB
        CollectionWorkflow[Account Collection Workflow]
        AnalysisWorkflow[Account Analysis Workflow]
        TaggingWorkflow[Account Tagging Workflow]
        EngagementWorkflow[Engagement Workflow]
        LogProgressNode[LogProgressNode]
        QueueMessageNode[QueueMessageNode]
    end
    
    %% Database Subcomponents
    subgraph PostgreSQL
        direction TB
        CampaignModels[Campaign Models]
        InstagramModels[Instagram Models]
        WorkflowModels[Workflow Models]
        TagModels[Tag Models]
    end
    
    %% Redis Subcomponents
    subgraph Redis
        direction TB
        WorkflowQueue[Workflow Queue]
        ProgressUpdates[Progress Updates]
        APIRateLimits[API Rate Limits]
    end
    
    %% User Interactions
    User -->|Interacts with| Django
    
    %% Django Connections
    CampaignApp -->|Creates/Manages| CampaignModels
    InstagramApp -->|Manages| InstagramModels
    DashboardService -->|Reads| CampaignModels
    DashboardService -->|Reads| InstagramModels
    DashboardService -->|Reads| WorkflowModels
    AirflowService -->|Triggers| Airflow
    ResourceService -->|Manages| Redis
    ProgressService -->|Tracks| WorkflowModels
    ProgressService -->|Monitors| PyFlow
    
    %% Airflow Connections
    CollectionDAG -->|Executes| CollectionWorkflow
    TaggingDAG -->|Executes| AnalysisWorkflow
    TaggingDAG -->|Executes| TaggingWorkflow
    CEPDDAG -->|Executes| EngagementWorkflow
    CollectionDAG -->|Updates| CampaignModels
    TaggingDAG -->|Updates| CampaignModels
    CEPDDAG -->|Updates| CampaignModels
    
    %% PyFlow Connections
    CollectionWorkflow -->|Collects from| Instagram
    CollectionWorkflow -->|Stores in| InstagramModels
    AnalysisWorkflow -->|Analyzes| InstagramModels
    TaggingWorkflow -->|Tags| InstagramModels
    EngagementWorkflow -->|Engages with| Instagram
    LogProgressNode -->|Updates| ProgressUpdates
    QueueMessageNode -->|Queues in| WorkflowQueue
    
    %% Redis Connections
    WorkflowQueue -->|Schedules| PyFlow
    ProgressUpdates -->|Informs| ProgressService
    APIRateLimits -->|Controls| PyFlow
    
    %% Database Connections
    CampaignModels -.->|Stored in| PostgreSQL
    InstagramModels -.->|Stored in| PostgreSQL
    WorkflowModels -.->|Stored in| PostgreSQL
    TagModels -.->|Stored in| PostgreSQL
    
    %% Styling
    classDef primary fill:#f9f,stroke:#333,stroke-width:2px;
    classDef secondary fill:#bbf,stroke:#333,stroke-width:1px;
    classDef storage fill:#bfb,stroke:#333,stroke-width:1px;
    classDef user fill:#fbb,stroke:#333,stroke-width:1px;
    
    class Django,Airflow,PyFlow primary;
    class CampaignApp,InstagramApp,CollectionDAG,TaggingDAG,CEPDDAG,CollectionWorkflow,AnalysisWorkflow,TaggingWorkflow,EngagementWorkflow secondary;
    class PostgreSQL,Redis storage;
    class User user;
