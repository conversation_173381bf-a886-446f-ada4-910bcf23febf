classDiagram
    %% Campaign and Target Models
    Campaign "1" -- "0..*" LocationTarget : has
    Campaign "1" -- "0..*" UsernameTarget : has
    Campaign "1" -- "0..*" CampaignTag : has
    Campaign "1" -- "0..*" CampaignResult : has
    Campaign "1" -- "0..*" WorkflowExecution : has
    Campaign "1" -- "0..*" CampaignROI : has
    User "1" -- "0..*" Campaign : creates

    %% Tag Models
    DynamicTag "0..*" -- "0..*" DynamicTag : related to
    TagCategory "1" -- "0..*" DynamicTag : categorizes
    TagGroup "1" -- "0..*" DynamicTag : groups
    CampaignTag "0..*" -- "1" DynamicTag : uses
    CampaignTag "0..*" -- "1" Campaign : belongs to

    %% Tag Rules
    CampaignTagRule "1" -- "0..*" TagRuleCondition : has
    CampaignTagRule "1" -- "0..*" CampaignTagCondition : has

    %% Analysis Results
    TagAnalysisResult "0..*" -- "1" Campaign : belongs to
    TagAnalysisResult "0..*" -- "1" DynamicTag : applies

    %% Workflow Tracking
    WorkflowExecution "1" -- "0..*" WorkflowProgressUpdate : has

    %% Notification System
    Notification "0..*" -- "1" Campaign : about

    %% Campaign Model
    class Campaign {
        +UUID id
        +String name
        +String description
        +JSON dmp_conf
        +String status
        +String target_type
        +String audience_type
        +DateTime created_at
        +DateTime updated_at
        +String airflow_run_id
        +String airflow_dag_id
        +Boolean is_favorite
        +ForeignKey creator
    }

    %% Target Models
    class LocationTarget {
        +UUID id
        +ForeignKey campaign
        +String location_id
        +String country
        +String city
        +Boolean processed
        +DateTime created_at
    }

    class UsernameTarget {
        +UUID id
        +ForeignKey campaign
        +String username
        +String audience_type
        +Boolean processed
        +DateTime created_at
    }

    %% Tag Models
    class DynamicTag {
        +UUID id
        +String name
        +String description
        +ForeignKey category
        +ForeignKey tag_group
        +String tag_type
        +String pattern
        +String field
        +Boolean is_global
        +String confidence_level
        +Float weight
        +DateTime created_at
        +DateTime updated_at
    }

    class TagGroup {
        +UUID id
        +String name
        +String description
        +String color
        +Boolean is_public
        +DateTime created_at
        +DateTime updated_at
    }

    class TagCategory {
        +UUID id
        +String name
        +String description
        +ForeignKey parent
        +String color
        +String icon
        +Integer priority
        +DateTime created_at
        +DateTime updated_at
    }

    class CampaignTag {
        +UUID id
        +ForeignKey campaign
        +ForeignKey tag
        +Boolean is_active
        +Float min_confidence
        +DateTime created_at
        +DateTime updated_at
    }

    %% Tag Rule Models
    class CampaignTagRule {
        +UUID id
        +String name
        +String tag
        +String description
        +Boolean active
        +Boolean is_global
        +DateTime created_at
        +DateTime updated_at
    }

    class TagRuleCondition {
        +UUID id
        +ForeignKey rule
        +String field
        +String field_type
        +String operator
        +String value
        +Float score
        +Boolean required
        +DateTime created_at
        +DateTime updated_at
    }

    class CampaignTagCondition {
        +UUID id
        +ForeignKey rule
        +String field
        +String operator
        +String value
        +Float score
        +Boolean required
        +DateTime created_at
    }

    %% Result Models
    class CampaignResult {
        +UUID id
        +ForeignKey campaign
        +Integer total_accounts_found
        +Integer total_accounts_processed
        +Integer total_accounts_pending
        +Integer total_accounts_tagged
        +Integer total_accounts_whitelisted
        +Float average_confidence_score
        +Float analysis_duration
        +Float engagement_rate
        +Float quality_score
        +DateTime last_processed_at
        +DateTime created_at
        +DateTime updated_at
    }

    class TagAnalysisResult {
        +UUID id
        +ForeignKey account
        +ForeignKey campaign
        +ForeignKey tag
        +Boolean matched
        +Float confidence_score
        +DateTime created_at
        +DateTime updated_at
    }

    %% ROI Model
    class CampaignROI {
        +UUID id
        +ForeignKey campaign
        +Float total_cost
        +Float actual_value
        +Float expected_value
        +Float roi_percentage
        +Integer accounts_collected
        +Integer accounts_whitelisted
        +Decimal cost_per_account
        +Decimal cost_per_whitelist
        +String notes
        +DateTime created_at
        +DateTime updated_at
    }

    %% Workflow Models
    class WorkflowExecution {
        +UUID id
        +String campaign_id
        +String workflow_name
        +String workflow_path
        +String workflow_type
        +String status
        +DateTime start_time
        +DateTime end_time
        +Float duration
        +Float progress
        +Integer total_items
        +Integer processed_items
        +Integer successful_items
        +Integer failed_items
        +String error_message
        +String log_file
        +JSON parameters
        +JSON results
        +Integer retry_count
        +DateTime last_retry
        +DateTime created_at
        +DateTime updated_at
    }

    class WorkflowProgressUpdate {
        +UUID id
        +ForeignKey workflow_execution
        +Float progress
        +Integer processed_items
        +Integer successful_items
        +Integer failed_items
        +String message
        +DateTime timestamp
    }

    %% Notification Model
    class Notification {
        +UUID id
        +ForeignKey campaign
        +String notification_type
        +String title
        +String message
        +Boolean is_read
        +DateTime created_at
    }

    %% External Models
    class User {
        +Integer id
        +String username
        +String email
    }
