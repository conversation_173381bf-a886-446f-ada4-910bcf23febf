---
title: Instagram Automation System Data Flow
---
flowchart TD
    %% Main Components
    Campaign["Campaign Creation\n(Django Model)"]
    Airflow["Airflow DAGs\n(Orchestration)"]
    PyFlow["PyFlow Workflows\n(Execution)"]
    ResourceMgr["Resource Manager\n(Queue System)"]
    ProgressTrack["Progress Tracking\n(Monitoring)"]
    DataStorage["Data Storage\n(PostgreSQL)"]
    Analysis["Account Analysis\n(Tagging Engine)"]
    Whitelist["Whitelist Generation\n(Filtering)"]
    Dashboard["Dashboard\n(Visualization)"]

    %% Campaign Creation Flow
    User([User]) -->|Creates Campaign| Campaign
    Campaign -->|Validates| CampaignValidation["Campaign Validation\n- Requires targets\n- Validates fields"]
    CampaignValidation -->|Stores| DataStorage
    
    %% Airflow Orchestration
    Campaign -->|Triggers| Airflow
    Airflow -->|Configures| AirflowDAG["DAG Configuration\n- campaign_data_collection.py\n- campaign_tagging.py\n- campaign_cep.py"]
    
    %% Resource Management
    AirflowDAG -->|Registers Workflow| ResourceMgr
    ResourceMgr -->|Prioritizes| Queue["Workflow Queue\n(Redis-based)"]
    Queue -->|Schedules| PyFlow
    
    %% PyFlow Execution
    PyFlow -->|Executes| Collection["Account Collection\n- Location-based\n- Username-based"]
    Collection -->|Logs Progress| LogProgress["LogProgressNode\n(Progress Updates)"]
    LogProgress -->|Updates| ProgressTrack
    
    %% Data Storage
    Collection -->|Stores Accounts| DataStorage
    DataStorage -->|Provides Data| Analysis
    
    %% Analysis and Tagging
    Analysis -->|Applies Tags| TagEngine["Tagging Engine\n- Pattern Matching\n- Field Analysis"]
    TagEngine -->|Updates| DataStorage
    
    %% Whitelist Generation
    Analysis -->|Filters Accounts| Whitelist
    Whitelist -->|Stores| DataStorage
    
    %% Progress Tracking
    ProgressTrack -->|Updates| Campaign
    ProgressTrack -->|Provides Data| Dashboard
    
    %% Dashboard Visualization
    DataStorage -->|Provides Stats| Dashboard
    Dashboard -->|Displays to| User

    %% Subgraphs for organization
    subgraph "Campaign Management"
        Campaign
        CampaignValidation
    end
    
    subgraph "Workflow Orchestration"
        Airflow
        AirflowDAG
        ResourceMgr
        Queue
    end
    
    subgraph "Data Processing"
        PyFlow
        Collection
        LogProgress
        Analysis
        TagEngine
        Whitelist
    end
    
    subgraph "Monitoring & Visualization"
        ProgressTrack
        Dashboard
    end
    
    %% Styling
    classDef primary fill:#f9f,stroke:#333,stroke-width:2px;
    classDef secondary fill:#bbf,stroke:#333,stroke-width:1px;
    classDef storage fill:#bfb,stroke:#333,stroke-width:1px;
    classDef user fill:#fbb,stroke:#333,stroke-width:1px;
    
    class Campaign,Airflow,PyFlow primary;
    class CampaignValidation,AirflowDAG,Collection,Analysis,Whitelist,ResourceMgr,Queue,LogProgress,TagEngine secondary;
    class DataStorage,ProgressTrack storage;
    class User,Dashboard user;
