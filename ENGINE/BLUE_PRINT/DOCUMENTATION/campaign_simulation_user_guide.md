# Campaign Simulation User Guide

This guide provides step-by-step instructions for using the Campaign Simulation System to test and develop the campaign functionality.

## Table of Contents

1. [Introduction](#introduction)
2. [Prerequisites](#prerequisites)
3. [Running a Simulation](#running-a-simulation)
4. [Viewing Simulation Results](#viewing-simulation-results)
5. [Exporting Simulation Results](#exporting-simulation-results)
6. [Advanced Usage](#advanced-usage)
7. [Troubleshooting](#troubleshooting)

## Introduction

The Campaign Simulation System allows you to test and develop the campaign functionality without requiring actual PyFlow workflows. It simulates the full campaign lifecycle, including account collection, tag analysis, and whitelist generation.

## Prerequisites

Before running a simulation, make sure you have:

1. **Django Environment**: Activated the virtual environment and installed all required dependencies.
2. **Database Migrations**: Applied all pending database migrations with `python manage.py migrate`.
3. **Predefined Data**: Populated the backend with predefined tag categories, tag groups, and tags.

## Running a Simulation

### Using the Web Interface

1. **Navigate to the Campaign Detail Page**: Go to the campaign detail page for the campaign you want to simulate.

2. **Click "Run Simulation"**: Click the "Run Simulation" button in the Account Analysis section.

3. **Configure the Simulation**:
   - **Number of Accounts**: Enter the number of accounts to simulate collecting (default: 100).
   - **Use Airflow**: Check this box to use Airflow DAGs for simulation instead of direct execution.
   - **Airflow Options** (if "Use Airflow" is checked):
     - **Minimum Followers**: Enter the minimum number of followers for tag analysis (default: 0).
     - **Maximum Followers**: Enter the maximum number of followers for tag analysis (default: 0).
     - **Enable Tagging**: Check this box to enable tagging (default: checked).

4. **Click "Run Simulation"**: Click the "Run Simulation" button to start the simulation.

### Using the Command Line

You can also run a simulation using the command line:

```bash
python manage.py simulate_full_campaign --name "Simulated Campaign" --accounts 100 --delay 0.5
```

Options:
- `--name NAME`: Name of the campaign to simulate (default: "Simulated Campaign").
- `--accounts ACCOUNTS`: Number of accounts to simulate collecting (default: 100).
- `--delay DELAY`: Delay between simulation steps in seconds (default: 0.5).
- `--use-airflow`: Use Airflow DAGs for simulation instead of direct execution.

### Using the run_campaign_simulation.py Script

The `run_campaign_simulation.py` script provides a convenient way to run a simulation:

```bash
python run_campaign_simulation.py --accounts 100 --skip-empty --skip-populate
```

Options:
- `--accounts ACCOUNTS`: Number of accounts to simulate collecting (default: 100).
- `--skip-empty`: Skip emptying the backend database.
- `--skip-populate`: Skip populating the backend with predefined data.
- `--use-airflow`: Use Airflow DAGs for simulation instead of direct execution.

## Viewing Simulation Results

### Simulation Dashboard

The simulation dashboard provides a visual representation of the simulation results:

1. **Navigate to the Simulation Dashboard**: Go to the simulation dashboard for the campaign by clicking the "Simulation Dashboard" button on the campaign detail page.

2. **View Campaign Information**: The top section shows campaign information, including name, description, status, and metrics.

3. **View Workflow Progress**: The workflow progress section shows the progress of each workflow execution, including start time, end time, and duration.

4. **View Tag Analysis Results**: The tag analysis results section shows the number of accounts that matched each tag.

5. **View Account Distribution**: The account distribution section shows the distribution of accounts by tag status (tagged & whitelisted, tagged but not whitelisted, not tagged).

6. **View Sample Accounts**: The sample accounts section shows a sample of collected accounts and whitelisted accounts.

### Campaign Detail Page

The campaign detail page also shows simulation results:

1. **Campaign Progress**: The campaign progress section shows the total accounts found, accounts processed, and progress percentage.

2. **Account Analysis**: The account analysis section shows the white listed accounts, conversion rate, and provides links to view collected accounts and the white list.

## Exporting Simulation Results

You can export simulation results in CSV format:

1. **Navigate to the Simulation Dashboard**: Go to the simulation dashboard for the campaign.

2. **Click "Export Results"**: Click the "Export Results" button in the top right corner.

3. **Select Export Type**:
   - **Accounts**: Export collected accounts.
   - **Tags**: Export tag analysis results.
   - **Whitelist**: Export whitelist entries.

4. **Download the CSV File**: The CSV file will be downloaded to your computer.

## Advanced Usage

### Emptying the Backend

You can empty the backend database to start with a clean slate:

```bash
python manage.py empty_backend --confirm
```

Options:
- `--confirm`: Confirm that you want to empty the backend database.
- `--keep-users`: Keep user accounts when emptying the backend.

### Populating the Backend

You can populate the backend with predefined tag categories, tag groups, and tags:

```bash
python manage.py populate_backend
```

Options:
- `--clear`: Clear existing data before populating.

### Using Airflow

You can use Airflow DAGs to simulate the campaign workflow:

1. **Make sure Airflow is running**: Start the Airflow webserver and scheduler.

2. **Run a simulation with Airflow**: Use the `--use-airflow` option when running a simulation.

3. **Monitor the DAG**: Go to the Airflow web interface to monitor the DAG execution.

## Troubleshooting

### Common Issues

1. **Database Errors**: If you encounter database errors, try running `python manage.py migrate` to apply any pending migrations.

2. **Import Errors**: If you encounter import errors, make sure you have activated the virtual environment and installed all required dependencies.

3. **Airflow Errors**: If you encounter Airflow errors, check the Airflow logs for more details.

### Getting Help

If you encounter issues that you can't resolve, please contact the development team for assistance.
