# Campaign Simulation System

This document provides comprehensive documentation for the Campaign Simulation System, which allows for testing and development of the campaign functionality without requiring actual PyFlow workflows.

## Table of Contents

1. [Overview](#overview)
2. [System Components](#system-components)
3. [Management Commands](#management-commands)
4. [Airflow Integration](#airflow-integration)
5. [User Interface](#user-interface)
6. [Data Models](#data-models)
7. [Testing](#testing)
8. [Troubleshooting](#troubleshooting)
9. [Future Enhancements](#future-enhancements)

## Overview

The Campaign Simulation System is designed to simulate the full campaign lifecycle, including:

1. Account collection
2. Tag analysis
3. Whitelist generation

This allows for testing and development of the campaign functionality without requiring actual PyFlow workflows, making it easier to test and develop the campaign system.

## System Components

The Campaign Simulation System consists of the following components:

### Backend Components

1. **Management Commands**: Django management commands for emptying the backend, populating it with predefined data, and simulating a full campaign.
2. **Simulation Scripts**: Python scripts that simulate the behavior of PyFlow workflows.
3. **Airflow DAGs**: Airflow DAGs that use PythonOperators to execute the simulation scripts.

### Frontend Components

1. **Simulation Dashboard**: A dashboard for viewing the simulation results.
2. **Run Simulation Page**: A page for configuring and running a simulation.
3. **Export Functionality**: Functionality for exporting simulation results.

## Management Commands

### empty_backend

Empties the backend database, removing all campaigns, tags, tag groups, and related data.

```bash
python manage.py empty_backend --confirm [--keep-users]
```

Options:
- `--confirm`: Confirm that you want to empty the backend database.
- `--keep-users`: Keep user accounts when emptying the backend.

### populate_backend

Populates the backend with predefined tag categories, tag groups, and tags.

```bash
python manage.py populate_backend [--clear]
```

Options:
- `--clear`: Clear existing data before populating.

### simulate_full_campaign

Simulates a full campaign lifecycle, including account collection, tag analysis, and whitelist generation.

```bash
python manage.py simulate_full_campaign [--name NAME] [--accounts ACCOUNTS] [--delay DELAY] [--use-airflow]
```

Options:
- `--name NAME`: Name of the campaign to simulate (default: "Simulated Campaign").
- `--accounts ACCOUNTS`: Number of accounts to simulate collecting (default: 100).
- `--delay DELAY`: Delay between simulation steps in seconds (default: 0.5).
- `--use-airflow`: Use Airflow DAGs for simulation instead of direct execution.

## Airflow Integration

The Campaign Simulation System can use Airflow DAGs to simulate the campaign workflow. This provides better monitoring, error handling, and notification capabilities.

### simulate_campaign_workflow DAG

The `simulate_campaign_workflow` DAG simulates a campaign workflow by executing Python scripts that simulate the behavior of PyFlow workflows.

Tasks:
1. `validate_campaign_config`: Validates the campaign configuration.
2. `simulate_account_collection`: Simulates the account collection phase.
3. `simulate_tag_analysis`: Simulates the tag analysis phase.
4. `update_campaign_status`: Updates the campaign status to completed.
5. `send_success_notification`: Sends a success notification.
6. `send_failure_notification`: Sends a failure notification if any task fails.

Configuration Parameters:
- `campaign_id`: ID of the campaign.
- `num_accounts`: Number of accounts to simulate (default: 100).
- `min_followers`: Minimum followers for tag analysis (default: 0).
- `max_followers`: Maximum followers for tag analysis (default: 0).
- `enable_tagging`: Whether to enable tagging (default: True).
- `notify_email`: Email to send notifications to (optional).

## User Interface

### Simulation Dashboard

The simulation dashboard provides a visual representation of the simulation results, including:

- Campaign information
- Workflow progress
- Tag analysis results
- Account distribution
- Sample collected accounts
- Sample whitelisted accounts

URL: `/campaigns/<campaign_id>/simulation/`

### Run Simulation Page

The run simulation page allows you to configure and run a simulation, including:

- Number of accounts to simulate
- Whether to use Airflow
- Airflow-specific options (minimum followers, maximum followers, enable tagging)

URL: `/campaigns/<campaign_id>/simulation/run/`

### Export Functionality

The export functionality allows you to export simulation results in CSV format, including:

- Collected accounts
- Tag analysis results
- Whitelist entries

URL: `/campaigns/<campaign_id>/simulation/export/?type=<type>`

Types:
- `accounts`: Export collected accounts.
- `tags`: Export tag analysis results.
- `whitelist`: Export whitelist entries.

## Data Models

The Campaign Simulation System uses the following data models:

### Campaign

The `Campaign` model represents a campaign, including its name, description, status, and other properties.

### WorkflowExecution

The `WorkflowExecution` model represents a workflow execution, including its name, status, progress, and other properties.

### WorkflowProgressUpdate

The `WorkflowProgressUpdate` model represents a progress update for a workflow execution, including its progress, processed items, and other properties.

### TagAnalysisResult

The `TagAnalysisResult` model represents a tag analysis result, including the account, tag, matched status, confidence score, and match details.

### WhiteListEntry

The `WhiteListEntry` model represents a whitelist entry, including the account, tags, and other properties.

## Testing

The Campaign Simulation System includes unit tests and integration tests for testing the tag matching logic and the full campaign workflow.

### Unit Tests

The unit tests test the tag matching logic, including:

- Bio tag matching
- Posts tag matching
- Followers tag matching
- Engagement tag matching
- Whitelist generation
- Edge cases

Run the unit tests with:

```bash
python manage.py test campaigns.tests.test_tag_matching
```

### Integration Tests

The integration tests test the full campaign workflow, including:

- Campaign creation
- Tag assignment
- Account collection
- Tag analysis
- Whitelist generation

Run the integration tests with:

```bash
python manage.py test campaigns.tests.test_campaign_workflow
```

## Troubleshooting

### Common Issues

1. **Database Errors**: If you encounter database errors, try running `python manage.py migrate` to apply any pending migrations.

2. **Import Errors**: If you encounter import errors, make sure you have activated the virtual environment and installed all required dependencies.

3. **Airflow Errors**: If you encounter Airflow errors, check the Airflow logs for more details.

### Debugging

1. **Enable Debug Mode**: Set `DEBUG = True` in `settings.py` to enable debug mode.

2. **Check Logs**: Check the Django logs and Airflow logs for more details about errors.

3. **Use the Django Shell**: Use the Django shell (`python manage.py shell`) to interact with the models and debug issues.

## Future Enhancements

1. **More Realistic Data**: Add more realistic data generation for simulated accounts.

2. **More Edge Cases**: Add more edge cases for testing the tag matching logic.

3. **Better Visualization**: Improve the visualization of simulation results.

4. **More Export Formats**: Add more export formats (Excel, PDF, etc.).

5. **More Airflow Integration**: Add more Airflow integration, such as SLAs, retries, and more sophisticated error handling.
