graph TB
    subgraph "Current Architecture (2023 Q2)"
        A1[Django Monolith] --> A2[PostgreSQL Database]
        A1 --> A3[Airflow Integration]
        A1 --> A4[PyFlow Integration]
        A1 --> A5[Django Cache]
        A3 --> A6[Airflow DAGs]
        A4 --> A7[PyFlow Workflows]
    end

    subgraph "Phase 2 Architecture (2023 Q4)"
        B1[Django with DDD] --> B2[PostgreSQL Database]
        B1 --> B3[Airflow Integration]
        B1 --> B4[PyFlow Integration]
        B1 --> B5[Redis Cache]
        B3 --> B6[Enhanced Airflow DAGs]
        B4 --> B7[Optimized PyFlow Workflows]
        B1 --> B8[Background Tasks]
        B1 --> B9[Workflow Tracking System]
    end

    subgraph "Phase 3 Architecture (2024 Q1)"
        C1[Domain Services] --> C2[PostgreSQL Database]
        C1 --> C3[Airflow Integration]
        C1 --> C4[PyFlow Integration]
        C1 --> C5[Redis Cache]
        C3 --> C6[Advanced Airflow DAGs]
        C4 --> C7[Compound Node Workflows]
        C1 --> C8[Message Queue]
        C8 --> C9[Worker Processes]
        C1 --> C10[WebSocket Server]
        C10 --> C11[Real-time Updates]
    end

    subgraph "Target Architecture (2024 Q2)"
        D1[API Gateway] --> D2[Campaign Service]
        D1 --> D3[Tag Service]
        D1 --> D4[Workflow Service]
        D1 --> D5[Analytics Service]
        D2 --> D6[Campaign Database]
        D3 --> D7[Tag Database]
        D4 --> D8[Workflow Database]
        D5 --> D9[Analytics Database]
        D4 --> D10[Airflow Orchestration]
        D4 --> D11[PyFlow Execution]
        D12[Message Broker] --> D2
        D12 --> D3
        D12 --> D4
        D12 --> D5
        D13[Redis Cluster] --> D2
        D13 --> D3
        D13 --> D4
        D13 --> D5
        D14[WebSocket Server] --> D15[Client Applications]
        D1 --> D16[Authentication Service]
    end

    %% Evolution Connections
    A1 -.-> B1
    B1 -.-> C1
    C1 -.-> D1

    %% Legend
    classDef current fill:#d5e8d4,stroke:#82b366,stroke-width:1px;
    classDef phase2 fill:#dae8fc,stroke:#6c8ebf,stroke-width:1px;
    classDef phase3 fill:#ffe6cc,stroke:#d79b00,stroke-width:1px;
    classDef target fill:#e1d5e7,stroke:#9673a6,stroke-width:1px;
    classDef evolution fill:#f5f5f5,stroke:#666666,stroke-width:1px,stroke-dasharray: 5 5;

    class A1,A2,A3,A4,A5,A6,A7 current;
    class B1,B2,B3,B4,B5,B6,B7,B8,B9 phase2;
    class C1,C2,C3,C4,C5,C6,C7,C8,C9,C10,C11 phase3;
    class D1,D2,D3,D4,D5,D6,D7,D8,D9,D10,D11,D12,D13,D14,D15,D16 target;
