graph TD
    %% Workflow Tracking System Overview
    A[Workflow Tracking System] --> B[Workflow Creation]
    A --> C[Workflow Execution]
    A --> D[Progress Monitoring]
    A --> E[Error Handling]
    A --> F[Reporting & Analytics]
    
    %% Workflow Creation
    B --> B1[Create WorkflowExecution Record]
    B1 --> B2[Set Initial Parameters]
    B2 --> B3[Link to Campaign]
    
    B2 --> B2a[Set Workflow Type]
    B2 --> B2b[Set Workflow Path]
    B2 --> B2c[Set Status to 'pending']
    
    %% Workflow Execution
    C --> C1[Update Status to 'running']
    C1 --> C2[Record Start Time]
    C2 --> C3[Execute PyFlow Workflow]
    C3 --> C4[Track Items Processed]
    
    C4 --> C4a[Update processed_items]
    C4 --> C4b[Update successful_items]
    C4 --> C4c[Update failed_items]
    C4 --> C4d[Calculate Progress Percentage]
    
    %% Progress Monitoring
    D --> D1[Real-time Progress Updates]
    D1 --> D2[Create WorkflowLog Entries]
    D1 --> D3[Update Campaign active_workflows]
    
    D2 --> D2a[Log Important Events]
    D2 --> D2b[Log Performance Metrics]
    
    D3 --> D3a[Track Multiple Concurrent Workflows]
    D3 --> D3b[Update Workflow Statistics]
    
    %% Error Handling
    E --> E1[Detect Workflow Errors]
    E1 --> E2[Create WorkflowError Record]
    E2 --> E3[Implement Retry Logic]
    
    E3 --> E3a[Increment retry_count]
    E3 --> E3b[Record last_retry]
    E3 --> E3c{Max Retries?}
    
    E3c -->|Yes| E4[Mark as 'failed']
    E3c -->|No| E5[Retry Workflow]
    E5 --> C3
    
    E4 --> E4a[Record Error Details]
    E4 --> E4b[Update Campaign Status]
    E4 --> E4c[Send Failure Notification]
    
    %% Workflow Completion
    C3 --> C5{Successful?}
    C5 -->|Yes| C6[Update Status to 'completed']
    C5 -->|No| E1
    
    C6 --> C6a[Record End Time]
    C6a --> C6b[Calculate Duration]
    C6b --> C6c[Update Results JSON]
    C6c --> C6d[Move from active_workflows to completed_workflows]
    
    %% Reporting & Analytics
    F --> F1[Generate Workflow Metrics]
    F1 --> F2[Update Campaign Metrics]
    F1 --> F3[Create Performance Dashboards]
    
    F2 --> F2a[Track Success Rates]
    F2 --> F2b[Track Processing Times]
    F2 --> F2c[Track Resource Usage]
    
    F3 --> F3a[Workflow Performance Trends]
    F3 --> F3b[Error Rate Analysis]
    F3 --> F3c[Optimization Opportunities]
    
    %% Integration with Campaign
    G[Campaign Model] --> G1[active_workflows JSON]
    G --> G2[completed_workflows JSON]
    G --> G3[workflow_statistics JSON]
    
    G1 --> D3
    G2 --> C6d
    G3 --> F2
    
    %% Integration with PyFlow
    H[PyFlow Workflow] --> H1[Progress Reporting]
    H1 --> C4
    H --> H2[Error Reporting]
    H2 --> E1
    H --> H3[Result Reporting]
    H3 --> C6c
    
    %% Data Models
    I[Workflow Data Models] --> I1[WorkflowExecution]
    I --> I2[WorkflowLog]
    I --> I3[WorkflowError]
    I --> I4[WorkflowMetrics]
    
    %% Legend
    classDef overview fill:#d4f1f9,stroke:#333,stroke-width:1px;
    classDef creation fill:#e1d5e7,stroke:#333,stroke-width:1px;
    classDef execution fill:#d5e8d4,stroke:#333,stroke-width:1px;
    classDef monitoring fill:#fff2cc,stroke:#333,stroke-width:1px;
    classDef errorHandling fill:#f8cecc,stroke:#333,stroke-width:1px;
    classDef reporting fill:#ffe6cc,stroke:#333,stroke-width:1px;
    classDef integration fill:#d0cee2,stroke:#333,stroke-width:1px;
    classDef dataModel fill:#dae8fc,stroke:#333,stroke-width:1px;
    
    class A overview;
    class B,B1,B2,B2a,B2b,B2c,B3 creation;
    class C,C1,C2,C3,C4,C4a,C4b,C4c,C4d,C5,C6,C6a,C6b,C6c,C6d execution;
    class D,D1,D2,D2a,D2b,D3,D3a,D3b monitoring;
    class E,E1,E2,E3,E3a,E3b,E3c,E4,E4a,E4b,E4c,E5 errorHandling;
    class F,F1,F2,F2a,F2b,F2c,F3,F3a,F3b,F3c reporting;
    class G,G1,G2,G3,H,H1,H2,H3 integration;
    class I,I1,I2,I3,I4 dataModel;
