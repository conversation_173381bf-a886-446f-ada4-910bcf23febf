graph TD
    %% Tag System Overview
    A[Tag Management System] --> B[Tag Creation]
    A --> C[Tag Organization]
    A --> D[Tag Application]
    A --> E[Tag Analysis]
    
    %% Tag Creation
    B --> B1[Create Individual Tag]
    B --> B2[Bulk Tag Creation]
    B --> B3[Tag Builder Interface]
    
    B1 --> B1a[Define Tag Name]
    B1 --> B1b[Select Tag Type]
    B1 --> B1c[Define Pattern]
    B1 --> B1d[Select Field]
    B1 --> B1e[Set Confidence Level]
    B1 --> B1f[Set Global Flag]
    
    B3 --> B3a[Pattern Testing]
    B3 --> B3b[Preview Matches]
    
    %% Tag Organization
    C --> C1[Create Tag Categories]
    C --> C2[Create Tag Groups]
    C --> C3[Define Related Tags]
    
    C1 --> C1a[Set Category Name]
    C1 --> C1b[Set Priority]
    C1 --> C1c[Set Color]
    
    C2 --> C2a[Set Group Name]
    C2 --> C2b[Add Tags to Group]
    C2 --> C2c[Set Public Flag]
    
    C3 --> C3a[Link Related Tags]
    C3 --> C3b[Define Relationship Strength]
    
    %% Tag Application
    D --> D1[Assign Tags to Campaign]
    D --> D2[Assign Tag Groups to Campaign]
    D --> D3[Define Tag Rules]
    
    D1 --> D1a[Select from Global Tags]
    D1 --> D1b[Select from Custom Tags]
    
    D3 --> D3a[Define Rule Conditions]
    D3 --> D3b[Set Rule Priority]
    D3 --> D3c[Set Rule Weight]
    
    %% Tag Analysis
    E --> E1[Auto-Tagging System]
    E --> E2[Manual Tag Review]
    E --> E3[Tag Performance Metrics]
    
    E1 --> E1a[Pattern Matching]
    E1 --> E1b[NLP Processing]
    E1 --> E1c[Machine Learning]
    
    E2 --> E2a[Review Tag Matches]
    E2 --> E2b[Adjust Confidence Scores]
    
    E3 --> E3a[Tag Usage Statistics]
    E3 --> E3b[Tag Accuracy Metrics]
    E3 --> E3c[Tag ROI Analysis]
    
    %% Tag Application in Workflow
    F[Campaign Analysis Phase] --> F1[Fetch Account Data]
    F1 --> F2[Apply Tag Rules]
    F2 --> F3[Calculate Tag Matches]
    F3 --> F4[Store Tag Analysis Results]
    F4 --> F5[Generate Tag-based Whitelist]
    
    %% Tag Optimization
    G[Tag System Optimization] --> G1[Cache Frequently Used Tags]
    G --> G2[Batch Processing]
    G --> G3[Parallel Tag Processing]
    G --> G4[Tag Index Optimization]
    
    %% Integration with Instagram
    H[Instagram Integration] --> H1[Extract Profile Data]
    H1 --> H2[Extract Bio Keywords]
    H1 --> H3[Extract Interests]
    H1 --> H4[Extract Location Data]
    
    H2 --> F2
    H3 --> F2
    H4 --> F2
    
    %% Tag Data Model
    I[Tag Data Models] --> I1[DynamicTag Model]
    I --> I2[TagGroup Model]
    I --> I3[TagCategory Model]
    I --> I4[TagAnalysisResult Model]
    
    %% Legend
    classDef userInterface fill:#d4f1f9,stroke:#333,stroke-width:1px;
    classDef dataModel fill:#e1d5e7,stroke:#333,stroke-width:1px;
    classDef process fill:#d5e8d4,stroke:#333,stroke-width:1px;
    classDef optimization fill:#fff2cc,stroke:#333,stroke-width:1px;
    classDef integration fill:#ffe6cc,stroke:#333,stroke-width:1px;
    
    class A,B,C,D,E userInterface;
    class B1,B2,B3,B1a,B1b,B1c,B1d,B1e,B1f,B3a,B3b,C1,C2,C3,C1a,C1b,C1c,C2a,C2b,C2c,C3a,C3b,D1,D2,D3,D1a,D1b,D3a,D3b,D3c,E2a,E2b userInterface;
    class I,I1,I2,I3,I4 dataModel;
    class F,F1,F2,F3,F4,F5,E1,E1a,E1b,E1c,E3,E3a,E3b,E3c process;
    class G,G1,G2,G3,G4 optimization;
    class H,H1,H2,H3,H4 integration;
