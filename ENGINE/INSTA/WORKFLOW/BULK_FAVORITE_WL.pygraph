{"name": "root", "category": "", "vars": [], "nodes": [{"package": "PyFlowBase", "lib": null, "type": "graphInputs", "owningGraphName": "root", "name": "graphInputs", "uuid": "e78c5696-6ca3-4855-bc03-5bf0f884affd", "inputs": [], "outputs": [{"name": "in1", "package": "PyFlowBase", "fullName": "graphInputs_in1", "dataType": "StringPin", "direction": 1, "value": "\"\"", "uuid": "8d9e6180-219f-40e9-bb9a-492c55985eb1", "linkedTo": [{"lhsNodeName": "graphInputs", "outPinId": 2, "rhsNodeName": "TimestampNode77", "inPinId": 1, "lhsNodeUid": "e78c5696-6ca3-4855-bc03-5bf0f884affd", "rhsNodeUid": "d4cfa6f6-ebb9-4af8-a3e0-4c6dfa35d241"}], "pinIndex": 2, "options": [32, 64, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "in1", "wires": {"1": {"sourceUUID": "8d9e6180-219f-40e9-bb9a-492c55985eb1", "destinationUUID": "96b9a2ff-ef15-4495-8d6d-8e4af48112ec", "sourceName": "graphInputs_in1", "destinationName": "TimestampNode77_offset", "uuid": "e0be959e-0cb0-4f3d-a1e9-8fa2f996057f", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "in", "package": "PyFlowBase", "fullName": "graphInputs_in", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "9c361ee0-47c9-48bb-9c5d-47908aaa82c3", "linkedTo": [{"lhsNodeName": "graphInputs", "outPinId": 1, "rhsNodeName": "TimestampNode77", "inPinId": 3, "lhsNodeUid": "e78c5696-6ca3-4855-bc03-5bf0f884affd", "rhsNodeUid": "d4cfa6f6-ebb9-4af8-a3e0-4c6dfa35d241"}], "pinIndex": 1, "options": [32, 64, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "in", "wires": {"3": {"sourceUUID": "9c361ee0-47c9-48bb-9c5d-47908aaa82c3", "destinationUUID": "2ee0f787-9100-44c9-9d4a-5c6eb153361b", "sourceName": "graphInputs_in", "destinationName": "TimestampNode77_Execute", "uuid": "46cb7a3c-75a3-4243-b16e-45d85fd2af64", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "in2", "package": "PyFlowBase", "fullName": "graphInputs_in2", "dataType": "IntPin", "direction": 1, "value": "0", "uuid": "8a36c521-9c1b-45ea-87cb-a698ff7683b4", "linkedTo": [{"lhsNodeName": "graphInputs", "outPinId": 3, "rhsNodeName": "makeInt", "inPinId": 1, "lhsNodeUid": "e78c5696-6ca3-4855-bc03-5bf0f884affd", "rhsNodeUid": "0283d726-6434-4d1e-8bae-d99fc5578ba8"}], "pinIndex": 3, "options": [32, 64, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "in2", "wires": {"1": {"sourceUUID": "8a36c521-9c1b-45ea-87cb-a698ff7683b4", "destinationUUID": "0d4de256-e3ab-4aeb-9e15-ec2bbff4e25b", "sourceName": "graphInputs_in2", "destinationName": "makeInt_i", "uuid": "daeb0c4b-e364-4749-9f34-b0fd149b7cf7", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "graphInputs"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">graphInputs</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -5346.37892332936, "y": -1898.************}, {"package": "Automator", "lib": null, "type": "TimeScaleDBNode", "owningGraphName": "root", "name": "TimeScaleDBNode82", "uuid": "2437e69a-7067-45c7-b9f6-01684530eb46", "inputs": [{"name": "limit", "package": "PyFlowBase", "fullName": "TimeScaleDBNode82_limit", "dataType": "IntPin", "direction": 0, "value": "1", "uuid": "33883a4b-bb88-41a8-8b1e-a0c35e9e78e6", "linkedTo": [], "pinIndex": 6, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "limit", "wires": {}}}, {"name": "fields", "package": "PyFlowBase", "fullName": "TimeScaleDBNode82_fields", "dataType": "StringPin", "direction": 0, "value": "\"[\\\"username\\\"]\"", "uuid": "194a20cd-213d-4237-814c-af06532d6447", "linkedTo": [], "pinIndex": 8, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "fields", "wires": {}}}, {"name": "data", "package": "PyFlowBase", "fullName": "TimeScaleDBNode82_data", "dataType": "StringPin", "direction": 0, "value": "\"{ \\\"favorite\\\":false}\\n\"", "uuid": "00b2bfca-685b-44b3-9cd1-e5464e098322", "linkedTo": [], "pinIndex": 5, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "data", "wires": {}}}, {"name": "query", "package": "PyFlowBase", "fullName": "TimeScaleDBNode82_query", "dataType": "StringPin", "direction": 0, "value": "\"{\\\"timestamp\\\":{\\\"$lt\\\": \\\"2025-01-30T13:39:51.100350Z\\\"},\\\"discover\\\":false, \\\"post_like\\\":false,\\\"comment\\\":false, \\\"dm\\\":false,\\\"follow\\\":false, \\\"favorite\\\":false}\\n\"", "uuid": "edd28a5e-379b-48f1-be9e-d2979d7d9890", "linkedTo": [{"lhsNodeName": "ConcatenateStringsNode84", "outPinId": 2, "rhsNodeName": "TimeScaleDBNode82", "inPinId": 4, "lhsNodeUid": "7ccfc68b-a9e0-4b8d-b1f5-308e025a9bae", "rhsNodeUid": "2437e69a-7067-45c7-b9f6-01684530eb46"}], "pinIndex": 4, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "query", "wires": {"4": {"sourceUUID": "b8fe530c-a2e5-4de5-94a6-0c4a58ffdbf8", "destinationUUID": "edd28a5e-379b-48f1-be9e-d2979d7d9890", "sourceName": "ConcatenateStringsNode84_result", "destinationName": "TimeScaleDBNode82_query", "uuid": "0e6c49e3-301e-46e4-8aad-16ea53872055", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "operation", "package": "PyFlowBase", "fullName": "TimeScaleDBNode82_operation", "dataType": "StringPin", "direction": 0, "value": "\"read\"", "uuid": "c81ff506-b01b-4de1-979e-8ddb5ca55426", "linkedTo": [], "pinIndex": 3, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "operation", "wires": {}}}, {"name": "table", "package": "PyFlowBase", "fullName": "TimeScaleDBNode82_table", "dataType": "StringPin", "direction": 0, "value": "\"instagram_whitelistentry\"", "uuid": "1fd0a913-63e4-4051-a05c-fc865befbf38", "linkedTo": [], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "table", "wires": {}}}, {"name": "Execute", "package": "PyFlowBase", "fullName": "TimeScaleDBNode82_Execute", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "44590be5-c543-4eeb-9d11-c185efd2e116", "linkedTo": [{"lhsNodeName": "ConcatenateStringsNode84", "outPinId": 1, "rhsNodeName": "TimeScaleDBNode82", "inPinId": 9, "lhsNodeUid": "7ccfc68b-a9e0-4b8d-b1f5-308e025a9bae", "rhsNodeUid": "2437e69a-7067-45c7-b9f6-01684530eb46"}], "pinIndex": 9, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Execute", "wires": {"9": {"sourceUUID": "cd61ead4-72f2-45cb-bafd-454611641bf5", "destinationUUID": "44590be5-c543-4eeb-9d11-c185efd2e116", "sourceName": "ConcatenateStringsNode84_out", "destinationName": "TimeScaleDBNode82_Execute", "uuid": "5e7397c0-4fdf-437f-9dd8-fe300dabbb41", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "connection_string", "package": "PyFlowBase", "fullName": "TimeScaleDBNode82_connection_string", "dataType": "StringPin", "direction": 0, "value": "\"host=localhost port=5432 dbname=test_commandnet user=admin password=51x3\"", "uuid": "83b3ff96-5e01-4538-a4c2-d7209b0fc33a", "linkedTo": [], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "connection_string", "wires": {}}}, {"name": "sort", "package": "PyFlowBase", "fullName": "TimeScaleDBNode82_sort", "dataType": "StringPin", "direction": 0, "value": "\"[]\"", "uuid": "d7bd53c2-65d5-41c1-8835-426450434c7e", "linkedTo": [], "pinIndex": 7, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "sort", "wires": {}}}], "outputs": [{"name": "Failed", "package": "PyFlowBase", "fullName": "TimeScaleDBNode82_Failed", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "633df72d-4737-45df-8dd1-c0e7731536b6", "linkedTo": [{"lhsNodeName": "TimeScaleDBNode82", "outPinId": 3, "rhsNodeName": "cliexit", "inPinId": 1, "lhsNodeUid": "2437e69a-7067-45c7-b9f6-01684530eb46", "rhsNodeUid": "7dd020be-9d70-4594-a994-158d70242eab"}], "pinIndex": 3, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Failed", "wires": {"1": {"sourceUUID": "633df72d-4737-45df-8dd1-c0e7731536b6", "destinationUUID": "e3da099e-911b-4cb8-a1e3-eb8a84ff1ffa", "sourceName": "TimeScaleDBNode82_Failed", "destinationName": "cliexit_inExec", "uuid": "f6a2e21f-6ed7-4072-b62b-3cd07d09b0fc", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "Completed", "package": "PyFlowBase", "fullName": "TimeScaleDBNode82_Completed", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "cdee7998-267b-4258-a428-ad630eed32f2", "linkedTo": [{"lhsNodeName": "TimeScaleDBNode82", "outPinId": 2, "rhsNodeName": "CompareStringsNode", "inPinId": 3, "lhsNodeUid": "2437e69a-7067-45c7-b9f6-01684530eb46", "rhsNodeUid": "d7aaf045-6ea2-45d3-a757-79d20301f4be"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Completed", "wires": {"3": {"sourceUUID": "cdee7998-267b-4258-a428-ad630eed32f2", "destinationUUID": "fdc0c5df-bb56-417c-95be-3255731872f2", "sourceName": "TimeScaleDBNode82_Completed", "destinationName": "CompareStringsNode_Execute", "uuid": "df3780c2-3e6d-4d54-8c09-d1b644669eec", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "result", "package": "PyFlowBase", "fullName": "TimeScaleDBNode82_result", "dataType": "StringPin", "direction": 1, "value": "\"[{\\\"username\\\": \\\"lucrez<PERSON>_bar<PERSON><PERSON>i\\\"}]\"", "uuid": "3da04181-1a58-4539-8c25-5a468f2c7d68", "linkedTo": [{"lhsNodeName": "TimeScaleDBNode82", "outPinId": 1, "rhsNodeName": "CompareStringsNode", "inPinId": 2, "lhsNodeUid": "2437e69a-7067-45c7-b9f6-01684530eb46", "rhsNodeUid": "d7aaf045-6ea2-45d3-a757-79d20301f4be"}, {"lhsNodeName": "TimeScaleDBNode82", "outPinId": 1, "rhsNodeName": "StringSplitterNode", "inPinId": 3, "lhsNodeUid": "2437e69a-7067-45c7-b9f6-01684530eb46", "rhsNodeUid": "8da62c32-daaa-4a7d-9b82-6917827f6763"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "result", "wires": {"2": {"sourceUUID": "3da04181-1a58-4539-8c25-5a468f2c7d68", "destinationUUID": "57841392-fb23-4547-a8b0-82fa36ba5689", "sourceName": "TimeScaleDBNode82_result", "destinationName": "CompareStringsNode_String B", "uuid": "e5be17a1-32fa-46fa-a68d-a2d055d8856b", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}, "3": {"sourceUUID": "3da04181-1a58-4539-8c25-5a468f2c7d68", "destinationUUID": "0186819f-f966-4ef6-94f3-38cf8d8473e6", "sourceName": "TimeScaleDBNode82_result", "destinationName": "StringSplitterNode_InputString", "uuid": "01d9e40f-6bc2-44e5-a63c-1272407e0930", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "TimeScaleDBNode82"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">TimeScaleDBNode82</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -4689.************, "y": -1937.************}, {"package": "PyFlowBase", "lib": null, "type": "ConcatenateStringsNode", "owningGraphName": "root", "name": "ConcatenateStringsNode84", "uuid": "7ccfc68b-a9e0-4b8d-b1f5-308e025a9bae", "inputs": [{"name": "in", "package": "PyFlowBase", "fullName": "ConcatenateStringsNode84_in", "dataType": "StringPin", "direction": 0, "value": "\"2025-01-30T13:39:51.100350Z\"", "uuid": "437e03ed-c4f4-4c7b-81a7-217bd6839edd", "linkedTo": [{"lhsNodeName": "TimestampNode77", "outPinId": 2, "rhsNodeName": "ConcatenateStringsNode84", "inPinId": 3, "lhsNodeUid": "d4cfa6f6-ebb9-4af8-a3e0-4c6dfa35d241", "rhsNodeUid": "7ccfc68b-a9e0-4b8d-b1f5-308e025a9bae"}], "pinIndex": 3, "options": [32, 64, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "in", "wires": {"3": {"sourceUUID": "94a39991-7fc6-416b-8a66-b84a3368aae4", "destinationUUID": "437e03ed-c4f4-4c7b-81a7-217bd6839edd", "sourceName": "TimestampNode77_offsetTimestamp", "destinationName": "ConcatenateStringsNode84_in", "uuid": "3877d6df-faa8-4fd1-85f3-ed24e8819822", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "in1", "package": "PyFlowBase", "fullName": "ConcatenateStringsNode84_in1", "dataType": "StringPin", "direction": 0, "value": "\"{\\\"timestamp\\\":{\\\"$lt\\\": \\\"\"", "uuid": "03e4645e-734e-4eeb-b717-16abcc4a9c90", "linkedTo": [{"lhsNodeName": "makeString85", "outPinId": 1, "rhsNodeName": "ConcatenateStringsNode84", "inPinId": 2, "lhsNodeUid": "7ff5169d-ea1c-43c6-8210-040028f9836b", "rhsNodeUid": "7ccfc68b-a9e0-4b8d-b1f5-308e025a9bae"}], "pinIndex": 2, "options": [32, 64, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "in1", "wires": {"2": {"sourceUUID": "3ee2d493-7a29-479e-aa78-58a1732e28b1", "destinationUUID": "03e4645e-734e-4eeb-b717-16abcc4a9c90", "sourceName": "makeString85_out", "destinationName": "ConcatenateStringsNode84_in1", "uuid": "58e7c67d-baab-4d62-8031-17617534ee2f", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "inExec", "package": "PyFlowBase", "fullName": "ConcatenateStringsNode84_inExec", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "ece69f71-95aa-4f46-a549-097d44da309c", "linkedTo": [{"lhsNodeName": "TimestampNode77", "outPinId": 3, "rhsNodeName": "ConcatenateStringsNode84", "inPinId": 1, "lhsNodeUid": "d4cfa6f6-ebb9-4af8-a3e0-4c6dfa35d241", "rhsNodeUid": "7ccfc68b-a9e0-4b8d-b1f5-308e025a9bae"}], "pinIndex": 1, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": true, "displayName": "inExec", "wires": {"1": {"sourceUUID": "3c6f73d2-a947-4a54-b1db-ea6173c50dd2", "destinationUUID": "ece69f71-95aa-4f46-a549-097d44da309c", "sourceName": "TimestampNode77_Completed", "destinationName": "ConcatenateStringsNode84_inExec", "uuid": "a3b2406b-c709-4919-81ba-9851f510750d", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "in2", "package": "PyFlowBase", "fullName": "ConcatenateStringsNode84_in2", "dataType": "StringPin", "direction": 0, "value": "\"\\\"},\\\"discover\\\":false, \\\"post_like\\\":false,\\\"comment\\\":false, \\\"dm\\\":false,\\\"follow\\\":false, \\\"favorite\\\":false}\\n\"", "uuid": "643b7e23-47df-4883-a03b-0ce232ebc2cd", "linkedTo": [{"lhsNodeName": "makeString87", "outPinId": 1, "rhsNodeName": "ConcatenateStringsNode84", "inPinId": 4, "lhsNodeUid": "39868be7-c62d-4214-903a-4c2d28e37248", "rhsNodeUid": "7ccfc68b-a9e0-4b8d-b1f5-308e025a9bae"}], "pinIndex": 4, "options": [32, 64, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "in2", "wires": {"4": {"sourceUUID": "807ea64c-532f-470e-aeb8-5ae39e95259d", "destinationUUID": "643b7e23-47df-4883-a03b-0ce232ebc2cd", "sourceName": "makeString87_out", "destinationName": "ConcatenateStringsNode84_in2", "uuid": "52393370-6d8b-4245-b105-d55daea5199f", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "outputs": [{"name": "result", "package": "PyFlowBase", "fullName": "ConcatenateStringsNode84_result", "dataType": "StringPin", "direction": 1, "value": "\"{\\\"timestamp\\\":{\\\"$lt\\\": \\\"2025-01-30T13:39:51.100350Z\\\"},\\\"discover\\\":false, \\\"post_like\\\":false,\\\"comment\\\":false, \\\"dm\\\":false,\\\"follow\\\":false, \\\"favorite\\\":false}\\n\"", "uuid": "b8fe530c-a2e5-4de5-94a6-0c4a58ffdbf8", "linkedTo": [{"lhsNodeName": "ConcatenateStringsNode84", "outPinId": 2, "rhsNodeName": "TimeScaleDBNode82", "inPinId": 4, "lhsNodeUid": "7ccfc68b-a9e0-4b8d-b1f5-308e025a9bae", "rhsNodeUid": "2437e69a-7067-45c7-b9f6-01684530eb46"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "result", "wires": {"4": {"sourceUUID": "b8fe530c-a2e5-4de5-94a6-0c4a58ffdbf8", "destinationUUID": "edd28a5e-379b-48f1-be9e-d2979d7d9890", "sourceName": "ConcatenateStringsNode84_result", "destinationName": "TimeScaleDBNode82_query", "uuid": "0e6c49e3-301e-46e4-8aad-16ea53872055", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "out", "package": "PyFlowBase", "fullName": "ConcatenateStringsNode84_out", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "cd61ead4-72f2-45cb-bafd-454611641bf5", "linkedTo": [{"lhsNodeName": "ConcatenateStringsNode84", "outPinId": 1, "rhsNodeName": "TimeScaleDBNode82", "inPinId": 9, "lhsNodeUid": "7ccfc68b-a9e0-4b8d-b1f5-308e025a9bae", "rhsNodeUid": "2437e69a-7067-45c7-b9f6-01684530eb46"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "out", "wires": {"9": {"sourceUUID": "cd61ead4-72f2-45cb-bafd-454611641bf5", "destinationUUID": "44590be5-c543-4eeb-9d11-c185efd2e116", "sourceName": "ConcatenateStringsNode84_out", "destinationName": "TimeScaleDBNode82_Execute", "uuid": "5e7397c0-4fdf-437f-9dd8-fe300dabbb41", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "ConcatenateStringsNode84"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">ConcatenateStringsNode84</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -4960.************, "y": -2029.6612136859285}, {"package": "PyFlowBase", "lib": "DefaultLib", "type": "makeString", "owningGraphName": "root", "name": "makeString85", "uuid": "7ff5169d-ea1c-43c6-8210-040028f9836b", "inputs": [{"name": "s", "package": "PyFlowBase", "fullName": "makeString85_s", "dataType": "StringPin", "direction": 0, "value": "\"{\\\"timestamp\\\":{\\\"$lt\\\": \\\"\"", "uuid": "a8b9492a-6ce1-49f2-b841-86d738d8bd12", "linkedTo": [], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "s", "wires": {}}}], "outputs": [{"name": "out", "package": "PyFlowBase", "fullName": "makeString85_out", "dataType": "StringPin", "direction": 1, "value": "\"{\\\"timestamp\\\":{\\\"$lt\\\": \\\"\"", "uuid": "3ee2d493-7a29-479e-aa78-58a1732e28b1", "linkedTo": [{"lhsNodeName": "makeString85", "outPinId": 1, "rhsNodeName": "ConcatenateStringsNode84", "inPinId": 2, "lhsNodeUid": "7ff5169d-ea1c-43c6-8210-040028f9836b", "rhsNodeUid": "7ccfc68b-a9e0-4b8d-b1f5-308e025a9bae"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "out", "wires": {"2": {"sourceUUID": "3ee2d493-7a29-479e-aa78-58a1732e28b1", "destinationUUID": "03e4645e-734e-4eeb-b717-16abcc4a9c90", "sourceName": "makeString85_out", "destinationName": "ConcatenateStringsNode84_in1", "uuid": "58e7c67d-baab-4d62-8031-17617534ee2f", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "makeString85"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">makeString85</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -5149.************, "y": -2107.************}, {"package": "PyFlowBase", "lib": "DefaultLib", "type": "makeString", "owningGraphName": "root", "name": "makeString87", "uuid": "39868be7-c62d-4214-903a-4c2d28e37248", "inputs": [{"name": "s", "package": "PyFlowBase", "fullName": "makeString87_s", "dataType": "StringPin", "direction": 0, "value": "\"\\\"},\\\"discover\\\":false, \\\"post_like\\\":false,\\\"comment\\\":false, \\\"dm\\\":false,\\\"follow\\\":false, \\\"favorite\\\":false}\\n\"", "uuid": "b67ccb2a-fac2-451f-b552-2aceb22e42b3", "linkedTo": [], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "s", "wires": {}}}], "outputs": [{"name": "out", "package": "PyFlowBase", "fullName": "makeString87_out", "dataType": "StringPin", "direction": 1, "value": "\"\\\"},\\\"discover\\\":false, \\\"post_like\\\":false,\\\"comment\\\":false, \\\"dm\\\":false,\\\"follow\\\":false, \\\"favorite\\\":false}\\n\"", "uuid": "807ea64c-532f-470e-aeb8-5ae39e95259d", "linkedTo": [{"lhsNodeName": "makeString87", "outPinId": 1, "rhsNodeName": "ConcatenateStringsNode84", "inPinId": 4, "lhsNodeUid": "39868be7-c62d-4214-903a-4c2d28e37248", "rhsNodeUid": "7ccfc68b-a9e0-4b8d-b1f5-308e025a9bae"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "out", "wires": {"4": {"sourceUUID": "807ea64c-532f-470e-aeb8-5ae39e95259d", "destinationUUID": "643b7e23-47df-4883-a03b-0ce232ebc2cd", "sourceName": "makeString87_out", "destinationName": "ConcatenateStringsNode84_in2", "uuid": "52393370-6d8b-4245-b105-d55daea5199f", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "makeString87"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">makeString87</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -5146.************, "y": -2023.7626267475907}, {"package": "Automator", "lib": null, "type": "TimestampNode", "owningGraphName": "root", "name": "TimestampNode77", "uuid": "d4cfa6f6-ebb9-4af8-a3e0-4c6dfa35d241", "inputs": [{"name": "timezone", "package": "PyFlowBase", "fullName": "TimestampNode77_timezone", "dataType": "StringPin", "direction": 0, "value": "\"Asia/Beirut\"", "uuid": "30c11981-60a0-414a-b3f0-88b5bc1d881d", "linkedTo": [], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "timezone", "wires": {}}}, {"name": "offset", "package": "PyFlowBase", "fullName": "TimestampNode77_offset", "dataType": "StringPin", "direction": 0, "value": "\"\"", "uuid": "96b9a2ff-ef15-4495-8d6d-8e4af48112ec", "linkedTo": [{"lhsNodeName": "graphInputs", "outPinId": 2, "rhsNodeName": "TimestampNode77", "inPinId": 1, "lhsNodeUid": "e78c5696-6ca3-4855-bc03-5bf0f884affd", "rhsNodeUid": "d4cfa6f6-ebb9-4af8-a3e0-4c6dfa35d241"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "offset", "wires": {"1": {"sourceUUID": "8d9e6180-219f-40e9-bb9a-492c55985eb1", "destinationUUID": "96b9a2ff-ef15-4495-8d6d-8e4af48112ec", "sourceName": "graphInputs_in1", "destinationName": "TimestampNode77_offset", "uuid": "e0be959e-0cb0-4f3d-a1e9-8fa2f996057f", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "Execute", "package": "PyFlowBase", "fullName": "TimestampNode77_Execute", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "2ee0f787-9100-44c9-9d4a-5c6eb153361b", "linkedTo": [{"lhsNodeName": "graphInputs", "outPinId": 1, "rhsNodeName": "TimestampNode77", "inPinId": 3, "lhsNodeUid": "e78c5696-6ca3-4855-bc03-5bf0f884affd", "rhsNodeUid": "d4cfa6f6-ebb9-4af8-a3e0-4c6dfa35d241"}, {"lhsNodeName": "delay", "outPinId": 1, "rhsNodeName": "TimestampNode77", "inPinId": 3, "lhsNodeUid": "e0bc091b-54cc-4447-b1a3-6baf8de079b9", "rhsNodeUid": "d4cfa6f6-ebb9-4af8-a3e0-4c6dfa35d241"}], "pinIndex": 3, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Execute", "wires": {"3": {"sourceUUID": "f5a0afac-2309-476e-879c-804a41990bf3", "destinationUUID": "2ee0f787-9100-44c9-9d4a-5c6eb153361b", "sourceName": "delay_outExec", "destinationName": "TimestampNode77_Execute", "uuid": "ba4c71af-86ba-44eb-83fc-4a44ce19dc18", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "outputs": [{"name": "offsetTimestamp", "package": "PyFlowBase", "fullName": "TimestampNode77_offsetTimestamp", "dataType": "StringPin", "direction": 1, "value": "\"2025-01-30T13:39:51.100350Z\"", "uuid": "94a39991-7fc6-416b-8a66-b84a3368aae4", "linkedTo": [{"lhsNodeName": "TimestampNode77", "outPinId": 2, "rhsNodeName": "ConcatenateStringsNode84", "inPinId": 3, "lhsNodeUid": "d4cfa6f6-ebb9-4af8-a3e0-4c6dfa35d241", "rhsNodeUid": "7ccfc68b-a9e0-4b8d-b1f5-308e025a9bae"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "offsetTimestamp", "wires": {"3": {"sourceUUID": "94a39991-7fc6-416b-8a66-b84a3368aae4", "destinationUUID": "437e03ed-c4f4-4c7b-81a7-217bd6839edd", "sourceName": "TimestampNode77_offsetTimestamp", "destinationName": "ConcatenateStringsNode84_in", "uuid": "3877d6df-faa8-4fd1-85f3-ed24e8819822", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "Completed", "package": "PyFlowBase", "fullName": "TimestampNode77_Completed", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "3c6f73d2-a947-4a54-b1db-ea6173c50dd2", "linkedTo": [{"lhsNodeName": "TimestampNode77", "outPinId": 3, "rhsNodeName": "ConcatenateStringsNode84", "inPinId": 1, "lhsNodeUid": "d4cfa6f6-ebb9-4af8-a3e0-4c6dfa35d241", "rhsNodeUid": "7ccfc68b-a9e0-4b8d-b1f5-308e025a9bae"}], "pinIndex": 3, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Completed", "wires": {"1": {"sourceUUID": "3c6f73d2-a947-4a54-b1db-ea6173c50dd2", "destinationUUID": "ece69f71-95aa-4f46-a549-097d44da309c", "sourceName": "TimestampNode77_Completed", "destinationName": "ConcatenateStringsNode84_inExec", "uuid": "a3b2406b-c709-4919-81ba-9851f510750d", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "timestamp", "package": "PyFlowBase", "fullName": "TimestampNode77_timestamp", "dataType": "StringPin", "direction": 1, "value": "\"2025-01-30T13:39:51.100350Z\"", "uuid": "68a7d982-99b7-4398-8df6-71b6471dbf96", "linkedTo": [], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "timestamp", "wires": {}}}], "meta": {"var": {}, "label": "TimestampNode77"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">TimestampNode77</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -5212.************, "y": -1936.************}, {"package": "PyFlowBase", "lib": null, "type": "cliexit", "owningGraphName": "root", "name": "cliexit", "uuid": "7dd020be-9d70-4594-a994-158d70242eab", "inputs": [{"name": "inExec", "package": "PyFlowBase", "fullName": "cliexit_inExec", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "e3da099e-911b-4cb8-a1e3-eb8a84ff1ffa", "linkedTo": [{"lhsNodeName": "TimeScaleDBNode82", "outPinId": 3, "rhsNodeName": "cliexit", "inPinId": 1, "lhsNodeUid": "2437e69a-7067-45c7-b9f6-01684530eb46", "rhsNodeUid": "7dd020be-9d70-4594-a994-158d70242eab"}, {"lhsNodeName": "branch", "outPinId": 1, "rhsNodeName": "cliexit", "inPinId": 1, "lhsNodeUid": "08fd13d4-d878-4116-8044-21cb4153b614", "rhsNodeUid": "7dd020be-9d70-4594-a994-158d70242eab"}, {"lhsNodeName": "sequence8", "outPinId": 2, "rhsNodeName": "cliexit", "inPinId": 1, "lhsNodeUid": "f05f116e-3e1d-4421-893e-00b45f4fd4b4", "rhsNodeUid": "7dd020be-9d70-4594-a994-158d70242eab"}], "pinIndex": 1, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "inExec", "wires": {"1": {"sourceUUID": "b185b66d-eb67-472f-8256-b51642e5dd5a", "destinationUUID": "e3da099e-911b-4cb8-a1e3-eb8a84ff1ffa", "sourceName": "sequence8_2", "destinationName": "cliexit_inExec", "uuid": "71d498a3-13ad-4970-b9e1-1563c66fad05", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "outputs": [], "meta": {"var": {}, "label": "cliexit"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">cliexit</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -3873.************, "y": -1577.2775912910042}, {"package": "Automator", "lib": null, "type": "TimeScaleDBNode", "owningGraphName": "root", "name": "TimeScaleDBNode3", "uuid": "358dda32-bc2e-4028-9a88-3d44acd549d4", "inputs": [{"name": "data", "package": "PyFlowBase", "fullName": "TimeScaleDBNode3_data", "dataType": "StringPin", "direction": 0, "value": "\"{ \\\"favorite\\\":true}\\n\"", "uuid": "3b9696eb-5255-431a-bf63-44617bb2876b", "linkedTo": [], "pinIndex": 5, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "data", "wires": {}}}, {"name": "query", "package": "PyFlowBase", "fullName": "TimeScaleDBNode3_query", "dataType": "StringPin", "direction": 0, "value": "\"{\\\"username\\\": \\\"lucrez<PERSON>_bar<PERSON><PERSON>i\\\"}\"", "uuid": "b105a86a-595b-4add-ae38-010481ec55ef", "linkedTo": [{"lhsNodeName": "StringSplitterNode1", "outPinId": 2, "rhsNodeName": "TimeScaleDBNode3", "inPinId": 4, "lhsNodeUid": "0ebadde2-f46f-4e74-9c19-dd117c8c01cd", "rhsNodeUid": "358dda32-bc2e-4028-9a88-3d44acd549d4"}], "pinIndex": 4, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "query", "wires": {"4": {"sourceUUID": "e15db70a-4c6a-463f-bdaa-8a7e999e7dac", "destinationUUID": "b105a86a-595b-4add-ae38-010481ec55ef", "sourceName": "StringSplitterNode1_Element", "destinationName": "TimeScaleDBNode3_query", "uuid": "4ef66c33-e6f3-4c05-be40-7b61d55e36f7", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "operation", "package": "PyFlowBase", "fullName": "TimeScaleDBNode3_operation", "dataType": "StringPin", "direction": 0, "value": "\"update\"", "uuid": "b471f86c-907b-41c2-bfef-cb5f23978bdd", "linkedTo": [], "pinIndex": 3, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "operation", "wires": {}}}, {"name": "table", "package": "PyFlowBase", "fullName": "TimeScaleDBNode3_table", "dataType": "StringPin", "direction": 0, "value": "\"instagram_whitelistentry\"", "uuid": "a3eb1d9f-5447-43cd-8df6-04b0ff033f52", "linkedTo": [], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "table", "wires": {}}}, {"name": "Execute", "package": "PyFlowBase", "fullName": "TimeScaleDBNode3_Execute", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "3b4958ed-fabe-44b0-831b-8da207748b4f", "linkedTo": [{"lhsNodeName": "StringSplitterNode1", "outPinId": 1, "rhsNodeName": "TimeScaleDBNode3", "inPinId": 9, "lhsNodeUid": "0ebadde2-f46f-4e74-9c19-dd117c8c01cd", "rhsNodeUid": "358dda32-bc2e-4028-9a88-3d44acd549d4"}], "pinIndex": 9, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Execute", "wires": {"9": {"sourceUUID": "c0d8e913-9c7f-4c63-a339-46a7b249348b", "destinationUUID": "3b4958ed-fabe-44b0-831b-8da207748b4f", "sourceName": "StringSplitterNode1_completed", "destinationName": "TimeScaleDBNode3_Execute", "uuid": "464f1bdb-3650-410b-ae6d-9442f1bcc962", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "connection_string", "package": "PyFlowBase", "fullName": "TimeScaleDBNode3_connection_string", "dataType": "StringPin", "direction": 0, "value": "\"host=localhost port=5432 dbname=test_commandnet user=admin password=51x3\"", "uuid": "d5e5f1ce-ed5c-4bd5-b444-9869335b3bd3", "linkedTo": [], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "connection_string", "wires": {}}}, {"name": "fields", "package": "PyFlowBase", "fullName": "TimeScaleDBNode3_fields", "dataType": "StringPin", "direction": 0, "value": "\"[]\"", "uuid": "eec43c93-f412-4182-84ee-322d4613fd88", "linkedTo": [], "pinIndex": 8, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "fields", "wires": {}}}, {"name": "sort", "package": "PyFlowBase", "fullName": "TimeScaleDBNode3_sort", "dataType": "StringPin", "direction": 0, "value": "\"[]\"", "uuid": "a4ab5ae6-672e-4494-8ce4-df0ad25ef00d", "linkedTo": [], "pinIndex": 7, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "sort", "wires": {}}}, {"name": "limit", "package": "PyFlowBase", "fullName": "TimeScaleDBNode3_limit", "dataType": "IntPin", "direction": 0, "value": "1", "uuid": "adc2e117-10af-4848-a5e8-3398b0da45c9", "linkedTo": [], "pinIndex": 6, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "limit", "wires": {}}}], "outputs": [{"name": "Failed", "package": "PyFlowBase", "fullName": "TimeScaleDBNode3_Failed", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "469a1f3c-2c49-40e3-99ee-7f02293ad39d", "linkedTo": [{"lhsNodeName": "TimeScaleDBNode3", "outPinId": 3, "rhsNodeName": "sequence7", "inPinId": 1, "lhsNodeUid": "358dda32-bc2e-4028-9a88-3d44acd549d4", "rhsNodeUid": "fd624b5c-7f64-41b9-a430-c31bef8d3959"}], "pinIndex": 3, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Failed", "wires": {"1": {"sourceUUID": "469a1f3c-2c49-40e3-99ee-7f02293ad39d", "destinationUUID": "79d47da0-671a-49b2-88bf-03285326eaf3", "sourceName": "TimeScaleDBNode3_Failed", "destinationName": "sequence7_inExec", "uuid": "1d6d07a7-c856-4a95-992f-f26c2e5fb7f1", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "Completed", "package": "PyFlowBase", "fullName": "TimeScaleDBNode3_Completed", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "85669a51-9047-415b-bd87-c658f7918a85", "linkedTo": [{"lhsNodeName": "TimeScaleDBNode3", "outPinId": 2, "rhsNodeName": "sequence7", "inPinId": 1, "lhsNodeUid": "358dda32-bc2e-4028-9a88-3d44acd549d4", "rhsNodeUid": "fd624b5c-7f64-41b9-a430-c31bef8d3959"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Completed", "wires": {"1": {"sourceUUID": "85669a51-9047-415b-bd87-c658f7918a85", "destinationUUID": "79d47da0-671a-49b2-88bf-03285326eaf3", "sourceName": "TimeScaleDBNode3_Completed", "destinationName": "sequence7_inExec", "uuid": "0d38a796-0e16-4268-9990-21c7751cf38d", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "result", "package": "PyFlowBase", "fullName": "TimeScaleDBNode3_result", "dataType": "StringPin", "direction": 1, "value": "\"[{\\\"id\\\": 8907, \\\"follow\\\": false, \\\"dm\\\": false, \\\"favorite\\\": false, \\\"timestamp\\\": \\\"2025-01-30 13:04:38.543378+02:00\\\", \\\"username\\\": \\\"lucrez<PERSON>_<PERSON><PERSON><PERSON>i\\\", \\\"comment\\\": false, \\\"discover\\\": false, \\\"post_like\\\": false, \\\"requested\\\": 0, \\\"tags\\\": [\\\"bronze\\\", \\\"world_traveler\\\", \\\"content_creator\\\", \\\"influencer\\\", \\\"rising_star\\\"], \\\"is_auto\\\": true}]\"", "uuid": "4c5323e0-c523-49c3-a2d8-eb53e0ed8407", "linkedTo": [], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "result", "wires": {}}}], "meta": {"var": {}, "label": "TimeScaleDBNode3"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">TimeScaleDBNode3</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -4789.090237443166, "y": -1517.8375919748066}, {"package": "Automator", "lib": null, "type": "StringSplitterNode", "owningGraphName": "root", "name": "StringSplitterNode", "uuid": "8da62c32-daaa-4a7d-9b82-6917827f6763", "inputs": [{"name": "OptionalIndex", "package": "PyFlowBase", "fullName": "StringSplitterNode_OptionalIndex", "dataType": "IntPin", "direction": 0, "value": "1", "uuid": "39e25a95-cde3-460d-b4f0-36a439e01666", "linkedTo": [], "pinIndex": 5, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "OptionalIndex", "wires": {}}}, {"name": "Delimiter", "package": "PyFlowBase", "fullName": "StringSplitterNode_Delimiter", "dataType": "StringPin", "direction": 0, "value": "\"[\"", "uuid": "0a341056-da0b-4e51-a8f8-84b03ef6dae9", "linkedTo": [], "pinIndex": 4, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Delimiter", "wires": {}}}, {"name": "InputString", "package": "PyFlowBase", "fullName": "StringSplitterNode_InputString", "dataType": "StringPin", "direction": 0, "value": "\"[{\\\"username\\\": \\\"lucrez<PERSON>_bar<PERSON><PERSON>i\\\"}]\"", "uuid": "0186819f-f966-4ef6-94f3-38cf8d8473e6", "linkedTo": [{"lhsNodeName": "TimeScaleDBNode82", "outPinId": 1, "rhsNodeName": "StringSplitterNode", "inPinId": 3, "lhsNodeUid": "2437e69a-7067-45c7-b9f6-01684530eb46", "rhsNodeUid": "8da62c32-daaa-4a7d-9b82-6917827f6763"}], "pinIndex": 3, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "InputString", "wires": {"3": {"sourceUUID": "3da04181-1a58-4539-8c25-5a468f2c7d68", "destinationUUID": "0186819f-f966-4ef6-94f3-38cf8d8473e6", "sourceName": "TimeScaleDBNode82_result", "destinationName": "StringSplitterNode_InputString", "uuid": "01d9e40f-6bc2-44e5-a63c-1272407e0930", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "ResetExec", "package": "PyFlowBase", "fullName": "StringSplitterNode_ResetExec", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "77d3d8f6-5da9-423c-bf7b-f060c5468757", "linkedTo": [], "pinIndex": 2, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "ResetExec", "wires": {}}}, {"name": "inExec", "package": "PyFlowBase", "fullName": "StringSplitterNode_inExec", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "588f8265-e13b-4945-99ed-494bebb3ad62", "linkedTo": [{"lhsNodeName": "branch", "outPinId": 2, "rhsNodeName": "StringSplitterNode", "inPinId": 1, "lhsNodeUid": "08fd13d4-d878-4116-8044-21cb4153b614", "rhsNodeUid": "8da62c32-daaa-4a7d-9b82-6917827f6763"}], "pinIndex": 1, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "inExec", "wires": {"1": {"sourceUUID": "4fb9b444-3d07-41fb-b5a0-847ba8dc51b6", "destinationUUID": "588f8265-e13b-4945-99ed-494bebb3ad62", "sourceName": "branch_False", "destinationName": "StringSplitterNode_inExec", "uuid": "e81b8fdd-88cd-4184-9243-b5e058eaa0a2", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "outputs": [{"name": "Element", "package": "PyFlowBase", "fullName": "StringSplitterNode_Element", "dataType": "StringPin", "direction": 1, "value": "\"{\\\"username\\\": \\\"lucrez<PERSON>_bar<PERSON><PERSON>i\\\"}]\"", "uuid": "2a5e40d5-8d53-4bd3-bf2b-c684c948fce6", "linkedTo": [{"lhsNodeName": "StringSplitterNode", "outPinId": 2, "rhsNodeName": "StringSplitterNode1", "inPinId": 3, "lhsNodeUid": "8da62c32-daaa-4a7d-9b82-6917827f6763", "rhsNodeUid": "0ebadde2-f46f-4e74-9c19-dd117c8c01cd"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Element", "wires": {"3": {"sourceUUID": "2a5e40d5-8d53-4bd3-bf2b-c684c948fce6", "destinationUUID": "1ab0e886-30c0-4e42-ba15-12d481013301", "sourceName": "StringSplitterNode_Element", "destinationName": "StringSplitterNode1_InputString", "uuid": "7e0cf63a-df91-4231-ae87-95f1900a3708", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "completed", "package": "PyFlowBase", "fullName": "StringSplitterNode_completed", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "1180f436-0a3a-40c0-bef3-04de84af8225", "linkedTo": [{"lhsNodeName": "StringSplitterNode", "outPinId": 1, "rhsNodeName": "StringSplitterNode1", "inPinId": 1, "lhsNodeUid": "8da62c32-daaa-4a7d-9b82-6917827f6763", "rhsNodeUid": "0ebadde2-f46f-4e74-9c19-dd117c8c01cd"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "completed", "wires": {"1": {"sourceUUID": "1180f436-0a3a-40c0-bef3-04de84af8225", "destinationUUID": "dba0a55f-87d6-4557-adcb-7d501144f922", "sourceName": "StringSplitterNode_completed", "destinationName": "StringSplitterNode1_inExec", "uuid": "32c86b23-ff2e-4bf0-b07a-0450815c7901", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "ListOutput", "package": "PyFlowBase", "fullName": "StringSplitterNode_ListOutput", "dataType": "StringPin", "direction": 1, "value": "[\"\", \"{\\\"username\\\": \\\"lucrez<PERSON>_bar<PERSON><PERSON>i\\\"}]\"]", "uuid": "5dd4ee23-b71e-4ff1-93f3-200906c47894", "linkedTo": [], "pinIndex": 3, "options": [4, 256], "structure": 1, "alwaysList": true, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "ListOutput", "wires": {}}}], "meta": {"var": {}, "label": "StringSplitterNode"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">StringSplitterNode</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -5295.************, "y": -1530.2784194424153}, {"package": "Automator", "lib": null, "type": "CompareStringsNode", "owningGraphName": "root", "name": "CompareStringsNode", "uuid": "d7aaf045-6ea2-45d3-a757-79d20301f4be", "inputs": [{"name": "Execute", "package": "PyFlowBase", "fullName": "CompareStringsNode_Execute", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "fdc0c5df-bb56-417c-95be-3255731872f2", "linkedTo": [{"lhsNodeName": "TimeScaleDBNode82", "outPinId": 2, "rhsNodeName": "CompareStringsNode", "inPinId": 3, "lhsNodeUid": "2437e69a-7067-45c7-b9f6-01684530eb46", "rhsNodeUid": "d7aaf045-6ea2-45d3-a757-79d20301f4be"}], "pinIndex": 3, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Execute", "wires": {"3": {"sourceUUID": "cdee7998-267b-4258-a428-ad630eed32f2", "destinationUUID": "fdc0c5df-bb56-417c-95be-3255731872f2", "sourceName": "TimeScaleDBNode82_Completed", "destinationName": "CompareStringsNode_Execute", "uuid": "df3780c2-3e6d-4d54-8c09-d1b644669eec", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "String B", "package": "PyFlowBase", "fullName": "CompareStringsNode_String B", "dataType": "StringPin", "direction": 0, "value": "\"[{\\\"username\\\": \\\"lucrez<PERSON>_bar<PERSON><PERSON>i\\\"}]\"", "uuid": "57841392-fb23-4547-a8b0-82fa36ba5689", "linkedTo": [{"lhsNodeName": "TimeScaleDBNode82", "outPinId": 1, "rhsNodeName": "CompareStringsNode", "inPinId": 2, "lhsNodeUid": "2437e69a-7067-45c7-b9f6-01684530eb46", "rhsNodeUid": "d7aaf045-6ea2-45d3-a757-79d20301f4be"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "String B", "wires": {"2": {"sourceUUID": "3da04181-1a58-4539-8c25-5a468f2c7d68", "destinationUUID": "57841392-fb23-4547-a8b0-82fa36ba5689", "sourceName": "TimeScaleDBNode82_result", "destinationName": "CompareStringsNode_String B", "uuid": "e5be17a1-32fa-46fa-a68d-a2d055d8856b", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "String A", "package": "PyFlowBase", "fullName": "CompareStringsNode_String A", "dataType": "StringPin", "direction": 0, "value": "\"[]\"", "uuid": "f7914390-d54b-48f1-854d-8f14962d1c69", "linkedTo": [{"lhsNodeName": "makeString", "outPinId": 1, "rhsNodeName": "CompareStringsNode", "inPinId": 1, "lhsNodeUid": "b68b77a9-271b-442e-9f76-65eab4727f16", "rhsNodeUid": "d7aaf045-6ea2-45d3-a757-79d20301f4be"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "String A", "wires": {"1": {"sourceUUID": "60c6c2b3-6642-4c52-8f1d-fd1a00fb4d00", "destinationUUID": "f7914390-d54b-48f1-854d-8f14962d1c69", "sourceName": "makeString_out", "destinationName": "CompareStringsNode_String A", "uuid": "a75e29f7-5136-472d-81aa-f116652267f6", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "outputs": [{"name": "Result", "package": "PyFlowBase", "fullName": "CompareStringsNode_Result", "dataType": "BoolPin", "direction": 1, "value": "false", "uuid": "bda353be-d56d-4faa-a7ff-ea8c0ec65823", "linkedTo": [{"lhsNodeName": "CompareStringsNode", "outPinId": 2, "rhsNodeName": "branch", "inPinId": 2, "lhsNodeUid": "d7aaf045-6ea2-45d3-a757-79d20301f4be", "rhsNodeUid": "08fd13d4-d878-4116-8044-21cb4153b614"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Result", "wires": {"2": {"sourceUUID": "bda353be-d56d-4faa-a7ff-ea8c0ec65823", "destinationUUID": "b4426cbb-e331-436a-a4d9-2b85b20a1190", "sourceName": "CompareStringsNode_Result", "destinationName": "branch_Condition", "uuid": "1333b442-36e0-4c0a-ba85-5716680bdee3", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "Completed", "package": "PyFlowBase", "fullName": "CompareStringsNode_Completed", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "0d290ccf-2cf1-4669-bc2d-9c2d3b92e2d6", "linkedTo": [{"lhsNodeName": "CompareStringsNode", "outPinId": 1, "rhsNodeName": "branch", "inPinId": 1, "lhsNodeUid": "d7aaf045-6ea2-45d3-a757-79d20301f4be", "rhsNodeUid": "08fd13d4-d878-4116-8044-21cb4153b614"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Completed", "wires": {"1": {"sourceUUID": "0d290ccf-2cf1-4669-bc2d-9c2d3b92e2d6", "destinationUUID": "ea634431-5e37-4e2b-b1a3-2b9612396be8", "sourceName": "CompareStringsNode_Completed", "destinationName": "branch_In", "uuid": "32b6e05c-464a-41c9-af58-6abcd7141e82", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "CompareStringsNode"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">CompareStringsNode</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -4332.79809955384, "y": -2071.8559253664043}, {"package": "PyFlowBase", "lib": "DefaultLib", "type": "makeString", "owningGraphName": "root", "name": "makeString", "uuid": "b68b77a9-271b-442e-9f76-65eab4727f16", "inputs": [{"name": "s", "package": "PyFlowBase", "fullName": "makeString_s", "dataType": "StringPin", "direction": 0, "value": "\"[]\"", "uuid": "f1a77cb2-d2bc-4f29-ac1d-a6a43c6ae42a", "linkedTo": [], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "s", "wires": {}}}], "outputs": [{"name": "out", "package": "PyFlowBase", "fullName": "makeString_out", "dataType": "StringPin", "direction": 1, "value": "\"[]\"", "uuid": "60c6c2b3-6642-4c52-8f1d-fd1a00fb4d00", "linkedTo": [{"lhsNodeName": "makeString", "outPinId": 1, "rhsNodeName": "CompareStringsNode", "inPinId": 1, "lhsNodeUid": "b68b77a9-271b-442e-9f76-65eab4727f16", "rhsNodeUid": "d7aaf045-6ea2-45d3-a757-79d20301f4be"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "out", "wires": {"1": {"sourceUUID": "60c6c2b3-6642-4c52-8f1d-fd1a00fb4d00", "destinationUUID": "f7914390-d54b-48f1-854d-8f14962d1c69", "sourceName": "makeString_out", "destinationName": "CompareStringsNode_String A", "uuid": "a75e29f7-5136-472d-81aa-f116652267f6", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "makeString"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">makeString</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -4483.101209245259, "y": -2086.8277074040516}, {"package": "PyFlowBase", "lib": null, "type": "branch", "owningGraphName": "root", "name": "branch", "uuid": "08fd13d4-d878-4116-8044-21cb4153b614", "inputs": [{"name": "In", "package": "PyFlowBase", "fullName": "branch_In", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "ea634431-5e37-4e2b-b1a3-2b9612396be8", "linkedTo": [{"lhsNodeName": "CompareStringsNode", "outPinId": 1, "rhsNodeName": "branch", "inPinId": 1, "lhsNodeUid": "d7aaf045-6ea2-45d3-a757-79d20301f4be", "rhsNodeUid": "08fd13d4-d878-4116-8044-21cb4153b614"}], "pinIndex": 1, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "In", "wires": {"1": {"sourceUUID": "0d290ccf-2cf1-4669-bc2d-9c2d3b92e2d6", "destinationUUID": "ea634431-5e37-4e2b-b1a3-2b9612396be8", "sourceName": "CompareStringsNode_Completed", "destinationName": "branch_In", "uuid": "32b6e05c-464a-41c9-af58-6abcd7141e82", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "Condition", "package": "PyFlowBase", "fullName": "branch_Condition", "dataType": "BoolPin", "direction": 0, "value": "false", "uuid": "b4426cbb-e331-436a-a4d9-2b85b20a1190", "linkedTo": [{"lhsNodeName": "CompareStringsNode", "outPinId": 2, "rhsNodeName": "branch", "inPinId": 2, "lhsNodeUid": "d7aaf045-6ea2-45d3-a757-79d20301f4be", "rhsNodeUid": "08fd13d4-d878-4116-8044-21cb4153b614"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Condition", "wires": {"2": {"sourceUUID": "bda353be-d56d-4faa-a7ff-ea8c0ec65823", "destinationUUID": "b4426cbb-e331-436a-a4d9-2b85b20a1190", "sourceName": "CompareStringsNode_Result", "destinationName": "branch_Condition", "uuid": "1333b442-36e0-4c0a-ba85-5716680bdee3", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "outputs": [{"name": "True", "package": "PyFlowBase", "fullName": "branch_True", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "086465d5-900a-4c32-9e4f-a4338faa6280", "linkedTo": [{"lhsNodeName": "branch", "outPinId": 1, "rhsNodeName": "cliexit", "inPinId": 1, "lhsNodeUid": "08fd13d4-d878-4116-8044-21cb4153b614", "rhsNodeUid": "7dd020be-9d70-4594-a994-158d70242eab"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "True", "wires": {"1": {"sourceUUID": "086465d5-900a-4c32-9e4f-a4338faa6280", "destinationUUID": "e3da099e-911b-4cb8-a1e3-eb8a84ff1ffa", "sourceName": "branch_True", "destinationName": "cliexit_inExec", "uuid": "d4f6f985-e972-4071-8b52-1af0c454fe11", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "False", "package": "PyFlowBase", "fullName": "branch_False", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "4fb9b444-3d07-41fb-b5a0-847ba8dc51b6", "linkedTo": [{"lhsNodeName": "branch", "outPinId": 2, "rhsNodeName": "StringSplitterNode", "inPinId": 1, "lhsNodeUid": "08fd13d4-d878-4116-8044-21cb4153b614", "rhsNodeUid": "8da62c32-daaa-4a7d-9b82-6917827f6763"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "False", "wires": {"1": {"sourceUUID": "4fb9b444-3d07-41fb-b5a0-847ba8dc51b6", "destinationUUID": "588f8265-e13b-4945-99ed-494bebb3ad62", "sourceName": "branch_False", "destinationName": "StringSplitterNode_inExec", "uuid": "e81b8fdd-88cd-4184-9243-b5e058eaa0a2", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "branch"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">branch</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -4158.************, "y": -2029.************}, {"package": "Automator", "lib": null, "type": "StringSplitterNode", "owningGraphName": "root", "name": "StringSplitterNode1", "uuid": "0ebadde2-f46f-4e74-9c19-dd117c8c01cd", "inputs": [{"name": "Delimiter", "package": "PyFlowBase", "fullName": "StringSplitterNode1_Delimiter", "dataType": "StringPin", "direction": 0, "value": "\"]\"", "uuid": "57bfab50-819e-4822-9398-fed25eba10e2", "linkedTo": [], "pinIndex": 4, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Delimiter", "wires": {}}}, {"name": "InputString", "package": "PyFlowBase", "fullName": "StringSplitterNode1_InputString", "dataType": "StringPin", "direction": 0, "value": "\"{\\\"username\\\": \\\"lucrez<PERSON>_bar<PERSON><PERSON>i\\\"}]\"", "uuid": "1ab0e886-30c0-4e42-ba15-12d481013301", "linkedTo": [{"lhsNodeName": "StringSplitterNode", "outPinId": 2, "rhsNodeName": "StringSplitterNode1", "inPinId": 3, "lhsNodeUid": "8da62c32-daaa-4a7d-9b82-6917827f6763", "rhsNodeUid": "0ebadde2-f46f-4e74-9c19-dd117c8c01cd"}], "pinIndex": 3, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "InputString", "wires": {"3": {"sourceUUID": "2a5e40d5-8d53-4bd3-bf2b-c684c948fce6", "destinationUUID": "1ab0e886-30c0-4e42-ba15-12d481013301", "sourceName": "StringSplitterNode_Element", "destinationName": "StringSplitterNode1_InputString", "uuid": "7e0cf63a-df91-4231-ae87-95f1900a3708", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "ResetExec", "package": "PyFlowBase", "fullName": "StringSplitterNode1_ResetExec", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "91c78e7b-5bc3-4745-a951-6790e3ed4d70", "linkedTo": [], "pinIndex": 2, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "ResetExec", "wires": {}}}, {"name": "inExec", "package": "PyFlowBase", "fullName": "StringSplitterNode1_inExec", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "dba0a55f-87d6-4557-adcb-7d501144f922", "linkedTo": [{"lhsNodeName": "StringSplitterNode", "outPinId": 1, "rhsNodeName": "StringSplitterNode1", "inPinId": 1, "lhsNodeUid": "8da62c32-daaa-4a7d-9b82-6917827f6763", "rhsNodeUid": "0ebadde2-f46f-4e74-9c19-dd117c8c01cd"}], "pinIndex": 1, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "inExec", "wires": {"1": {"sourceUUID": "1180f436-0a3a-40c0-bef3-04de84af8225", "destinationUUID": "dba0a55f-87d6-4557-adcb-7d501144f922", "sourceName": "StringSplitterNode_completed", "destinationName": "StringSplitterNode1_inExec", "uuid": "32c86b23-ff2e-4bf0-b07a-0450815c7901", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "OptionalIndex", "package": "PyFlowBase", "fullName": "StringSplitterNode1_OptionalIndex", "dataType": "IntPin", "direction": 0, "value": "0", "uuid": "422f6f58-99a7-4155-8cbe-11ed1547b597", "linkedTo": [], "pinIndex": 5, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "OptionalIndex", "wires": {}}}], "outputs": [{"name": "ListOutput", "package": "PyFlowBase", "fullName": "StringSplitterNode1_ListOutput", "dataType": "StringPin", "direction": 1, "value": "[\"{\\\"username\\\": \\\"lucrez<PERSON>_bar<PERSON><PERSON>i\\\"}\", \"\"]", "uuid": "bc9832d2-1081-432b-a561-ae807caaa64b", "linkedTo": [], "pinIndex": 3, "options": [4, 256], "structure": 1, "alwaysList": true, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "ListOutput", "wires": {}}}, {"name": "Element", "package": "PyFlowBase", "fullName": "StringSplitterNode1_Element", "dataType": "StringPin", "direction": 1, "value": "\"{\\\"username\\\": \\\"lucrez<PERSON>_bar<PERSON><PERSON>i\\\"}\"", "uuid": "e15db70a-4c6a-463f-bdaa-8a7e999e7dac", "linkedTo": [{"lhsNodeName": "StringSplitterNode1", "outPinId": 2, "rhsNodeName": "TimeScaleDBNode3", "inPinId": 4, "lhsNodeUid": "0ebadde2-f46f-4e74-9c19-dd117c8c01cd", "rhsNodeUid": "358dda32-bc2e-4028-9a88-3d44acd549d4"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Element", "wires": {"4": {"sourceUUID": "e15db70a-4c6a-463f-bdaa-8a7e999e7dac", "destinationUUID": "b105a86a-595b-4add-ae38-010481ec55ef", "sourceName": "StringSplitterNode1_Element", "destinationName": "TimeScaleDBNode3_query", "uuid": "4ef66c33-e6f3-4c05-be40-7b61d55e36f7", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "completed", "package": "PyFlowBase", "fullName": "StringSplitterNode1_completed", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "c0d8e913-9c7f-4c63-a339-46a7b249348b", "linkedTo": [{"lhsNodeName": "StringSplitterNode1", "outPinId": 1, "rhsNodeName": "TimeScaleDBNode3", "inPinId": 9, "lhsNodeUid": "0ebadde2-f46f-4e74-9c19-dd117c8c01cd", "rhsNodeUid": "358dda32-bc2e-4028-9a88-3d44acd549d4"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "completed", "wires": {"9": {"sourceUUID": "c0d8e913-9c7f-4c63-a339-46a7b249348b", "destinationUUID": "3b4958ed-fabe-44b0-831b-8da207748b4f", "sourceName": "StringSplitterNode1_completed", "destinationName": "TimeScaleDBNode3_Execute", "uuid": "464f1bdb-3650-410b-ae6d-9442f1bcc962", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "StringSplitterNode1"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">StringSplitterNode1</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -5067.74806043132, "y": -1482.9716239987688}, {"package": "PyFlowBase", "lib": null, "type": "branch", "owningGraphName": "root", "name": "branch304", "uuid": "3a2930bc-ac9f-4bf0-aa4c-96c4df528a8b", "inputs": [{"name": "In", "package": "PyFlowBase", "fullName": "branch304_In", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "535a5f56-8ef9-4313-92f1-e18fc194b007", "linkedTo": [{"lhsNodeName": "sequence7", "outPinId": 2, "rhsNodeName": "branch304", "inPinId": 1, "lhsNodeUid": "fd624b5c-7f64-41b9-a430-c31bef8d3959", "rhsNodeUid": "3a2930bc-ac9f-4bf0-aa4c-96c4df528a8b"}], "pinIndex": 1, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "In", "wires": {"1": {"sourceUUID": "0609e4cb-8c1d-4e33-8ad8-14c77515ca8a", "destinationUUID": "535a5f56-8ef9-4313-92f1-e18fc194b007", "sourceName": "sequence7_2", "destinationName": "branch304_In", "uuid": "d64836b5-3f22-4ead-9c1a-734197031c01", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "Condition", "package": "PyFlowBase", "fullName": "branch304_Condition", "dataType": "BoolPin", "direction": 0, "value": "false", "uuid": "233de6b8-edfd-47f0-8321-5206eaa0ffed", "linkedTo": [{"lhsNodeName": "charge17", "outPinId": 2, "rhsNodeName": "branch304", "inPinId": 2, "lhsNodeUid": "be4f8737-3e6f-4d5d-9e5a-9330bdab486c", "rhsNodeUid": "3a2930bc-ac9f-4bf0-aa4c-96c4df528a8b"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Condition", "wires": {"2": {"sourceUUID": "36dcfcad-3cca-45ff-9082-e31963eabb24", "destinationUUID": "233de6b8-edfd-47f0-8321-5206eaa0ffed", "sourceName": "charge17_isCompleted", "destinationName": "branch304_Condition", "uuid": "43333f2b-a6b4-41b6-83ee-db04085dd954", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "outputs": [{"name": "False", "package": "PyFlowBase", "fullName": "branch304_False", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "9f20836d-7878-4d5d-8f1e-d72440cb8c3c", "linkedTo": [{"lhsNodeName": "branch304", "outPinId": 2, "rhsNodeName": "delay", "inPinId": 1, "lhsNodeUid": "3a2930bc-ac9f-4bf0-aa4c-96c4df528a8b", "rhsNodeUid": "e0bc091b-54cc-4447-b1a3-6baf8de079b9"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "False", "wires": {"1": {"sourceUUID": "9f20836d-7878-4d5d-8f1e-d72440cb8c3c", "destinationUUID": "e1827db3-f407-4f86-8b55-9f210920635d", "sourceName": "branch304_False", "destinationName": "delay_inExec", "uuid": "f49fe3f5-a83f-4ccf-853e-5be20e5f4239", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "True", "package": "PyFlowBase", "fullName": "branch304_True", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "b6da1016-d17c-48fd-b4fa-01495642b245", "linkedTo": [{"lhsNodeName": "branch304", "outPinId": 1, "rhsNodeName": "sequence8", "inPinId": 1, "lhsNodeUid": "3a2930bc-ac9f-4bf0-aa4c-96c4df528a8b", "rhsNodeUid": "f05f116e-3e1d-4421-893e-00b45f4fd4b4"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "True", "wires": {"1": {"sourceUUID": "b6da1016-d17c-48fd-b4fa-01495642b245", "destinationUUID": "aba51901-e561-4d22-a11f-3728accca01e", "sourceName": "branch304_True", "destinationName": "sequence8_inExec", "uuid": "aa9632be-950e-4fdd-9b25-b0f20877e905", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "branch304"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">branch304</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -4295.032833602402, "y": -1385.21094737412}, {"package": "PyFlowBase", "lib": null, "type": "sequence", "owningGraphName": "root", "name": "sequence8", "uuid": "f05f116e-3e1d-4421-893e-00b45f4fd4b4", "inputs": [{"name": "randomMode", "package": "PyFlowBase", "fullName": "sequence8_randomMode", "dataType": "BoolPin", "direction": 0, "value": "false", "uuid": "b75f79af-ca70-4788-b98c-593ace43713e", "linkedTo": [], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "randomMode", "wires": {}}}, {"name": "inExec", "package": "PyFlowBase", "fullName": "sequence8_inExec", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "aba51901-e561-4d22-a11f-3728accca01e", "linkedTo": [{"lhsNodeName": "branch304", "outPinId": 1, "rhsNodeName": "sequence8", "inPinId": 1, "lhsNodeUid": "3a2930bc-ac9f-4bf0-aa4c-96c4df528a8b", "rhsNodeUid": "f05f116e-3e1d-4421-893e-00b45f4fd4b4"}], "pinIndex": 1, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "inExec", "wires": {"1": {"sourceUUID": "b6da1016-d17c-48fd-b4fa-01495642b245", "destinationUUID": "aba51901-e561-4d22-a11f-3728accca01e", "sourceName": "branch304_True", "destinationName": "sequence8_inExec", "uuid": "aa9632be-950e-4fdd-9b25-b0f20877e905", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "outputs": [{"name": "2", "package": "PyFlowBase", "fullName": "sequence8_2", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "b185b66d-eb67-472f-8256-b51642e5dd5a", "linkedTo": [{"lhsNodeName": "sequence8", "outPinId": 2, "rhsNodeName": "cliexit", "inPinId": 1, "lhsNodeUid": "f05f116e-3e1d-4421-893e-00b45f4fd4b4", "rhsNodeUid": "7dd020be-9d70-4594-a994-158d70242eab"}], "pinIndex": 2, "options": [64, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Then 2", "wires": {"1": {"sourceUUID": "b185b66d-eb67-472f-8256-b51642e5dd5a", "destinationUUID": "e3da099e-911b-4cb8-a1e3-eb8a84ff1ffa", "sourceName": "sequence8_2", "destinationName": "cliexit_inExec", "uuid": "71d498a3-13ad-4970-b9e1-1563c66fad05", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "1", "package": "PyFlowBase", "fullName": "sequence8_1", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "ee877a40-34b9-4fa1-a428-0ce0c9ee5ef2", "linkedTo": [{"lhsNodeName": "sequence8", "outPinId": 1, "rhsNodeName": "charge17", "inPinId": 4, "lhsNodeUid": "f05f116e-3e1d-4421-893e-00b45f4fd4b4", "rhsNodeUid": "be4f8737-3e6f-4d5d-9e5a-9330bdab486c"}], "pinIndex": 1, "options": [64, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Then 1", "wires": {"4": {"sourceUUID": "ee877a40-34b9-4fa1-a428-0ce0c9ee5ef2", "destinationUUID": "30d5705c-1b95-4f05-9b85-ff6263942542", "sourceName": "sequence8_1", "destinationName": "charge17_resetExec", "uuid": "3a7b951e-543c-4fef-b9f0-622829fa0bc6", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "sequence8"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">sequence8</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -4090.************, "y": -1418.2957421838748, "numOutputs": 2}, {"package": "PyFlowBase", "lib": "DefaultLib", "type": "makeInt", "owningGraphName": "root", "name": "makeInt", "uuid": "0283d726-6434-4d1e-8bae-d99fc5578ba8", "inputs": [{"name": "i", "package": "PyFlowBase", "fullName": "makeInt_i", "dataType": "IntPin", "direction": 0, "value": "0", "uuid": "0d4de256-e3ab-4aeb-9e15-ec2bbff4e25b", "linkedTo": [{"lhsNodeName": "graphInputs", "outPinId": 3, "rhsNodeName": "makeInt", "inPinId": 1, "lhsNodeUid": "e78c5696-6ca3-4855-bc03-5bf0f884affd", "rhsNodeUid": "0283d726-6434-4d1e-8bae-d99fc5578ba8"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "i", "wires": {"1": {"sourceUUID": "8a36c521-9c1b-45ea-87cb-a698ff7683b4", "destinationUUID": "0d4de256-e3ab-4aeb-9e15-ec2bbff4e25b", "sourceName": "graphInputs_in2", "destinationName": "makeInt_i", "uuid": "daeb0c4b-e364-4749-9f34-b0fd149b7cf7", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "outputs": [{"name": "out", "package": "PyFlowBase", "fullName": "makeInt_out", "dataType": "IntPin", "direction": 1, "value": "13", "uuid": "4198f561-e35f-4721-ac27-4d5793f080b7", "linkedTo": [{"lhsNodeName": "makeInt", "outPinId": 1, "rhsNodeName": "charge17", "inPinId": 2, "lhsNodeUid": "0283d726-6434-4d1e-8bae-d99fc5578ba8", "rhsNodeUid": "be4f8737-3e6f-4d5d-9e5a-9330bdab486c"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "out", "wires": {"2": {"sourceUUID": "4198f561-e35f-4721-ac27-4d5793f080b7", "destinationUUID": "6c58b341-9c70-4471-89ce-04d337cdae51", "sourceName": "makeInt_out", "destinationName": "charge17_Amount", "uuid": "0485b2a2-c1ab-4125-9ffd-3e9946c8c54c", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "makeInt"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">makeInt</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -4358.************, "y": -1660.8720877901794}, {"package": "PyFlowBase", "lib": null, "type": "charge", "owningGraphName": "root", "name": "charge17", "uuid": "be4f8737-3e6f-4d5d-9e5a-9330bdab486c", "inputs": [{"name": "inExec", "package": "PyFlowBase", "fullName": "charge17_inExec", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "66a5a79a-c050-4a2c-af80-5c12476dac4e", "linkedTo": [{"lhsNodeName": "sequence7", "outPinId": 1, "rhsNodeName": "charge17", "inPinId": 1, "lhsNodeUid": "fd624b5c-7f64-41b9-a430-c31bef8d3959", "rhsNodeUid": "be4f8737-3e6f-4d5d-9e5a-9330bdab486c"}], "pinIndex": 1, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "inExec", "wires": {"1": {"sourceUUID": "2ce135bb-9892-4331-8c0b-43bbed98b9fb", "destinationUUID": "66a5a79a-c050-4a2c-af80-5c12476dac4e", "sourceName": "sequence7_1", "destinationName": "charge17_inExec", "uuid": "4ab09264-b2e2-4f40-8fcd-afdcc6689b07", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "package": "PyFlowBase", "fullName": "charge17_Threshold", "dataType": "IntPin", "direction": 0, "value": "10", "uuid": "8c653ef8-805f-48e1-980b-1a09c809685a", "linkedTo": [], "pinIndex": 5, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "<PERSON><PERSON><PERSON><PERSON>", "wires": {}}}, {"name": "resetExec", "package": "PyFlowBase", "fullName": "charge17_resetExec", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "30d5705c-1b95-4f05-9b85-ff6263942542", "linkedTo": [{"lhsNodeName": "sequence8", "outPinId": 1, "rhsNodeName": "charge17", "inPinId": 4, "lhsNodeUid": "f05f116e-3e1d-4421-893e-00b45f4fd4b4", "rhsNodeUid": "be4f8737-3e6f-4d5d-9e5a-9330bdab486c"}], "pinIndex": 4, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "resetExec", "wires": {"4": {"sourceUUID": "ee877a40-34b9-4fa1-a428-0ce0c9ee5ef2", "destinationUUID": "30d5705c-1b95-4f05-9b85-ff6263942542", "sourceName": "sequence8_1", "destinationName": "charge17_resetExec", "uuid": "3a7b951e-543c-4fef-b9f0-622829fa0bc6", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "Step", "package": "PyFlowBase", "fullName": "charge17_Step", "dataType": "FloatPin", "direction": 0, "value": "1.0", "uuid": "ef63d86e-714a-472b-9253-fd1c3179e77d", "linkedTo": [], "pinIndex": 3, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Step", "wires": {}}}, {"name": "Amount", "package": "PyFlowBase", "fullName": "charge17_Amount", "dataType": "FloatPin", "direction": 0, "value": "13.0", "uuid": "6c58b341-9c70-4471-89ce-04d337cdae51", "linkedTo": [{"lhsNodeName": "makeInt", "outPinId": 1, "rhsNodeName": "charge17", "inPinId": 2, "lhsNodeUid": "0283d726-6434-4d1e-8bae-d99fc5578ba8", "rhsNodeUid": "be4f8737-3e6f-4d5d-9e5a-9330bdab486c"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Amount", "wires": {"2": {"sourceUUID": "4198f561-e35f-4721-ac27-4d5793f080b7", "destinationUUID": "6c58b341-9c70-4471-89ce-04d337cdae51", "sourceName": "makeInt_out", "destinationName": "charge17_Amount", "uuid": "0485b2a2-c1ab-4125-9ffd-3e9946c8c54c", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "outputs": [{"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>Reached", "package": "PyFlowBase", "fullName": "charge17_isThresholdReached", "dataType": "BoolPin", "direction": 1, "value": "false", "uuid": "7679a5f0-ce0a-490a-aba9-7b6040ca2950", "linkedTo": [], "pinIndex": 4, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>Reached", "wires": {}}}, {"name": "currentIndex", "package": "PyFlowBase", "fullName": "charge17_currentIndex", "dataType": "IntPin", "direction": 1, "value": "2", "uuid": "d7cdc7c6-f878-4dae-a707-13c38255ea68", "linkedTo": [], "pinIndex": 3, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "currentIndex", "wires": {}}}, {"name": "isCompleted", "package": "PyFlowBase", "fullName": "charge17_isCompleted", "dataType": "BoolPin", "direction": 1, "value": "false", "uuid": "36dcfcad-3cca-45ff-9082-e31963eabb24", "linkedTo": [{"lhsNodeName": "charge17", "outPinId": 2, "rhsNodeName": "branch304", "inPinId": 2, "lhsNodeUid": "be4f8737-3e6f-4d5d-9e5a-9330bdab486c", "rhsNodeUid": "3a2930bc-ac9f-4bf0-aa4c-96c4df528a8b"}], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "isCompleted", "wires": {"2": {"sourceUUID": "36dcfcad-3cca-45ff-9082-e31963eabb24", "destinationUUID": "233de6b8-edfd-47f0-8321-5206eaa0ffed", "sourceName": "charge17_isCompleted", "destinationName": "branch304_Condition", "uuid": "43333f2b-a6b4-41b6-83ee-db04085dd954", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "completed", "package": "PyFlowBase", "fullName": "charge17_completed", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "71f5ccec-4004-435f-a85a-f3b172a0f712", "linkedTo": [], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "completed", "wires": {}}}], "meta": {"var": {}, "label": "charge17"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">charge17</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -4235.************, "y": -1659.119134323625}, {"package": "PyFlowBase", "lib": null, "type": "sequence", "owningGraphName": "root", "name": "sequence7", "uuid": "fd624b5c-7f64-41b9-a430-c31bef8d3959", "inputs": [{"name": "randomMode", "package": "PyFlowBase", "fullName": "sequence7_randomMode", "dataType": "BoolPin", "direction": 0, "value": "false", "uuid": "21246cbf-d53d-4c5a-bf9e-2f9166e81208", "linkedTo": [], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "randomMode", "wires": {}}}, {"name": "inExec", "package": "PyFlowBase", "fullName": "sequence7_inExec", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "79d47da0-671a-49b2-88bf-03285326eaf3", "linkedTo": [{"lhsNodeName": "TimeScaleDBNode3", "outPinId": 2, "rhsNodeName": "sequence7", "inPinId": 1, "lhsNodeUid": "358dda32-bc2e-4028-9a88-3d44acd549d4", "rhsNodeUid": "fd624b5c-7f64-41b9-a430-c31bef8d3959"}, {"lhsNodeName": "TimeScaleDBNode3", "outPinId": 3, "rhsNodeName": "sequence7", "inPinId": 1, "lhsNodeUid": "358dda32-bc2e-4028-9a88-3d44acd549d4", "rhsNodeUid": "fd624b5c-7f64-41b9-a430-c31bef8d3959"}], "pinIndex": 1, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "inExec", "wires": {"1": {"sourceUUID": "469a1f3c-2c49-40e3-99ee-7f02293ad39d", "destinationUUID": "79d47da0-671a-49b2-88bf-03285326eaf3", "sourceName": "TimeScaleDBNode3_Failed", "destinationName": "sequence7_inExec", "uuid": "1d6d07a7-c856-4a95-992f-f26c2e5fb7f1", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "outputs": [{"name": "2", "package": "PyFlowBase", "fullName": "sequence7_2", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "0609e4cb-8c1d-4e33-8ad8-14c77515ca8a", "linkedTo": [{"lhsNodeName": "sequence7", "outPinId": 2, "rhsNodeName": "branch304", "inPinId": 1, "lhsNodeUid": "fd624b5c-7f64-41b9-a430-c31bef8d3959", "rhsNodeUid": "3a2930bc-ac9f-4bf0-aa4c-96c4df528a8b"}], "pinIndex": 2, "options": [64, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Then 2", "wires": {"1": {"sourceUUID": "0609e4cb-8c1d-4e33-8ad8-14c77515ca8a", "destinationUUID": "535a5f56-8ef9-4313-92f1-e18fc194b007", "sourceName": "sequence7_2", "destinationName": "branch304_In", "uuid": "d64836b5-3f22-4ead-9c1a-734197031c01", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}, {"name": "1", "package": "PyFlowBase", "fullName": "sequence7_1", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "2ce135bb-9892-4331-8c0b-43bbed98b9fb", "linkedTo": [{"lhsNodeName": "sequence7", "outPinId": 1, "rhsNodeName": "charge17", "inPinId": 1, "lhsNodeUid": "fd624b5c-7f64-41b9-a430-c31bef8d3959", "rhsNodeUid": "be4f8737-3e6f-4d5d-9e5a-9330bdab486c"}], "pinIndex": 1, "options": [64, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Then 1", "wires": {"1": {"sourceUUID": "2ce135bb-9892-4331-8c0b-43bbed98b9fb", "destinationUUID": "66a5a79a-c050-4a2c-af80-5c12476dac4e", "sourceName": "sequence7_1", "destinationName": "charge17_inExec", "uuid": "4ab09264-b2e2-4f40-8fcd-afdcc6689b07", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "sequence7"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">sequence7</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -4483.062930732986, "y": -1457.9250831086579, "numOutputs": 2}, {"package": "PyFlowBase", "lib": null, "type": "delay", "owningGraphName": "root", "name": "delay", "uuid": "e0bc091b-54cc-4447-b1a3-6baf8de079b9", "inputs": [{"name": "RoamingMode", "package": "PyFlowBase", "fullName": "delay_RoamingMode", "dataType": "BoolPin", "direction": 0, "value": "false", "uuid": "ec23dd37-d821-46da-8f35-46c561c4a6fa", "linkedTo": [], "pinIndex": 4, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "RoamingMode", "wires": {}}}, {"name": "RandomDelay", "package": "PyFlowBase", "fullName": "delay_RandomDelay", "dataType": "IntPin", "direction": 0, "value": "0", "uuid": "fc4de608-a07f-4ec4-b89a-01e6e61add6b", "linkedTo": [], "pinIndex": 3, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "RandomDelay", "wires": {}}}, {"name": "Delay(s)", "package": "PyFlowBase", "fullName": "delay_Delay(s)", "dataType": "FloatPin", "direction": 0, "value": "0.1", "uuid": "605fabd7-daf7-4945-82a9-c7c87e300427", "linkedTo": [], "pinIndex": 2, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "Delay(s)", "wires": {}}}, {"name": "inExec", "package": "PyFlowBase", "fullName": "delay_inExec", "dataType": "ExecPin", "direction": 0, "value": "null", "uuid": "e1827db3-f407-4f86-8b55-9f210920635d", "linkedTo": [{"lhsNodeName": "branch304", "outPinId": 2, "rhsNodeName": "delay", "inPinId": 1, "lhsNodeUid": "3a2930bc-ac9f-4bf0-aa4c-96c4df528a8b", "rhsNodeUid": "e0bc091b-54cc-4447-b1a3-6baf8de079b9"}], "pinIndex": 1, "options": [8, 256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "inExec", "wires": {"1": {"sourceUUID": "9f20836d-7878-4d5d-8f1e-d72440cb8c3c", "destinationUUID": "e1827db3-f407-4f86-8b55-9f210920635d", "sourceName": "branch304_False", "destinationName": "delay_inExec", "uuid": "f49fe3f5-a83f-4ccf-853e-5be20e5f4239", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "outputs": [{"name": "outExec", "package": "PyFlowBase", "fullName": "delay_outExec", "dataType": "ExecPin", "direction": 1, "value": "null", "uuid": "f5a0afac-2309-476e-879c-804a41990bf3", "linkedTo": [{"lhsNodeName": "delay", "outPinId": 1, "rhsNodeName": "TimestampNode77", "inPinId": 3, "lhsNodeUid": "e0bc091b-54cc-4447-b1a3-6baf8de079b9", "rhsNodeUid": "d4cfa6f6-ebb9-4af8-a3e0-4c6dfa35d241"}], "pinIndex": 1, "options": [256], "structure": 0, "alwaysList": false, "alwaysSingle": false, "alwaysDict": false, "wrapper": {"bLabelHidden": false, "displayName": "outExec", "wires": {"3": {"sourceUUID": "f5a0afac-2309-476e-879c-804a41990bf3", "destinationUUID": "2ee0f787-9100-44c9-9d4a-5c6eb153361b", "sourceName": "delay_outExec", "destinationName": "TimestampNode77_Execute", "uuid": "ba4c71af-86ba-44eb-83fc-4a44ce19dc18", "hOffsetL": "0.0", "hOffsetR": "0.0", "hOffsetLSShape": "0.0", "hOffsetRSShape": "0.0", "vOffset": "0.0", "vOffsetSShape": "0.0", "snapVToFirst": 1, "snapVToSecond": 0}}}}], "meta": {"var": {}, "label": "delay"}, "wrapper": {"collapsed": false, "headerHtml": "<!DOCTYPE HTML PUBLIC \"-//W3C//DTD HTML 4.0//EN\" \"http://www.w3.org/TR/REC-html40/strict.dtd\">\n<html><head><meta name=\"qrichtext\" content=\"1\" /><meta charset=\"utf-8\" /><style type=\"text/css\">\np, li { white-space: pre-wrap; }\nhr { height: 1px; border-width: 0; }\nli.unchecked::marker { content: \"\\2610\"; }\nli.checked::marker { content: \"\\2612\"; }\n</style></head><body style=\" font-family:'Consolas'; font-size:6pt; font-weight:400; font-style:normal;\">\n<p style=\" margin-top:0px; margin-bottom:0px; margin-left:0px; margin-right:0px; -qt-block-indent:0; text-indent:0px;\">delay</p></body></html>", "exposeInputsToCompound": false, "groups": {"input": {}, "output": {}}}, "x": -4821.397729372561, "y": -1220.7073577038627}], "depth": 1, "isRoot": true, "parentGraphName": "None", "fileVersion": "3.0.0", "activeGraph": "root"}