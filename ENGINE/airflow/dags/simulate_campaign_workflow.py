"""
Simulate Campaign Workflow DAG

This DAG simulates a campaign workflow by executing Python scripts that simulate
the behavior of PyFlow workflows. It is used for testing and development purposes.

The DAG performs the following tasks:
1. Validates the campaign configuration
2. Simulates the account collection phase
3. Simulates the tag analysis phase
4. Updates the campaign results
5. Sends notifications on success or failure

Configuration Parameters:
- campaign_id: ID of the campaign
- num_accounts: Number of accounts to simulate (default: 100)
- min_followers: Minimum followers for tag analysis (default: 0)
- max_followers: Maximum followers for tag analysis (default: 0)
- enable_tagging: Whether to enable tagging (default: True)
- notify_email: Email to send notifications to (optional)
"""
import os
import sys
import json
import logging
import traceback
from datetime import datetime, timedelta

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.email import EmailOperator
from airflow.operators.dummy import DummyOperator
from airflow.utils.dates import days_ago
from airflow.utils.trigger_rule import TriggerRule
from airflow.models import Variable
from airflow.hooks.base_hook import BaseHook

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get default email from Airflow configuration
try:
    default_email = Variable.get("default_notification_email", "<EMAIL>")
except:
    default_email = "<EMAIL>"

# Default arguments for the DAG
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': True,
    'email_on_retry': True,
    'email': default_email,
    'retries': 2,
    'retry_delay': timedelta(minutes=5),
    'execution_timeout': timedelta(hours=2),
    'on_failure_callback': lambda context: logger.error(f"Task failed: {context['task_instance'].task_id}")
}

# Create the DAG
dag = DAG(
    'simulate_campaign_workflow',
    default_args=default_args,
    description='Simulate a campaign workflow',
    schedule_interval=None,  # This DAG is triggered manually
    start_date=days_ago(1),
    tags=['campaign', 'simulation'],
    catchup=False,
    max_active_runs=1,  # Only one active run at a time
    concurrency=4  # Maximum number of tasks to run simultaneously
)

def validate_campaign_config(**context):
    """
    Validate the campaign configuration from the trigger.
    """
    try:
        # Get the configuration from the trigger
        conf = context['dag_run'].conf

        # Log the configuration
        logger.info(f"Campaign configuration: {conf}")

        # Check if configuration is empty
        if not conf:
            raise ValueError("Empty campaign configuration")

        # Validate required fields
        required_fields = ['campaign_id']
        missing_fields = [field for field in required_fields if field not in conf]
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

        # Get campaign ID
        campaign_id = conf['campaign_id']
        if not campaign_id:
            raise ValueError("Empty campaign_id")

        # Get number of accounts to simulate
        num_accounts = conf.get('num_accounts', 100)
        if not isinstance(num_accounts, int) or num_accounts <= 0:
            logger.warning(f"Invalid num_accounts: {num_accounts}. Using default value of 100.")
            num_accounts = 100

        # Set up Django environment
        pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
        django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")

        # Add Django directory to path if not already there
        if django_dir not in sys.path:
            sys.path.append(django_dir)

        # Set up Django environment
        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

        # Import Django and set up
        import django
        django.setup()

        # Import models
        from campaigns.models import Campaign

        # Check if campaign exists
        try:
            campaign = Campaign.objects.get(id=campaign_id)
            logger.info(f"Found campaign: {campaign.name}")
        except Campaign.DoesNotExist:
            raise ValueError(f"Campaign with ID {campaign_id} does not exist")

        # Update campaign status to running
        campaign.status = 'running'
        campaign.save()
        logger.info(f"Updated campaign status to 'running'")

        # Return validated configuration
        return {
            'campaign_id': campaign_id,
            'num_accounts': num_accounts
        }

    except Exception as e:
        logger.exception(f"Error validating campaign configuration: {str(e)}")
        raise

def simulate_account_collection(**context):
    """
    Simulate the account collection phase.
    """
    # Get the configuration from the previous task
    conf = context['task_instance'].xcom_pull(task_ids='validate_campaign_config')
    campaign_id = conf['campaign_id']
    num_accounts = conf['num_accounts']

    logger.info(f"Simulating account collection for campaign {campaign_id}")
    logger.info(f"Creating {num_accounts} dummy accounts")

    # Set up Django environment
    pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
    django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")
    script_dir = os.path.join(django_dir, "campaigns", "scripts")

    # Add Django directory to path if not already there
    if django_dir not in sys.path:
        sys.path.append(django_dir)

    # Set up Django environment
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

    # Import Django and set up
    import django
    django.setup()

    # Import models
    from campaigns.models import Campaign, WorkflowExecution, WorkflowProgressUpdate
    from instagram.models import Accounts

    # Get campaign
    campaign = Campaign.objects.get(id=campaign_id)

    # Create workflow execution
    workflow = WorkflowExecution.objects.create(
        id=django.utils.crypto.get_random_string(32),
        campaign=campaign,
        workflow_name='account_collection.pygraph',
        workflow_path='/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/INSTA/WORKFLOW/account_collection.pygraph',
        workflow_type='collection',
        status='running',
        start_time=django.utils.timezone.now(),
        total_items=num_accounts
    )

    # Execute the simulation script
    try:
        # Check if the script exists
        script_path = os.path.join(script_dir, 'simulate_account_collection.py')
        if not os.path.exists(script_path):
            # If not, use the Django management command
            from django.core.management import call_command
            logger.info("Using Django management command to simulate account collection")
            call_command('simulate_campaign', name=f"Simulated Campaign {campaign_id}", accounts=num_accounts, delay=0.1)
        else:
            # Execute the script
            logger.info(f"Executing script: {script_path}")
            import subprocess
            cmd = [sys.executable, script_path, '--campaign_id', campaign_id, '--num_accounts', str(num_accounts)]
            subprocess.run(cmd, check=True)

        # Update workflow status
        workflow.status = 'completed'
        workflow.end_time = django.utils.timezone.now()
        workflow.duration = (workflow.end_time - workflow.start_time).total_seconds()
        workflow.progress = 100.0
        workflow.processed_items = num_accounts
        workflow.successful_items = num_accounts
        workflow.save()

        # Count the number of accounts created
        account_count = Accounts.objects.filter(campaign_id=campaign_id).count()
        logger.info(f"Created {account_count} accounts for campaign {campaign_id}")

        return {
            'campaign_id': campaign_id,
            'workflow_id': str(workflow.id),
            'account_count': account_count
        }

    except Exception as e:
        logger.exception(f"Error simulating account collection: {str(e)}")
        # Update workflow status
        workflow.status = 'failed'
        workflow.end_time = django.utils.timezone.now()
        workflow.duration = (workflow.end_time - workflow.start_time).total_seconds()
        workflow.save()
        raise

def simulate_tag_analysis(**context):
    """
    Simulate the tag analysis phase.
    """
    # Get the configuration from the previous task
    collection_result = context['task_instance'].xcom_pull(task_ids='simulate_account_collection')
    campaign_id = collection_result['campaign_id']
    account_count = collection_result['account_count']

    logger.info(f"Simulating tag analysis for campaign {campaign_id}")
    logger.info(f"Analyzing {account_count} accounts")

    # Set up Django environment
    pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
    django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")
    script_dir = os.path.join(django_dir, "campaigns", "scripts")

    # Add Django directory to path if not already there
    if django_dir not in sys.path:
        sys.path.append(django_dir)

    # Set up Django environment
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

    # Import Django and set up
    import django
    django.setup()

    # Import models
    from campaigns.models import Campaign, WorkflowExecution, WorkflowProgressUpdate
    from instagram.models import Accounts, WhiteListEntry

    # Get campaign
    campaign = Campaign.objects.get(id=campaign_id)

    # Create workflow execution
    workflow = WorkflowExecution.objects.create(
        id=django.utils.crypto.get_random_string(32),
        campaign=campaign,
        workflow_name='tag_analysis.pygraph',
        workflow_path='/usr/local/lib/python3.10/dist-packages/PyFlow/ENGINE/INSTA/WORKFLOW/tag_analysis.pygraph',
        workflow_type='analysis',
        status='running',
        start_time=django.utils.timezone.now(),
        total_items=account_count
    )

    # Execute the simulation script
    try:
        # Check if the script exists
        script_path = os.path.join(script_dir, 'simulate_tag_analysis.py')
        if not os.path.exists(script_path):
            # If not, use the Django management command
            from django.core.management import call_command
            logger.info("Using Django management command to simulate tag analysis")
            # The simulate_campaign command already does tag analysis after account collection
            # So we don't need to do anything here
        else:
            # Execute the script
            logger.info(f"Executing script: {script_path}")
            import subprocess
            cmd = [sys.executable, script_path, '--campaign_id', campaign_id]
            subprocess.run(cmd, check=True)

        # Update workflow status
        workflow.status = 'completed'
        workflow.end_time = django.utils.timezone.now()
        workflow.duration = (workflow.end_time - workflow.start_time).total_seconds()
        workflow.progress = 100.0
        workflow.processed_items = account_count
        workflow.successful_items = account_count
        workflow.save()

        # Count the number of whitelist entries created
        whitelist_count = WhiteListEntry.objects.filter(account__campaign_id=campaign_id).count()
        logger.info(f"Created {whitelist_count} whitelist entries for campaign {campaign_id}")

        return {
            'campaign_id': campaign_id,
            'workflow_id': str(workflow.id),
            'account_count': account_count,
            'whitelist_count': whitelist_count
        }

    except Exception as e:
        logger.exception(f"Error simulating tag analysis: {str(e)}")
        # Update workflow status
        workflow.status = 'failed'
        workflow.end_time = django.utils.timezone.now()
        workflow.duration = (workflow.end_time - workflow.start_time).total_seconds()
        workflow.save()
        raise

def update_campaign_status(**context):
    """
    Update the campaign status to completed.
    """
    # Get the configuration from the previous task
    analysis_result = context['task_instance'].xcom_pull(task_ids='simulate_tag_analysis')
    campaign_id = analysis_result['campaign_id']
    account_count = analysis_result['account_count']
    whitelist_count = analysis_result['whitelist_count']

    logger.info(f"Updating campaign status for campaign {campaign_id}")

    # Set up Django environment
    pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
    django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")

    # Add Django directory to path if not already there
    if django_dir not in sys.path:
        sys.path.append(django_dir)

    # Set up Django environment
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

    # Import Django and set up
    import django
    django.setup()

    # Import models
    from campaigns.models import Campaign, CampaignResult
    from django.utils import timezone

    # Get campaign
    campaign = Campaign.objects.get(id=campaign_id)

    # Update campaign status
    campaign.status = 'completed'
    campaign.updated_at = timezone.now()
    campaign.save()

    # Create or update campaign result
    result, created = CampaignResult.objects.get_or_create(campaign=campaign)
    result.total_accounts_found = account_count
    result.total_accounts_processed = account_count
    result.total_accounts_pending = 0
    result.total_accounts_tagged = account_count
    result.total_accounts_whitelisted = whitelist_count
    result.last_processed_at = timezone.now()
    result.save()

    logger.info(f"Updated campaign status to 'completed' for campaign {campaign_id}")
    logger.info(f"Campaign result: {account_count} accounts processed, {whitelist_count} accounts whitelisted")

    return {
        'campaign_id': campaign_id,
        'status': 'completed',
        'account_count': account_count,
        'whitelist_count': whitelist_count
    }

def send_success_notification(**context):
    """
    Send a success notification.
    """
    # Get the campaign ID from the context
    campaign_id = context['task_instance'].xcom_pull(task_ids='update_campaign_status')['campaign_id']
    account_count = context['task_instance'].xcom_pull(task_ids='update_campaign_status')['account_count']
    whitelist_count = context['task_instance'].xcom_pull(task_ids='update_campaign_status')['whitelist_count']

    # Get the notification email from the DAG run configuration
    conf = context['dag_run'].conf
    notify_email = conf.get('notify_email', default_email)

    # Create the success message
    message = f"""
    Campaign Simulation Completed Successfully

    Campaign ID: {campaign_id}
    Accounts Collected: {account_count}
    Accounts Whitelisted: {whitelist_count}

    The campaign simulation has completed successfully. You can view the results in the web interface.
    """

    # Return the message and recipient for the EmailOperator
    return {
        'subject': f'Campaign Simulation Completed: {campaign_id}',
        'html_content': message.replace('\n', '<br>'),
        'to': [notify_email]
    }

def send_failure_notification(**context):
    """
    Send a failure notification.
    """
    # Get the exception information
    exception = context.get('exception')
    task_id = context['task_instance'].task_id
    dag_id = context['task_instance'].dag_id
    execution_date = context['execution_date']

    # Get the campaign ID from the context if available
    try:
        campaign_id = context['task_instance'].xcom_pull(task_ids='validate_campaign_config')['campaign_id']
    except:
        # If not available, get it from the DAG run configuration
        conf = context['dag_run'].conf
        campaign_id = conf.get('campaign_id', 'Unknown')

    # Get the notification email from the DAG run configuration
    conf = context['dag_run'].conf
    notify_email = conf.get('notify_email', default_email)

    # Create the failure message
    message = f"""
    Campaign Simulation Failed

    Campaign ID: {campaign_id}
    Task: {task_id}
    DAG: {dag_id}
    Execution Date: {execution_date}

    Error: {str(exception)}

    Please check the Airflow logs for more details.
    """

    # Return the message and recipient for the EmailOperator
    return {
        'subject': f'Campaign Simulation Failed: {campaign_id}',
        'html_content': message.replace('\n', '<br>'),
        'to': [notify_email]
    }

# Define the tasks
start_task = DummyOperator(
    task_id='start_simulation',
    dag=dag,
)

validate_config_task = PythonOperator(
    task_id='validate_campaign_config',
    python_callable=validate_campaign_config,
    provide_context=True,
    dag=dag,
)

simulate_collection_task = PythonOperator(
    task_id='simulate_account_collection',
    python_callable=simulate_account_collection,
    provide_context=True,
    dag=dag,
)

simulate_analysis_task = PythonOperator(
    task_id='simulate_tag_analysis',
    python_callable=simulate_tag_analysis,
    provide_context=True,
    dag=dag,
)

update_status_task = PythonOperator(
    task_id='update_campaign_status',
    python_callable=update_campaign_status,
    provide_context=True,
    dag=dag,
)

success_notification_task = PythonOperator(
    task_id='send_success_notification',
    python_callable=send_success_notification,
    provide_context=True,
    trigger_rule=TriggerRule.ALL_SUCCESS,
    dag=dag,
)

success_email_task = EmailOperator(
    task_id='send_success_email',
    to="{{ task_instance.xcom_pull(task_ids='send_success_notification')['to'] }}",
    subject="{{ task_instance.xcom_pull(task_ids='send_success_notification')['subject'] }}",
    html_content="{{ task_instance.xcom_pull(task_ids='send_success_notification')['html_content'] }}",
    trigger_rule=TriggerRule.ALL_SUCCESS,
    dag=dag,
)

failure_notification_task = PythonOperator(
    task_id='send_failure_notification',
    python_callable=send_failure_notification,
    provide_context=True,
    trigger_rule=TriggerRule.ONE_FAILED,
    dag=dag,
)

failure_email_task = EmailOperator(
    task_id='send_failure_email',
    to="{{ task_instance.xcom_pull(task_ids='send_failure_notification')['to'] }}",
    subject="{{ task_instance.xcom_pull(task_ids='send_failure_notification')['subject'] }}",
    html_content="{{ task_instance.xcom_pull(task_ids='send_failure_notification')['html_content'] }}",
    trigger_rule=TriggerRule.ONE_FAILED,
    dag=dag,
)

end_task = DummyOperator(
    task_id='end_simulation',
    trigger_rule=TriggerRule.NONE_FAILED_OR_SKIPPED,
    dag=dag,
)

# Define the task dependencies
start_task >> validate_config_task >> simulate_collection_task >> simulate_analysis_task >> update_status_task

# Success path
update_status_task >> success_notification_task >> success_email_task >> end_task

# Failure paths
validate_config_task >> failure_notification_task
simulate_collection_task >> failure_notification_task
simulate_analysis_task >> failure_notification_task
update_status_task >> failure_notification_task

failure_notification_task >> failure_email_task
