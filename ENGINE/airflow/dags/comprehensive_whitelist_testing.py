"""
Comprehensive Whitelist Testing DAG

This DAG provides a complete end-to-end testing system for campaign whitelist functionality.
It simulates realistic account collection, applies tag analysis, generates whitelist entries,
and provides comprehensive analytics data for testing the whitelist dashboard.

The DAG performs the following tasks:
1. Validates the campaign configuration
2. Simulates realistic account collection with diverse account types
3. Applies tag analysis based on campaign tag conditions
4. Generates whitelist entries with appropriate privileges
5. Creates workflow execution records for tracking
6. Updates campaign status and results

Configuration Parameters:
- campaign_id: ID of the campaign to test
- num_accounts: Number of accounts to simulate (default: 150)
- clear_existing: Whether to clear existing data (default: true)
- notify_email: Email for notifications (optional)
"""

import os
import sys
import logging
from datetime import datetime, timedelta

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import BashOperator
from airflow.operators.dummy import DummyOperator
from airflow.utils.dates import days_ago
from airflow.utils.trigger_rule import TriggerRule
from airflow.models import Variable

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Get default email from Airflow configuration
try:
    default_email = Variable.get("default_notification_email", "<EMAIL>")
except:
    default_email = "<EMAIL>"

# Default arguments for the DAG
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': True,
    'email_on_retry': False,
    'email': default_email,
    'retries': 2,
    'retry_delay': timedelta(minutes=3),
    'execution_timeout': timedelta(hours=1),
}

# Create the DAG
dag = DAG(
    'comprehensive_whitelist_testing',
    default_args=default_args,
    description='Comprehensive whitelist testing system',
    schedule_interval=None,  # This DAG is triggered manually
    start_date=days_ago(1),
    tags=['campaign', 'whitelist', 'testing'],
    catchup=False,
    max_active_runs=1,
    concurrency=3
)

def validate_testing_config(**context):
    """
    Validate the testing configuration from the trigger.
    """
    try:
        # Get the configuration from the trigger
        conf = context['dag_run'].conf
        logger.info(f"Testing configuration: {conf}")

        # Check if configuration is empty
        if not conf:
            raise ValueError("Empty testing configuration")

        # Validate required fields
        required_fields = ['campaign_id']
        missing_fields = [field for field in required_fields if field not in conf]
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

        # Get campaign ID
        campaign_id = conf['campaign_id']
        if not campaign_id:
            raise ValueError("Empty campaign_id")

        # Get optional parameters with defaults
        num_accounts = conf.get('num_accounts', 150)
        clear_existing = conf.get('clear_existing', True)
        notify_email = conf.get('notify_email', default_email)

        # Validate num_accounts
        if not isinstance(num_accounts, int) or num_accounts <= 0:
            logger.warning(f"Invalid num_accounts: {num_accounts}. Using default value of 150.")
            num_accounts = 150

        # Set up Django environment
        pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
        django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")

        # Add Django directory to path if not already there
        if django_dir not in sys.path:
            sys.path.append(django_dir)

        # Set up Django environment
        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

        # Import Django and set up
        import django
        django.setup()

        # Import models
        from campaigns.models import Campaign

        # Check if campaign exists
        try:
            campaign = Campaign.objects.get(id=campaign_id)
            logger.info(f"Found campaign: {campaign.name}")
        except Campaign.DoesNotExist:
            raise ValueError(f"Campaign with ID {campaign_id} does not exist")

        # Update campaign status to running
        campaign.status = 'running'
        campaign.save()
        logger.info(f"Updated campaign status to 'running'")

        # Return validated configuration
        return {
            'campaign_id': campaign_id,
            'campaign_name': campaign.name,
            'num_accounts': num_accounts,
            'clear_existing': clear_existing,
            'notify_email': notify_email
        }

    except Exception as e:
        logger.exception(f"Error validating testing configuration: {str(e)}")
        raise

def run_whitelist_simulation(**context):
    """
    Run the comprehensive whitelist simulation using Django management command.
    """
    # Get the configuration from the previous task
    conf = context['task_instance'].xcom_pull(task_ids='validate_testing_config')
    campaign_id = conf['campaign_id']
    num_accounts = conf['num_accounts']
    clear_existing = conf['clear_existing']

    logger.info(f"Running whitelist simulation for campaign {campaign_id}")
    logger.info(f"Parameters: {num_accounts} accounts, clear_existing={clear_existing}")

    # Set up Django environment
    pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
    django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")

    # Add Django directory to path if not already there
    if django_dir not in sys.path:
        sys.path.append(django_dir)

    # Set up Django environment
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

    # Import Django and set up
    import django
    django.setup()

    # Import and run the management command
    from django.core.management import call_command
    from io import StringIO
    import sys

    # Capture command output
    old_stdout = sys.stdout
    sys.stdout = captured_output = StringIO()

    try:
        # Build command arguments
        cmd_args = [campaign_id, '--num-accounts', str(num_accounts)]
        if clear_existing:
            cmd_args.append('--clear-existing')

        # Run the simulation command
        call_command('simulate_whitelist_testing', *cmd_args)

        # Get the output
        output = captured_output.getvalue()
        logger.info(f"Simulation command output:\n{output}")

        # Restore stdout
        sys.stdout = old_stdout

        # Get final statistics
        from campaigns.models import Campaign
        from instagram.models import Accounts, WhiteListEntry

        campaign = Campaign.objects.get(id=campaign_id)
        account_count = Accounts.objects.filter(campaign_id=campaign_id).count()
        whitelist_count = WhiteListEntry.objects.filter(account__campaign_id=campaign_id).count()

        logger.info(f"Simulation completed: {account_count} accounts, {whitelist_count} whitelist entries")

        return {
            'campaign_id': campaign_id,
            'campaign_name': conf['campaign_name'],
            'account_count': account_count,
            'whitelist_count': whitelist_count,
            'simulation_output': output
        }

    except Exception as e:
        # Restore stdout
        sys.stdout = old_stdout
        logger.exception(f"Error running whitelist simulation: {str(e)}")
        raise

    finally:
        # Ensure stdout is restored
        if sys.stdout != old_stdout:
            sys.stdout = old_stdout

def verify_whitelist_data(**context):
    """
    Verify that the whitelist data was created correctly and calculate analytics.
    """
    # Get the simulation results
    simulation_result = context['task_instance'].xcom_pull(task_ids='run_whitelist_simulation')
    campaign_id = simulation_result['campaign_id']
    account_count = simulation_result['account_count']
    whitelist_count = simulation_result['whitelist_count']

    logger.info(f"Verifying whitelist data for campaign {campaign_id}")

    # Set up Django environment
    pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
    django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")

    if django_dir not in sys.path:
        sys.path.append(django_dir)

    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

    import django
    django.setup()

    # Import models
    from campaigns.models import Campaign, CampaignTag, TagAnalysisResult
    from instagram.models import Accounts, WhiteListEntry

    # Get campaign and related data
    campaign = Campaign.objects.get(id=campaign_id)
    accounts = Accounts.objects.filter(campaign_id=campaign_id)
    whitelist_entries = WhiteListEntry.objects.filter(account__campaign_id=campaign_id)
    tag_results = TagAnalysisResult.objects.filter(campaign=campaign)
    campaign_tags = CampaignTag.objects.filter(campaign=campaign)

    # Calculate analytics
    tagged_accounts_count = tag_results.filter(matches=True).values('account').distinct().count()
    conversion_rate = (whitelist_count / tagged_accounts_count * 100) if tagged_accounts_count > 0 else 0

    # Privilege distribution
    dm_count = whitelist_entries.filter(dm=True).count()
    follow_count = whitelist_entries.filter(follow=True).count()
    comment_count = whitelist_entries.filter(comment=True).count()
    like_count = whitelist_entries.filter(post_like=True).count()
    discover_count = whitelist_entries.filter(discover=True).count()
    favorite_count = whitelist_entries.filter(favorite=True).count()

    # Quality metrics
    verified_count = whitelist_entries.filter(account__is_verified=True).count()
    high_follower_count = whitelist_entries.filter(account__followers__gt=50000).count()

    # Tag effectiveness
    tag_effectiveness = {}
    for campaign_tag in campaign_tags:
        tag_matches = tag_results.filter(tag=campaign_tag.tag, matches=True).count()
        tag_effectiveness[campaign_tag.tag.name] = tag_matches

    verification_results = {
        'campaign_id': campaign_id,
        'campaign_name': simulation_result['campaign_name'],
        'total_accounts': account_count,
        'tagged_accounts': tagged_accounts_count,
        'whitelist_entries': whitelist_count,
        'conversion_rate': round(conversion_rate, 2),
        'privilege_distribution': {
            'dm': dm_count,
            'follow': follow_count,
            'comment': comment_count,
            'like': like_count,
            'discover': discover_count,
            'favorite': favorite_count
        },
        'quality_metrics': {
            'verified_accounts': verified_count,
            'high_follower_accounts': high_follower_count
        },
        'tag_effectiveness': tag_effectiveness,
        'campaign_tags_count': campaign_tags.count(),
        'tag_results_count': tag_results.count()
    }

    logger.info(f"Verification completed: {verification_results}")

    return verification_results

def send_completion_notification(**context):
    """
    Send a completion notification with test results.
    """
    # Get verification results
    verification_result = context['task_instance'].xcom_pull(task_ids='verify_whitelist_data')

    campaign_id = verification_result['campaign_id']
    campaign_name = verification_result['campaign_name']

    # Get notification email from configuration
    conf = context['dag_run'].conf
    notify_email = conf.get('notify_email', default_email)

    # Create detailed results message
    message = f"""
    Comprehensive Whitelist Testing Completed Successfully

    Campaign: {campaign_name} (ID: {campaign_id})

    📊 SIMULATION RESULTS:
    • Total Accounts Created: {verification_result['total_accounts']}
    • Accounts Tagged: {verification_result['tagged_accounts']}
    • Whitelist Entries: {verification_result['whitelist_entries']}
    • Conversion Rate: {verification_result['conversion_rate']}%

    🔑 PRIVILEGE DISTRIBUTION:
    • DM Permission: {verification_result['privilege_distribution']['dm']}
    • Follow Permission: {verification_result['privilege_distribution']['follow']}
    • Comment Permission: {verification_result['privilege_distribution']['comment']}
    • Like Permission: {verification_result['privilege_distribution']['like']}
    • Discover Permission: {verification_result['privilege_distribution']['discover']}
    • Favorite Permission: {verification_result['privilege_distribution']['favorite']}

    ⭐ QUALITY METRICS:
    • Verified Accounts: {verification_result['quality_metrics']['verified_accounts']}
    • High Follower Accounts (>50K): {verification_result['quality_metrics']['high_follower_accounts']}

    🏷️ TAG ANALYSIS:
    • Campaign Tags: {verification_result['campaign_tags_count']}
    • Tag Analysis Results: {verification_result['tag_results_count']}

    The whitelist testing simulation has completed successfully.
    You can now view the comprehensive analytics in the Whitelist Analytics Dashboard.

    Dashboard URL: http://127.0.0.1:8001/campaigns/{campaign_id}/simulation/
    """

    logger.info(f"Sending completion notification to {notify_email}")
    logger.info(f"Test results summary: {verification_result}")

    return {
        'subject': f'Whitelist Testing Completed: {campaign_name}',
        'html_content': message.replace('\n', '<br>'),
        'to': [notify_email],
        'verification_result': verification_result
    }

# Define the tasks
start_task = DummyOperator(
    task_id='start_whitelist_testing',
    dag=dag,
)

validate_config_task = PythonOperator(
    task_id='validate_testing_config',
    python_callable=validate_testing_config,
    provide_context=True,
    dag=dag,
    doc_md="""
    Validates the testing configuration and prepares the campaign for simulation.

    **Inputs:**
    - campaign_id: Required campaign ID
    - num_accounts: Number of accounts to simulate (default: 150)
    - clear_existing: Whether to clear existing data (default: true)
    - notify_email: Email for notifications (optional)

    **Outputs:**
    - Validated configuration parameters
    - Campaign status updated to 'running'
    """
)

run_simulation_task = PythonOperator(
    task_id='run_whitelist_simulation',
    python_callable=run_whitelist_simulation,
    provide_context=True,
    dag=dag,
    execution_timeout=timedelta(minutes=30),
    doc_md="""
    Runs the comprehensive whitelist simulation using Django management command.

    This task:
    1. Simulates realistic account collection based on campaign type
    2. Applies tag analysis against collected accounts
    3. Generates whitelist entries with appropriate privileges
    4. Creates workflow execution records

    **Inputs:**
    - Validated configuration from previous task

    **Outputs:**
    - Account count, whitelist count, and simulation results
    """
)

verify_data_task = PythonOperator(
    task_id='verify_whitelist_data',
    python_callable=verify_whitelist_data,
    provide_context=True,
    dag=dag,
    doc_md="""
    Verifies the generated whitelist data and calculates comprehensive analytics.

    This task validates:
    - Account creation and data quality
    - Tag analysis results and effectiveness
    - Whitelist entry generation and privilege distribution
    - Conversion rates and quality metrics

    **Inputs:**
    - Simulation results from previous task

    **Outputs:**
    - Comprehensive verification and analytics results
    """
)

notification_task = PythonOperator(
    task_id='send_completion_notification',
    python_callable=send_completion_notification,
    provide_context=True,
    dag=dag,
    trigger_rule=TriggerRule.ALL_SUCCESS,
    doc_md="""
    Sends a detailed completion notification with test results and analytics.

    **Inputs:**
    - Verification results from previous task
    - Notification email from configuration

    **Outputs:**
    - Email notification with comprehensive test results
    """
)

end_task = DummyOperator(
    task_id='whitelist_testing_complete',
    dag=dag,
    trigger_rule=TriggerRule.ALL_DONE,
)

# Define task dependencies
start_task >> validate_config_task >> run_simulation_task >> verify_data_task >> notification_task >> end_task
