"""
Campaign CEP (Customer Engagement Process) DAG for executing workflows on whitelisted accounts.

This DAG is responsible for:
1. Retrieving whitelisted accounts from a campaign
2. Determining the appropriate subscription tier (Bronze, Silver, Gold)
3. Configuring and executing PyFlow workflows for engagement actions based on tier
4. Tracking workflow progress and updating results

The DAG supports different subscription tiers:
- Bronze: Basic follow and like actions with lower rate limits (max 50 actions/day)
- Silver: Follow, like, and comment actions with medium rate limits (max 100 actions/day)
- Gold: All engagement actions (follow, like, comment, DM) with higher rate limits (max 200 actions/day)

Configuration Parameters:
- campaign_id: ID of the campaign (required)
- subscription_tier: Tier to use ('bronze', 'silver', 'gold') - determined automatically if not provided
- batch_size: Number of accounts to process in each batch (default: 10)
- max_actions: Maximum number of actions to perform (default based on tier)
- delay_between_actions: Delay between actions in seconds (default: 60)
"""
import os
import json
import time
import random
import logging
import sys
import subprocess
from datetime import datetime, timedelta

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from airflow.models import Variable
from airflow.hooks.base import BaseHook

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Default arguments for the DAG
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
}

# Create the DAG
dag = DAG(
    'campaign_cep',
    default_args=default_args,
    description='Execute CEP workflows on whitelisted accounts based on subscription tier',
    schedule_interval=None,  # This DAG is triggered manually
    start_date=days_ago(1),
    tags=['campaign', 'cep', 'engagement'],
)

# Tier-specific settings
TIER_SETTINGS = {
    'bronze': {
        'max_actions': 50,
        'allowed_actions': ['follow', 'like'],
        'workflow_dir': 'BRONZE_MEMBERSHIP'
    },
    'silver': {
        'max_actions': 100,
        'allowed_actions': ['follow', 'like', 'comment'],
        'workflow_dir': 'SILVER_MEMBERSHIP'
    },
    'gold': {
        'max_actions': 200,
        'allowed_actions': ['follow', 'like', 'comment', 'dm', 'all'],
        'workflow_dir': 'GOLD_MEMBERSHIP'
    }
}

def validate_cep_config(**context):
    """
    Validate the CEP configuration and prepare for execution.

    This function validates the campaign CEP configuration passed to the DAG run.
    It checks for required fields, validates field values, and sets default values
    for optional parameters. It also determines the subscription tier if not provided.

    Args:
        context: Airflow task context

    Returns:
        dict: Validated CEP configuration

    Raises:
        ValueError: If the configuration is invalid
    """
    try:
        # Get the campaign configuration from the DAG run configuration
        conf = context['dag_run'].conf

        # Log the configuration
        logger.info(f"Received CEP configuration: {conf}")

        # Check if configuration is empty
        if not conf:
            raise ValueError("Empty CEP configuration")

        # Validate required fields
        required_fields = ['campaign_id']
        missing_fields = [field for field in required_fields if field not in conf]
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

        # Get campaign ID
        campaign_id = conf['campaign_id']
        if not campaign_id:
            raise ValueError("Empty campaign_id")

        # Set up Django environment
        pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
        django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")

        # Add Django directory to path if not already there
        if django_dir not in sys.path:
            sys.path.append(django_dir)

        # Set up Django environment
        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

        # Determine subscription tier
        subscription_tier = conf.get('subscription_tier', None)

        try:
            import django
            django.setup()

            # Import models
            from campaigns.models import Campaign
            from campaigns.models.cep import CEPWorkflow
            from django.contrib.auth.models import User

            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)

            # If subscription tier not provided, determine from user's profile
            if not subscription_tier:
                # Get user's subscription tier from profile
                creator = campaign.creator
                if creator:
                    # This is a placeholder - in a real implementation, you would
                    # get the subscription tier from the user's profile or a subscription model
                    # For now, we'll use a simple mapping based on user ID
                    user_id = creator.id
                    if user_id % 3 == 0:
                        subscription_tier = 'gold'
                    elif user_id % 3 == 1:
                        subscription_tier = 'silver'
                    else:
                        subscription_tier = 'bronze'
                else:
                    # Default to bronze if no creator
                    subscription_tier = 'bronze'

            # Validate subscription tier
            if subscription_tier not in TIER_SETTINGS:
                logger.warning(f"Invalid subscription_tier: {subscription_tier}. Using default: bronze")
                subscription_tier = 'bronze'

            # Get tier settings
            tier_settings = TIER_SETTINGS[subscription_tier]

            # Get workflow type with validation
            workflow_type = conf.get('workflow_type', 'all')
            allowed_actions = tier_settings['allowed_actions']

            if workflow_type not in allowed_actions:
                if workflow_type == 'all' and subscription_tier != 'gold':
                    logger.warning(f"'all' workflow type not available for {subscription_tier} tier. Using 'follow' instead.")
                    workflow_type = 'follow'
                elif workflow_type not in ['follow', 'like', 'comment', 'dm']:
                    logger.warning(f"Invalid workflow_type: {workflow_type}. Using default: follow")
                    workflow_type = 'follow'
                elif workflow_type not in allowed_actions:
                    logger.warning(f"{workflow_type} not available for {subscription_tier} tier. Using {allowed_actions[0]} instead.")
                    workflow_type = allowed_actions[0]

            # Get batch size with validation
            batch_size = conf.get('batch_size', 10)
            if not isinstance(batch_size, int) or batch_size <= 0:
                logger.warning(f"Invalid batch_size: {batch_size}. Using default: 10")
                batch_size = 10

            # Get max actions with validation (use tier-specific default)
            max_actions = conf.get('max_actions', tier_settings['max_actions'])
            tier_max = tier_settings['max_actions']
            if not isinstance(max_actions, int) or max_actions <= 0 or max_actions > tier_max:
                logger.warning(f"Invalid max_actions: {max_actions}. Using tier default: {tier_max}")
                max_actions = tier_max

            # Get delay between actions with validation
            delay_between_actions = conf.get('delay_between_actions', 60)
            if not isinstance(delay_between_actions, (int, float)) or delay_between_actions < 0:
                logger.warning(f"Invalid delay_between_actions: {delay_between_actions}. Using default: 60")
                delay_between_actions = 60

            # Get additional parameters
            additional_params = {}

            # Add message template for DM workflows
            if workflow_type == 'dm' and 'message_template' in conf:
                additional_params['message_template'] = conf['message_template']

            # Add comment template for comment workflows
            if workflow_type == 'comment' and 'comment_template' in conf:
                additional_params['comment_template'] = conf['comment_template']

            # Add custom parameters for specific workflow types
            if 'custom_params' in conf and isinstance(conf['custom_params'], dict):
                additional_params.update(conf['custom_params'])

            # Create or update CEP workflow record
            try:
                cep_workflow = CEPWorkflow.objects.get(campaign_id=campaign_id)
                # Update existing workflow
                cep_workflow.subscription_tier = subscription_tier
                cep_workflow.status = 'pending'
                cep_workflow.save()
            except CEPWorkflow.DoesNotExist:
                # Create new workflow
                cep_workflow = CEPWorkflow.objects.create(
                    campaign_id=campaign_id,
                    subscription_tier=subscription_tier,
                    status='pending'
                )

            # Log successful validation
            logger.info(f"CEP configuration validated successfully for campaign {campaign_id} with {subscription_tier} tier")

            # Return the validated configuration
            validated_conf = {
                'campaign_id': campaign_id,
                'subscription_tier': subscription_tier,
                'workflow_type': workflow_type,
                'batch_size': batch_size,
                'max_actions': max_actions,
                'delay_between_actions': delay_between_actions,
                'workflow_dir': tier_settings['workflow_dir'],
                'cep_workflow_id': str(cep_workflow.id)
            }

            # Add additional parameters if any
            if additional_params:
                validated_conf['additional_params'] = additional_params

            return validated_conf

        except Exception as e:
            # Log Django error
            logger.exception(f"Error accessing Django models: {str(e)}")

            # Fallback to default tier
            if not subscription_tier:
                subscription_tier = 'bronze'

            # Validate subscription tier
            if subscription_tier not in TIER_SETTINGS:
                subscription_tier = 'bronze'

            # Get tier settings
            tier_settings = TIER_SETTINGS[subscription_tier]

            # Return basic configuration
            return {
                'campaign_id': campaign_id,
                'subscription_tier': subscription_tier,
                'workflow_type': 'follow',  # Default to follow for fallback
                'batch_size': 10,
                'max_actions': tier_settings['max_actions'],
                'delay_between_actions': 60,
                'workflow_dir': tier_settings['workflow_dir']
            }

    except ValueError as e:
        # Log validation error
        logger.error(f"CEP configuration validation error: {str(e)}")
        raise

    except Exception as e:
        # Log unexpected error
        logger.exception(f"Unexpected error validating CEP configuration: {str(e)}")
        raise ValueError(f"Unexpected error validating CEP configuration: {str(e)}")

def retrieve_whitelist(**context):
    """
    Retrieve whitelisted accounts for the campaign.

    This function retrieves the whitelisted accounts for the campaign from the database.
    It filters the accounts based on the workflow type and returns the accounts that
    should be processed by the workflow.

    Args:
        context: Airflow task context

    Returns:
        dict: Whitelist information
    """
    # Get the configuration from the previous task
    conf = context['task_instance'].xcom_pull(task_ids='validate_cep_config')

    # Get campaign ID
    campaign_id = conf['campaign_id']

    # Get workflow type
    workflow_type = conf['workflow_type']

    # Get subscription tier
    subscription_tier = conf['subscription_tier']

    # Log the retrieval
    logger.info(f"Retrieving whitelist for campaign: {campaign_id} with {subscription_tier} tier")

    # Set up Django environment
    pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
    django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")

    # Add Django directory to path if not already there
    if django_dir not in sys.path:
        sys.path.append(django_dir)

    # Set up Django environment
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

    try:
        import django
        django.setup()

        # Import models
        from campaigns.models import Campaign
        from campaigns.models.cep import CEPWorkflow
        from instagram.models import Accounts, WhiteListEntry

        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)

        # Get whitelisted accounts for campaign
        whitelist_entries = WhiteListEntry.objects.filter(
            account__campaign_id=str(campaign.id)
        ).select_related('account')

        # Get whitelist count
        whitelist_count = whitelist_entries.count()

        # Update CEP workflow with whitelist count
        if 'cep_workflow_id' in conf:
            try:
                cep_workflow = CEPWorkflow.objects.get(id=conf['cep_workflow_id'])
                cep_workflow.whitelist_count = whitelist_count
                cep_workflow.save(update_fields=['whitelist_count'])
            except CEPWorkflow.DoesNotExist:
                logger.warning(f"CEP workflow {conf['cep_workflow_id']} not found")

        # Filter based on workflow type if not 'all'
        if workflow_type != 'all':
            if workflow_type == 'follow':
                whitelist_entries = whitelist_entries.filter(follow=True)
            elif workflow_type == 'like':
                whitelist_entries = whitelist_entries.filter(post_like=True)
            elif workflow_type == 'comment':
                whitelist_entries = whitelist_entries.filter(comment=True)
            elif workflow_type == 'dm':
                whitelist_entries = whitelist_entries.filter(dm=True)

        # Convert to list of dictionaries
        whitelist = []
        for entry in whitelist_entries:
            whitelist.append({
                'username': entry.account.username,
                'tags': entry.tags,
                'dm': entry.dm,
                'follow': entry.follow,
                'like': entry.post_like,
                'comment': entry.comment,
                'favorite': entry.favorite,
                'discover': entry.discover
            })

        # Log the number of accounts retrieved
        logger.info(f"Retrieved {len(whitelist)} whitelisted accounts for campaign: {campaign_id}")

        # Return the whitelist
        return {
            'campaign_id': campaign_id,
            'campaign_name': campaign.name,
            'whitelist': whitelist,
            'subscription_tier': subscription_tier,
            'workflow_type': conf['workflow_type'],
            'batch_size': conf['batch_size'],
            'max_actions': conf['max_actions'],
            'delay_between_actions': conf['delay_between_actions'],
            'workflow_dir': conf['workflow_dir'],
            'additional_params': conf.get('additional_params', {}),
            'cep_workflow_id': conf.get('cep_workflow_id')
        }

    except Exception as e:
        # Log error
        error_msg = f"Error retrieving whitelist: {str(e)}"
        logger.exception(error_msg)

        # Generate test data for development/testing purposes
        logger.warning("Generating test whitelist data for development/testing")

        whitelist = [
            {
                'username': f'user_{i}',
                'tags': ['travel', 'food'] if i % 2 == 0 else ['photography', 'fashion'],
                'dm': i % 3 == 0,
                'follow': i % 2 == 0,
                'like': True,
                'comment': i % 4 == 0,
                'favorite': i % 5 == 0,
                'discover': i % 6 == 0
            }
            for i in range(1, 16)  # Generate 15 test accounts
        ]

        # Filter based on workflow type if not 'all'
        if workflow_type != 'all':
            if workflow_type == 'follow':
                whitelist = [account for account in whitelist if account['follow']]
            elif workflow_type == 'like':
                whitelist = [account for account in whitelist if account['like']]
            elif workflow_type == 'comment':
                whitelist = [account for account in whitelist if account['comment']]
            elif workflow_type == 'dm':
                whitelist = [account for account in whitelist if account['dm']]

        # Log the number of accounts in test data
        logger.info(f"Generated {len(whitelist)} test whitelist accounts for campaign: {campaign_id}")

        # Return the test whitelist
        return {
            'campaign_id': campaign_id,
            'campaign_name': f"Campaign {campaign_id}",
            'whitelist': whitelist,
            'subscription_tier': subscription_tier,
            'workflow_type': conf['workflow_type'],
            'batch_size': conf['batch_size'],
            'max_actions': conf['max_actions'],
            'delay_between_actions': conf['delay_between_actions'],
            'workflow_dir': conf['workflow_dir'],
            'additional_params': conf.get('additional_params', {}),
            'is_test_data': True
        }

def prepare_workflow_batches(**context):
    """
    Prepare batches of accounts for workflow execution.

    This function prepares batches of accounts for workflow execution based on
    the subscription tier and workflow type.

    Args:
        context: Airflow task context

    Returns:
        dict: Workflow batch information
    """
    # Get the data from the previous task
    data = context['task_instance'].xcom_pull(task_ids='retrieve_whitelist')

    # Get campaign ID
    campaign_id = data['campaign_id']

    # Get whitelist
    whitelist = data['whitelist']

    # Get workflow type
    workflow_type = data['workflow_type']

    # Get subscription tier
    subscription_tier = data['subscription_tier']

    # Get batch size
    batch_size = data['batch_size']

    # Get max actions
    max_actions = data['max_actions']

    # Log the preparation
    logger.info(f"Preparing workflow batches for campaign: {campaign_id} with {subscription_tier} tier")

    # Filter accounts based on workflow type
    filtered_accounts = []
    if workflow_type == 'all':
        filtered_accounts = whitelist
    elif workflow_type == 'follow':
        filtered_accounts = [account for account in whitelist if account['follow']]
    elif workflow_type == 'like':
        filtered_accounts = [account for account in whitelist if account['like']]
    elif workflow_type == 'comment':
        filtered_accounts = [account for account in whitelist if account['comment']]
    elif workflow_type == 'dm':
        filtered_accounts = [account for account in whitelist if account['dm']]

    # Limit to max actions
    filtered_accounts = filtered_accounts[:max_actions]

    # Create batches
    batches = []
    for i in range(0, len(filtered_accounts), batch_size):
        batch = filtered_accounts[i:i+batch_size]
        batches.append(batch)

    # Log the batches
    logger.info(f"Prepared {len(batches)} batches with {len(filtered_accounts)} accounts for workflow type: {workflow_type}")

    # Return the batches
    return {
        'campaign_id': campaign_id,
        'campaign_name': data.get('campaign_name', f"Campaign {campaign_id}"),
        'subscription_tier': subscription_tier,
        'workflow_type': workflow_type,
        'batches': batches,
        'total_accounts': len(filtered_accounts),
        'delay_between_actions': data['delay_between_actions'],
        'workflow_dir': data['workflow_dir'],
        'additional_params': data.get('additional_params', {}),
        'cep_workflow_id': data.get('cep_workflow_id')
    }

def execute_workflow(**context):
    """
    Execute PyFlow workflow for a batch of accounts.

    This function executes the appropriate PyFlow workflow for each batch of accounts
    based on the subscription tier and workflow type. It tracks the execution progress
    and updates the campaign with the results.

    Args:
        context: Airflow task context

    Returns:
        dict: Workflow execution results
    """
    # Get the data from the previous task
    data = context['task_instance'].xcom_pull(task_ids='prepare_workflow_batches')

    # Get campaign ID
    campaign_id = data['campaign_id']

    # Get workflow type
    workflow_type = data['workflow_type']

    # Get subscription tier
    subscription_tier = data['subscription_tier']

    # Get batches
    batches = data['batches']

    # Get delay between actions
    delay_between_actions = data['delay_between_actions']

    # Get workflow directory
    workflow_dir = data['workflow_dir']

    # Get additional parameters if available
    additional_params = data.get('additional_params', {})

    # Log the execution
    logger.info(f"Executing {workflow_type} workflow for campaign: {campaign_id} with {subscription_tier} tier")
    logger.info(f"Processing {len(batches)} batches with {data['total_accounts']} accounts")

    # Set up Django environment for workflow tracking
    pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
    django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")

    # Add Django directory to path if not already there
    if django_dir not in sys.path:
        sys.path.append(django_dir)

    # Set up Django environment
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

    try:
        import django
        django.setup()

        # Import models
        from campaigns.models import Campaign
        from campaigns.models.workflow import WorkflowExecution
        from campaigns.models.cep import CEPWorkflow
        from campaigns.services.workflow_progress_service import WorkflowProgressService

        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)

        # Create workflow name
        workflow_name = f"campaign_{campaign_id}_cep_{workflow_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Determine workflow file based on subscription tier and workflow type
        workflow_file = os.path.join(
            pyflow_base_dir,
            "ENGINE",
            "INSTA",
            "WORKFLOW",
            "INTERACTIONS",
            workflow_dir,
            f"{workflow_type.upper()}.pygraph"
        )

        # Check if workflow file exists
        if not os.path.exists(workflow_file):
            # Fallback to default workflow file
            workflow_file = os.path.join(
                pyflow_base_dir,
                "ENGINE",
                "INSTA",
                "WORKFLOW",
                f"{workflow_type.upper()}.pygraph"
            )

            # Check if fallback workflow file exists
            if not os.path.exists(workflow_file):
                error_msg = f"PyFlow workflow file not found: {workflow_file}"
                logger.error(error_msg)
                raise FileNotFoundError(error_msg)

        # Create workflow execution record
        workflow_execution = WorkflowExecution.objects.create(
            campaign_id=campaign_id,
            workflow_name=workflow_name,
            workflow_path=workflow_file,
            workflow_type=workflow_type,
            status='pending',
            parameters=data,
            total_items=data['total_accounts'],
            log_file=os.path.join(pyflow_base_dir, "ENGINE", "logs", f"{workflow_name}.log")
        )

        # Update CEP workflow with workflow execution ID
        if 'cep_workflow_id' in data:
            try:
                cep_workflow = CEPWorkflow.objects.get(id=data['cep_workflow_id'])
                cep_workflow.status = 'running'
                cep_workflow.airflow_dag_id = context['dag'].dag_id
                cep_workflow.airflow_run_id = context['run_id']
                cep_workflow.started_at = timezone.now()
                cep_workflow.save()
            except CEPWorkflow.DoesNotExist:
                logger.warning(f"CEP workflow {data['cep_workflow_id']} not found")

        # Start tracking workflow progress
        progress_service = WorkflowProgressService()
        progress_service.start_tracking(workflow_execution)

        # Update workflow execution ID in XCom
        context['task_instance'].xcom_push(key='workflow_execution_id', value=str(workflow_execution.id))

        logger.info(f"Created workflow execution record: {workflow_execution.id}")

        # Process each batch
        results = []
        successful_actions = 0
        failed_actions = 0

        for i, batch in enumerate(batches):
            # Log the batch
            logger.info(f"Processing batch {i+1}/{len(batches)} with {len(batch)} accounts")

            # Extract usernames from batch
            usernames = [account['username'] for account in batch]

            # Build PyFlow command
            pyflow_command = f"pyflow -m run -f {workflow_file} --campaign_id {campaign_id} --usernames {','.join(usernames)} --num_retries 3"

            # Add workflow type if it's the 'all' workflow
            if workflow_type == 'all':
                pyflow_command += f" --workflow_type {workflow_type}"

            # Add delay between actions
            pyflow_command += f" --delay_between_actions {delay_between_actions}"

            # Add workflow name for tracking
            pyflow_command += f" --workflow_name {workflow_name}"

            # Add subscription tier
            pyflow_command += f" --subscription_tier {subscription_tier}"

            # Add additional parameters if available
            if 'message_template' in additional_params and workflow_type == 'dm':
                message_template = additional_params['message_template']
                pyflow_command += f" --message_template \"{message_template}\""

            if 'comment_template' in additional_params and workflow_type == 'comment':
                comment_template = additional_params['comment_template']
                pyflow_command += f" --comment_template \"{comment_template}\""

            # Execute the PyFlow workflow
            try:
                # Change to PyFlow engine directory
                os.chdir(pyflow_base_dir)

                # Execute the command
                process = subprocess.Popen(
                    pyflow_command,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True
                )

                # Monitor the process
                stdout = []
                for line in iter(process.stdout.readline, ''):
                    stdout.append(line)
                    logger.info(f"PyFlow: {line.strip()}")

                    # Update progress for CEP workflow
                    if 'cep_workflow_id' in data and line.startswith("PROGRESS:"):
                        try:
                            # Parse progress update
                            progress_parts = line.strip().split(":")
                            if len(progress_parts) == 2:
                                progress_values = progress_parts[1].split("/")
                                if len(progress_values) == 2:
                                    current = int(progress_values[0])
                                    total = int(progress_values[1])
                                    progress_percentage = (current / total) * 100 if total > 0 else 0

                                    # Update CEP workflow progress
                                    cep_workflow = CEPWorkflow.objects.get(id=data['cep_workflow_id'])
                                    if workflow_type == 'follow':
                                        cep_workflow.follow_progress = progress_percentage
                                    elif workflow_type == 'like':
                                        cep_workflow.like_progress = progress_percentage
                                    elif workflow_type == 'comment':
                                        cep_workflow.comment_progress = progress_percentage
                                    elif workflow_type == 'dm':
                                        cep_workflow.dm_progress = progress_percentage
                                    cep_workflow.save(update_fields=[f'{workflow_type}_progress', 'updated_at'])
                        except Exception as e:
                            logger.exception(f"Error updating CEP workflow progress: {str(e)}")

                # Wait for the process to complete
                process.wait()

                # Check return code
                if process.returncode != 0:
                    error_msg = f"PyFlow workflow failed with return code {process.returncode}"
                    logger.error(error_msg)

                    # Update batch results
                    batch_results = []
                    for account in batch:
                        batch_results.append({
                            'username': account['username'],
                            'success': False,
                            'error': error_msg
                        })

                    # Add to results
                    results.append({
                        'batch': i+1,
                        'accounts': batch_results,
                        'success': False,
                        'error': error_msg
                    })

                    # Update failed actions count
                    failed_actions += len(batch)
                else:
                    # Update batch results
                    batch_results = []
                    for account in batch:
                        batch_results.append({
                            'username': account['username'],
                            'success': True
                        })

                    # Add to results
                    results.append({
                        'batch': i+1,
                        'accounts': batch_results,
                        'success': True
                    })

                    # Update successful actions count
                    successful_actions += len(batch)

            except Exception as e:
                # Log error
                error_msg = f"Error executing PyFlow workflow: {str(e)}"
                logger.exception(error_msg)

                # Update batch results
                batch_results = []
                for account in batch:
                    batch_results.append({
                        'username': account['username'],
                        'success': False,
                        'error': str(e)
                    })

                # Add to results
                results.append({
                    'batch': i+1,
                    'accounts': batch_results,
                    'success': False,
                    'error': str(e)
                })

                # Update failed actions count
                failed_actions += len(batch)

        # Update workflow execution record
        workflow_execution.status = 'completed'
        workflow_execution.end_time = timezone.now()
        workflow_execution.duration = (workflow_execution.end_time - workflow_execution.start_time).total_seconds()
        workflow_execution.processed_items = successful_actions + failed_actions
        workflow_execution.successful_items = successful_actions
        workflow_execution.failed_items = failed_actions
        workflow_execution.progress = 100.0
        workflow_execution.results = {
            'batches': results,
            'successful_actions': successful_actions,
            'failed_actions': failed_actions,
            'total_accounts': data['total_accounts']
        }
        workflow_execution.save()

        # Update CEP workflow
        if 'cep_workflow_id' in data:
            try:
                cep_workflow = CEPWorkflow.objects.get(id=data['cep_workflow_id'])

                # Set progress to 100% for the current workflow type
                if workflow_type == 'follow':
                    cep_workflow.follow_progress = 100.0
                elif workflow_type == 'like':
                    cep_workflow.like_progress = 100.0
                elif workflow_type == 'comment':
                    cep_workflow.comment_progress = 100.0
                elif workflow_type == 'dm':
                    cep_workflow.dm_progress = 100.0

                # If this is the 'all' workflow, set all progress to 100%
                if workflow_type == 'all':
                    cep_workflow.follow_progress = 100.0
                    cep_workflow.like_progress = 100.0
                    cep_workflow.comment_progress = 100.0
                    cep_workflow.dm_progress = 100.0

                cep_workflow.save()
            except CEPWorkflow.DoesNotExist:
                logger.warning(f"CEP workflow {data['cep_workflow_id']} not found")

        # Return the results
        return {
            'success': True,
            'campaign_id': campaign_id,
            'subscription_tier': subscription_tier,
            'workflow_type': workflow_type,
            'workflow_execution_id': str(workflow_execution.id),
            'total_accounts': data['total_accounts'],
            'successful_actions': successful_actions,
            'failed_actions': failed_actions,
            'success_rate': (successful_actions / data['total_accounts'] * 100) if data['total_accounts'] > 0 else 0
        }

    except Exception as e:
        # Log error
        error_msg = f"Error executing workflow: {str(e)}"
        logger.exception(error_msg)

        # Update CEP workflow if available
        if 'cep_workflow_id' in data:
            try:
                cep_workflow = CEPWorkflow.objects.get(id=data['cep_workflow_id'])
                cep_workflow.status = 'failed'
                cep_workflow.completed_at = timezone.now()
                cep_workflow.save()
            except Exception:
                pass

        # Raise exception to mark task as failed
        raise RuntimeError(error_msg)

def update_workflow_results(**context):
    """
    Update campaign with workflow execution results.

    This function updates the campaign with the results of the workflow execution.
    It also updates the CEP workflow status to reflect the completion of the process.

    Args:
        context: Airflow task context

    Returns:
        dict: Updated campaign information
    """
    # Get the data from the previous task
    data = context['task_instance'].xcom_pull(task_ids='execute_workflow')

    # Check if execution was successful
    if not data or not data.get('success', False):
        error_msg = "Workflow execution failed or returned no data"
        logger.error(error_msg)
        return {
            'success': False,
            'error': error_msg
        }

    # Get campaign ID
    campaign_id = data['campaign_id']

    # Get workflow type
    workflow_type = data['workflow_type']

    # Get subscription tier
    subscription_tier = data['subscription_tier']

    # Get workflow execution ID
    workflow_execution_id = data['workflow_execution_id']

    # Log the update
    logger.info(f"Updating campaign {campaign_id} with {workflow_type} workflow results")

    # Set up Django environment
    pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
    django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")

    # Add Django directory to path if not already there
    if django_dir not in sys.path:
        sys.path.append(django_dir)

    # Set up Django environment
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

    try:
        import django
        django.setup()

        # Import models
        from campaigns.models import Campaign
        from campaigns.models.cep import CEPWorkflow
        from django.utils import timezone

        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)

        # Update campaign with workflow results
        campaign.updated_at = timezone.now()
        campaign.save()

        # Check if this is the last workflow in a CEP workflow
        cep_workflow_id = context['task_instance'].xcom_pull(
            task_ids='prepare_workflow_batches',
            key='cep_workflow_id'
        )

        if cep_workflow_id:
            try:
                cep_workflow = CEPWorkflow.objects.get(id=cep_workflow_id)

                # Check if all actions are completed
                all_completed = True

                # For Bronze tier, only follow and like are relevant
                if subscription_tier == 'bronze':
                    all_completed = (
                        cep_workflow.follow_progress == 100.0 and
                        cep_workflow.like_progress == 100.0
                    )
                # For Silver tier, follow, like, and comment are relevant
                elif subscription_tier == 'silver':
                    all_completed = (
                        cep_workflow.follow_progress == 100.0 and
                        cep_workflow.like_progress == 100.0 and
                        cep_workflow.comment_progress == 100.0
                    )
                # For Gold tier, all actions are relevant
                else:
                    all_completed = (
                        cep_workflow.follow_progress == 100.0 and
                        cep_workflow.like_progress == 100.0 and
                        cep_workflow.comment_progress == 100.0 and
                        cep_workflow.dm_progress == 100.0
                    )

                # If all actions are completed, mark the CEP workflow as completed
                if all_completed or workflow_type == 'all':
                    cep_workflow.status = 'completed'
                    cep_workflow.completed_at = timezone.now()
                    cep_workflow.save()

                    # Update campaign is_in_cep flag
                    campaign.is_in_cep = False
                    campaign.save(update_fields=['is_in_cep', 'updated_at'])

                    logger.info(f"CEP workflow {cep_workflow_id} completed successfully")
                else:
                    logger.info(f"CEP workflow {cep_workflow_id} has more actions to complete")

            except CEPWorkflow.DoesNotExist:
                logger.warning(f"CEP workflow {cep_workflow_id} not found")

        # Return the results
        return {
            'success': True,
            'campaign_id': campaign_id,
            'workflow_type': workflow_type,
            'subscription_tier': subscription_tier,
            'workflow_execution_id': workflow_execution_id,
            'message': f"Campaign {campaign_id} updated with {workflow_type} workflow results"
        }

    except Exception as e:
        # Log error
        error_msg = f"Error updating campaign with workflow results: {str(e)}"
        logger.exception(error_msg)

        # Return error
        return {
            'success': False,
            'error': error_msg
        }

# Define the tasks
validate_config_task = PythonOperator(
    task_id='validate_cep_config',
    python_callable=validate_cep_config,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=5),
    doc_md="""
    Validate the CEP configuration and prepare for execution.

    This task validates the campaign CEP configuration passed to the DAG run.
    It checks for required fields, validates field values, and sets default values
    for optional parameters. It also determines the subscription tier if not provided.

    **Inputs:**
    - DAG run configuration

    **Outputs:**
    - Validated CEP configuration
    """
)

retrieve_whitelist_task = PythonOperator(
    task_id='retrieve_whitelist',
    python_callable=retrieve_whitelist,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=10),
    doc_md="""
    Retrieve whitelisted accounts for the campaign.

    This task retrieves the whitelisted accounts for the campaign from the database.
    It filters the accounts based on the workflow type and returns the accounts that
    should be processed by the workflow.

    **Inputs:**
    - Validated CEP configuration

    **Outputs:**
    - Whitelist information
    """
)

prepare_batches_task = PythonOperator(
    task_id='prepare_workflow_batches',
    python_callable=prepare_workflow_batches,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=5),
    doc_md="""
    Prepare batches of accounts for workflow execution.

    This task prepares batches of accounts for workflow execution based on
    the subscription tier and workflow type.

    **Inputs:**
    - Whitelist information

    **Outputs:**
    - Workflow batch information
    """
)

execute_workflow_task = PythonOperator(
    task_id='execute_workflow',
    python_callable=execute_workflow,
    provide_context=True,
    dag=dag,
    retries=2,
    retry_delay=timedelta(minutes=5),
    execution_timeout=timedelta(hours=2),
    doc_md="""
    Execute PyFlow workflow for batches of accounts.

    This task executes the appropriate PyFlow workflow for each batch of accounts
    based on the subscription tier and workflow type. It tracks the execution progress
    and updates the campaign with the results.

    **Inputs:**
    - Workflow batch information

    **Outputs:**
    - Workflow execution results
    - Workflow execution ID (XCom)
    """
)

update_results_task = PythonOperator(
    task_id='update_workflow_results',
    python_callable=update_workflow_results,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=10),
    doc_md="""
    Update campaign with workflow execution results.

    This task updates the campaign with the results of the workflow execution.
    It also updates the CEP workflow status to reflect the completion of the process.

    **Inputs:**
    - Workflow execution results

    **Outputs:**
    - Updated campaign information
    """
)

# Define the task dependencies
validate_config_task >> retrieve_whitelist_task >> prepare_batches_task >> execute_workflow_task >> update_results_task