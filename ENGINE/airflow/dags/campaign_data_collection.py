"""
Campaign Data Collection DAG

This DAG processes campaign data collection requests from the CommandNetSolutions system.
It handles location-based, username-based, and mixed campaigns with different audience types.

The DAG performs the following tasks:
1. Validates the campaign configuration
2. Processes location and/or username targets
3. Runs the appropriate PyFlow workflow
4. Updates the campaign results in the database
5. Tracks workflow progress and updates campaign status

Configuration Parameters:
- campaign_id: ID of the campaign
- target_type: 'location', 'username', or 'mixed'
- audience_type: 'profile', 'followers', 'following', or 'both'
- location_targets: List of location targets (for location-based or mixed campaigns)
- username_targets: List of username targets (for username-based or mixed campaigns)
- max_accounts: (optional) Maximum number of accounts to collect
- min_followers: (optional) Minimum number of followers for collected accounts
- max_followers: (optional) Maximum number of followers for collected accounts
"""

from datetime import datetime, timedelta
import json
import random
import time
import logging
import os
import subprocess
import requests
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.bash import BashOperator
from airflow.models import Variable
from airflow.utils.dates import days_ago
from airflow.hooks.base import BaseHook

# Import resource manager utilities
from utils.resource_manager import (
    check_resources_available,
    register_workflow,
    wait_for_resources,
    unregister_workflow
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Default arguments for the DAG
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
}

# Create the DAG
dag = DAG(
    'campaign_data_collection',
    default_args=default_args,
    description='Process campaign data collection requests',
    schedule_interval=None,  # This DAG is triggered manually
    start_date=days_ago(1),
    tags=['campaign', 'data_collection'],
)

def validate_campaign_config(**context):
    """
    Validate the campaign configuration from the trigger.

    This function validates the campaign configuration passed to the DAG run.
    It checks for required fields, validates field values, and ensures that
    the appropriate targets are provided based on the target type.

    Args:
        context: Airflow task context

    Returns:
        dict: Validated campaign configuration

    Raises:
        ValueError: If the configuration is invalid
    """
    try:
        # Get the configuration from the trigger
        conf = context['dag_run'].conf

        # Log the configuration (with sensitive data redacted)
        safe_conf = conf.copy() if conf else {}
        if 'username_targets' in safe_conf:
            safe_conf['username_targets'] = f"[{len(safe_conf['username_targets'])} usernames]"
        logger.info(f"Campaign configuration: {safe_conf}")

        # Check if configuration is empty
        if not conf:
            raise ValueError("Empty campaign configuration")

        # Validate required fields
        required_fields = ['campaign_id', 'target_type', 'audience_type']
        missing_fields = [field for field in required_fields if field not in conf]
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

        # Validate campaign_id
        campaign_id = conf['campaign_id']
        if not campaign_id:
            raise ValueError("Empty campaign_id")

        # Validate target_type
        valid_target_types = ['location', 'username', 'mixed']
        if conf['target_type'] not in valid_target_types:
            raise ValueError(f"Invalid target_type: {conf['target_type']}. Must be one of: {', '.join(valid_target_types)}")

        # Validate audience_type
        valid_audience_types = ['profile', 'followers', 'following', 'both']
        if conf['audience_type'] not in valid_audience_types:
            raise ValueError(f"Invalid audience_type: {conf['audience_type']}. Must be one of: {', '.join(valid_audience_types)}")

        # Validate targets based on target_type
        if conf['target_type'] in ['location', 'mixed']:
            # For location-based or mixed campaigns, validate location targets
            if 'location_targets' not in conf:
                if conf['target_type'] == 'location':
                    raise ValueError("Missing location_targets for location-based campaign")
                else:
                    # For mixed campaigns, we need at least one type of target
                    if 'username_targets' not in conf:
                        raise ValueError("Mixed campaign requires at least one type of target (location or username)")
                    # Initialize empty location_targets for mixed campaigns without locations
                    conf['location_targets'] = []
            else:
                location_targets = conf['location_targets']
                if not isinstance(location_targets, list):
                    raise ValueError("location_targets must be a list")

                if conf['target_type'] == 'location' and not location_targets:
                    raise ValueError("Empty location_targets for location-based campaign")

                # Validate each location target
                for i, location in enumerate(location_targets):
                    if not isinstance(location, dict):
                        raise ValueError(f"Location target at index {i} must be a dictionary")

                    required_location_fields = ['location_id', 'city', 'country']
                    missing_location_fields = [field for field in required_location_fields if field not in location]
                    if missing_location_fields:
                        raise ValueError(f"Location target at index {i} is missing required fields: {', '.join(missing_location_fields)}")

        if conf['target_type'] in ['username', 'mixed']:
            # For username-based or mixed campaigns, validate username targets
            if 'username_targets' not in conf:
                if conf['target_type'] == 'username':
                    raise ValueError("Missing username_targets for username-based campaign")
                else:
                    # For mixed campaigns, we need at least one type of target
                    if 'location_targets' not in conf or not conf['location_targets']:
                        raise ValueError("Mixed campaign requires at least one type of target (location or username)")
                    # Initialize empty username_targets for mixed campaigns without usernames
                    conf['username_targets'] = []
            else:
                username_targets = conf['username_targets']
                if not isinstance(username_targets, list):
                    raise ValueError("username_targets must be a list")

                if conf['target_type'] == 'username' and not username_targets:
                    raise ValueError("Empty username_targets for username-based campaign")

                # Validate each username
                for i, username in enumerate(username_targets):
                    if not username:
                        raise ValueError(f"Empty username at index {i}")

        # For mixed campaigns, ensure at least one target type has data
        if conf['target_type'] == 'mixed':
            has_location_targets = 'location_targets' in conf and conf['location_targets']
            has_username_targets = 'username_targets' in conf and conf['username_targets']

            if not has_location_targets and not has_username_targets:
                raise ValueError("Mixed campaign requires at least one location target or username target")

        # Add additional validation for optional fields
        if 'max_accounts' in conf:
            max_accounts = conf['max_accounts']
            if not isinstance(max_accounts, int) or max_accounts <= 0:
                raise ValueError("max_accounts must be a positive integer")

        if 'min_followers' in conf:
            min_followers = conf['min_followers']
            if not isinstance(min_followers, int) or min_followers < 0:
                raise ValueError("min_followers must be a non-negative integer")

        if 'max_followers' in conf:
            max_followers = conf['max_followers']
            if not isinstance(max_followers, int) or max_followers < 0:
                raise ValueError("max_followers must be a non-negative integer")

            # Check if min_followers and max_followers are consistent
            if 'min_followers' in conf and max_followers < conf['min_followers']:
                raise ValueError("max_followers must be greater than or equal to min_followers")

        # Log successful validation
        logger.info(f"Campaign configuration validated successfully for campaign {campaign_id}")

        # Pass the validated configuration to the next task
        return conf

    except ValueError as e:
        # Log validation error
        logger.error(f"Campaign configuration validation error: {str(e)}")
        raise

    except Exception as e:
        # Log unexpected error
        logger.exception(f"Unexpected error validating campaign configuration: {str(e)}")
        raise ValueError(f"Unexpected error validating campaign configuration: {str(e)}")

def process_location_targets(**context):
    """
    Process location-based targets.
    """
    # Get the configuration from the previous task
    conf = context['task_instance'].xcom_pull(task_ids='validate_campaign_config')

    # Skip if not a location-based or mixed campaign
    if conf['target_type'] not in ['location', 'mixed']:
        logging.info("Not a location-based or mixed campaign, skipping location processing")
        return

    # Skip if no location targets in a mixed campaign
    if conf['target_type'] == 'mixed' and (not conf.get('location_targets') or len(conf['location_targets']) == 0):
        logging.info("Mixed campaign with no location targets, skipping location processing")
        return

    # Get the location targets
    location_targets = conf['location_targets']
    logging.info(f"Processing {len(location_targets)} location targets")

    # Process each location
    for location in location_targets:
        logging.info(f"Processing location: {location['city']}, {location['country']} (ID: {location['location_id']})")

        # Simulate processing time
        time.sleep(random.uniform(1, 3))

    # Return results
    total_accounts_found = random.randint(50, 200) * len(location_targets)
    total_accounts_processed = int(total_accounts_found * 0.8)  # 80% processed
    total_accounts_pending = total_accounts_found - total_accounts_processed

    return {
        'total_accounts_found': total_accounts_found,
        'total_accounts_processed': total_accounts_processed,
        'total_accounts_pending': total_accounts_pending,
        'source': 'location',
    }

def process_username_targets(**context):
    """
    Process username-based targets.
    """
    # Get the configuration from the previous task
    conf = context['task_instance'].xcom_pull(task_ids='validate_campaign_config')

    # Skip if not a username-based or mixed campaign
    if conf['target_type'] not in ['username', 'mixed']:
        logging.info("Not a username-based or mixed campaign, skipping username processing")
        return

    # Skip if no username targets in a mixed campaign
    if conf['target_type'] == 'mixed' and (not conf.get('username_targets') or len(conf['username_targets']) == 0):
        logging.info("Mixed campaign with no username targets, skipping username processing")
        return

    # Get the username targets
    username_targets = conf['username_targets']
    logging.info(f"Processing {len(username_targets)} username targets")

    # Process each username
    for username in username_targets:
        logging.info(f"Processing username: {username}")

        # Simulate processing time
        time.sleep(random.uniform(1, 3))

    # Return results with more realistic values
    # For username targets, we typically get the account plus some followers/following
    accounts_per_username = random.randint(10, 50)  # Each username might yield multiple accounts
    total_accounts_found = len(username_targets) * accounts_per_username
    total_accounts_processed = total_accounts_found  # Usually all accounts are processed for username targets

    return {
        'total_accounts_found': total_accounts_found,
        'total_accounts_processed': total_accounts_processed,
        'total_accounts_pending': 0,
        'source': 'username',
    }

def update_campaign_results(**context):
    """
    Update the campaign results in the database.

    This function updates the campaign results in the database based on the
    results of the workflow execution. It also updates the campaign status
    to reflect the completion of the data collection process.

    Args:
        context: Airflow task context

    Returns:
        dict: Updated campaign information
    """
    # Get the configuration from the previous task
    conf = context['task_instance'].xcom_pull(task_ids='validate_campaign_config')

    # Get campaign ID
    campaign_id = conf['campaign_id']

    # Get workflow execution results
    workflow_results = context['task_instance'].xcom_pull(task_ids='run_pyflow_workflow')

    # Get the results from the appropriate tasks based on target type
    location_results = None
    username_results = None

    if conf['target_type'] in ['location', 'mixed']:
        location_results = context['task_instance'].xcom_pull(task_ids='process_location_targets')

    if conf['target_type'] in ['username', 'mixed']:
        username_results = context['task_instance'].xcom_pull(task_ids='process_username_targets')

    # Combine results for mixed campaigns or use the appropriate single result
    if conf['target_type'] == 'mixed':
        # Initialize with zeros
        target_results = {
            'total_accounts_found': 0,
            'total_accounts_processed': 0,
            'total_accounts_pending': 0,
            'sources': []
        }

        # Add location results if available
        if location_results:
            target_results['total_accounts_found'] += location_results.get('total_accounts_found', 0)
            target_results['total_accounts_processed'] += location_results.get('total_accounts_processed', 0)
            target_results['total_accounts_pending'] += location_results.get('total_accounts_pending', 0)
            target_results['sources'].append('location')

        # Add username results if available
        if username_results:
            target_results['total_accounts_found'] += username_results.get('total_accounts_found', 0)
            target_results['total_accounts_processed'] += username_results.get('total_accounts_processed', 0)
            target_results['total_accounts_pending'] += username_results.get('total_accounts_pending', 0)
            target_results['sources'].append('username')
    else:
        # Use the appropriate single result
        target_results = location_results if conf['target_type'] == 'location' else username_results

    # Check if we have results to update
    if not target_results:
        logger.warning("No target results to update")
        target_results = {
            'total_accounts_found': 0,
            'total_accounts_processed': 0,
            'total_accounts_pending': 0,
            'sources': []
        }

    # Log the results
    logger.info(f"Updating campaign {campaign_id} with results: {target_results}")

    # Update the database
    try:
        # Import Django models
        import sys
        pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
        django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")
        if django_dir not in sys.path:
            sys.path.append(django_dir)

        # Set up Django environment
        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")
        import django
        django.setup()

        # Import models
        from campaigns.models import Campaign
        from django.utils import timezone

        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)

        # Update campaign status
        campaign.status = 'completed'
        campaign.updated_at = timezone.now()

        # Save campaign
        campaign.save()

        # Check if CampaignResult model exists
        try:
            from campaigns.models import CampaignResult

            # Create or update campaign result
            result, created = CampaignResult.objects.get_or_create(campaign=campaign)

            # Update result fields
            result.total_accounts_found = target_results.get('total_accounts_found', 0)
            result.total_accounts_processed = target_results.get('total_accounts_processed', 0)
            result.total_accounts_pending = target_results.get('total_accounts_pending', 0)
            result.last_processed_at = timezone.now()

            # Save result
            result.save()

            logger.info(f"Updated campaign result: {result.id}")
        except ImportError:
            logger.info("CampaignResult model not found, skipping result update")

        # Update workflow execution if available
        workflow_execution_id = context['task_instance'].xcom_pull(key='workflow_execution_id')
        if workflow_execution_id:
            try:
                from campaigns.models.workflow import WorkflowExecution

                # Get workflow execution
                workflow_execution = WorkflowExecution.objects.get(id=workflow_execution_id)

                # Update workflow execution
                if workflow_execution.status != 'completed':
                    workflow_execution.complete({
                        'success': True,
                        'message': "Workflow completed successfully",
                        'target_results': target_results
                    })

                    logger.info(f"Updated workflow execution: {workflow_execution.id}")
            except Exception as e:
                logger.warning(f"Could not update workflow execution: {str(e)}")

        # Return success
        return {
            'success': True,
            'campaign_id': str(campaign.id),
            'status': campaign.status,
            'total_accounts_found': target_results.get('total_accounts_found', 0),
            'total_accounts_processed': target_results.get('total_accounts_processed', 0),
            'total_accounts_pending': target_results.get('total_accounts_pending', 0)
        }

    except Exception as e:
        # Log error
        error_msg = f"Error updating campaign results: {str(e)}"
        logger.exception(error_msg)

        # Raise exception to mark task as failed
        raise RuntimeError(error_msg)

def run_pyflow_workflow(**context):
    """
    Run a PyFlow workflow based on the campaign configuration.

    This function determines the appropriate PyFlow workflow to run based on the campaign
    configuration, builds the command with the necessary parameters, and executes the workflow.
    It also tracks the workflow execution and updates the campaign with the results.

    Args:
        context: Airflow task context

    Returns:
        dict: Workflow execution results
    """
    # Get the configuration from the previous task
    conf = context['task_instance'].xcom_pull(task_ids='validate_campaign_config')

    # Get campaign ID
    campaign_id = conf['campaign_id']

    # Get target type
    target_type = conf['target_type']

    # Get audience type
    audience_type = conf['audience_type']

    # Determine which PyFlow workflow to run
    pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
    workflow_dir = os.path.join(pyflow_base_dir, "ENGINE", "INSTA", "WORKFLOW")

    # For mixed campaigns, we'll use a combined workflow or the MANUAL_DATA_MINING workflow
    # which can handle both location and username targets
    if target_type == 'mixed':
        workflow_file = os.path.join(workflow_dir, "MANUAL_DATA_MINING.pygraph")
    elif target_type == 'location':
        workflow_file = os.path.join(workflow_dir, "MANUAL_DATA_MINING.pygraph")
    else:  # username
        workflow_file = os.path.join(workflow_dir, "FEED.pygraph")

    # Check if workflow file exists
    if not os.path.exists(workflow_file):
        error_msg = f"PyFlow workflow file not found: {workflow_file}"
        logger.error(error_msg)
        raise FileNotFoundError(error_msg)

    # Log the workflow
    logger.info(f"Running PyFlow workflow: {workflow_file}")

    # Check if resources are available
    workflow_type = 'collection'
    if not check_resources_available(workflow_type, **context):
        logger.warning(f"Resources not available for {workflow_type} workflow. Waiting...")
        if not wait_for_resources(workflow_type, max_wait_time=1800, **context):  # Wait up to 30 minutes
            error_msg = f"Timed out waiting for resources for {workflow_type} workflow"
            logger.error(error_msg)
            raise RuntimeError(error_msg)
        logger.info(f"Resources now available for {workflow_type} workflow")

    # Create workflow execution record in Django
    try:
        # Import Django models
        import sys
        django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")
        if django_dir not in sys.path:
            sys.path.append(django_dir)

        # Set up Django environment
        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")
        import django
        django.setup()

        # Import models
        from campaigns.models import Campaign
        from campaigns.models.workflow import WorkflowExecution

        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)

        # Create workflow name
        workflow_name = f"campaign_{campaign_id}_collection_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Create workflow execution record
        workflow_execution = WorkflowExecution.objects.create(
            campaign=campaign,
            workflow_name=workflow_name,
            workflow_path=workflow_file,
            workflow_type=workflow_type,
            status='pending',
            parameters=conf,
            total_items=len(conf['location_targets']) if target_type == 'location' else len(conf['username_targets']),
            log_file=os.path.join(pyflow_base_dir, "ENGINE", "logs", f"{workflow_name}.log")
        )

        # Register workflow with resource manager
        register_workflow(
            workflow_execution_id=str(workflow_execution.id),
            workflow_type=workflow_type,
            campaign_id=campaign_id
        )

        # Start tracking workflow progress
        from campaigns.services.workflow_progress_service import WorkflowProgressService
        progress_service = WorkflowProgressService()
        progress_service.start_tracking(workflow_execution)

        # Update workflow execution ID in XCom
        context['task_instance'].xcom_push(key='workflow_execution_id', value=str(workflow_execution.id))

        logger.info(f"Created workflow execution record: {workflow_execution.id}")
    except Exception as e:
        logger.warning(f"Could not create workflow execution record: {str(e)}")
        # Continue without tracking progress

    # Build PyFlow command
    pyflow_command = f"pyflow -m run -f {workflow_file} --campaign_id {campaign_id} --num_retries 3"

    # Add target-specific parameters
    if target_type in ['location', 'mixed']:
        # Add location IDs for location-based or mixed campaigns
        if 'location_targets' in conf and conf['location_targets']:
            location_ids = [str(loc['location_id']) for loc in conf['location_targets']]
            pyflow_command += f" --location_ids {','.join(location_ids)}"

    if target_type in ['username', 'mixed']:
        # Add usernames for username-based or mixed campaigns
        if 'username_targets' in conf and conf['username_targets']:
            usernames = conf['username_targets']
            pyflow_command += f" --usernames {','.join(usernames)}"

    # For mixed campaigns, add a flag to indicate mixed target type
    if target_type == 'mixed':
        pyflow_command += " --mixed_targets true"

    # Add audience type
    pyflow_command += f" --audience_type {audience_type}"

    # Add optional parameters if available
    if 'max_accounts' in conf:
        pyflow_command += f" --max_accounts {conf['max_accounts']}"

    if 'min_followers' in conf:
        pyflow_command += f" --min_followers {conf['min_followers']}"

    if 'max_followers' in conf:
        pyflow_command += f" --max_followers {conf['max_followers']}"

    # Add workflow name for tracking
    if 'workflow_execution' in locals():
        pyflow_command += f" --workflow_name {workflow_name}"

    # Add dynamic tag parameters if available
    try:
        # Get campaign analysis settings
        from campaigns.models import CampaignAnalysisSettings

        try:
            analysis_settings = CampaignAnalysisSettings.objects.get(campaign_id=campaign_id)

            # Add tagging flag if enabled
            if hasattr(analysis_settings, 'enable_tagging') and analysis_settings.enable_tagging:
                pyflow_command += " --enable_tagging true"

                # Add target tags if specified
                if hasattr(analysis_settings, 'target_tags') and analysis_settings.target_tags:
                    tags = ','.join(analysis_settings.target_tags)
                    pyflow_command += f" --target_tags {tags}"
        except CampaignAnalysisSettings.DoesNotExist:
            logger.info(f"No analysis settings found for campaign {campaign_id}")
    except Exception as e:
        logger.warning(f"Could not add dynamic tag parameters: {str(e)}")

    # Log the command
    logger.info(f"Executing PyFlow command: {pyflow_command}")

    # Execute the PyFlow workflow
    try:
        # Change to PyFlow engine directory
        os.chdir(pyflow_base_dir)

        # Execute the command
        process = subprocess.Popen(
            pyflow_command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )

        # Monitor the process
        stdout = []
        for line in iter(process.stdout.readline, ''):
            stdout.append(line)
            logger.info(f"PyFlow: {line.strip()}")

        # Wait for the process to complete
        process.wait()

        # Check return code
        if process.returncode != 0:
            error_msg = f"PyFlow workflow failed with return code {process.returncode}"
            logger.error(error_msg)

            # Update workflow execution record if available
            if 'workflow_execution' in locals():
                workflow_execution.fail(error_msg)

            raise RuntimeError(error_msg)

        # Log success
        logger.info("PyFlow workflow completed successfully")

        # Update workflow execution record if available
        if 'workflow_execution' in locals():
            workflow_execution.complete({
                'success': True,
                'message': "Workflow completed successfully"
            })

            # Unregister workflow from resource manager
            unregister_workflow(
                workflow_execution_id=str(workflow_execution.id),
                workflow_type=workflow_type
            )

        # Return success
        return {
            'success': True,
            'message': "PyFlow workflow completed successfully",
            'workflow_execution_id': str(workflow_execution.id) if 'workflow_execution' in locals() else None
        }

    except Exception as e:
        # Log error
        error_msg = f"Error executing PyFlow workflow: {str(e)}"
        logger.exception(error_msg)

        # Update workflow execution record if available
        if 'workflow_execution' in locals():
            workflow_execution.fail(error_msg)

            # Unregister workflow from resource manager
            try:
                unregister_workflow(
                    workflow_execution_id=str(workflow_execution.id),
                    workflow_type=workflow_type
                )
            except Exception as unregister_error:
                logger.warning(f"Error unregistering workflow: {str(unregister_error)}")

        # Raise exception to mark task as failed
        raise RuntimeError(error_msg)

def process_discovered_usernames(campaign_id, **context):
    """
    Process usernames from the queue and update the database
    """
    import redis
    from campaigns.models import Campaign, UsernameTarget, CampaignResult

    # Connect to Redis
    r = redis.Redis(host='localhost', port=6379, db=0)
    queue_name = 'discovered_usernames'

    # Get campaign
    campaign = Campaign.objects.get(id=campaign_id)

    # Process usernames in batches
    batch_size = 100
    processed = 0

    while True:
        # Get a batch of usernames
        usernames = []
        for _ in range(batch_size):
            username = r.rpop(queue_name)
            if username is None:
                break
            usernames.append(username.decode('utf-8'))

        if not usernames:
            break

        # Process the batch
        new_targets = []
        for username in usernames:
            if not UsernameTarget.objects.filter(campaign=campaign, username=username).exists():
                new_targets.append(UsernameTarget(
                    campaign=campaign,
                    username=username,
                    processed=False
                ))

        # Bulk create
        if new_targets:
            UsernameTarget.objects.bulk_create(new_targets)

        processed += len(usernames)

        # Update campaign statistics
        result, created = CampaignResult.objects.get_or_create(campaign=campaign)
        result.total_accounts_found = UsernameTarget.objects.filter(campaign=campaign).count()
        result.total_accounts_pending = UsernameTarget.objects.filter(
            campaign=campaign, processed=False).count()
        result.save()

    return f"Processed {processed} usernames"

# Define the tasks
validate_config_task = PythonOperator(
    task_id='validate_campaign_config',
    python_callable=validate_campaign_config,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=5),
    doc_md="""
    Validates the campaign configuration from the trigger.

    This task checks for required fields, validates field values, and ensures that
    the appropriate targets are provided based on the target type.

    **Inputs:**
    - DAG run configuration

    **Outputs:**
    - Validated campaign configuration
    """
)

process_location_task = PythonOperator(
    task_id='process_location_targets',
    python_callable=process_location_targets,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=10),
    doc_md="""
    Processes location-based targets.

    This task processes location targets for location-based or mixed campaigns.
    It is skipped for username-based campaigns or mixed campaigns without location targets.

    **Inputs:**
    - Validated campaign configuration

    **Outputs:**
    - Location target processing results including:
      - total_accounts_found
      - total_accounts_processed
      - total_accounts_pending
      - source identifier
    """
)

process_username_task = PythonOperator(
    task_id='process_username_targets',
    python_callable=process_username_targets,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=10),
    doc_md="""
    Processes username-based targets.

    This task processes username targets for username-based or mixed campaigns.
    It is skipped for location-based campaigns or mixed campaigns without username targets.

    **Inputs:**
    - Validated campaign configuration

    **Outputs:**
    - Username target processing results including:
      - total_accounts_found
      - total_accounts_processed
      - total_accounts_pending
      - source identifier
    """
)

run_pyflow_task = PythonOperator(
    task_id='run_pyflow_workflow',
    python_callable=run_pyflow_workflow,
    provide_context=True,
    dag=dag,
    retries=2,
    retry_delay=timedelta(minutes=5),
    execution_timeout=timedelta(hours=2),
    doc_md="""
    Runs a PyFlow workflow based on the campaign configuration.

    This task determines the appropriate PyFlow workflow to run based on the campaign
    configuration, builds the command with the necessary parameters, and executes the workflow.
    It also tracks the workflow execution and updates the campaign with the results.

    For mixed campaigns, it configures the workflow to handle both location and username targets.

    **Inputs:**
    - Validated campaign configuration
    - Target processing results from location and/or username tasks

    **Outputs:**
    - Workflow execution results
    - Workflow execution ID (XCom)
    - Workflow progress updates
    """
)

update_results_task = PythonOperator(
    task_id='update_campaign_results',
    python_callable=update_campaign_results,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=10),
    doc_md="""
    Updates the campaign results in the database.

    This task updates the campaign results in the database based on the
    results of the workflow execution. It also updates the campaign status
    to reflect the completion of the data collection process.

    For mixed campaigns, it combines the results from both location and username targets.

    **Inputs:**
    - Validated campaign configuration
    - Target processing results from location and/or username tasks
    - Workflow execution results
    - Workflow execution ID (XCom)

    **Outputs:**
    - Updated campaign information
    - Updated campaign results
    - Updated workflow execution status
    """
)

# Define the task dependencies with conditional branching
validate_config_task >> [process_location_task, process_username_task]

# Both target processing tasks lead to the PyFlow workflow
process_location_task >> run_pyflow_task
process_username_task >> run_pyflow_task

# PyFlow workflow leads to updating results
run_pyflow_task >> update_results_task
