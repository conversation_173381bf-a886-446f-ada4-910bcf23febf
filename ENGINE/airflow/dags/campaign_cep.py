"""
Campaign CEP (Customer Engagement Process) DAG for executing workflows on whitelisted accounts.

This DAG is responsible for:
1. Retrieving whitelisted accounts from a campaign
2. Configuring and executing PyFlow workflows for engagement actions
3. Tracking workflow progress and updating results

The DAG supports different workflow types:
- follow: Execute follow actions on whitelisted accounts
- like: Execute like actions on whitelisted accounts
- comment: Execute comment actions on whitelisted accounts
- dm: Execute direct message actions on whitelisted accounts
- all: Execute all engagement actions on whitelisted accounts

Configuration Parameters:
- campaign_id: ID of the campaign
- workflow_type: Type of workflow to execute ('follow', 'like', 'comment', 'dm', or 'all')
- batch_size: Number of accounts to process in each batch (default: 10)
- max_actions: Maximum number of actions to perform (default: 50)
- delay_between_actions: Delay between actions in seconds (default: 60)
"""
import os
import json
import time
import random
import logging
import sys
import subprocess
from datetime import datetime, timedelta

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from airflow.models import Variable
from airflow.hooks.base import BaseHook

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Default arguments for the DAG
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
}

# Create the DAG
dag = DAG(
    'campaign_cep',
    default_args=default_args,
    description='Execute CEP workflows on whitelisted accounts',
    schedule_interval=None,  # This DAG is triggered manually
    start_date=days_ago(1),
    tags=['campaign', 'cep', 'engagement'],
)

def validate_cep_config(**context):
    """
    Validate the CEP configuration and prepare for execution.

    This function validates the campaign CEP configuration passed to the DAG run.
    It checks for required fields, validates field values, and sets default values
    for optional parameters.

    Args:
        context: Airflow task context

    Returns:
        dict: Validated CEP configuration

    Raises:
        ValueError: If the configuration is invalid
    """
    try:
        # Get the campaign configuration from the DAG run configuration
        conf = context['dag_run'].conf

        # Log the configuration
        logger.info(f"Received CEP configuration: {conf}")

        # Check if configuration is empty
        if not conf:
            raise ValueError("Empty CEP configuration")

        # Validate required fields
        required_fields = ['campaign_id', 'workflow_type']
        missing_fields = [field for field in required_fields if field not in conf]
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

        # Get campaign ID
        campaign_id = conf['campaign_id']
        if not campaign_id:
            raise ValueError("Empty campaign_id")

        # Get workflow type
        workflow_type = conf['workflow_type']

        # Validate workflow type
        valid_workflow_types = ['follow', 'like', 'comment', 'dm', 'all']
        if workflow_type not in valid_workflow_types:
            raise ValueError(f"Invalid workflow_type: {workflow_type}. Must be one of: {', '.join(valid_workflow_types)}")

        # Get batch size with validation
        batch_size = conf.get('batch_size', 10)
        if not isinstance(batch_size, int) or batch_size <= 0:
            logger.warning(f"Invalid batch_size: {batch_size}. Using default: 10")
            batch_size = 10

        # Get max actions with validation
        max_actions = conf.get('max_actions', 50)
        if not isinstance(max_actions, int) or max_actions <= 0:
            logger.warning(f"Invalid max_actions: {max_actions}. Using default: 50")
            max_actions = 50

        # Get delay between actions with validation
        delay_between_actions = conf.get('delay_between_actions', 60)
        if not isinstance(delay_between_actions, (int, float)) or delay_between_actions < 0:
            logger.warning(f"Invalid delay_between_actions: {delay_between_actions}. Using default: 60")
            delay_between_actions = 60

        # Get additional parameters
        additional_params = {}

        # Add message template for DM workflows
        if workflow_type == 'dm' and 'message_template' in conf:
            additional_params['message_template'] = conf['message_template']

        # Add comment template for comment workflows
        if workflow_type == 'comment' and 'comment_template' in conf:
            additional_params['comment_template'] = conf['comment_template']

        # Add custom parameters for specific workflow types
        if 'custom_params' in conf and isinstance(conf['custom_params'], dict):
            additional_params.update(conf['custom_params'])

        # Log successful validation
        logger.info(f"CEP configuration validated successfully for campaign {campaign_id}")

        # Return the validated configuration
        validated_conf = {
            'campaign_id': campaign_id,
            'workflow_type': workflow_type,
            'batch_size': batch_size,
            'max_actions': max_actions,
            'delay_between_actions': delay_between_actions
        }

        # Add additional parameters if any
        if additional_params:
            validated_conf['additional_params'] = additional_params

        return validated_conf

    except ValueError as e:
        # Log validation error
        logger.error(f"CEP configuration validation error: {str(e)}")
        raise

    except Exception as e:
        # Log unexpected error
        logger.exception(f"Unexpected error validating CEP configuration: {str(e)}")
        raise ValueError(f"Unexpected error validating CEP configuration: {str(e)}")

def retrieve_whitelist(**context):
    """
    Retrieve whitelisted accounts for the campaign.

    This function retrieves the whitelisted accounts for the campaign from the database.
    It filters the accounts based on the workflow type and returns the accounts that
    should be processed by the workflow.

    Args:
        context: Airflow task context

    Returns:
        dict: Whitelist information
    """
    # Get the configuration from the previous task
    conf = context['task_instance'].xcom_pull(task_ids='validate_cep_config')

    # Get campaign ID
    campaign_id = conf['campaign_id']

    # Get workflow type
    workflow_type = conf['workflow_type']

    # Log the retrieval
    logger.info(f"Retrieving whitelist for campaign: {campaign_id}")

    # Set up Django environment
    pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
    django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")

    # Add Django directory to path if not already there
    if django_dir not in sys.path:
        sys.path.append(django_dir)

    # Set up Django environment
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

    try:
        import django
        django.setup()

        # Import models
        from campaigns.models import Campaign
        from instagram.models import Accounts, WhiteListEntry

        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)

        # Get whitelisted accounts for campaign
        whitelist_entries = WhiteListEntry.objects.filter(
            account__campaign=campaign
        ).select_related('account')

        # Filter based on workflow type if not 'all'
        if workflow_type != 'all':
            if workflow_type == 'follow':
                whitelist_entries = whitelist_entries.filter(follow=True)
            elif workflow_type == 'like':
                whitelist_entries = whitelist_entries.filter(post_like=True)
            elif workflow_type == 'comment':
                whitelist_entries = whitelist_entries.filter(comment=True)
            elif workflow_type == 'dm':
                whitelist_entries = whitelist_entries.filter(dm=True)

        # Convert to list of dictionaries
        whitelist = []
        for entry in whitelist_entries:
            whitelist.append({
                'username': entry.account.username,
                'tags': entry.tags,
                'dm': entry.dm,
                'follow': entry.follow,
                'like': entry.post_like,
                'comment': entry.comment,
                'favorite': entry.favorite,
                'discover': entry.discover
            })

        # Log the number of accounts retrieved
        logger.info(f"Retrieved {len(whitelist)} whitelisted accounts for campaign: {campaign_id}")

        # Return the whitelist
        return {
            'campaign_id': campaign_id,
            'campaign_name': campaign.name,
            'whitelist': whitelist,
            'workflow_type': conf['workflow_type'],
            'batch_size': conf['batch_size'],
            'max_actions': conf['max_actions'],
            'delay_between_actions': conf['delay_between_actions'],
            'additional_params': conf.get('additional_params', {})
        }

    except Exception as e:
        # Log error
        error_msg = f"Error retrieving whitelist: {str(e)}"
        logger.exception(error_msg)

        # Generate test data for development/testing purposes
        logger.warning("Generating test whitelist data for development/testing")

        whitelist = [
            {
                'username': f'user_{i}',
                'tags': ['travel', 'food'] if i % 2 == 0 else ['photography', 'fashion'],
                'dm': i % 3 == 0,
                'follow': i % 2 == 0,
                'like': True,
                'comment': i % 4 == 0,
                'favorite': i % 5 == 0,
                'discover': i % 6 == 0
            }
            for i in range(1, 16)  # Generate 15 test accounts
        ]

        # Filter based on workflow type if not 'all'
        if workflow_type != 'all':
            if workflow_type == 'follow':
                whitelist = [account for account in whitelist if account['follow']]
            elif workflow_type == 'like':
                whitelist = [account for account in whitelist if account['like']]
            elif workflow_type == 'comment':
                whitelist = [account for account in whitelist if account['comment']]
            elif workflow_type == 'dm':
                whitelist = [account for account in whitelist if account['dm']]

        # Log the number of accounts in test data
        logger.info(f"Generated {len(whitelist)} test whitelist accounts for campaign: {campaign_id}")

        # Return the test whitelist
        return {
            'campaign_id': campaign_id,
            'campaign_name': f"Campaign {campaign_id}",
            'whitelist': whitelist,
            'workflow_type': conf['workflow_type'],
            'batch_size': conf['batch_size'],
            'max_actions': conf['max_actions'],
            'delay_between_actions': conf['delay_between_actions'],
            'additional_params': conf.get('additional_params', {}),
            'is_test_data': True
        }

def prepare_workflow_batches(**context):
    """
    Prepare batches of accounts for workflow execution.
    """
    # Get the data from the previous task
    data = context['task_instance'].xcom_pull(task_ids='retrieve_whitelist')

    # Get campaign ID
    campaign_id = data['campaign_id']

    # Get whitelist
    whitelist = data['whitelist']

    # Get workflow type
    workflow_type = data['workflow_type']

    # Get batch size
    batch_size = data['batch_size']

    # Get max actions
    max_actions = data['max_actions']

    # Filter accounts based on workflow type
    filtered_accounts = []
    if workflow_type == 'all':
        filtered_accounts = whitelist
    elif workflow_type == 'follow':
        filtered_accounts = [account for account in whitelist if account['follow']]
    elif workflow_type == 'like':
        filtered_accounts = [account for account in whitelist if account['like']]
    elif workflow_type == 'comment':
        filtered_accounts = [account for account in whitelist if account['comment']]
    elif workflow_type == 'dm':
        filtered_accounts = [account for account in whitelist if account['dm']]

    # Limit to max actions
    filtered_accounts = filtered_accounts[:max_actions]

    # Create batches
    batches = []
    for i in range(0, len(filtered_accounts), batch_size):
        batch = filtered_accounts[i:i+batch_size]
        batches.append(batch)

    # Log the batches
    logging.info(f"Prepared {len(batches)} batches with {len(filtered_accounts)} accounts for workflow type: {workflow_type}")

    # Return the batches
    return {
        'campaign_id': campaign_id,
        'workflow_type': workflow_type,
        'batches': batches,
        'total_accounts': len(filtered_accounts),
        'delay_between_actions': data['delay_between_actions']
    }

def execute_workflow(**context):
    """
    Execute PyFlow workflow for a batch of accounts.

    This function executes the appropriate PyFlow workflow for each batch of accounts
    based on the workflow type. It tracks the execution progress and updates the
    campaign with the results.

    Args:
        context: Airflow task context

    Returns:
        dict: Workflow execution results
    """
    # Get the data from the previous task
    data = context['task_instance'].xcom_pull(task_ids='prepare_workflow_batches')

    # Get campaign ID
    campaign_id = data['campaign_id']

    # Get workflow type
    workflow_type = data['workflow_type']

    # Get batches
    batches = data['batches']

    # Get delay between actions
    delay_between_actions = data['delay_between_actions']

    # Get additional parameters if available
    additional_params = data.get('additional_params', {})

    # Log the execution
    logger.info(f"Executing {workflow_type} workflow for campaign: {campaign_id}")
    logger.info(f"Processing {len(batches)} batches with {data['total_accounts']} accounts")

    # Set up Django environment for workflow tracking
    pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
    django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")

    # Add Django directory to path if not already there
    if django_dir not in sys.path:
        sys.path.append(django_dir)

    # Set up Django environment
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

    try:
        import django
        django.setup()

        # Import models
        from campaigns.models import Campaign
        from campaigns.models.workflow import WorkflowExecution
        from campaigns.services.workflow_progress_service import WorkflowProgressService

        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)

        # Create workflow name
        workflow_name = f"campaign_{campaign_id}_cep_{workflow_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # Determine workflow file based on workflow type
        if workflow_type == 'follow':
            workflow_file = os.path.join(pyflow_base_dir, "ENGINE", "INSTA", "WORKFLOW", "FOLLOW.pygraph")
        elif workflow_type == 'like':
            workflow_file = os.path.join(pyflow_base_dir, "ENGINE", "INSTA", "WORKFLOW", "LIKE.pygraph")
        elif workflow_type == 'comment':
            workflow_file = os.path.join(pyflow_base_dir, "ENGINE", "INSTA", "WORKFLOW", "COMMENT.pygraph")
        elif workflow_type == 'dm':
            workflow_file = os.path.join(pyflow_base_dir, "ENGINE", "INSTA", "WORKFLOW", "DM.pygraph")
        else:  # all
            workflow_file = os.path.join(pyflow_base_dir, "ENGINE", "INSTA", "WORKFLOW", "CEP.pygraph")

        # Check if workflow file exists
        if not os.path.exists(workflow_file):
            error_msg = f"PyFlow workflow file not found: {workflow_file}"
            logger.error(error_msg)
            raise FileNotFoundError(error_msg)

        # Create workflow execution record
        workflow_execution = WorkflowExecution.objects.create(
            campaign=campaign,
            workflow_name=workflow_name,
            workflow_path=workflow_file,
            workflow_type=workflow_type,
            status='pending',
            parameters=data,
            total_items=data['total_accounts'],
            log_file=os.path.join(pyflow_base_dir, "ENGINE", "logs", f"{workflow_name}.log")
        )

        # Start tracking workflow progress
        progress_service = WorkflowProgressService()
        progress_service.start_tracking(workflow_execution)

        # Update workflow execution ID in XCom
        context['task_instance'].xcom_push(key='workflow_execution_id', value=str(workflow_execution.id))

        logger.info(f"Created workflow execution record: {workflow_execution.id}")

        # Process each batch
        results = []
        successful_actions = 0
        failed_actions = 0

        for i, batch in enumerate(batches):
            # Log the batch
            logger.info(f"Processing batch {i+1}/{len(batches)} with {len(batch)} accounts")

            # Extract usernames from batch
            usernames = [account['username'] for account in batch]

            # Build PyFlow command
            pyflow_command = f"pyflow -m run -f {workflow_file} --campaign_id {campaign_id} --usernames {','.join(usernames)} --num_retries 3"

            # Add workflow type if it's the 'all' workflow
            if workflow_type == 'all':
                pyflow_command += f" --workflow_type {workflow_type}"

            # Add delay between actions
            pyflow_command += f" --delay_between_actions {delay_between_actions}"

            # Add workflow name for tracking
            pyflow_command += f" --workflow_name {workflow_name}"

            # Add additional parameters if available
            if 'message_template' in additional_params and workflow_type == 'dm':
                message_template = additional_params['message_template']
                pyflow_command += f" --message_template \"{message_template}\""

            if 'comment_template' in additional_params and workflow_type == 'comment':
                comment_template = additional_params['comment_template']
                pyflow_command += f" --comment_template \"{comment_template}\""

            # Log the command
            logger.info(f"Executing PyFlow command: {pyflow_command}")

            # Execute the PyFlow workflow
            try:
                # Change to PyFlow engine directory
                os.chdir(pyflow_base_dir)

                # Execute the command
                process = subprocess.Popen(
                    pyflow_command,
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.STDOUT,
                    universal_newlines=True
                )

                # Monitor the process
                stdout = []
                for line in iter(process.stdout.readline, ''):
                    stdout.append(line)
                    logger.info(f"PyFlow: {line.strip()}")

                # Wait for the process to complete
                process.wait()

                # Check return code
                if process.returncode != 0:
                    error_msg = f"PyFlow workflow failed with return code {process.returncode}"
                    logger.error(error_msg)

                    # Update batch results
                    batch_results = []
                    for account in batch:
                        batch_results.append({
                            'username': account['username'],
                            'success': False,
                            'error': error_msg
                        })

                    # Add batch results to overall results
                    results.extend(batch_results)
                    failed_actions += len(batch)
                else:
                    # Parse results from stdout
                    batch_results = []
                    success_count = 0

                    # Try to parse JSON results from stdout
                    try:
                        result_line = next((line for line in stdout if "RESULT:" in line), None)
                        if result_line:
                            result_json = result_line.split("RESULT:", 1)[1].strip()
                            batch_result_data = json.loads(result_json)

                            # Process each account result
                            for account_result in batch_result_data.get('account_results', []):
                                username = account_result.get('username')
                                success = account_result.get('success', False)
                                error = account_result.get('error')

                                if success:
                                    success_count += 1

                                batch_results.append({
                                    'username': username,
                                    'success': success,
                                    'error': error
                                })
                        else:
                            # If no result line found, assume success for all accounts
                            for account in batch:
                                batch_results.append({
                                    'username': account['username'],
                                    'success': True,
                                    'error': None
                                })
                            success_count = len(batch)
                    except Exception as e:
                        logger.warning(f"Error parsing batch results: {str(e)}")

                        # Fallback to assuming success for all accounts
                        for account in batch:
                            batch_results.append({
                                'username': account['username'],
                                'success': True,
                                'error': None
                            })
                        success_count = len(batch)

                    # Add batch results to overall results
                    results.extend(batch_results)
                    successful_actions += success_count
                    failed_actions += len(batch) - success_count

            except Exception as e:
                # Log error
                error_msg = f"Error executing PyFlow workflow: {str(e)}"
                logger.exception(error_msg)

                # Update batch results
                batch_results = []
                for account in batch:
                    batch_results.append({
                        'username': account['username'],
                        'success': False,
                        'error': error_msg
                    })

                # Add batch results to overall results
                results.extend(batch_results)
                failed_actions += len(batch)

            # Update workflow execution progress
            workflow_execution.refresh_from_db()
            workflow_execution.update_progress(
                processed_items=(i + 1) * len(batch),
                successful_items=successful_actions,
                failed_items=failed_actions
            )

            # Delay between batches
            if i < len(batches) - 1:
                delay_seconds = min(delay_between_actions, 30)  # Limit to 30 seconds for reasonable execution time
                logger.info(f"Waiting {delay_seconds} seconds before next batch")
                time.sleep(delay_seconds)

        # Calculate statistics
        total_accounts = len(results)

        # Log the results
        logger.info(f"Workflow execution completed:")
        logger.info(f"Total accounts: {total_accounts}")
        logger.info(f"Successful actions: {successful_actions}")
        logger.info(f"Failed actions: {failed_actions}")

        # Update workflow execution record
        workflow_execution.complete({
            'success': True,
            'total_accounts': total_accounts,
            'successful_actions': successful_actions,
            'failed_actions': failed_actions,
            'results': results[:10]  # Store only first 10 results to avoid excessive storage
        })

        # Return the results
        return {
            'success': True,
            'campaign_id': campaign_id,
            'workflow_type': workflow_type,
            'workflow_execution_id': str(workflow_execution.id),
            'total_accounts': total_accounts,
            'successful_actions': successful_actions,
            'failed_actions': failed_actions,
            'success_rate': (successful_actions / total_accounts * 100) if total_accounts > 0 else 0
        }

    except Exception as e:
        # Log error
        error_msg = f"Error executing workflow: {str(e)}"
        logger.exception(error_msg)

        # Update workflow execution record if available
        if 'workflow_execution' in locals():
            workflow_execution.fail(error_msg)

        # Raise exception to mark task as failed
        raise RuntimeError(error_msg)

def update_workflow_results(**context):
    """
    Update campaign with workflow execution results.

    This function updates the campaign with the results of the workflow execution.
    It also updates the campaign status to reflect the completion of the CEP process.

    Args:
        context: Airflow task context

    Returns:
        dict: Updated campaign information
    """
    # Get the data from the previous task
    data = context['task_instance'].xcom_pull(task_ids='execute_workflow')

    # Check if we have data to update
    if not data:
        logger.warning("No workflow execution data to update")
        return {
            'success': False,
            'error': 'No workflow execution data to update'
        }

    # Get campaign ID
    campaign_id = data['campaign_id']

    # Get workflow type
    workflow_type = data['workflow_type']

    # Get statistics
    total_accounts = data['total_accounts']
    successful_actions = data['successful_actions']
    failed_actions = data['failed_actions']
    success_rate = data.get('success_rate', 0)

    # Get workflow execution ID if available
    workflow_execution_id = data.get('workflow_execution_id')

    # Log the update
    logger.info(f"Updating {workflow_type} workflow results for campaign: {campaign_id}")
    logger.info(f"Workflow type: {workflow_type}")
    logger.info(f"Total accounts: {total_accounts}")
    logger.info(f"Successful actions: {successful_actions}")
    logger.info(f"Failed actions: {failed_actions}")
    logger.info(f"Success rate: {success_rate:.2f}%")

    # Update the database
    try:
        # Set up Django environment
        import sys
        pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
        django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")
        if django_dir not in sys.path:
            sys.path.append(django_dir)

        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")
        import django
        django.setup()

        # Import models
        from campaigns.models import Campaign
        from django.utils import timezone

        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)

        # Update campaign status if needed
        if campaign.status != 'completed':
            campaign.status = 'completed'
            campaign.updated_at = timezone.now()
            campaign.save()
            logger.info(f"Updated campaign status to 'completed'")

        # Update campaign analysis settings with workflow statistics
        try:
            from campaigns.models import CampaignAnalysisSettings

            # Get or create analysis settings
            analysis_settings, created = CampaignAnalysisSettings.objects.get_or_create(campaign=campaign)

            # Update workflow statistics
            workflow_statistics = analysis_settings.workflow_statistics or {}

            # Add or update workflow type statistics
            workflow_statistics[workflow_type] = {
                'total_accounts': total_accounts,
                'successful_actions': successful_actions,
                'failed_actions': failed_actions,
                'success_rate': success_rate,
                'last_executed': timezone.now().isoformat(),
                'workflow_execution_id': workflow_execution_id
            }

            # Update completed workflows
            completed_workflows = analysis_settings.completed_workflows or {}
            if workflow_execution_id:
                completed_workflows[workflow_execution_id] = {
                    'workflow_type': workflow_type,
                    'completed_at': timezone.now().isoformat(),
                    'total_accounts': total_accounts,
                    'successful_actions': successful_actions,
                    'failed_actions': failed_actions,
                    'success_rate': success_rate
                }

            # Save changes
            analysis_settings.workflow_statistics = workflow_statistics
            analysis_settings.completed_workflows = completed_workflows
            analysis_settings.save()

            logger.info(f"Updated campaign analysis settings with workflow statistics")
        except Exception as e:
            logger.warning(f"Could not update campaign analysis settings: {str(e)}")

        # Return success
        return {
            'success': True,
            'campaign_id': str(campaign.id),
            'campaign_name': campaign.name,
            'workflow_type': workflow_type,
            'total_accounts': total_accounts,
            'successful_actions': successful_actions,
            'failed_actions': failed_actions,
            'success_rate': success_rate,
            'workflow_execution_id': workflow_execution_id
        }

    except Exception as e:
        # Log error
        error_msg = f"Error updating workflow results: {str(e)}"
        logger.exception(error_msg)

        # Return error
        return {
            'success': False,
            'error': error_msg,
            'campaign_id': campaign_id,
            'workflow_type': workflow_type
        }

# Define the tasks
validate_config_task = PythonOperator(
    task_id='validate_cep_config',
    python_callable=validate_cep_config,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=5),
    doc_md="""
    Validate the CEP configuration and prepare for execution.

    This task validates the campaign CEP configuration passed to the DAG run.
    It checks for required fields, validates field values, and sets default values
    for optional parameters.

    **Inputs:**
    - DAG run configuration

    **Outputs:**
    - Validated CEP configuration
    """
)

retrieve_whitelist_task = PythonOperator(
    task_id='retrieve_whitelist',
    python_callable=retrieve_whitelist,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=10),
    doc_md="""
    Retrieve whitelisted accounts for the campaign.

    This task retrieves the whitelisted accounts for the campaign from the database.
    It filters the accounts based on the campaign configuration and returns the
    accounts that should be processed by the workflow.

    **Inputs:**
    - Validated CEP configuration

    **Outputs:**
    - List of whitelisted accounts
    """
)

prepare_batches_task = PythonOperator(
    task_id='prepare_workflow_batches',
    python_callable=prepare_workflow_batches,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=5),
    doc_md="""
    Prepare workflow batches for execution.

    This task prepares the workflow batches for execution based on the
    whitelisted accounts and the batch size specified in the configuration.

    **Inputs:**
    - Validated CEP configuration
    - List of whitelisted accounts

    **Outputs:**
    - Workflow batches
    """
)

execute_workflow_task = PythonOperator(
    task_id='execute_workflow',
    python_callable=execute_workflow,
    provide_context=True,
    dag=dag,
    retries=2,
    retry_delay=timedelta(minutes=5),
    execution_timeout=timedelta(hours=2),
    doc_md="""
    Execute PyFlow workflow for batches of accounts.

    This task executes the appropriate PyFlow workflow for each batch of accounts
    based on the workflow type. It tracks the execution progress and updates the
    campaign with the results.

    **Inputs:**
    - Workflow batches

    **Outputs:**
    - Workflow execution results
    - Workflow execution ID (XCom)
    """
)

update_results_task = PythonOperator(
    task_id='update_workflow_results',
    python_callable=update_workflow_results,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=10),
    doc_md="""
    Update campaign with workflow execution results.

    This task updates the campaign with the results of the workflow execution.
    It also updates the campaign status to reflect the completion of the CEP process.

    **Inputs:**
    - Workflow execution results

    **Outputs:**
    - Updated campaign information
    """
)

# Define the task dependencies
validate_config_task >> retrieve_whitelist_task >> prepare_batches_task >> execute_workflow_task >> update_results_task
