"""
Resource Manager utilities for Airflow DAGs.

This module provides functions for interacting with the resource manager service
from Airflow DAGs to ensure proper resource allocation and scheduling.
"""
import logging
import requests
import time
import json
import redis
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

# Resource manager API endpoints
RESOURCE_MANAGER_API_BASE = 'http://localhost:8000/api'
RESOURCE_MANAGER_STATUS_URL = f"{RESOURCE_MANAGER_API_BASE}/resource-manager/status/"
RESOURCE_MANAGER_CHECK_RATE_LIMIT_URL = f"{RESOURCE_MANAGER_API_BASE}/resource-manager/check-rate-limit/"
RESOURCE_MANAGER_TRACK_API_USAGE_URL = f"{RESOURCE_MANAGER_API_BASE}/resource-manager/track-api-usage/"
RESOURCE_MANAGER_OPTIMIZE_URL = f"{RESOURCE_MANAGER_API_BASE}/resource-manager/optimize/"

# Redis client for direct communication
redis_client = redis.Redis(host='localhost', port=6379, db=0)

def check_resources_available(workflow_type, **context):
    """
    Check if the bot worker is available for a workflow.

    Args:
        workflow_type (str): Type of workflow
        context: Airflow task context

    Returns:
        bool: True if bot worker is available
    """
    try:
        # Try API call first
        response = requests.get(
            RESOURCE_MANAGER_STATUS_URL,
            headers={'Authorization': 'Token your_api_token'},
            timeout=5
        )

        if response.status_code == 200:
            resources = response.json().get('resources', {})

            # Check bot worker status
            bot_worker = resources.get('bot_worker', {})
            bot_status = bot_worker.get('status', 'busy')

            if bot_status == 'busy':
                active_workflow = bot_worker.get('active_workflow', {})
                active_id = active_workflow.get('id', 'unknown')
                logger.warning(f"Bot worker is busy with workflow {active_id}")
                return False

            # Check API rate limits
            api_usage = resources.get('api_usage', {})
            api_type = workflow_type if workflow_type in api_usage else 'default'

            if api_type in api_usage:
                current = api_usage[api_type].get('current', 0)
                limit = api_usage[api_type].get('limit', 100)

                if current >= limit:
                    logger.warning(f"API rate limit exceeded for {api_type}: {current}/{limit}")
                    return False

            return True
        else:
            # Fallback to direct check
            return _check_resources_direct(workflow_type)
    except Exception as e:
        logger.exception(f"Error checking resources: {str(e)}")
        # Fallback to direct check
        return _check_resources_direct(workflow_type)

def _check_resources_direct(workflow_type):
    """Fallback method to check if bot worker is available directly"""
    try:
        # Check if any active workflow exists
        active_workflow_key = redis_client.get("bot_worker:active_workflow")

        if active_workflow_key:
            logger.warning(f"Bot worker is busy (direct check)")
            return False

        # Check API rate limits
        api_key = f"api_rate:{workflow_type}"
        current_usage = redis_client.get(api_key)
        current_usage = int(current_usage) if current_usage else 0

        # Get rate limit settings
        rate_limits = {
            'default': {'limit': 200, 'window': 3600},
            'follow': {'limit': 60, 'window': 3600},
            'like': {'limit': 120, 'window': 3600},
            'comment': {'limit': 20, 'window': 3600},
            'dm': {'limit': 20, 'window': 3600}
        }

        rate_limit = rate_limits.get(workflow_type, rate_limits['default'])
        limit = rate_limit['limit']

        if current_usage >= limit:
            logger.warning(f"API rate limit exceeded for {workflow_type}: {current_usage}/{limit}")
            return False

        return True
    except Exception as e:
        logger.exception(f"Error in direct resource check: {str(e)}")
        # Be conservative on error
        return False

def register_workflow(workflow_execution_id, workflow_type, campaign_id, priority=None):
    """
    Register a workflow with the resource manager.

    Args:
        workflow_execution_id (str): Workflow execution ID
        workflow_type (str): Type of workflow
        campaign_id (str): Campaign ID
        priority (int, optional): Custom priority

    Returns:
        dict: Registration result with status and position in queue
    """
    try:
        # Try API call first
        response = requests.post(
            f"{RESOURCE_MANAGER_API_BASE}/resource-manager/register/",
            json={
                'workflow_id': workflow_execution_id,
                'workflow_type': workflow_type,
                'campaign_id': campaign_id,
                'priority': priority
            },
            headers={'Authorization': 'Token your_api_token'},
            timeout=5
        )

        if response.status_code == 200:
            result = response.json()
            if result.get('success', False):
                status = result.get('status', 'queued')
                if status == 'running':
                    logger.info(f"Workflow {workflow_execution_id} started immediately (bot worker available)")
                else:
                    queue_position = result.get('queue_position', 0)
                    logger.info(f"Workflow {workflow_execution_id} queued (position {queue_position})")
                return result
            else:
                logger.warning(f"Failed to register workflow: {result.get('error', 'Unknown error')}")
                return result
        else:
            # Fallback to direct registration
            return _register_workflow_direct(workflow_execution_id, workflow_type, campaign_id, priority)
    except Exception as e:
        logger.exception(f"Error registering workflow: {str(e)}")
        # Fallback to direct registration
        return _register_workflow_direct(workflow_execution_id, workflow_type, campaign_id, priority)

def _register_workflow_direct(workflow_execution_id, workflow_type, campaign_id, priority=None):
    """Fallback method to register workflow directly"""
    try:
        # Check if bot worker is available
        active_workflow_key = redis_client.get("bot_worker:active_workflow")

        if active_workflow_key:
            # Bot worker is busy, add to queue
            # Store workflow info
            workflow_info = {
                'id': workflow_execution_id,
                'campaign_id': campaign_id,
                'workflow_type': workflow_type,
                'priority': priority or 0,
                'submission_time': datetime.now().isoformat(),
                'status': 'queued'
            }

            # Add to queue
            redis_client.rpush("bot_worker:queue", workflow_execution_id)

            # Store workflow info
            redis_client.set(
                f"workflow:{workflow_execution_id}:info",
                json.dumps(workflow_info)
            )
            redis_client.expire(f"workflow:{workflow_execution_id}:info", 86400)  # 24 hours

            logger.info(f"Workflow {workflow_execution_id} added to queue (direct)")

            return {
                'success': True,
                'status': 'queued',
                'workflow_id': workflow_execution_id,
                'message': 'Workflow queued (bot worker busy)'
            }
        else:
            # Bot worker is available, set as active
            # Store workflow info
            workflow_info = {
                'id': workflow_execution_id,
                'campaign_id': campaign_id,
                'workflow_type': workflow_type,
                'priority': priority or 0,
                'start_time': datetime.now().isoformat(),
                'status': 'running'
            }

            # Set as active workflow
            redis_client.set("bot_worker:active_workflow", workflow_execution_id)

            # Store workflow info
            redis_client.set(
                f"workflow:{workflow_execution_id}:info",
                json.dumps(workflow_info)
            )
            redis_client.expire(f"workflow:{workflow_execution_id}:info", 86400)  # 24 hours

            logger.info(f"Workflow {workflow_execution_id} set as active (direct)")

            return {
                'success': True,
                'status': 'running',
                'workflow_id': workflow_execution_id,
                'message': 'Workflow started immediately (bot worker available)'
            }
    except Exception as e:
        logger.exception(f"Error in direct workflow registration: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'workflow_id': workflow_execution_id
        }

def wait_for_resources(workflow_type, max_wait_time=3600, **context):
    """
    Wait for resources to become available.

    Args:
        workflow_type (str): Type of workflow
        max_wait_time (int): Maximum wait time in seconds
        context: Airflow task context

    Returns:
        bool: True if resources became available within the wait time
    """
    start_time = time.time()
    wait_interval = 30  # Start with 30 seconds

    while (time.time() - start_time) < max_wait_time:
        if check_resources_available(workflow_type, **context):
            return True

        # Log and wait
        elapsed = time.time() - start_time
        remaining = max_wait_time - elapsed
        logger.info(f"Waiting for resources for {workflow_type} workflow. "
                   f"Elapsed: {elapsed:.0f}s, Remaining: {remaining:.0f}s")

        # Sleep with exponential backoff (max 5 minutes)
        time.sleep(min(wait_interval, 300))
        wait_interval *= 1.5

    logger.warning(f"Timed out waiting for resources for {workflow_type} workflow after {max_wait_time}s")
    return False

def unregister_workflow(workflow_execution_id, workflow_type):
    """
    Unregister a workflow from the resource manager.

    Args:
        workflow_execution_id (str): Workflow execution ID
        workflow_type (str): Type of workflow

    Returns:
        dict: Unregistration result
    """
    try:
        # Try API call first
        response = requests.post(
            f"{RESOURCE_MANAGER_API_BASE}/resource-manager/unregister/",
            json={
                'workflow_id': workflow_execution_id,
                'workflow_type': workflow_type
            },
            headers={'Authorization': 'Token your_api_token'},
            timeout=5
        )

        if response.status_code == 200:
            result = response.json()
            if result.get('success', False):
                logger.info(f"Workflow {workflow_execution_id} unregistered successfully")
            else:
                logger.warning(f"Failed to unregister workflow: {result.get('error', 'Unknown error')}")
            return result
        else:
            # Fallback to direct unregistration
            return _unregister_workflow_direct(workflow_execution_id, workflow_type)
    except Exception as e:
        logger.exception(f"Error unregistering workflow: {str(e)}")
        # Fallback to direct unregistration
        return _unregister_workflow_direct(workflow_execution_id, workflow_type)

def _unregister_workflow_direct(workflow_execution_id, workflow_type):
    """Fallback method to unregister workflow directly"""
    try:
        # Check if this is the active workflow
        active_workflow = redis_client.get("bot_worker:active_workflow")

        if active_workflow and active_workflow.decode('utf-8') == workflow_execution_id:
            # Remove active workflow
            redis_client.delete("bot_worker:active_workflow")

            # Check if there are any workflows in the queue
            queue_length = redis_client.llen("bot_worker:queue")

            if queue_length > 0:
                # Get the next workflow from the queue
                next_workflow_id = redis_client.lpop("bot_worker:queue")

                if next_workflow_id:
                    next_workflow_id = next_workflow_id.decode('utf-8')

                    # Get workflow info
                    workflow_info_json = redis_client.get(f"workflow:{next_workflow_id}:info")

                    if workflow_info_json:
                        # Parse workflow info
                        workflow_info = json.loads(workflow_info_json)

                        # Update status
                        workflow_info['status'] = 'running'
                        workflow_info['start_time'] = datetime.now().isoformat()

                        # Set as active workflow
                        redis_client.set("bot_worker:active_workflow", next_workflow_id)

                        # Update workflow info
                        redis_client.set(
                            f"workflow:{next_workflow_id}:info",
                            json.dumps(workflow_info)
                        )

                        logger.info(f"Started next workflow {next_workflow_id} from queue")
        else:
            # Check if workflow is in the queue
            queue_length = redis_client.llen("bot_worker:queue")

            if queue_length > 0:
                # Get all workflows in the queue
                queue = redis_client.lrange("bot_worker:queue", 0, -1)

                # Check if workflow is in the queue
                for i, workflow_id in enumerate(queue):
                    if workflow_id.decode('utf-8') == workflow_execution_id:
                        # Remove from queue
                        redis_client.lrem("bot_worker:queue", 1, workflow_id)
                        logger.info(f"Removed workflow {workflow_execution_id} from queue")
                        break

        # Remove workflow info
        redis_client.delete(f"workflow:{workflow_execution_id}:info")

        logger.info(f"Workflow {workflow_execution_id} unregistered successfully (direct)")

        return {
            'success': True,
            'message': f"Workflow {workflow_execution_id} unregistered successfully"
        }
    except Exception as e:
        logger.exception(f"Error in direct workflow unregistration: {str(e)}")
        return {
            'success': False,
            'error': str(e),
            'workflow_id': workflow_execution_id
        }
