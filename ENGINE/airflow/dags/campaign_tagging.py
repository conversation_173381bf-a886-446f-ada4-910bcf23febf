"""
Campaign Tagging DAG for analyzing accounts and applying tags.

This DAG is responsible for:
1. Retrieving accounts collected by a campaign
2. Analyzing accounts based on campaign settings
3. Applying tags based on analysis results
4. Updating campaign results
5. Generating a whitelist of accounts that match the criteria
6. Tracking workflow execution progress

This DAG uses the new workflow tracking system instead of scoring profiles.
It creates a WorkflowExecution record to track the progress of the analysis
and updates it with the results when complete.

Configuration Parameters:
- campaign_id: ID of the campaign
- analysis_settings: Dictionary of analysis settings
  - enable_tagging: Whether to enable tagging (default: true)
  - target_tags: List of target tags to apply
  - min_followers: Minimum number of followers
  - max_followers: Maximum number of followers
  - active_workflows: Dictionary of active workflows (added by the system)
  - completed_workflows: Dictionary of completed workflows (added by the system)
  - workflow_statistics: Dictionary of workflow statistics (added by the system)
"""
import os
import json
import time
import random
import logging
import sys
import subprocess
from datetime import datetime, timedelta

from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.utils.dates import days_ago
from airflow.models import Variable
from airflow.hooks.base import BaseHook

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Default arguments for the DAG
default_args = {
    'owner': 'airflow',
    'depends_on_past': False,
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
}

# Create the DAG
dag = DAG(
    'campaign_tagging',
    default_args=default_args,
    description='Process campaign account tagging and analysis',
    schedule_interval=None,  # This DAG is triggered manually
    start_date=days_ago(1),
    tags=['campaign', 'tagging', 'analysis'],
)

def validate_campaign_config(**context):
    """
    Validate the campaign configuration and prepare for analysis.

    This function validates the campaign configuration passed to the DAG run.
    It checks for required fields, validates field values, and sets default values
    for optional parameters.

    Args:
        context: Airflow task context

    Returns:
        dict: Validated campaign configuration

    Raises:
        ValueError: If the configuration is invalid
    """
    try:
        # Get the campaign configuration from the DAG run configuration
        conf = context['dag_run'].conf

        # Log the configuration
        logger.info(f"Received campaign configuration: {conf}")

        # Check if configuration is empty
        if not conf:
            raise ValueError("Empty campaign configuration")

        # Validate required fields
        required_fields = ['campaign_id', 'analysis_settings']
        missing_fields = [field for field in required_fields if field not in conf]
        if missing_fields:
            raise ValueError(f"Missing required fields: {', '.join(missing_fields)}")

        # Get campaign ID
        campaign_id = conf['campaign_id']
        if not campaign_id:
            raise ValueError("Empty campaign_id")

        # Get analysis settings
        analysis_settings = conf['analysis_settings']

        # Validate analysis settings
        if not isinstance(analysis_settings, dict):
            raise ValueError("Analysis settings must be a dictionary")

        # Set default values for optional parameters
        if 'enable_tagging' not in analysis_settings:
            analysis_settings['enable_tagging'] = True
            logger.info("Setting default value for enable_tagging: True")

        # Validate min_followers and max_followers if provided
        if 'min_followers' in analysis_settings:
            min_followers = analysis_settings['min_followers']
            if not isinstance(min_followers, int) or min_followers < 0:
                logger.warning(f"Invalid min_followers: {min_followers}. Must be a non-negative integer.")
                analysis_settings['min_followers'] = 0

        if 'max_followers' in analysis_settings:
            max_followers = analysis_settings['max_followers']
            if not isinstance(max_followers, int) or max_followers < 0:
                logger.warning(f"Invalid max_followers: {max_followers}. Must be a non-negative integer.")
                analysis_settings['max_followers'] = 0

            # Check if min_followers and max_followers are consistent
            if 'min_followers' in analysis_settings and max_followers < analysis_settings['min_followers']:
                logger.warning(f"max_followers ({max_followers}) is less than min_followers ({analysis_settings['min_followers']}). Setting max_followers to min_followers.")
                analysis_settings['max_followers'] = analysis_settings['min_followers']

        # Validate target_tags if provided
        if 'target_tags' in analysis_settings:
            target_tags = analysis_settings['target_tags']
            if not isinstance(target_tags, list):
                logger.warning(f"Invalid target_tags: {target_tags}. Must be a list. Setting to empty list.")
                analysis_settings['target_tags'] = []
            else:
                # Remove any non-string or empty tags
                valid_tags = [tag for tag in target_tags if isinstance(tag, str) and tag]
                if len(valid_tags) != len(target_tags):
                    logger.warning(f"Some target_tags were invalid and were removed.")
                    analysis_settings['target_tags'] = valid_tags

        # Validate custom_criteria if provided
        if 'custom_criteria' in analysis_settings:
            custom_criteria = analysis_settings['custom_criteria']
            if not isinstance(custom_criteria, dict):
                logger.warning(f"Invalid custom_criteria: {custom_criteria}. Must be a dictionary. Setting to empty dict.")
                analysis_settings['custom_criteria'] = {}

        # Log successful validation
        logger.info(f"Campaign configuration validated successfully for campaign {campaign_id}")

        # Return the validated configuration
        return {
            'campaign_id': campaign_id,
            'analysis_settings': analysis_settings
        }

    except ValueError as e:
        # Log validation error
        logger.error(f"Campaign configuration validation error: {str(e)}")
        raise

    except Exception as e:
        # Log unexpected error
        logger.exception(f"Unexpected error validating campaign configuration: {str(e)}")
        raise ValueError(f"Unexpected error validating campaign configuration: {str(e)}")

def retrieve_campaign_accounts(**context):
    """
    Retrieve accounts collected by the campaign.

    This function retrieves the accounts collected by the campaign from the database.
    It applies any filters specified in the analysis settings.

    Args:
        context: Airflow task context

    Returns:
        dict: Campaign accounts and analysis settings
    """
    # Get the configuration from the previous task
    conf = context['task_instance'].xcom_pull(task_ids='validate_campaign_config')

    # Get campaign ID
    campaign_id = conf['campaign_id']

    # Get analysis settings
    analysis_settings = conf['analysis_settings']

    # Log the retrieval
    logger.info(f"Retrieving accounts for campaign: {campaign_id}")

    # Set up Django environment
    pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
    django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")

    # Add Django directory to path if not already there
    if django_dir not in sys.path:
        sys.path.append(django_dir)

    # Set up Django environment
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

    try:
        import django
        django.setup()

        # Import models
        from campaigns.models import Campaign
        from instagram.models import Accounts

        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)

        # Get accounts for campaign
        accounts_query = Accounts.objects.filter(campaign=campaign)

        # Apply filters from analysis settings
        if 'min_followers' in analysis_settings and analysis_settings['min_followers'] is not None:
            accounts_query = accounts_query.filter(followers__gte=analysis_settings['min_followers'])

        if 'max_followers' in analysis_settings and analysis_settings['max_followers'] is not None:
            accounts_query = accounts_query.filter(followers__lte=analysis_settings['max_followers'])

        # Apply custom filters if provided
        if 'custom_criteria' in analysis_settings and analysis_settings['custom_criteria']:
            custom_criteria = analysis_settings['custom_criteria']

            # Example: Filter by bio keywords
            if 'bio_keywords' in custom_criteria and custom_criteria['bio_keywords']:
                for keyword in custom_criteria['bio_keywords']:
                    accounts_query = accounts_query.filter(bio__icontains=keyword)

            # Example: Filter by location
            if 'location' in custom_criteria and custom_criteria['location']:
                accounts_query = accounts_query.filter(location__icontains=custom_criteria['location'])

        # Get accounts
        accounts = list(accounts_query.values())

        # Log the number of accounts retrieved
        logger.info(f"Retrieved {len(accounts)} accounts for campaign: {campaign_id}")

        # Create workflow execution record
        try:
            from campaigns.models.workflow import WorkflowExecution

            # Create workflow name
            workflow_name = f"campaign_{campaign_id}_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

            # Create workflow execution record
            workflow_execution = WorkflowExecution.objects.create(
                campaign=campaign,
                workflow_name=workflow_name,
                workflow_type='analysis',
                status='pending',
                parameters=analysis_settings,
                total_items=len(accounts),
                log_file=os.path.join(pyflow_base_dir, "ENGINE", "logs", f"{workflow_name}.log")
            )

            # Update workflow execution ID in XCom
            context['task_instance'].xcom_push(key='workflow_execution_id', value=str(workflow_execution.id))

            logger.info(f"Created workflow execution record: {workflow_execution.id}")
        except Exception as e:
            logger.warning(f"Could not create workflow execution record: {str(e)}")

        # Return the accounts and analysis settings
        return {
            'campaign_id': campaign_id,
            'campaign_name': campaign.name,
            'accounts': accounts,
            'analysis_settings': analysis_settings,
            'total_accounts': len(accounts)
        }

    except Exception as e:
        # Log error
        error_msg = f"Error retrieving campaign accounts: {str(e)}"
        logger.exception(error_msg)

        # If we can't retrieve accounts from the database, generate some test accounts
        # This is just for testing purposes and should be removed in production
        logger.warning("Generating test accounts for testing purposes")

        accounts = [
            {
                'id': i,
                'username': f'user_{i}',
                'followers': random.randint(100, 10000),
                'following': random.randint(100, 1000),
                'bio': f'This is a test bio for user_{i}',
                'posts': random.randint(10, 100),
                'location': 'Test Location',
                'is_private': random.choice([True, False]),
                'is_verified': random.choice([True, False]),
                'campaign_id': campaign_id
            }
            for i in range(1, 21)  # Generate 20 test accounts
        ]

        # Log the number of test accounts generated
        logger.info(f"Generated {len(accounts)} test accounts for campaign: {campaign_id}")

        # Return the test accounts and analysis settings
        return {
            'campaign_id': campaign_id,
            'campaign_name': f"Campaign {campaign_id}",
            'accounts': accounts,
            'analysis_settings': analysis_settings,
            'total_accounts': len(accounts),
            'is_test_data': True
        }

def analyze_accounts(**context):
    """
    Analyze accounts based on campaign settings.

    This function executes the PyFlow workflow for analyzing accounts based on
    the campaign settings. It applies tags to accounts based on the analysis results.

    Args:
        context: Airflow task context

    Returns:
        dict: Analysis results
    """
    # Get the data from the previous task
    data = context['task_instance'].xcom_pull(task_ids='retrieve_campaign_accounts')

    # Get campaign ID
    campaign_id = data['campaign_id']

    # Get campaign name
    campaign_name = data.get('campaign_name', f"Campaign {campaign_id}")

    # Get accounts
    accounts = data['accounts']

    # Get analysis settings
    analysis_settings = data['analysis_settings']

    # Get workflow execution ID if available
    workflow_execution_id = context['task_instance'].xcom_pull(key='workflow_execution_id')

    # Log the analysis
    logger.info(f"Analyzing {len(accounts)} accounts for campaign: {campaign_id}")

    # Set up Django environment
    pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
    django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")

    # Add Django directory to path if not already there
    if django_dir not in sys.path:
        sys.path.append(django_dir)

    # Set up Django environment
    os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")

    # Determine workflow file
    workflow_file = os.path.join(pyflow_base_dir, "ENGINE", "INSTA", "WORKFLOW", "MANUAL_ANALYSIS.pygraph")

    # Check if workflow file exists
    if not os.path.exists(workflow_file):
        error_msg = f"PyFlow workflow file not found: {workflow_file}"
        logger.error(error_msg)
        raise FileNotFoundError(error_msg)

    # Build PyFlow command
    pyflow_command = f"pyflow -m run -f {workflow_file} --campaign_id {campaign_id} --num_retries 3"

    # Add analysis settings
    if 'min_followers' in analysis_settings and analysis_settings['min_followers'] is not None:
        pyflow_command += f" --min_followers {analysis_settings['min_followers']}"

    if 'max_followers' in analysis_settings and analysis_settings['max_followers'] is not None:
        pyflow_command += f" --max_followers {analysis_settings['max_followers']}"

    # Add dynamic tags if enabled
    if analysis_settings.get('enable_tagging', True):
        pyflow_command += " --enable_tagging true"

        # Add target tags if specified
        if 'target_tags' in analysis_settings and analysis_settings['target_tags']:
            tags = ','.join(analysis_settings['target_tags'])
            pyflow_command += f" --target_tags {tags}"

    # Add workflow name for tracking
    if workflow_execution_id:
        pyflow_command += f" --workflow_execution_id {workflow_execution_id}"

    # Add custom criteria if provided
    if 'custom_criteria' in analysis_settings and analysis_settings['custom_criteria']:
        custom_criteria = analysis_settings['custom_criteria']

        # Convert custom criteria to JSON string
        import json
        custom_criteria_json = json.dumps(custom_criteria).replace('"', '\\"')
        pyflow_command += f" --custom_criteria \"{custom_criteria_json}\""

    # Log the command
    logger.info(f"Executing PyFlow command: {pyflow_command}")

    try:
        # Get workflow execution if available
        if workflow_execution_id:
            try:
                import django
                django.setup()

                from campaigns.models.workflow import WorkflowExecution

                # Get workflow execution
                workflow_execution = WorkflowExecution.objects.get(id=workflow_execution_id)

                # Update workflow execution status
                workflow_execution.status = 'running'
                workflow_execution.start_time = django.utils.timezone.now()
                workflow_execution.save()

                logger.info(f"Updated workflow execution status to 'running'")
            except Exception as e:
                logger.warning(f"Could not update workflow execution status: {str(e)}")

        # Execute the PyFlow workflow
        try:
            # Change to PyFlow engine directory
            os.chdir(pyflow_base_dir)

            # Execute the command
            process = subprocess.Popen(
                pyflow_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True
            )

            # Monitor the process
            stdout = []
            for line in iter(process.stdout.readline, ''):
                stdout.append(line)
                logger.info(f"PyFlow: {line.strip()}")

            # Wait for the process to complete
            process.wait()

            # Check return code
            if process.returncode != 0:
                error_msg = f"PyFlow workflow failed with return code {process.returncode}"
                logger.error(error_msg)

                # Update workflow execution if available
                if workflow_execution_id:
                    try:
                        workflow_execution.fail(error_msg)
                    except Exception as e:
                        logger.warning(f"Could not update workflow execution status: {str(e)}")

                raise RuntimeError(error_msg)

            # Log success
            logger.info("PyFlow workflow completed successfully")

            # Try to parse results from stdout
            analysis_results = []
            try:
                result_line = next((line for line in stdout if "RESULT:" in line), None)
                if result_line:
                    result_json = result_line.split("RESULT:", 1)[1].strip()
                    result_data = json.loads(result_json)

                    # Get analysis results
                    analysis_results = result_data.get('analysis_results', [])

                    logger.info(f"Parsed {len(analysis_results)} analysis results from PyFlow output")
                else:
                    logger.warning("No analysis results found in PyFlow output")
            except Exception as e:
                logger.warning(f"Error parsing analysis results: {str(e)}")

        except Exception as e:
            # Log error
            error_msg = f"Error executing PyFlow workflow: {str(e)}"
            logger.exception(error_msg)

            # Update workflow execution if available
            if workflow_execution_id:
                try:
                    workflow_execution.fail(error_msg)
                except Exception as e:
                    logger.warning(f"Could not update workflow execution status: {str(e)}")

            raise RuntimeError(error_msg)

        # If we couldn't parse results from PyFlow output, generate test results
        if not analysis_results:
            logger.warning("Generating test analysis results")

            # Generate test analysis results
            for account in accounts:
                # Generate tags
                tags = []
                if 'target_tags' in analysis_settings and analysis_settings['target_tags']:
                    for tag in analysis_settings['target_tags']:
                        # Randomly match tags
                        if random.random() > 0.5:
                            tags.append(tag)

                # Add some random tags
                random_tags = ['travel', 'food', 'photography', 'fashion', 'fitness']
                for tag in random_tags:
                    if random.random() > 0.7:
                        tags.append(tag)

                # Create analysis result
                analysis_results.append({
                    'username': account['username'],
                    'account_id': account.get('id'),
                    'tags': tags,
                    'confidence_score': random.uniform(0.5, 1.0),
                    'matched': len(tags) > 0
                })

        # Save analysis results to database
        try:
            import django
            django.setup()

            from campaigns.models import Campaign, TagAnalysisResult
            from django.utils import timezone

            # Get campaign
            campaign = Campaign.objects.get(id=campaign_id)

            # Save analysis results
            saved_results = []
            for result in analysis_results:
                # Get username
                username = result['username']

                # Get account ID if available
                account_id = result.get('account_id')

                # Get tags
                tags = result.get('tags', [])

                # Get confidence score
                confidence_score = result.get('confidence_score', 0.0)

                # Get matched flag
                matched = result.get('matched', False)

                # Create or update tag analysis result
                tag_result, created = TagAnalysisResult.objects.update_or_create(
                    campaign=campaign,
                    username=username,
                    defaults={
                        'tags': tags,
                        'confidence_score': confidence_score,
                        'matched': matched,
                        'analyzed_at': timezone.now()
                    }
                )

                # Add to saved results
                saved_results.append({
                    'id': str(tag_result.id),
                    'username': username,
                    'tags': tags,
                    'confidence_score': confidence_score,
                    'matched': matched,
                    'created': created
                })

            logger.info(f"Saved {len(saved_results)} analysis results to database")

            # Update workflow execution if available
            if workflow_execution_id:
                try:
                    # Calculate statistics
                    total_accounts = len(analysis_results)
                    total_tagged = sum(1 for result in analysis_results if result.get('tags'))
                    total_matched = sum(1 for result in analysis_results if result.get('matched', False))
                    avg_confidence = sum(result.get('confidence_score', 0.0) for result in analysis_results) / total_accounts if total_accounts > 0 else 0

                    # Update workflow execution
                    workflow_execution.complete({
                        'success': True,
                        'total_accounts': total_accounts,
                        'total_tagged': total_tagged,
                        'total_matched': total_matched,
                        'avg_confidence': avg_confidence
                    })

                    logger.info(f"Updated workflow execution with results")
                except Exception as e:
                    logger.warning(f"Could not update workflow execution with results: {str(e)}")

            # Return the analysis results
            return {
                'success': True,
                'campaign_id': campaign_id,
                'campaign_name': campaign_name,
                'analysis_results': analysis_results,
                'total_accounts': len(analysis_results),
                'total_tagged': sum(1 for result in analysis_results if result.get('tags')),
                'total_matched': sum(1 for result in analysis_results if result.get('matched', False)),
                'workflow_execution_id': workflow_execution_id
            }

        except Exception as e:
            # Log error
            error_msg = f"Error saving analysis results to database: {str(e)}"
            logger.exception(error_msg)

            # Return the analysis results without database integration
            return {
                'success': True,
                'campaign_id': campaign_id,
                'campaign_name': campaign_name,
                'analysis_results': analysis_results,
                'total_accounts': len(analysis_results),
                'total_tagged': sum(1 for result in analysis_results if result.get('tags')),
                'total_matched': sum(1 for result in analysis_results if result.get('matched', False)),
                'workflow_execution_id': workflow_execution_id,
                'database_error': error_msg
            }

    except Exception as e:
        # Log error
        error_msg = f"Error analyzing accounts: {str(e)}"
        logger.exception(error_msg)

        # Return error
        return {
            'success': False,
            'error': error_msg,
            'campaign_id': campaign_id,
            'campaign_name': campaign_name
        }

def update_campaign_results(**context):
    """
    Update campaign results with analysis results.

    This function updates the campaign results in the database based on the
    analysis results. It also updates the campaign status to reflect the
    completion of the analysis process.

    Args:
        context: Airflow task context

    Returns:
        dict: Updated campaign information
    """
    # Get the data from the previous task
    data = context['task_instance'].xcom_pull(task_ids='analyze_accounts')

    # Check if we have data to update
    if not data:
        logger.warning("No analysis data to update")
        return {
            'success': False,
            'error': 'No analysis data to update'
        }

    # Check if analysis was successful
    if not data.get('success', False):
        logger.warning(f"Analysis failed: {data.get('error', 'Unknown error')}")
        return {
            'success': False,
            'error': data.get('error', 'Analysis failed'),
            'campaign_id': data.get('campaign_id')
        }

    # Get campaign ID
    campaign_id = data['campaign_id']

    # Get campaign name
    campaign_name = data.get('campaign_name', f"Campaign {campaign_id}")

    # Get analysis results
    analysis_results = data.get('analysis_results', [])

    # Get workflow execution ID if available
    workflow_execution_id = data.get('workflow_execution_id')

    # Log the update
    logger.info(f"Updating results for campaign: {campaign_id}")

    # Calculate statistics
    total_accounts = len(analysis_results)
    total_tagged = sum(1 for result in analysis_results if result.get('tags'))
    total_matched = sum(1 for result in analysis_results if result.get('matched', False))
    avg_confidence = sum(result.get('confidence_score', 0.0) for result in analysis_results) / total_accounts if total_accounts > 0 else 0

    # Log the results
    logger.info(f"Campaign {campaign_id} analysis results:")
    logger.info(f"Total accounts: {total_accounts}")
    logger.info(f"Total tagged: {total_tagged}")
    logger.info(f"Total matched: {total_matched}")
    logger.info(f"Average confidence: {avg_confidence:.2f}")

    # Update the database
    try:
        # Set up Django environment
        import sys
        pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
        django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")
        if django_dir not in sys.path:
            sys.path.append(django_dir)

        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")
        import django
        django.setup()

        # Import models
        from campaigns.models import Campaign, CampaignAnalysisSettings
        from django.utils import timezone

        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)

        # Update campaign status if needed
        if campaign.status != 'completed':
            campaign.status = 'completed'
            campaign.updated_at = timezone.now()
            campaign.save()
            logger.info(f"Updated campaign status to 'completed'")

        # Update campaign analysis settings
        try:
            # Get or create analysis settings
            analysis_settings, created = CampaignAnalysisSettings.objects.get_or_create(campaign=campaign)

            # Update analysis results
            analysis_settings.total_accounts = total_accounts
            analysis_settings.total_tagged = total_tagged
            analysis_settings.total_matched = total_matched
            analysis_settings.avg_confidence = avg_confidence
            analysis_settings.last_analyzed = timezone.now()

            # Update workflow tracking information
            current_time = timezone.now().isoformat()
            workflow_name = f"analysis_{current_time}"

            # Initialize workflow tracking dictionaries if they don't exist
            if not hasattr(analysis_settings, 'active_workflows') or not analysis_settings.active_workflows:
                analysis_settings.active_workflows = {}

            if not hasattr(analysis_settings, 'completed_workflows') or not analysis_settings.completed_workflows:
                analysis_settings.completed_workflows = {}

            if not hasattr(analysis_settings, 'workflow_statistics') or not analysis_settings.workflow_statistics:
                analysis_settings.workflow_statistics = {}

            # Remove this workflow from active_workflows if it exists
            if workflow_execution_id and workflow_execution_id in analysis_settings.active_workflows:
                del analysis_settings.active_workflows[workflow_execution_id]

            # Add to completed_workflows
            if workflow_execution_id:
                analysis_settings.completed_workflows[workflow_execution_id] = {
                    'name': workflow_name,
                    'type': 'analysis',
                    'completed_at': current_time,
                    'status': 'completed',
                    'results': {
                        'total_accounts': total_accounts,
                        'total_tagged': total_tagged,
                        'total_matched': total_matched,
                        'avg_confidence': avg_confidence
                    }
                }

            # Update workflow statistics
            analysis_settings.workflow_statistics['last_analysis'] = {
                'timestamp': current_time,
                'total_accounts': total_accounts,
                'total_tagged': total_tagged,
                'total_matched': total_matched,
                'avg_confidence': avg_confidence
            }

            # Save changes
            analysis_settings.save()

            logger.info(f"Updated campaign analysis settings with workflow tracking information")
        except Exception as e:
            logger.warning(f"Could not update campaign analysis settings: {str(e)}")

        # Return success
        return {
            'success': True,
            'campaign_id': str(campaign.id),
            'campaign_name': campaign.name,
            'total_accounts': total_accounts,
            'total_tagged': total_tagged,
            'total_matched': total_matched,
            'avg_confidence': avg_confidence,
            'workflow_execution_id': workflow_execution_id
        }

    except Exception as e:
        # Log error
        error_msg = f"Error updating campaign results: {str(e)}"
        logger.exception(error_msg)

        # Return error
        return {
            'success': False,
            'error': error_msg,
            'campaign_id': campaign_id,
            'campaign_name': campaign_name,
            'total_accounts': total_accounts,
            'total_tagged': total_tagged,
            'total_matched': total_matched,
            'avg_confidence': avg_confidence
        }

def generate_whitelist(**context):
    """
    Generate a whitelist based on analysis results.

    This function generates a whitelist of accounts that matched the tagging criteria.
    It saves the whitelist to the database for use in CEP workflows.

    Args:
        context: Airflow task context

    Returns:
        dict: Whitelist information
    """
    # Get the data from the previous task
    data = context['task_instance'].xcom_pull(task_ids='analyze_accounts')

    # Check if we have data to process
    if not data:
        logger.warning("No analysis data to generate whitelist")
        return {
            'success': False,
            'error': 'No analysis data to generate whitelist'
        }

    # Check if analysis was successful
    if not data.get('success', False):
        logger.warning(f"Analysis failed: {data.get('error', 'Unknown error')}")
        return {
            'success': False,
            'error': data.get('error', 'Analysis failed'),
            'campaign_id': data.get('campaign_id')
        }

    # Get campaign ID
    campaign_id = data['campaign_id']

    # Get campaign name
    campaign_name = data.get('campaign_name', f"Campaign {campaign_id}")

    # Get analysis results
    analysis_results = data.get('analysis_results', [])

    # Log the whitelist generation
    logger.info(f"Generating whitelist for campaign: {campaign_id}")

    # Filter accounts that matched tags
    whitelist = [result for result in analysis_results if result.get('matched', False)]

    # Log the whitelist
    logger.info(f"Generated whitelist with {len(whitelist)} accounts")

    # Log the first few accounts
    for account in whitelist[:5]:  # Log first 5 accounts
        logger.info(f"- {account['username']} (Tags: {', '.join(account.get('tags', []))})")

    if len(whitelist) > 5:
        logger.info(f"... and {len(whitelist) - 5} more accounts")

    # Update the database
    try:
        # Set up Django environment
        import sys
        pyflow_base_dir = "/usr/local/lib/python3.10/dist-packages/PyFlow"
        django_dir = os.path.join(pyflow_base_dir, "ENGINE", "CommandNetSolutions")
        if django_dir not in sys.path:
            sys.path.append(django_dir)

        os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")
        import django
        django.setup()

        # Import models
        from campaigns.models import Campaign
        from instagram.models import Accounts, WhiteListEntry
        from django.utils import timezone

        # Get campaign
        campaign = Campaign.objects.get(id=campaign_id)

        # Create whitelist entries
        whitelist_entries = []
        for account_data in whitelist:
            # Get username
            username = account_data['username']

            # Get tags
            tags = account_data.get('tags', [])

            # Get confidence score
            confidence_score = account_data.get('confidence_score', 0.0)

            try:
                # Get account
                account = Accounts.objects.get(username=username, campaign=campaign)

                # Create or update whitelist entry
                whitelist_entry, created = WhiteListEntry.objects.update_or_create(
                    account=account,
                    defaults={
                        'tags': tags,
                        'confidence_score': confidence_score,
                        'added_at': timezone.now()
                    }
                )

                # Add to whitelist entries
                whitelist_entries.append({
                    'id': str(whitelist_entry.id),
                    'username': username,
                    'tags': tags,
                    'confidence_score': confidence_score,
                    'created': created
                })
            except Accounts.DoesNotExist:
                logger.warning(f"Account not found: {username}")
                continue
            except Exception as e:
                logger.warning(f"Error creating whitelist entry for {username}: {str(e)}")
                continue

        logger.info(f"Saved {len(whitelist_entries)} whitelist entries to database")

        # Update campaign with whitelist count
        campaign.whitelist_count = len(whitelist_entries)
        campaign.save()

        # Return success
        return {
            'success': True,
            'campaign_id': str(campaign.id),
            'campaign_name': campaign.name,
            'whitelist_count': len(whitelist_entries),
            'total_matched': len(whitelist)
        }

    except Exception as e:
        # Log error
        error_msg = f"Error generating whitelist: {str(e)}"
        logger.exception(error_msg)

        # Return error
        return {
            'success': False,
            'error': error_msg,
            'campaign_id': campaign_id,
            'campaign_name': campaign_name,
            'whitelist': whitelist,
            'whitelist_count': len(whitelist)
        }

# Define the tasks
validate_config_task = PythonOperator(
    task_id='validate_campaign_config',
    python_callable=validate_campaign_config,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=5),
    doc_md="""
    Validate the campaign configuration and prepare for analysis.

    This task validates the campaign configuration passed to the DAG run.
    It checks for required fields, validates field values, and sets default values
    for optional parameters.

    **Inputs:**
    - DAG run configuration

    **Outputs:**
    - Validated campaign configuration
    """
)

retrieve_accounts_task = PythonOperator(
    task_id='retrieve_campaign_accounts',
    python_callable=retrieve_campaign_accounts,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=10),
    doc_md="""
    Retrieve accounts collected by the campaign.

    This task retrieves the accounts collected by the campaign from the database.
    It applies any filters specified in the analysis settings.

    **Inputs:**
    - Validated campaign configuration

    **Outputs:**
    - Campaign accounts and analysis settings
    """
)

analyze_accounts_task = PythonOperator(
    task_id='analyze_accounts',
    python_callable=analyze_accounts,
    provide_context=True,
    dag=dag,
    retries=2,
    retry_delay=timedelta(minutes=5),
    execution_timeout=timedelta(hours=2),
    doc_md="""
    Analyze accounts based on campaign settings.

    This task executes the PyFlow workflow for analyzing accounts based on
    the campaign settings. It applies tags to accounts based on the analysis results.

    **Inputs:**
    - Campaign accounts and analysis settings

    **Outputs:**
    - Analysis results
    """
)

update_results_task = PythonOperator(
    task_id='update_campaign_results',
    python_callable=update_campaign_results,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=10),
    doc_md="""
    Update campaign results with analysis results.

    This task updates the campaign results in the database based on the
    analysis results. It also updates the campaign status to reflect the
    completion of the analysis process.

    **Inputs:**
    - Analysis results

    **Outputs:**
    - Updated campaign information
    """
)

generate_whitelist_task = PythonOperator(
    task_id='generate_whitelist',
    python_callable=generate_whitelist,
    provide_context=True,
    dag=dag,
    retries=3,
    retry_delay=timedelta(minutes=1),
    execution_timeout=timedelta(minutes=10),
    doc_md="""
    Generate a whitelist based on analysis results.

    This task generates a whitelist of accounts that matched the tagging criteria.
    It saves the whitelist to the database for use in CEP workflows.

    **Inputs:**
    - Analysis results

    **Outputs:**
    - Whitelist information
    """
)

# Define the task dependencies
validate_config_task >> retrieve_accounts_task >> analyze_accounts_task >> [update_results_task, generate_whitelist_task]
