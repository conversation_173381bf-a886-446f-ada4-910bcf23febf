# Airflow DAGs for Campaign System

This directory contains Airflow DAGs for the CommandNetSolutions campaign system. These DAGs orchestrate the execution of PyFlow workflows for data collection, analysis, and customer engagement processes.

## DAG Overview

### 1. campaign_data_collection.py

This DAG processes campaign data collection requests from the CommandNetSolutions system. It handles location-based, username-based, and mixed campaigns with different audience types.

**Key Features:**
- Support for mixed target types (both location and username targets)
- Workflow progress tracking
- Error handling and reporting
- Integration with Django models

**Configuration Parameters:**
- `campaign_id`: ID of the campaign
- `target_type`: 'location', 'username', or 'mixed'
- `audience_type`: 'profile', 'followers', 'following', or 'both'
- `location_targets`: List of location targets (for location-based or mixed campaigns)
- `username_targets`: List of username targets (for username-based or mixed campaigns)
- `max_accounts`: (optional) Maximum number of accounts to collect
- `min_followers`: (optional) Minimum number of followers for collected accounts
- `max_followers`: (optional) Maximum number of followers for collected accounts

### 2. campaign_tagging.py

This DAG analyzes accounts collected by a campaign and applies tags based on analysis settings. It uses the new workflow tracking system instead of scoring profiles.

**Key Features:**
- Dynamic tag application based on campaign settings
- Workflow progress tracking
- Whitelist generation for accounts that match criteria
- Integration with Django models

**Configuration Parameters:**
- `campaign_id`: ID of the campaign
- `analysis_settings`: Dictionary of analysis settings
  - `enable_tagging`: Whether to enable tagging (default: true)
  - `target_tags`: List of target tags to apply
  - `min_followers`: Minimum number of followers
  - `max_followers`: Maximum number of followers
  - `active_workflows`: Dictionary of active workflows (added by the system)
  - `completed_workflows`: Dictionary of completed workflows (added by the system)
  - `workflow_statistics`: Dictionary of workflow statistics (added by the system)

### 3. campaign_cep.py

This DAG executes Customer Engagement Process (CEP) workflows on whitelisted accounts. It supports different workflow types for various engagement actions.

**Key Features:**
- Support for different workflow types (follow, like, comment, DM, all)
- Batch processing of accounts
- Workflow progress tracking
- Integration with Django models

**Configuration Parameters:**
- `campaign_id`: ID of the campaign
- `workflow_type`: Type of workflow to execute ('follow', 'like', 'comment', 'dm', or 'all')
- `batch_size`: Number of accounts to process in each batch (default: 10)
- `max_actions`: Maximum number of actions to perform (default: 50)
- `delay_between_actions`: Delay between actions in seconds (default: 60)
- `message_template`: Template for DM messages (for 'dm' workflow type)
- `comment_template`: Template for comments (for 'comment' workflow type)

## Integration with Django

The DAGs integrate with the Django application through:

1. **AirflowService**: A Django service that triggers DAGs with proper configuration
2. **WorkflowExecution**: A Django model that tracks workflow execution progress
3. **WorkflowProgressService**: A Django service that updates workflow progress

## Workflow Tracking System

The new workflow tracking system replaces the scoring profiles system. It tracks:

1. **Active Workflows**: Workflows that are currently running
2. **Completed Workflows**: Workflows that have completed execution
3. **Workflow Statistics**: Statistics about workflow execution

This information is stored in the `CampaignAnalysisSettings` model as JSON fields.

## Running DAGs

DAGs can be triggered through the AirflowService in the Django application:

```python
from campaigns.services import AirflowService

# Initialize service
airflow_service = AirflowService()

# Trigger data collection DAG
response = airflow_service.trigger_campaign_data_collection(
    campaign_id='campaign-uuid',
    target_type='mixed',
    audience_type='profile',
    location_targets=[{'location_id': '123', 'city': 'New York', 'country': 'USA'}],
    username_targets=['user1', 'user2']
)

# Trigger tagging DAG
response = airflow_service.trigger_campaign_tagging(
    campaign_id='campaign-uuid',
    analysis_settings={
        'enable_tagging': True,
        'target_tags': ['travel', 'food'],
        'min_followers': 1000,
        'max_followers': 10000
    }
)

# Trigger CEP DAG
response = airflow_service.trigger_campaign_cep(
    campaign_id='campaign-uuid',
    workflow_type='follow',
    batch_size=10,
    max_actions=50,
    delay_between_actions=60
)
```

## Error Handling

All DAGs include comprehensive error handling:

1. **Input Validation**: Validate all input parameters before execution
2. **Exception Handling**: Catch and log exceptions during execution
3. **Retry Logic**: Retry failed tasks with exponential backoff
4. **Fallback Mechanisms**: Generate test data if database access fails

## Monitoring

DAG execution can be monitored through:

1. **Airflow UI**: View DAG runs, task instances, and logs
2. **Django Admin**: View workflow execution records and progress
3. **Campaign Detail Page**: View workflow progress and results
