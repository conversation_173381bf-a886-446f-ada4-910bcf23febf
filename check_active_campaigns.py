#!/usr/bin/env python
"""
Scrip<PERSON> to check for active campaigns in the Instagram automation system.
"""
import os
import sys
import django
from datetime import datetime

# Set up Django environment
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "CommandNetSolutions.settings")
django.setup()

# Import models after Django setup
from campaigns.models import Campaign

def check_active_campaigns():
    """
    Query the database for campaigns with 'running' or 'pending' status.
    """
    active_campaigns = Campaign.objects.filter(status__in=['running', 'pending'])
    
    if not active_campaigns.exists():
        print("No active campaigns found with 'running' or 'pending' status.")
        return
    
    print(f"Found {active_campaigns.count()} active campaign(s):\n")
    print(f"{'Name':<30} {'Status':<10} {'Launch Date':<20} {'Target Type':<15} {'Airflow DAG ID':<30} {'Airflow Run ID':<30}")
    print("-" * 135)
    
    for campaign in active_campaigns:
        # Format created_at as launch date
        launch_date = campaign.created_at.strftime("%Y-%m-%d %H:%M:%S")
        
        # Get target type display
        target_type = campaign.get_target_type_display() if hasattr(campaign, 'get_target_type_display') else campaign.target_type
        
        # Print campaign details
        print(f"{campaign.name:<30} {campaign.status:<10} {launch_date:<20} {target_type:<15} {campaign.airflow_dag_id or 'N/A':<30} {campaign.airflow_run_id or 'N/A':<30}")

if __name__ == "__main__":
    check_active_campaigns()
