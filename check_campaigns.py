import os
import sys
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'CommandNetSolutions.settings')
django.setup()

# Import models
from campaigns.models import Campaign

# Get all campaigns
campaigns = Campaign.objects.all()
print(f"Total campaigns: {campaigns.count()}")

# Initialize counters
location_only = 0
username_only = 0
mixed = 0

# Count campaigns by their actual targets
print("\nCampaign Types:")
for campaign in campaigns:
    has_location_targets = campaign.location_targets.exists()
    has_username_targets = campaign.username_targets.exists()
    
    print(f"ID: {campaign.id}")
    print(f"Name: {campaign.name}")
    print(f"Target Type: {campaign.target_type}")
    print(f"Has Location Targets: {has_location_targets}")
    print(f"Has Username Targets: {has_username_targets}")
    print("-" * 50)
    
    # Count by actual targets
    if has_location_targets and has_username_targets:
        mixed += 1
    elif has_location_targets:
        location_only += 1
    elif has_username_targets:
        username_only += 1

# Calculate percentages
total_with_targets = location_only + username_only + mixed
print(f"\nSummary:")
print(f"Location Only: {location_only} campaigns")
print(f"Username Only: {username_only} campaigns")
print(f"Mixed: {mixed} campaigns")
print(f"Total with targets: {total_with_targets} campaigns")

if total_with_targets > 0:
    location_percentage = round((location_only / total_with_targets) * 100)
    username_percentage = round((username_only / total_with_targets) * 100)
    mixed_percentage = round((mixed / total_with_targets) * 100)
    
    print(f"\nPercentages:")
    print(f"Location Only: {location_percentage}%")
    print(f"Username Only: {username_percentage}%")
    print(f"Mixed: {mixed_percentage}%")
    print(f"Total: {location_percentage + username_percentage + mixed_percentage}%")
